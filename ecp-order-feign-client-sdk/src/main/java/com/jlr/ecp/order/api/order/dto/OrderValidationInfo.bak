package com.jlr.ecp.order.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 1.里面的属性需要被后台校验
 * 2.需要与product-service同步
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "后端逻辑需要校验的OrderDTO")
public class OrderValidationInfo {

    /**
     * 购物车item基本信息
     * TODO carItem 改成productItem
     */
    private List<CartItemDTO> cartItems;

    /**
     * 用于快速校验的映射（可选）
     */
    private Map<String, CartItemDTO> cartItemMap;

    /**
     * 所有需要校验的金额
     */
    private PaymentInfoDTO paymentInfoDTO;
}
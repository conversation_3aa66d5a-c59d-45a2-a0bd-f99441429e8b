package com.jlr.ecp.order.api.refund.vo;


import com.jlr.ecp.order.api.order.vo.ECouponOrderItemVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @createDate 14/03/2025 16:38
 **/
@Data
public class RefundOrderProductInfoVO extends ECouponOrderItemVO {

    @Schema(description = "退款金额")
    private String refundMoney;

    @Schema(description = "退单实付积分")
    private Integer refundPoint;

    @Schema(description = "是否退回卡券")
    private Boolean isCouponRefunded = false;

    @Schema(description = "BG最大可退金额")
    private String maxRefundMoney;
}

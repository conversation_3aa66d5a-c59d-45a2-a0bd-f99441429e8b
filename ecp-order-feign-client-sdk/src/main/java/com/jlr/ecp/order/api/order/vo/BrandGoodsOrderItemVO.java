package com.jlr.ecp.order.api.order.vo;

import java.util.List;

import com.jlr.ecp.order.api.cart.vo.ProductAttributeRespVO;
import com.jlr.ecp.order.api.order.vo.coupon.ECouponOrderDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - BG订单项VO")
public class BrandGoodsOrderItemVO {

    // t_order_refund_item => refund_order_code
    @Schema(description = "退单编号")
    private String refundOrderCode;

    // t_order_item => order_code
    @Schema(description = "订单编号", hidden = true)
    private String orderCode;

    // t_order_item => order_item_code
    @Schema(description = "订单明细编号")
    private String orderItemCode;
    
    // t_order_item => product_image_url
    @Schema(description = "商品主图URL")
    private String productImageUrl;
    
    // t_order_item => product_name
    @Schema(description = "商品名称")
    private String productName;

    // t_order_item_logistics => logistics_no
    @Schema(description = "物流单号")
    private String logisticsNo;

    // t_order_item => product_code
    @Schema(description = "商品SPU编码")
    private String productCode;

    // t_order_item => product_sku_code
    @Schema(description = "商品SKU编码")
    private String productSkuCode;

    // t_order_item => kingdee_sku_code
    @Schema(description = "订单商品SKU ID - 金蝶sku编码")
    private String kingdeeSkuCode;

    // t_order_item => product_quantity
    @Schema(description = "购买数量")
    private Integer productQuantity;
    
    // t_order_item => product_sale_price
    @Schema(description = "售价(单价)")
    private String productSalePrice;
    
    // t_order_item => cost_amount
    @Schema(description = "实付现金金额")
    private String costAmount;

    // t_order_item => point_amount
    @Schema(description = "实付积分")
    private Integer pointAmount;

    // t_order_item => aftersales_status
    @Schema(description = "售后状态")
    private Integer afterSalesStatus;
    
    // t_order_refund => refund_order_status
    @Schema(description = "售后订单状态，1-4表示处理中")
    private Integer refundOrderStatus;
    // t_order_item_logistics => logistics_status
    @Schema(description = "物流状态")
    private Integer logisticsStatus;

    // t_order_item => item_status
    @Schema(description = "订单行状态")
    private Integer itemStatus;

    // 订单详情.商品信息.兑换券列表
    // t_order_coupon_detail => order_item_code => return ECouponOrderDetailVO
    @Schema(description = "兑换券列表")
    private List<ECouponOrderDetailVO> couponCodeList;

    // t_order_item => product_attribute
    @Schema(description = "属性")
    private String productAttribute;

    // t_order_item_detail product_market_price
    @Schema(description = "标价（单价）")
    private String productMarketPrice;

    //  t_order_discount_detail discount_amount
    @Schema(description = "卡券分摊金额")
    private String discountAmount;

    //  t_order_discount_detail coupon_model_classify
    @Schema(description = "卡券类型")
    private String discountCouponType;

    //  t_order_discount_detail coupon_model_name
    @Schema(description = "卡券名称")
    private String discountCouponName;

    //  t_order_discount_detail coupon_code
    @Schema(description = "卡券编码")
    private String discountCouponCode;

    // 最大可退款金额
    @Schema(description = "最大可退款金额")
    private String maxRefundableAmount;
    
    // 退款理由-7天无理由的可选的最后一天
    @Schema(description = "退款理由-7天无理由的可选的最后一天")
    private String noReasonReturnsTime;

    // 正向订单——订单行上的售后状态
    @Schema(description = "正向订单行的售后状态")
    private Integer itemAfterSalesStatus;

}

package com.jlr.ecp.order.api.refund.dto;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "查询售后单 For 管易云入参")
@Data
public class OrderRefundForGyyPageReqDTO  extends PageParam {

    @Schema(description = "修改时间开始")
    private LocalDateTime startTime;
    @Schema(description = "修改时间结束")
    private LocalDateTime endTime;
    @Schema(description = "售后单号")
    private String refundOrderCode;
    @Schema(description = "物流退单状态")
    private List<Integer> logisticsRefundStatusList;
    @Schema(description = "业务线编码")
    private List<String> businessCodes;
    @Schema(description = "履约方式（order里会强制设置为“实物商品”）")
    private List<Integer> filfullments;

}
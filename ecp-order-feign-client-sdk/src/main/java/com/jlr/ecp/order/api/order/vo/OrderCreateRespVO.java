package com.jlr.ecp.order.api.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端 - OrderCreateRespVO")
public class OrderCreateRespVO {
    @Schema(description = "父订单号")
    String parentOrderCode;

    @Schema(description = "订单号列表")
    private List<String> orderCodeList;

    @Schema(description = "vcs类型下是否有未支付的订单")
    private Boolean hasUnpaidOrder;

    @Schema(description = "当前有已支付的订单，请勿重复下单")
    private Boolean hasPaidOrder;

    @Schema(description = "当前有售后处理中的订单，请勿重复下单")
    private Boolean hasAfterSalesOrder;

    @Schema(description = "vcs类型下是否有正在激活中 激活关闭中的服务")
    private Boolean hasLongExpireOrActiveOrder;

    @Schema(description = "用户对同个VIN，在别人的微信上有未支付订单但想在自己的微信下单时")
    private Boolean hasUnpaidOrderInOtherWechat;

    @Schema(description = "用户有VIN在手动续费中")
    private Boolean hasManualRenewalOrder;

    @Schema(description = "当前有售后处理完成的订单，请勿重复下单")
    private Boolean hasRefundedOrder;

    @Schema(description = "支付金额")
    private BigDecimal amountToPay;

    @Schema(description = "当前有ICR关系验证中，请稍后再试")
    private Boolean hasICRValidating;

    @Schema(description = "当前有未绑定的ICR")
    private Boolean hasUnbindICR;

    @Schema(description = "当前有已换绑的ICR")
    private Boolean hasChangedICR;

    @Schema(description = "vinList")
    private List<String> carVinList;
}

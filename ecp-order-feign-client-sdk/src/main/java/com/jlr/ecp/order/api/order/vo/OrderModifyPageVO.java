package com.jlr.ecp.order.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - order Modify VO")
public class OrderModifyPageVO {

    /**
     * 操作记录id
     */
    @Schema(description = "操作记录id")
    private Long id;

    /**
     * 操作时间
     */
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;


    /**
     * 操作内容
     */
    @Schema(description = "修改模块")
    private String modifyModule;

    /**
     * 操作人
     */
    @Schema(description = "修改人")
    private String operateUser;

    /**
     * 修改字段
     */
    @Schema(description = "修改字段")
    private String modifyField;

    /**
     * 修改数量
     */
    @Schema(description = "修改数量")
    private int modifyFieldCount;
}

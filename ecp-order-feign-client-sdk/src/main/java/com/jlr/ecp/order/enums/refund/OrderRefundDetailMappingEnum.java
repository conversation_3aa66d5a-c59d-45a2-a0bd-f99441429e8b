package com.jlr.ecp.order.enums.refund;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 * 退单订单状态：
 * 1：发起整单退款申请
 * 2：发起部分退款申请
 * 3：同意整单退款申请
 * 4：同意部分退款申请
 * 5：订单整单退款完成
 * 6：订单部分退款完成
 * 7：拒绝整单退单申请
 * 8：拒绝部分退款申请
 */
@AllArgsConstructor
@Getter
public enum OrderRefundDetailMappingEnum {

    /****
     * 退单订单状态：
     * 1：发起整单退款申请
     * 2：发起部分退款申请
     * 3：同意整单退款申请
     * 4：同意部分退款申请
     * 5：订单整单退款完成
     * 6：订单部分退款完成
     * 7：拒绝整单退单申请
     * 8：拒绝部分退款申请
     */
    FULL_REFUND_APPLY(1, "退款申请已提交，请耐心等待。"),
    PARTIAL_REFUND_APPLY(2, "退款申请已提交，请耐心等待。"),
    FULL_REFUND_APPROVE(3, "银行已受理，请耐心等待。"),
    PARTIAL_REFUND_APPROVE(4, "银行已受理，请耐心等待。"),
    FULL_REFUND_COMPLETED(5, "钱款已原路退回。"),
    PARTIAL_REFUND_COMPLETED(6, "钱款已原路退回。"),
    FULL_REFUND_REFUSE(7, "您可联系客服中心了解详情。"),
    PARTIAL_REFUND_REFUSE(8, "您可联系客服中心了解详情。");

    /**
     * code
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;

    public static String getDescriptionByCode(Integer code) {
        for (OrderRefundDetailMappingEnum status : OrderRefundDetailMappingEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
       return null;
    }

    public static String getDescriptionCode(Integer code) {
        for (OrderRefundDetailMappingEnum status : OrderRefundDetailMappingEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        return null;
    }
}

package com.jlr.ecp.order.api.order.vo.detail;

import com.jlr.ecp.order.api.order.vo.address.OrderGiftAddressDetailVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.ProductBrandCategoriedItemInfoVO;
import com.jlr.ecp.order.api.order.vo.feedback.OrderFeedbackInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 订单详情的 Response VO")
@Data
public class OrderDetailRespVO {
    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderCode;


    /**
     * 订单状态
     */
    @Schema(description = "订单状态")
    private OrderDetailOrderStatusVO orderStatusInfo;

    /**
     * 订单信息
     */
    @Schema(description = "订单信息")
    private OrderDetailOrderInfoVO orderInfo;

    /**
     * 客户信息
     */
    @Schema(description = "客户信息")
    private OrderDetailCustomerInfoVO customerInfo;

    /**
     * 车辆信息
     */
    @Schema(description = "车辆信息")
    private OrderDetailVehicleInfoVO carInfo;

    /**
     * 电子普票信息
     */
    @Schema(description = "电子普票信息")
    private OrderDetailEInvoiceInfoVO eInvoiceInfo;

    /**
     * 纸质专票信息
     */
    @Schema(description = "纸质专票信息")
    private OrderDetailPaperInvoiceInfoVO paperInvoiceInfo;

    /**
     * 商品信息
     */
    @Schema(description = "商品信息")
    private OrderDetailProductInfoVO productInfo;

    /**
     * 赠品信息
     */
    @Schema(description = "赠品信息")
    private OrderGiftAddressDetailVO giftInfo;

    /**
     * sprint47 订单-评价信息
     */
    @Schema(description = "订单-评价信息")
    private List<OrderFeedbackInfo> feedbackInfos;


    /**
     * 支付信息
     * 
     * LRE 订单
     */
    @Schema(description = "支付信息")
    private OrderDetailPaymentInfoVO paymentInfo;

    /**
     * 电子专票信息
     * LRE 订单
     */
    @Schema(description = "电子专票信息")
    private OrderDetailESpecialInvoiceInfoVO eSpecialInvoiceInfo;

    /**
     * 是否展示完成订单按钮
     */
    @Schema(description = "是否展示完成订单按钮")
    private boolean showCompleteOrderButton;
}

package com.jlr.ecp.order.enums.refund;

/**
 * 售后类型
 * author: maojie
 */
public enum RefundOrderTypeEnum {
    REFUND_RETURN_GOODS(1, "退货退款"),
    REFUND_ONLY(2, "仅退款");

    private final int code;
    private final String name;

    RefundOrderTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static RefundOrderTypeEnum getByCode(int code) {
        for (RefundOrderTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null; // 或抛出 IllegalArgumentException
    }

    public static String getNameByCode(int code) {
        for (RefundOrderTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type.getName();
            }
        }
        return "";
    }
}

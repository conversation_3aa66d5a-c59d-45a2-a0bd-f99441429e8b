package com.jlr.ecp.order.api.refund.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "订单管理 - 用户提交物流信息")
@Data
@ToString(callSuper = true)
@Validated
public class OrderRefundLogisticsDTO {

    @Schema(description = "物流单号 ")
    @NotNull(message = "物流单号不能为空")
    private String logisticsCode;

    @Schema(description = "退单订单号")
    @NotBlank(message = "订单号不能为空")
    private String refundOrderCode;

    @Schema(description = "退单订单号")
    @NotBlank(message = "订单行号不能为空")
    private String refundOrderItemCode;

    @Schema(description = "物流公司名称")
    @NotNull(message = "物流公司不能为空")
    private String logisticsCompanyName;

    @Schema(description = "物流公司Code")
    @NotNull(message = "物流公司Code不能为空")
    private String logisticsCompanyCode;

    @Schema(description = "凭证图片地址")
    private List<String> attachmentUrls;

}

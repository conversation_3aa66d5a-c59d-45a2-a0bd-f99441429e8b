package com.jlr.ecp.order.api.refund.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - product item DTO")
@Validated
public class ProductItemInfoDTO {

    /**
     * 订单item编码
     */
    @Schema(description = "订单item编码")
    private String orderItemCode;

    /**
     * 商品快照编码
     */
    @Schema(description = "商品快照编码")
    private String productVersionCode;

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    private String productCode;

    /**
     * 商品SKU编码
     */
    @Schema(description = "商品SKU编码")
    private String productSkuCode;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String productName;

    /**
     * 购买数量
     */
    @Schema(description = "购买数量")
    private Integer productQuantity;

    /**
     * 售价
     */
    @Schema(description = "售价")
    private String productSalePrice;

    /**
     * 服务结束时间;服务结束时间
     */
    @Schema(description = "实际服务截止日期")
    @NotNull(message = "实际服务截止日期不能为空")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime serviceEndDate;

    /**
     * 子商品 item list
     */
    @Schema(description = "子商品信息")
    List<ProductItemInfoDTO> next;
}

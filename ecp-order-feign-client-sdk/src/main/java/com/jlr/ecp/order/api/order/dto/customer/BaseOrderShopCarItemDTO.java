package com.jlr.ecp.order.api.order.dto.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "订单创建-购物车item 基础DTO")
public class BaseOrderShopCarItemDTO {
    @Schema(description="单个商品品牌编码 路虎：LR + 捷豹：JA")
    @NotBlank(message = "品牌编码不能为空")
    private String itemBrandCode;

    /**
     * 购物车code
     */
    @Schema(description = "购物车code" +
            "待客下单；pdp无", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cartCode;

    /**
     * 购物车商品编码
     */
    @Schema(description = "购物车商品编码" +
            "待客下单；pdp无", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cartItemCode;

    /**
     * 购物车商品类型=商品履约类型 枚举值0 1 2 3
     * 0：虚拟组合商品履约
     * 1：远程车控Remote Service
     * 2：PIVI Subscription Service
     * 3：实物商品
     */
    @Schema(description = "购物车商品类型=商品履约类型 枚举值0 1 2 3 。0：虚拟组合商品履约 1：远程车控；VCS 2：PIVI Subscription Service 3：实物商品；Brand Goods", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "购物车商品类型不能为空")
    private Integer cartItemType;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "商品编码不能为空")
    private String productCode;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productName;

    /**
     * 商品主图URL
     */
    @Schema(description = "商品主图URL", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productImageUrl;

    @Schema(description = "跟单个item关联的条款编码list 这个productCode对应的A，B policy 下一个productCode对应的可能是B C", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单个item关联的条款编码list不能为空")
    private List<String> policyCodeList;

    @Schema(description = "商品SKU编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "商品SKU编码不能为空")
    private String productSkuCode;

    @Schema(description = "商品快照编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productVersionCode;

    /**
     * SKU属性值
     * attribute_name_code:attribute_value_code, 多个属性值用，隔开 fuwushichang:sannian,color:blue
     */
    @Schema(description = "SKU属性值,attribute_name_code:attribute_value_code, 多个属性值用，隔开 fuwushichang:sannian,color:blue", requiredMode = Schema.RequiredMode.REQUIRED)
    private String attributeValues;

    @Schema(description = "stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "stock组合属性不能为空")
    private String productAttribute;

    @Schema(description = "实付价格 " +
            "代客下单时： 0<payPrice<=salePrice" +
            "原有小程序端创建订接口：payPrice=salePrice", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "实付价格不能为空")
    private String payPrice;

    /**
     * 销售价格
     * ---消费者实际支付的价格
     */
    @Schema(description = "销售价格", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "销售价格不能为空")
    private String salePrice;

    /**
     * 划线价
     * ---划线价、建议零售价或原价
     */
    @Schema(description = "划线价", requiredMode = Schema.RequiredMode.REQUIRED)
    private String marketPrice;

    @Schema(description = "商品数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品数量不能为空")
    private Integer quantity;

    @Schema(description = "车型编码，不为 远程车控产品时可为空", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车型编码不能为空")
    private String seriesCode;

    @Schema(description = "车型名字，不为 远程车控产品时可为空", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车型名字不能为空")
    private String seriesName;

    @Schema(description = "车辆VIN码，不为远程车控产品时 可为空", requiredMode = Schema.RequiredMode.REQUIRED)
    private String carVin;
}
package com.jlr.ecp.order.api.order.dto;

import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.framework.common.util.string.StrUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.junit.platform.commons.util.StringUtils;

import javax.validation.constraints.NotBlank;
import java.util.Objects;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.invalidParamException;

/**
 * <AUTHOR>
 */
@Schema(description = " 小程序端 - 最近三年订单列表")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderLatestPageReqDTO extends PageParam {
    @Schema(description = "暂定传的是consumerCode，后续可能接收的是oneId")
    private String jlrId;

    @Schema(description = "通过创建时间（添加日期）排序方式（asc：升序，desc：降序）")
    private String createdTimeSort;

    @Schema(description = "渠道编码")
    private Integer channelCode;

    public void verifyParamValidity() {
        if (StringUtils.isBlank(jlrId)) {
            throw invalidParamException("jlrId不能为空");
        }
    }
}

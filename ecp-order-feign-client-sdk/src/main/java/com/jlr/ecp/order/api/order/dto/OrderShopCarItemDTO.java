package com.jlr.ecp.order.api.order.dto;

import cn.hutool.core.util.StrUtil;
import com.jlr.ecp.order.api.order.dto.customer.BaseOrderShopCarItemDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单创建-购物车item DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端 - 订单创建-购物车item DTO")
public class OrderShopCarItemDTO extends BaseOrderShopCarItemDTO {
    /**
     * 销售价格
     * ---消费者实际支付的价格
     */
    @Schema(description = "税率")
    private BigDecimal taxRate;

    /**
     * 业务线名称
     */
    private String businessName;
    /**
     * 业务线code
     */
    private String businessCode;
    /**
     * 组合商品下item列表 后端数据处理中获得
     */
    private List<OrderShopCarItemDTO> childList;
    /**
     * 是否开启赠礼功能
     */
    private Boolean giftEnable;

    /**
     * 校验小程序下单的实付价格
     */
    public void validatePayPrice() {
        BigDecimal salePrice = new BigDecimal(getSalePrice());
        BigDecimal payPrice = new BigDecimal(getPayPrice());

        if (payPrice.compareTo(salePrice) != 0) {
            throw new IllegalArgumentException("小程序下单时，实付价格必须等于销售价格");
        }
        // 可以在这里添加更多的校验逻辑
    }

    /**
     * 校验请求参数
     * 创建订单（非代客下单、pdp），需校验
     * cartCode
     * cartItemCode
     * productName
     * productImageUrl
     */
    public void validateCartItemDetails() {
        if (StrUtil.isBlank(getCartCode())) {
            throw new IllegalArgumentException("购物车code不能为空");
        }
        if (StrUtil.isBlank(getCartItemCode())) {
            throw new IllegalArgumentException("购物车商品编码不能为空");
        }
        if (StrUtil.isBlank(getProductName())) {
            throw new IllegalArgumentException("商品名称不能为空");
        }
        if (StrUtil.isBlank(getProductImageUrl())) {
            throw new IllegalArgumentException("商品主图URL不能为空");
        }
        // 可以在这里添加更多的校验逻辑，如格式校验等
    }
}

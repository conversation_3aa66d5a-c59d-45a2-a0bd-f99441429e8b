package com.jlr.ecp.order.api.order.vo;

import java.util.List;

import com.jlr.ecp.order.api.cart.vo.ProductAttributeRespVO;
import com.jlr.ecp.order.api.order.vo.coupon.ECouponOrderDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - lre order info VO")
public class ECouponOrderItemVO {

    // t_order_refund_item => refund_order_code
    @Schema(description = "退单编号")
    private String refundOrderCode;

    // t_order_item => order_code
    @Schema(description = "订单编号", hidden = true)
    private String orderCode;

    // t_order_item => order_item_code
    @Schema(description = "订单明细编号")
    private String orderItemCode;

    @Schema(description = "商品SKU编码")
    private String productSkuCode;

    @Schema(description = "商品SPU编码")
    private String productCode;

    @Schema(description = "订单商品SKU ID - 金蝶sku编码")
    private String kingdeeSkuCode;

    // t_order_item => product_image_url
    @Schema(description = "商品主图URL")
    private String productImageUrl;
    
    // t_order_item => product_name
    @Schema(description = "商品名称")
    private String productName;
    
    // t_order_item => coupon_model_code
    @Schema(description = "虚拟商品卡券(模版)编码")
    private String couponModelCode;

    // t_order_coupon_detail => valid_start_time
    @Schema(description = "有效期开始时间")
    private String validStartTime;

    // t_order_coupon_detail => valid_end_time
    @Schema(description = "有效期结束时间")
    private String validEndTime;
    
    // t_order_item => product_quantity
    @Schema(description = "购买数量")
    private Integer productQuantity;
    
    // t_order_item => product_sale_price
    @Schema(description = "售价(单价)")
    private String productSalePrice;
    
    // t_order_item => cost_amount
    @Schema(description = "实付现金金额")
    private String costAmount;

    // t_order_item => point_amount
    @Schema(description = "实付积分")
    private Integer pointAmount;

    // t_order_item => aftersales_status
    @Schema(description = "售后状态")
    private Integer afterSalesStatus;

    // t_order_item => item_status
    @Schema(description = "订单行状态")
    private Integer itemStatus;

    // 订单详情.商品信息.兑换券列表
    // t_order_coupon_detail => order_item_code => return ECouponOrderDetailVO
    @Schema(description = "兑换券列表")
    private List<ECouponOrderDetailVO> couponCodeList;

    // t_order_item => product_attribute
    @Schema(description = "属性")
    private String productAttribute;

    // t_order_coupon_detail order_item_code =>  count 
    @Schema(description = "已核销张数")
    private Integer usedCouponCount;

    // t_order_coupon_detail order_item_code =>  count 
    @Schema(description = "已退单张数")
    private Integer refundCouponCount;

    // t_order_item_detail product_market_price
    @Schema(description = "标价（单价）")
    private String productMarketPrice;

    //  t_order_discount_detail discount_amount
    @Schema(description = "卡券分摊金额")
    private String discountAmount;

    //  t_order_discount_detail coupon_model_classify
    @Schema(description = "卡券类型")
    private String discountCouponType;

    //  t_order_discount_detail coupon_model_name
    @Schema(description = "卡券名称")
    private String discountCouponName;

    //  t_order_discount_detail coupon_code
    @Schema(description = "卡券编码")
    private String discountCouponCode;

    // 最大可退款金额
    @Schema(description = "最大可退款金额")
    private String maxRefundableAmount;
}

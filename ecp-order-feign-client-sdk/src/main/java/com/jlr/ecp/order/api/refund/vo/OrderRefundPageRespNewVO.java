package com.jlr.ecp.order.api.refund.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 09/03/2025 17:14
 **/
@Schema(description = "管理后台 - 退款列表 page VO")
@Data
public class OrderRefundPageRespNewVO {

    @Schema(description = "退单订单ID")
    private Long id;

    @Schema(description = "退单订单号")
    private String refundOrderCode;

    @Schema(description = "主订单号")
    private String parentOrderCode;

    @Schema(description = "微信手机号")
    private String wxPhone;

    @Schema(description = "微信手机号-掩码")
    private String wxPhoneMix;

    @Schema(description = "买家手机号")
    private String recipientPhone;

    @Schema(description = "买家手机号-掩码")
    private String recipientPhoneMix;

    @Schema(description = "订单手机号")
    private String contactPhone;

    @Schema(description = "订单手机号-掩码")
    private String contactPhoneMix;

    @Schema(description = "售后类型")
    private Integer refundOrderType;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime createdTime;

    @Schema(description = "创建方")
    private Integer refundSource;

    @Schema(description = "创建方名称")
    private String refundSourceName;

    @Schema(description = "业务线")
    private String businessCode;

    @Schema(description = "运费")
    private String freightAmount;

    @Schema(description = "退单运费")
    private String refundFreight;

    @Schema(description = "退款订单商品行")
    private List<RefundOrderItemVO> refundOrderItemVOList;
}

package com.jlr.ecp.order.api.order.vo.detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 电子普票信息 respVO")
@Data
public class OrderDetailEInvoiceInfoVO {

    @Schema(description = "发票类型", example = "普通发票-电子")
    private String invoiceType;
    @Schema(description = "开票金额", example = "￥1000.00")
    private String orderPaymentAmount;
    @Schema(description = "申请开票时间", example = "2024/02/02 10:30")
    private String applyTime;
    @Schema(description = "开票完成时间", example = "2024/02/02 11:30")
    private String finishTime;
    @Schema(description = "抬头类型,1：个人/非企业 2：企业", example = "1")
    private Integer titleType;
    @Schema(description = "发票抬头")
    private String invoiceTitleName;
    @Schema(description = "税号")
    private String titleTaxNo;
    @Schema(description = "接收邮箱")
    private String invoiceEmail;
    @Schema(description = "开票状态,1:已申请 2:已完成 3:已红冲 4:已重开", example = "1")
    private Integer status;
    @Schema(description = "企业地址")
    private String companyAddress;
    @Schema(description = "企业电话")
    private String companyMobile;
    @Schema(description = "开户银行")
    private String companyBankName;
    @Schema(description = "银行账号")
    private String companyBankAccount;
    @Schema(description = "发票查看链接")
    private String pdfUrl;
}

package com.jlr.ecp.order.api.order.vo.logistics;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@Schema(description = "退款原因参数")
public class LogisticsCompanyVO {
    @Schema(description = "快递公司code")
    private String companyCode;

    @Schema(description = "快递公司名称")
    private String companyName;
}

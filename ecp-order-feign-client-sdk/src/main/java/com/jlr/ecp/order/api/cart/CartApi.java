package com.jlr.ecp.order.api.cart;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.security.PermitAll;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 购物车")
public interface CartApi {
    String PREFIX = ApiConstants.PREFIX + "/feign/v1/cart";



    /**
     * 登出清空VCS相关商品
     * @param consumerCode
     * @return
     */
    @DeleteMapping(PREFIX + "/delete")
    @Operation(summary = "登出清空VCS相关商品")
    @Parameter(name = "consumerCode", description = "用户编码")
    @Deprecated
    @PermitAll
    CommonResult<String> deleteByConsumerCode(@RequestParam(value = "consumerCode")String consumerCode);

    /**
     * 登出清空VCS相关商品
     * @param consumerCode
     * @return
     */
    @DeleteMapping(PREFIX + "/deleteByConsumerCodeAndICR")
    @Operation(summary = "登出清空VCS相关商品")
    @Parameter(name = "consumerCode", description = "用户编码")
    @Parameter(name = "incontrolId", description = "incontrol账号")
    @Parameter(name = "brandCode", description = "brandCode")
    @PermitAll
    CommonResult<String> deleteByConsumerCodeAndICR(@RequestParam(value = "consumerCode") String consumerCode,
                                                    @RequestParam(value = "incontrolId") String incontrolId,
                                                    @RequestParam(value = "brandCode") String brandCode);


    /**
     * 登出清空VCS相关商品
     * @param consumerCode
     * @return
     */
    @DeleteMapping(PREFIX + "/deleteByConsumerCodeAndVinList")
    @Operation(summary = "登出清空VCS相关商品")
    @Parameter(name = "consumerCode", description = "用户编码")
    @Parameter(name = "vinList", description = "车架号合集")
    CommonResult<String> deleteByConsumerCodeAndVinList(@RequestParam(value = "consumerCode") String consumerCode,
                                                        @RequestParam(value = "vinList") List<String> vinList);

}

package com.jlr.ecp.order.api.order.vo.feedback;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "单个环节（支付、完成、整单取消） 下的评价信息")
@Data
public class OrderFeedbackInfo {
    /**
     * feedback_dimensions 评价环节：PM开头、已支付OR、订单完成、CL、订单整单取消
     */
    @Schema(description = "评价维度：PM开头：已支付 OR：订单完成、 CL：订单整单取消")
    private String feedbackDimensions;

    /**
     * 单个环节下 评价总分
     */
    @Schema(description = "单个环节下 评价总分")
    private Integer total;

    /**
     * 单个环节下 不同的维度
     *  名称
     *  类型
     *  是否必填
     *  排序
     *  选项
     *  结果
     */
    @Schema(description = "单个环节下 不同的 维度 eg. 购物满意度、产品满意度")
    private List<OrderFeedbackDimension> dimensionsContent;

    /**
     * 输入框额外内容
     */
    @Schema(description = "单个环节下 输入框额外内容")
    private String inputExtra;
}

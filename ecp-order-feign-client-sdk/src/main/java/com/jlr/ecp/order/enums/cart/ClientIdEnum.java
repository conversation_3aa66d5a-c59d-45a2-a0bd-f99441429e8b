package com.jlr.ecp.order.enums.cart;

import cn.hutool.core.util.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 13/03/2024 14:31
 */

@AllArgsConstructor
@Getter
public enum ClientIdEnum {

    JAG("JAG","捷豹","JAG",2),
    LAN("LAN","路虎","LAN",1);

    /**
     * 品牌
     */
    private String brandCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 客户端
     */
    private String clientId;

    /**
     * 下单渠道
     */
    private Integer orderChannel;
}

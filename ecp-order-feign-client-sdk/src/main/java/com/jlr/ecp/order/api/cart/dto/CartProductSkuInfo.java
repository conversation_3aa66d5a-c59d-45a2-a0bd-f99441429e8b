package com.jlr.ecp.order.api.cart.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.api.cart.dto
 * @className: ProductSkuDetailInfo
 * @author: gaoqig
 * @description: 商品详情信息 SKU Level
 * @date: 2025/3/6 09:09
 * @version: 1.0
 */
@Data
@Schema(description = " 购物车/待下单 请求/响应- DTO")
@ToString(callSuper = true)
@Validated
public class CartProductSkuInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "商品SPU编码,后端自行赋值", hidden = true)
    private String productCode;

    @Schema(description = "商品SKU编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "商品SKU编码不能为空")
    private String productSkuCode;

    /**
     * 售价，并非市场价。
     */
    @Schema(description = "售价")
    private String salePrice;

    @Schema(description = "现金+积分方式：销售金额")
    private String salePointsPrice;

    @Schema(description = "现金+积分方式：积分")
    private Integer salePoints;

    /**
     * 默认值为false，如果前端传了则以前端为准
     */
    @Schema(description = "当前SKU是否参与积分优惠")
    private Boolean chooseFlag = false;

    @Schema(description = "当前SKU能使用的优惠券模版")
    private List<String> couponModuleCodeList;

    @Schema(description = "用户选择的优惠券，冗余字段，后端自行赋值", hidden = true)
    private String chooseCouponCode;

    @Schema(description = "用户选择的优惠券类型，冗余字段，后端自行赋值", hidden = true)
    private Integer chooseCouponType;

    @Schema(description = "优惠金额")
    private String discountAmount;

    //优惠后价格
    @Schema(description = "优惠后价格,包括积分或者优惠券抵扣")
    private String couponPrice;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "是否支持现金+积分")
    private Boolean supportCashAndPoints;

    @Schema(description = "购物车 行号Id", hidden = true)
    private long cartItemId;

    /**
     * 能否参与计算：如果这个为false，则不计算
     * 此为基础条件之1 （此条件与库存相关，如果为true 说明库存这块条件满足）
     */
    @Schema(hidden = true)
    private boolean joinCalculateFlag;

    /**
     * 最终能否参与计算优惠的标识
     * 对于优惠券商品，实际和joinCalculateFlag一致
     * 积分商品的话，在joinCalculateFlag基础上，还考虑用户的是否选择积分模式
     */
    @Schema(hidden = true)
    private boolean finalJoinCalculateFlag;

    @Schema(description = "业务编码")
    private String businessCode;
}

package com.jlr.ecp.order.api.refund.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - order refund page VO")
public class OrderRefundPageVO {

    @Schema(description = "退单订单号")
    private String refundOrderCode;

    @Schema(description = "原始订单号")
    private String originOrderCode;

    @Schema(description = "inControl账号")
    private String inControlId;

    @Schema(description = "inControl账号加密信息")
    private String icrCT;

    @Schema(description = "客户留言")
    private String carVin;

    @Schema(description = "VIN密文")
    private String carVinCT;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系电话密文")
    private String contactPhoneCT;

    @Schema(description = "微信授权手机号")
    private String wxPhone;

    @Schema(description = "订单状态")
    private Integer refundOrderStatus;

    @Schema(description = "通过创建时间排序方式（asc：升序，desc：降序）")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime createdTime;

    @Schema(description = "创建人员")
    private String orderCreator;

    @Schema(description = "订单来源")
    private String orderSource;
}

package com.jlr.ecp.order.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 订单编辑DTO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后端 - 前端传入的订单编辑DTO")
@Data
public class OrderEditDTO {
    /**
     * 订单号
     */
    @Schema(description = "订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderCode;

    /**
     * 订单备注
     */
    @Schema(description = "订单备注")
    @Length(max = 100, message = "订单备注长度不能超过100个字符")
    private String operatorRemark;

    /**
     * 商品item list
     */
    @Schema(description = "商品item list")
    List<OrderEditItemDTO> productItemInfoList;

    /**
     * 纸质发票开票信息
     */
    @Schema(description = "纸质发票开票信息")
    private PaperInvoiceDTO paperInvoiceInfo;


    /**
     * 电子专票开票信息
     */
    @Schema(description = "电子专票开票信息")
    private ESpecialInvoiceDTO eSpecialInvoiceDTO;

    /**
     * 是否有查看完整纸质发票收件人信息权限
     */
    @Schema(description = "是否有查看完整纸质发票收件人信息权限")
    private boolean viewInvoice;
}

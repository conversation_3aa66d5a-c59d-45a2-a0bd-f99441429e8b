package com.jlr.ecp.order.api.cart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;


/**
 * 关联属性
 * <AUTHOR>
 *
 */
@Data
@Schema(description = "管理后台 - 属性关联 VO")
@ToString(callSuper = true)
public class ProductAttributeRespVO{

    /**
     * 属性名称
     */
    @Schema(description = "属性名称")
    private String attributeName;


    /**
     * 属性值名称
     */
    @Schema(description = "属性值名称")
    private String attributeValue;


    @Schema(description = "属性名称编码")
    @NotNull(message = "属性名称编码不能为空")
    private String attributeNameCode;

    @Schema(description = "属性值名称编码")
    @NotNull(message = "属性值名称编码不能为空")
    private String attributeValueCode;

    @Schema(description = "排序")
    @NotNull(message = "排序不能为空")
    private Integer sort;

}
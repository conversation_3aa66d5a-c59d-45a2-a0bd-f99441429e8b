package com.jlr.ecp.order.api.refund.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "订单管理 - 退单申请校验DTO")
@Data
@ToString(callSuper = true)
@Validated
public class OrderRefundCheckDTO {

    @Schema(description = "原始订单号")
    @NotBlank(message = "订单号不能为空")
    private String originOrderCode;


    /**
     * 商品item list
     */
    @Schema(description = "商品信息")
    @NotNull(message = "主商品信息不能为空")
    List<ProductItemInfoDTO> productItemInfoList;
}

package com.jlr.ecp.order.api.order.vo;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - bg order info VO")
public class BrandGoodsOrderInfoPageVO {

    // t_order_info => parent_order_code
    @Schema(description = "父订单号")
    private String parentOrderCode;

    // t_order_info => order_code
    @Schema(description = "订单号")
    private String orderCode;

    @Schema(description = "订单主状态")
    private Integer orderStatus;

    // t_order_info => wx_phone_mix
    @Schema(description = "微信手机号-掩码")
    private String wxPhoneMix;

    // t_order_info => wx_phone_md5
    @Schema(description = "微信手机号-md5")
    private String wxPhone;

    // t_order_info => contact_phone_mix
    @Schema(description = "订单手机号-掩码")
    private String contactPhoneMix;

    // t_order_info => contact_phone_md5
    @Schema(description = "订单手机号-md5")
    private String contactPhone;

    @Schema(description = "买家")
    private String recipient;

    // t_order_item_logistics=> recipient_phone_mix
    @Schema(description = "买家手机号-掩码")
    private String recipientPhoneMix;

    // t_order_item_logistics=> recipient_phone_md5
    @Schema(description = "买家手机号-md5")
    private String recipientPhone;

    // t_order_info => created_time
    @Schema(description = "订单创建时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime createdTime;

    // t_order_info => updated_time
    @Schema(description = "订单更新时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime updatedTime;

    // t_order_info => logistics_status => logisticsOrderStatus
    @Schema(description = "订单状态")
    private Integer logisticsOrderStatus;

    
    // 新增显示用订单状态，当有售后中的订单时显示为售后处理中(5)
    @Schema(description = "显示用订单状态，当有售后中的订单时显示为售后处理中(90501)")
    private Integer displayLogisticsOrderStatus;


    @Schema(description = "订单商品项")
    private List<BrandGoodsOrderItemVO> orderItems;

    // t_order_item => order_item_code
    @Schema(description = "订单商品项编码")
    private String orderItemCodes;

    /**
     * 微信昵称
     */
    @Schema(description = "微信昵称")
    private String wxNickName;

    /**
     * 实付金额;实付金额，单位分
     */
    @Schema(description = "实付金额;实付金额，单位分")
    private String costAmount;

    /**
     * 物流状态
     */
    @Schema(description = "物流状态")
    private Integer deliveryStatus;

    /**
     * 物流单号
     */
    @Schema(description = "物流单号")
    private String deliveryNo;

    /**
     * 折扣金额;折扣金额，单位分
     */
    @Schema(description = "折扣金额;折扣金额，单位元")
    private String discountTotalAmount;

    /**
     * 运费金额;运费金额，虚拟商品为0
     */
    @Schema(description = "运费金额;运费金额，虚拟商品为0")
    private String freightAmount;

    /**
     * 积分
     */
    @Schema(description = "积分")
    private Integer pointAmount;


    /**
     * 客户留言信息;客户留言信息
     */
    @Schema(description = "客户留言信息;客户留言信息")
    private String customerRemark;

    /**
     * 操作员备注
     */
    @Schema(description = "操作员备注")
    private String operatorRemark;

    /**
     * 支付方式
     */
    @Schema(description = "payment_type")
    private String paymentType;

    /**
     * 订单提交时间;订单提交时间
     */
    @Schema(description = "订单提交时间;订单提交时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime orderTime;

    /**
     * 支付时间;支付时间
     */
    @Schema(description = "支付时间;支付时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime paymentTime;

    /**
     * 收件省;收件省
     */
    @Schema(description = "收件省;收件省")
    private String provinceCode;

    /**
     * 收件市;收件市
     */
    @Schema(description = "收件市;收件市")
    private String cityCode;

    /**
     * 收件区;收件区
     */
    @Schema(description = "收件区;收件区")
    private String areaCode;

    /**
     * 收件详细地址;收件详细地址
     */
    @Schema(description = "收件详细地址;收件详细地址")
    private String detailAddress;

    /**
     * 邮编地址;邮编地址
     */
    @Schema(description = "邮编地址;邮编地址")
    private String postCode;

    /**
     * 物流公司
     */
    @Schema(description = "物流公司")
    private String logisticsCompany;

    /**
     * 发货时间
     */
    @Schema(description = "发货时间")
    private LocalDateTime sendTime;

    /**
     * 是否同步到erp
     */
    @Schema(description = "是否同步到erp")
    private Boolean erpSync;
}

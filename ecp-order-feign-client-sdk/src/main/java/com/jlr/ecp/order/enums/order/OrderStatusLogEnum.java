package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum OrderStatusLogEnum {

    /**
     * 已下单
     * 订单状态；1：已下单
     */
    ORDERED(1,1,-1,"已下单"),

    /**
     * 已支付
     * 订单状态；2：已支付
     */
    PAID(2, 2,-1,"已支付"),

    /**
     * 订单完成
     * 订单状态；3：订单完成
     */
    COMPLETED(3,3,-1, "订单完成"),

    /**
     * 订单关闭
     * 订单状态；4：订单关闭
     */
    CLOSED(4,4,-1, "订单关闭"),


    /**
     * 售后处理中
     */
    AFTER_SALES(5, 5,-1,"售后处理中"),

    /**
     * 订单部分取消
     * 订单状态；6：订单部分取消
     */
    PARTIAL_CANCELLED(6, 6,6,"订单部分取消"),

    /**
     * 订单整单取消
     * 订单状态；7：订单整单取消
     */
    FULLY_CANCELLED(7,7,5, "订单整单取消"),


    /**
     * 退单流程
     */
    FULL_REFUND_APPLY(101,5,1, "发起整单退款申请"),

    PARTIAL_REFUND_APPLY(102,5,2, "发起部分退款申请"),

    FULL_REFUND_APPROVE(103,5,3, "同意整单退款申请"),

    PARTIAL_REFUND_APPROVE(104,5,4, "同意部分退款申请"),

    FULL_REFUND_COMPLETED(105,5,5, "订单整单退款完成"),

    PARTIAL_REFUND_COMPLETED(106,5,6, "订单部分退款完成");






    private final Integer code;
    private final Integer orderStatus;
    private final Integer refundStatus;
    private final String description;

    /**
     * 根据订单状态的整数值获取对应的描述。
     *
     * @param code 订单状态的整数值
     * @return 对应的订单状态描述
     */
    public static String getDescriptionByCode(Integer code) {
        for (OrderStatusLogEnum status : OrderStatusLogEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        // 兼容DEV脏数据
        if(code == null){
            return "";
        }
        throw new IllegalArgumentException("Invalid order status code: " + code);
    }
    public static OrderStatusLogEnum getEnumByCode(Integer code) {
        for (OrderStatusLogEnum status : OrderStatusLogEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
       return null;
    }


    /**
     * 传入订单状态和退单状态，如果找不到就返回正向流程本身的状态
     * @param orderStatus
     * @param refundStatus
     * @return
     */
    public static Integer getLogStatusByOrderAndRefund(Integer orderStatus,Integer refundStatus) {
        for (OrderStatusLogEnum status : OrderStatusLogEnum.values()) {
            if (status.getOrderStatus().equals(orderStatus)
                    &&status.getRefundStatus().equals(refundStatus)) {
                return status.getCode();
            }
        }
        return orderStatus;
    }
}
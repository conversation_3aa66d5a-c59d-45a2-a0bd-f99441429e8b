package com.jlr.ecp.order.api.test;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.unit.vo.ProductUnitListRespVO;
import com.jlr.ecp.order.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.security.PermitAll;
import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - demo")
public interface TestDemoApi {
    String PREFIX = "/v1/product";
    @GetMapping(PREFIX + "/test/testDep")
    @Operation(summary = "demo查询")
    @PermitAll
    CommonResult<List<ProductUnitListRespVO>> testDep(@RequestParam Map<String,String> productUnitListReqVO);
}

package com.jlr.ecp.order.api.order.vo.orderlist;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.api.order.vo.base
 * @className: OrderSkuItemVo
 * @author: gaoqig
 * @description: TODO
 * @date: 2025/3/8 19:59
 * @version: 1.0
 */
@Data
public class OrderSkuItemVo {
    /**
     * 订单item编码
     */
    @Schema(description = "订单item编码")
    private String orderItemCode;


    /**
     * 商品快照编码
     */
    @Schema(description = "商品快照编码")
    private String productVersionCode;

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    private String productCode;

    /**
     * 商品SKU编码
     */
    @Schema(description = "商品SKU编码")
    private String productSkuCode;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String productName;

    /**
     * 商品主图URL
     */
    @Schema(description = "商品主图URL")
    private String productImageUrl;

    /**
     * stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI
     */
    @Schema(description = "stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI")
    private String productAttribute;

    /**
     * 标价
     */
    @Schema(description = "标价")
    private String productMarketPrice;

    /**
     * 售价
     */
    @Schema(description = "售价")
    private String productSalePrice;

    /**
     * 购买数量
     */
    @Schema(description = "购买数量")
    private Integer productQuantity;

    /**
     * 积分模式下所需的金额
     */
    @Schema(description = "现金+积分方式：销售金额")
    private String salePointsPrice;

    /**
     * 积分模式下所需的积分
     */
    @Schema(description = "现金+积分方式：积分")
    private Integer salePoints;

    @Schema(description = "业务线编码")
    private String businessCode;

    @Schema(description = "已核销的数量")
    private Integer usedCouponCount;

    @Schema(description = "已退回的数量")
    private Integer backCouponCount;

    @Schema(description = "优惠券有效期")
    private String couponValidityStr;

    @Schema(description = "履约类型")
    private Integer itemFufilementType;

    @Schema(description = "所属一级分类，多个由逗号隔开")
    private String categoryCodeLevel1Name;

    @Schema(description = "所属二级分类，多个由逗号隔开")
    private String categoryCodeLevel2Name;

    @Schema(description = "所属三级分类，多个由逗号隔开")
    private String categoryCodeLevel3Name;

    @Schema(description = "售后状态:1：售后处理中 2：售后已完成 3：售后关闭")
    private Integer aftersalesStatus;

    @Schema(description = "售后状态描述:1：售后处理中 2：售后已完成 3：售后关闭")
    private String aftersalesStatusDesc;
}

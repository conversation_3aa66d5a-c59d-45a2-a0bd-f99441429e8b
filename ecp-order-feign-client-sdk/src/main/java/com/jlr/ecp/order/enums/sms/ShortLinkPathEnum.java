package com.jlr.ecp.order.enums.sms;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 短链生成参数path枚举
 *
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum ShortLinkPathEnum {

    // RENEWAL_REMINDER("%2Fpages%2FsubscribeTo%2FshopDetails%2Fshopdetails", "续费提醒路由"),
    RENEWAL_REMINDER("ecp/pages/subscribeTo/shopDetails/shopDetails", "续费提醒路由"),
    PATH_AVAILABLE("ecp/pages/subscribeTo/subscribeTo", "开发环境可用的地址"),

    // 路虎路径：订单支付成功or订单取消成功 跳转订单详情
    ORDER_DETAIL("ecp/pages/mine/order/detail/index?orderCode=", "订单详情"),

    //Coupon路径 订单退款成功跳转退单详情
    COUPON_REFUND_SUCCESS_DETAIL("brandGoods/pages/refund/detail", "优惠券退款成功跳转退单详情"),

    BG_ORDER_DETAIL("brandGoods/pages/order/detail/index","BG/LRE订单详情路由"),

    // 捷豹路径：订单支付成功or订单取消成功 跳转订单详情 test环境改为
    JAGUAR_ORDER_DETAIL("ecp/ecpJaguar/pages/mine/order/detail/index", "捷豹小程序订单详情"),

    // 临时方案：捷豹小程序官方首页（捷豹小程序暂时只能 跳转官方首页 "pages/home/<USER>" 不能跳转ecp首页）
    JAGUAR_OFFICIAL_HOME("pages/home/<USER>", "捷豹小程序官方首页跳转路由"),

    //代客下单：跳转详情路由 沿用之前跳转详情的地址
    // 捷豹 ecp/ecpJaguar/pages/mine/order/detail/behalfofOrder
    JAGUAR_BEHALFOF_ORDER("ecp/ecpJaguar/pages/mine/order/detail/index", "代客下单跳转捷豹详情路由"),

    // 路虎 ecp/pages/mine/order/detail/behalfofOrder
    BEHALFOF_ORDER("ecp/pages/mine/order/detail/index", "代客下单跳转路虎详情路由");

    private final String path;

    private final String desc;
}
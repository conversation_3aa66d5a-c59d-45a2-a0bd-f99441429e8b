package com.jlr.ecp.order.api.cart.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 购物车创建DTO
 * <AUTHOR>
 */
@Data
@Schema(description = " 购物车创建- DTO")
@ToString(callSuper = true)
public class CartCreatDTO {

    /**
     * 用户编码
     */
    @Schema(description = "用户编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户编码不能为空")
    private String consumerCode;

    /**
     * 购物车code
     */
    @Schema(description = "购物车code", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cartCode;


    @Schema(description = "车辆信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "车辆信息不能为空")
    List<CarInfoDTO> carInfoDTOList;

    /**
     * incontrol账号
     */
    @Schema(description = "incontrol账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String incontrolName;
}

package com.jlr.ecp.order.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jlr.ecp.order.api.order.vo.base.OrderItemBaseVO;
import com.jlr.ecp.order.api.order.vo.subscriptioncategory.OrderItemLogisticsVO;
import com.jlr.ecp.order.api.order.vo.subscriptioncategory.OrderPaymentRecordsBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 */
@Schema(description = "订单列表分页 Response VO")
@Data
public class OrderListVO {
    @Schema(description = "订单号")
    private String orderNumber;

    @Schema(description = "InControl账号")
    private String inControlId;

    @Schema(description = "VIN码")
    private String vinNumber;

    @Schema(description = "微信授权手机号")
    private String weChatAuthorizedMobileNumber;

    @Schema(description = "下单联系人手机号")
    private String contactMobileNumber;

    @Schema(description = "订单状态")
    private int orderStatus;

    @Schema(description = "物流状态(只有实物商品会返回)")
    private int logisticsStatus;

    @Schema(description = "客户留言备注")
    private String customerRemark;

    @Schema(description = "操作员备注(卖家备注)")
    private String operatorRemark;

    @Schema(description = "微信昵称")
    private String wxNickName;

    @Schema(description = "支付方式")
    private String paymentType;

    @Schema(description = "订单总金额")
    private String costAmount;

    @Schema(description = "优惠总金额")
    private String discountTotalAmount;

    @Schema(description = "支付时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime paymentTime;

    @Schema(description = "花费积分")
    private Integer pointAmount;

    @Schema(description = "运费,单位元，保留两位小数")
    private String freightAmount;

    @Schema(description = "收货人信息")
    private OrderItemLogisticsVO receiverInfo;

    @Schema(description = "订单行商品信息")
    List<OrderItemBaseVO> orderItemList;

    private OrderPaymentRecordsBaseVO orderPaymentRecords;
    @Schema(description = "订单创建时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime orderTime;

}

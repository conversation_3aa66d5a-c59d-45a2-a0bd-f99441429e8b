package com.jlr.ecp.order.api.refund.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Schema(description = "订单管理 - 物流单退单审批DTO")
@Data
@ToString(callSuper = true)
@Validated
public class LogisticsOrderReturnApproveDTO {

    @Schema(description = "审核状态 ")
    @NotNull(message = "审核状态不能为空")
    private Boolean approveStatus;

    @Schema(description = "退单订单号")
    @NotBlank(message = "订单号不能为空")
    private String refundOrderCode;

    @Schema(description = "退单订单号")
    @NotBlank(message = "订单行号不能为空")
    private String orderItemCode;

    @Schema(description = "审核备注")
    private String auditReason;

}

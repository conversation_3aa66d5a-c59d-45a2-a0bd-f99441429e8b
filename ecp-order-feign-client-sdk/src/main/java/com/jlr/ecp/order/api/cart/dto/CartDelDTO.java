package com.jlr.ecp.order.api.cart.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 购物车创建DTO
 * <AUTHOR>
 */
@Data
@Schema(description = " 购物车创建- DTO")
@ToString(callSuper = true)
public class CartDelDTO {


    @Schema(description = "购物车编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "购物车编码不能空")
    List<String> cartItemCodes;
}

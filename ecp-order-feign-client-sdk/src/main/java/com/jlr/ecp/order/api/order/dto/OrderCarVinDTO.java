package com.jlr.ecp.order.api.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderCarVinDTO {
    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 订单编码;订单编码，编码规则：业务线code+时间戳(日期+时分秒)+6位随机数
     */
    private String orderCode;


    /**
     * 微信授权手机
     */
    private String wxPhone;


    /**
     * 客户留言信息;
     */
    private String customerRemark;

    /**
     * 客户联系手机;
     */
    private String contactPhone;


    /**
     * incontrol账号;
     */
    private String incontrolId;


    /**
     * 车辆VIN;车辆VIN
     */
    private String carVin;

    /**
     * 产品code
     * */
    private String productCode;

    /**
     * car类型
     * */
    private String carType;
}


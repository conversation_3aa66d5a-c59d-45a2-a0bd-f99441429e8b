package com.jlr.ecp.order.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - order Modify Detail VO")
public class OrderModifyDetailVO {

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String operateUser;

    /**
     * 修改前数据
     */
    @Schema(description = "修改前数据")
    private String modifyFieldOldValue;

    /**
     * 修改后数据
     */
    @Schema(description = "修改后数据")
    private String modifyFieldNewValue;
}

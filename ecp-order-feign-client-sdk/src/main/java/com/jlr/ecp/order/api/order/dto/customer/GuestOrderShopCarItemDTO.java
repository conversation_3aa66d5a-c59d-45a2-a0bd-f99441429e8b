package com.jlr.ecp.order.api.order.dto.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;


@Data
@Schema(description = "代客下单 - 订单创建-购物车item DTO")
public class GuestOrderShopCarItemDTO extends BaseOrderShopCarItemDTO {
    /**
     * 校验代客下单的实付价格
     */
    public void validatePayPrice() {
        BigDecimal salePrice = new BigDecimal(getSalePrice());
        BigDecimal payPrice = new BigDecimal(getPayPrice());

//        if (StrUtil.isBlank(getPayPrice())) {
//            throw new IllegalArgumentException("代客下单时 实付价格不能为空");
//        }
        if (payPrice.compareTo(BigDecimal.ZERO) <= 0 || payPrice.compareTo(salePrice) > 0) {
            throw new IllegalArgumentException("实付价格必须大于0且小于等于销售价格");
        }
        // 可以在这里添加更多的校验逻辑
    }

//    public static void main(String[] args) {
//        // 测试用例1: 正常情况
//        GuestOrderShopCarItemDTO item1 = new GuestOrderShopCarItemDTO();
//        item1.setSalePrice("100.00");
//        item1.setPayPrice("50.00");
//
//        try {
//            item1.validatePayPrice();
//            System.out.println("测试用例1通过：实付价格在合理范围内");
//        } catch (IllegalArgumentException e) {
//            System.err.println("测试用例1失败：" + e.getMessage());
//        }
//
//        // 测试用例2: 实付价格为空
//        GuestOrderShopCarItemDTO item2 = new GuestOrderShopCarItemDTO();
//        item2.setSalePrice("100.00");
//        item2.setPayPrice(null);
//
//        try {
//            item2.validatePayPrice();
//        } catch (Exception e) {
//            System.out.println("测试用例2通过：实付价格为空时抛出异常 - " + e.getMessage());
//        }
//
//        // 测试用例3: 实付价格小于等于0
//        GuestOrderShopCarItemDTO item3 = new GuestOrderShopCarItemDTO();
//        item3.setSalePrice("100.00");
//        item3.setPayPrice("0.00");
//
//        try {
//            item3.validatePayPrice();
//        } catch (Exception e) {
//            System.out.println("测试用例3通过：实付价格小于等于0时抛出异常 - " + e.getMessage());
//        }
//
//        // 测试用例4: 实付价格大于销售价格
//        GuestOrderShopCarItemDTO item4 = new GuestOrderShopCarItemDTO();
//        item4.setSalePrice("100.00");
//        item4.setPayPrice("150.00");
//
//        try {
//            item4.validatePayPrice();
//        } catch (Exception e) {
//            System.out.println("测试用例4通过：实付价格大于销售价格时抛出异常 - " + e);
//        }
//    }
}
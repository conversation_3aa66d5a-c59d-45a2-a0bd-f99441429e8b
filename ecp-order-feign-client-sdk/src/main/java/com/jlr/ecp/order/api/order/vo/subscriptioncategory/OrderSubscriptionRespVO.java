package com.jlr.ecp.order.api.order.vo.subscriptioncategory;


//import com.jlr.ecp.order.api.order.vo.brandcategory.OrderBrandProductInfoVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.OrderBrandVehicleInfoVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.ProductBrandCategoriedItemInfoVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 以一张vcs单子为维度进行拆分
 *
 * <AUTHOR>
 */
@Schema(description = "小程序端 - 我的订阅 不同品牌下 订单的 Response VO，其中订单包括itemA itemB")
@Data
public class OrderSubscriptionRespVO {
    /**
     * 1.车型信息 seriesCode seriesName carVin
     */
    @Schema(description = "车型信息 seriesCode seriesName carVin")
    private OrderBrandVehicleInfoVO vehicleInfo;

    /**
     * 2.商品信息 productItemInfoList itemA itemB
     */
    @Schema(description = "商品信息 productItemInfoList itemA itemB")
    private List<ProductBrandCategoriedItemInfoVO> productInfo;
}

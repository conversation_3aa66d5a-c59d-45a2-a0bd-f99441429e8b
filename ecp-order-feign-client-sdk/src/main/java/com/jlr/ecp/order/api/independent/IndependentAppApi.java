package com.jlr.ecp.order.api.independent;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.independent.dto.TriggerIndependentReqDTO;
import com.jlr.ecp.order.api.independent.dto.UpdateStatusToSuccReqDTO;
import com.jlr.ecp.order.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.security.PermitAll;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 分账相关")
public interface IndependentAppApi {

    String PREFIX = ApiConstants.PREFIX + "/independent";

    /**
     * 触发分账申请
     */
    @PostMapping(PREFIX + "/triggerIndependent")
    @Operation(summary = "触发类型为“订单完成”的分账")
    @PermitAll
    CommonResult<Boolean> triggerIndependent(@RequestBody TriggerIndependentReqDTO req);

    @PostMapping(PREFIX + "/status/success")
    @Operation(summary = "更新分账状态为分账成功")
    @PermitAll
    CommonResult<Boolean> updateStatusToSucc(@RequestBody UpdateStatusToSuccReqDTO req);

}

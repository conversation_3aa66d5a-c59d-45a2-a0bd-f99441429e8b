package com.jlr.ecp.order.api.order.dto;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "订单管理 - 订单列表")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderPageReqDTO extends PageParam {

    @Schema(description = "订单号")
    private String orderCode;

    @Schema(description = "inControl账号")
    private String inControlId;

    @Schema(description = "订单来源")
    private Integer orderSource;

    @Schema(description = "VIN码")
    private String vin;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "订单状态")
    private String orderStatus;

    @Schema(description = "通过创建时间（添加日期）排序方式（asc：升序，desc：降序）")
    private String createdTimeSort;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "订单履约类型")
    private Integer orderType;

    @Schema(description = "创建人员")
    private String orderCreator;

    @Schema(description = "Business Code")
    private String businessCode;

    private List<Integer> statusList;

    private List<Integer> orderChannelList;

    public void validateParameters() {
        if (startTime !=null && !startTime.isEmpty()){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
            LocalDateTime dateTime = null;
            try {
                 dateTime = LocalDateTime.parse(startTime + " 00:00:00", formatter);
                // 日期字符串验证和清理成功，可以将dateTime对象用于后续操作
            } catch (DateTimeParseException e) {
                // 日期字符串验证和清理失败，处理异常
            }
            assert dateTime != null;
            this.startTime = dateTime.toString();
        }

        if (endTime !=null && !endTime.isEmpty()){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
            LocalDateTime dateTime = null;
            try {
                dateTime = LocalDateTime.parse(endTime + " 23:59:59", formatter);
                // 日期字符串验证和清理成功，可以将dateTime对象用于后续操作
            } catch (DateTimeParseException e) {
                // 日期字符串验证和清理失败，处理异常
            }
            assert dateTime != null;
            this.endTime = dateTime.toString();
        }
    }
}

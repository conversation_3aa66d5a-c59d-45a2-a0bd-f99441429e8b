package com.jlr.ecp.order.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 1.里面的属性需要被后台校验
 * 2.需要与product-service同步
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端 - 订单创建-支付信息 DTO")
public class PaymentInfoDTO {
    @Schema(description = "原始订单金额，单位分，" +
            "---销售价格(sale_price)*数量累加和")
    @NotBlank(message = "原始订单金额不能为空")
    private String originalFeeTotalAmount;

    @Schema(description = "应付金额，订单金额，单位分， 原始订单金额上 " +
            "---eg. 减去所有可用的折扣（如促销折扣、优惠券等）" +
            "---销售价格(sale_price)*数量累加和")
    @NotBlank(message = "应付金额不能为空")
    private String feeTotalAmount;

    @Schema(description = "实付金额，单位分 应付金额 " +
            "---eg. 进一步减去了任何额外的优惠（如积分使用、礼品卡等）" +
            "---SUM(payPrice*数量)")
    @NotBlank(message = "实付金额不能为空")
    private String costAmount;

    @Schema(description = "折扣金额，单位分 " +
            "---所有折扣的总和" +
            "代客下单时 销售价格(sale_price)*数量累加和-SUM(实付价格*数量)")
    @NotBlank(message = "折扣金额不能为空")
    private String discountTotalAmount;

    @Schema(description = "运费金额 ---虚拟商品为0")
    @NotBlank(message = "运费金额不能为空")
    private String freightAmount;

    @Schema(description = "优惠券类型：1-兑换券(暂不支持) 2-代金券 3-折扣券 4-满减券")
    private Integer couponType;

    @Schema(description = "优惠券编码")
    private String couponCode;

    @Schema(description = "1:现金 ，2：积分，3:优惠券")
    private Integer paymentType;

    @Schema(description = "积分")
    private Integer points;
}

package com.jlr.ecp.order.api.payment.dto;

import com.jlr.ecp.order.api.order.dto.address.OrderGiftAddressDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
public class PayRequestDTO {
    @Valid
//    @NotNull(message = "赠品信息不能为空") // 暂时先不校验，后续再加4.订单创建后发起支付会失败，缺少礼物参数（可以临时改后端代码逻辑兼容）
    OrderGiftAddressDTO giftInfoDTO;

    /**
     * 应用编号;前端传入的应用编号
     */
    @Schema(description = "应用编号", required = true)
    @NotBlank(message = "应用编号不能为空")
    private String appNo;

    /**
     * 订单编号;组合支付时为父单号，拆单场景下为子单号
     */
    @Schema(description = "订单编号", required = true)
    @NotBlank(message = "订单编号不能为空")
    private String orderCode;

    /**
     * 后端组装
     */
    @Schema(description = "后端组装，订单金额，单位分", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "1")
    private Integer orderAmount;

    /**
     * 用户OpenID;用户唯一标识（openid）
     */
    @Schema(description = "用户OpenID", required = true)
    @NotBlank(message = "openid不能为空")
    private String openid;

    /**
     * MiniAPP ID (小程序标识 ID)
     */
    @Schema(description = "MiniAPP ID")
    private String miniAppId;

    /**
     * 渠道编码;固定值 'cusc' <br/>
     * @deprecated 已在后端通过 Business Code 判断
     */
    @Schema(description = "渠道编码")
    private String channelCode;

    /**
     * 渠道额外参数;包含支付金额、商品代码、通知URL等信息
     *
     * "amount":"1",        // 当前订单的金额 取t_order_info表中的cost_amount
     * "productCode":"test",        // 如果是组合支付的话，则取第一个子订单的productCode
     * "orderNo":"BS0034659FB777E4B0435C5323C174",  // 需支付的订单号
     *
     * "notifyUrl":"https://ecp-api-dev.jaguarlandrover.cn/app-api/pay/order/pay-callback", //暂时先写死，后续写到配置文件
     * "remark":"1111", //取订单t_order_info表中的的customer_remark
     * "orderTitle":"test", // 取productCode相应商品的名称product_name
     * "merchantNo":"merpre12000011",   //暂时先写死，后续写到配置文件
     * "callbackType":"HTTP", //暂时先写死，后续写到配置文件
     * "openId":"oYmCP4hkJnFqEkY9aKqrSbdapgUc"
     */
    @Schema(description = "渠道额外参数", required = true)
    private Map<String, Object> channelExtras;

    /**
     * 展示模式;如 'url'，表示在页面上显示支付链接
     */
    @Schema(description = "展示模式")
    private String displayMode;

    /**
     * 返回URL;用户支付完成后跳转的地址
     */
    @Schema(description = "返回URL")
    private String returnUrl;

    /**
     * 客户留言
     */
    @Schema(description = "客户留言")
    @Length(max = 100,message = "客户留言长度不能超过100个字符")
    private String operatorRemark;
}
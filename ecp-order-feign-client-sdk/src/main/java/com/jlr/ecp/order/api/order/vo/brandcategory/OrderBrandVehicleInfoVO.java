package com.jlr.ecp.order.api.order.vo.brandcategory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端-我的订单 商品分类下 车型信息")
public class OrderBrandVehicleInfoVO {
    /**
     * 车辆型号 编码
     */
    @Schema(description = "车辆型号")
    private String seriesCode;

    /**
     * 车辆名称
     */
    @Schema(description = "车辆名称")
    private String seriesName;

    /**
     * 车辆VIN
     */
    @Schema(description = "车辆VIN")
    private String carVin;
}

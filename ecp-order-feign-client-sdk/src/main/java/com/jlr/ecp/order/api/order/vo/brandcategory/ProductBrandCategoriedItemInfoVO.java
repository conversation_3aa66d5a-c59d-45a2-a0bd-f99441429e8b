package com.jlr.ecp.order.api.order.vo.brandcategory;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jlr.ecp.order.api.order.vo.detail.ProductItemInfoVO;
import com.jlr.ecp.order.api.order.vo.subscriptioncategory.ProductItemSubDurationHistoryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端-我的订单 商品分类下 商品item")
public class ProductBrandCategoriedItemInfoVO extends ProductItemInfoVO {
    /**
     * 商品主图URL
     */
    @Schema(description = "商品主图URL")
    private String productImageUrl;

    /**
     * 小程序端-我的订阅 商品item下 商品历史订阅时间段 list
     */
    @Schema(description = "商品item下 商品历史订阅 list")
    private List<ProductItemSubDurationHistoryVO> productItemSubDurationHistoryList;

    /**
     * 组合商品信息列表
     */
    @Schema(description = "组合商品信息列表")
    private List<ProductBrandCategoriedItemInfoVO> next;

    @Schema(description = "优惠券有效期")
    private String couponValidityStr;

    @Schema(description = "已核销优惠券数量")
    private Integer usedCouponCount;

    @Schema(description = "已退回优惠券数量")
    private Integer backCouponCount;

    @Schema(description = "能否发起退款")
    private Boolean canRefundOrder;

    @Schema(description = "能否【查看卡券】")
    private Boolean canShowCardStatus;

    @Schema(description = "销售价")
    private String SalePrice;

    @Schema(description = "单个商品优惠价")
    private String unitCouponPrice;

    @Schema(description = "最大可退款金额")
    private String maxRefundAmount;

    @Schema(description = "最大可退款数量")
    private Integer maxRefundSkuCount;

    @Schema(description = "可退积分")
    private Integer refundSkuPoints;

    @Schema(description = "模版名称，如果没有返回则表示该订单行没有返回")
    private String couponModuleName;

    /**
     * 商品行实际支付积分
     */
    @Schema(description = "商品行实际支付积分,可能为空或者0(此时不展示)")
    private Integer productSalePoints;

    @Schema(description = "现金+积分方式：销售金额")
    private String unitSalePointsPrice;

    @Schema(description = "现金+积分方式：积分")
    private Integer unitSalePoints;

    /**
     * 物流公司名称
     */
    @Schema(description = "物流公司名称")
    private String logisticsCompany;

    /**
     * 运单号
     */
    @Schema(description = "运单号")
    private String logisticsNo;
    /**
     * 运单状态
     */
    @Schema(description = "运单状态")
    private Integer serviceStatus;

    /**
     * 订单行状态
     */
    @Schema(description = "物流履约订单状态：1：待发货，2：已发货，3：已妥投，4: 已收货  5:已关闭")
    private Integer itemStatus;

    @Schema(description = "所属一级分类，多个由逗号隔开")
    private String categoryCodeLevel1Name;

    @Schema(description = "所属二级分类，多个由逗号隔开")
    private String categoryCodeLevel2Name;

    @Schema(description = "所属三级分类，多个由逗号隔开")
    private String categoryCodeLevel3Name;

    @Schema(description = "业务编码")
    private String businessCode;

    @Schema(description = "无理由退货截至时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime noReasonReturnsTime;
}

package com.jlr.ecp.order.api.cart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/***
 * 商品sku具体信息
 */
@Data
@Schema(description = "管理后台 - sku reqVO")
@ToString(callSuper = true)
public class SkuRespVO {

    @Schema(description = "商品属性数组")
    @NotNull(message = "商品属性数组不能为空")
    private List<ProductAttributeRespVO> attributeArr;


    @Schema(description = "库存数量")
    @NotNull(message = "库存数量不能为空")
    private Integer skuQuantity;


    @Schema(description = "标价")
    @NotNull(message = "标价不能为空")
    private String salePrice;

    @Schema(description = "市场价（划线价）")
    private String marketPrice;


    @Schema(description = "单个订单限制购买数量")
    private Long orderUnitLimit;


    @Schema(description = "排序")
    private Integer sort;

}

package com.jlr.ecp.order.api.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderItemDTO {
    /**
     * 订单item编码;订单item编码，雪花算法ID
     */
    private String orderItemCode;

    /**
     * 订单编码;订单编码
     */
    private String orderCode;

    /**
     * 商品快照编码;商品快照编码
     */
    private String productVersionCode;

    /**
     * 商品编码;商品编码
     */
    private String productCode;

    /**
     * 商品SKU编码;商品SKU编码
     */
    private String productSkuCode;

    /**
     * 商品名称;商品名称
     */
    private String productName;

    /**
     * 商品主图URL;商品主图URL
     */
    private String productImageUrl;

    /**
     * stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI
     */
    private String productAttribute;

    /**
     * 商品市场价格
     */
    private Integer productMarketPrice;

    /**
     * 商品销售单价;商品销售单价，单位分
     */
    private Integer productSalePrice;

    /**
     * 商品数量;商品数量
     */
    private Integer productQuantity;

    /**
     * 应付总金额;应付总金额，单位分
     */
    private Integer totalAmount;

    /**
     * 实付金额;实付金额，单位分
     */
    private Integer costAmount;

    /**
     * 折扣金额;折扣金额，单位分
     */
    private Integer discountFeeAmount;

    /**
     * 订单item商品的类型，1普通商品 2组合商品
     */
    private Integer orderItemSpuType;

    /**
     * 商品备注信息
     */
    private String remark;

    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税收 (分类) 编码
     */
    private String taxCode;

}

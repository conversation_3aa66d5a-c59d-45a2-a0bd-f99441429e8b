package com.jlr.ecp.order.api.refund.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @createDate 12/03/2025 14:15
 **/
@Data
@Schema(description = "退单详情VO")
public class OrderRefundDetailVO {

    @Schema(description = "退单订单号")
    private String refundOrderCode;

    @Schema(description = "主订单号")
    private String parentOrderCode;

    @Schema(description = "子订单号")
    private String originOrderCode;

    @Schema(description = "退单状态")
    private Integer couponRefundStatus;

    @Schema(description = "物流退单状态")
    private Integer logisticsRefundStatus;

    @Schema(description = "退款状态名称")
    private String couponRefundStatusName;

    @Schema(description = "履约方式")
    private Integer fulfillmentType;

    @Schema(description = "履约方式名称")
    private String fulfillmentTypeName;

    @Schema(description = "创建方")
    private String refundSourceName;

    @Schema(description = "售后类型")
    private Integer refundType;

    @Schema(description = "售后类型名称")
    private String refundTypeName;

    @Schema(description = "退单原因")
    private String refundReason;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime createdTime;

    @Schema(description = "补充描述")
    private String supDesc;

    @Schema(description = "售后状态(补充说明)")
    private String refundStatusSup;

    @Schema(description = "补充图片")
    private List<String> attachmentUrl;

    @Schema(description = "微信手机号")
    private String wxPhone;

    @Schema(description = "微信手机号-掩码")
    private String wxPhoneMix;

    @Schema(description = "买家手机号")
    private String recipientPhone;

    @Schema(description = "买家手机号-掩码")
    private String recipientPhoneMix;

    @Schema(description = "买家地址-掩码")
    private String recipientAddressMix;

    @Schema(description = "买家地址")
    private String recipientAddress;

    @Schema(description = "买家姓名-掩码")
    private String recipientMix;

    @Schema(description = "买家姓名")
    private String recipient;

    @Schema(description = "订单手机号")
    private String contactPhone;

    @Schema(description = "订单手机号-掩码")
    private String contactPhoneMix;

    @Schema(description = "退单订单商品行")
    private List<RefundOrderProductInfoVO> refundOrderProductInfoVOList;

    @Schema(description = "状态记录")
    List<RefundDetailLogVO> logList;

    @Schema(description = "运营人员备注")
    private String refundRemark;

    @Schema(description = "物流单号")
    private String logisticsCode;

    @Schema(description = "物流公司code")
    private String logisticsCompanyCode;

    @Schema(description = "物流公司名称")
    private String logisticsCompanyName;

    @Schema(description = "凭证图片")
    private List<String> logisticsAttachmentList;

    @Schema(description = "退货审核备注")
    private String returnAuditRemark;

    @Schema(description = "退款审核备注")
    private String refundAuditRemark;

    @Schema(description = "优惠方式")
    private String paymentMethod;

    @Schema(description = "应付现金金额")
    private String costAmount;

    @Schema(description = "应付积分")
    private Integer pointAmount;

    @Schema(description = "运费")
    private String freightAmount;

    @Schema(description = "退单运费")
    private String refundFreight;

    @Schema(description = "卡券类型")
    private String couponType;

    @Schema(description = "卡券名称")
    private String couponName;

    @Schema(description = "卡券券码")
    private String couponCode;

    @Schema(description = "付款时间")
    private String paymentTime;

    @Data
    @Schema(description = "portal - 售后单状态log VO")
    public static class RefundDetailLogVO {

        @Schema(description = "退单状态")
        private Integer refundOrderStatus;

        @Schema(description = "文本")
        private String text ;

        @Schema(description = "详细文本")
        private String detail ;

        @Schema(description = "申请时间")
        @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime changeTime;
    }
}

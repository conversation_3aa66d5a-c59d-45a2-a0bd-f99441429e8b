package com.jlr.ecp.order.api.order.vo.bak;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "客户信息")
public class CustomerInfoVO {
    @Schema(description = "客户微信昵称", required = true)
    private String weChatNickname;

    @Schema(description = "微信授权手机号", required = true)
    private String authenticatedMobileNumber;

    @Schema(description = "下单联系人手机号", required = true)
    private String contactMobileNumber;

    @Schema(description = "InControl账户", required = true)
    private String inControlId;
}

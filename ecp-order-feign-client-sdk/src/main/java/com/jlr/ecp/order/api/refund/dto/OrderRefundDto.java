package com.jlr.ecp.order.api.refund.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * OrderRefundDto
 *
 * <AUTHOR> <PERSON>
 * @since 2025-03-23 23:08:06
 */
@Data
public class OrderRefundDto {

    /**
     * 退单订单号 订单号规则: R + 子单号 + 3 位数
     */
    private String refundOrderCode;

    /**
     * 原始订单号
     */
    private String originOrderCode;

    /**
     * 退单订单状态:
     * 1-发起整单退款申请
     * 2-发起部分退款申请
     * 3-同意整单退款申请
     * 4-同意部分退款申请
     * 5-订单整单退款完成
     * 6-订单部分退款完成
     * 7-拒绝整单退单申请
     * 8-拒绝部分退款申请
     */
    private Integer refundOrderStatus;

    /**
     * 提交人
     */
    private String submitUser;

    /**
     * 是否退款 是否退款: 0-否; 1-是
     */
    private Integer refundMoney;

    /**
     * 退款金额
     */
    private Integer refundMoneyAmount;

    /**
     * 服务结束时间
     */
    private LocalDateTime serviceEndDate;

    /**
     * 退款备注信息
     */
    private String refundRemark;

    /**
     * 补充描述
     */
    private String supDesc;

    /**
     * 审批拒绝理由
     */
    private String rejectReason;

    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 履约方式:
     * 1-PIVI-Remote
     * 2-PIVI-Online Pack
     * 3-实物商品子订单
     * 4-组合商品
     * 5-电子兑换券
     */
    private Integer originOrderType;

    /**
     * 退款请求来源:
     * 1-用户发起
     * 2-系统自动
     * 3-运营发起
     */
    private Integer refundSource;

    /**
     * 售后类型:
     * 1-退货退款
     * 2-仅退款
     */
    private Integer refundOrderType;

    /**
     * 退款原因
     */
    private Integer refundReason;

    /**
     * 物流退单状态:
     * 90101-待退货审核 - 买家已提交退货申请
     * 90102-待商品寄回 - 同意退货，待买家寄回商品
     * 90103-待退款审核 - 买家已寄回，退款待审核
     * 90301-分账退款中 - 分账退款处理中
     * 90302-退款处理中 - 退款处理中
     * 90501-售后完成, 已退款 - 售后关闭，已退款
     * 90701-售后关闭, 拒绝退款申请
     * 90702-售后关闭, 买家已撤销申请
     */
    private Integer logisticsRefundStatus;

    /**
     * 优惠券退单状态:
     * 90101-发起退款申请
     * 90701-拒绝退款申请
     * 90301-退款处理中
     * 90501-退款完成售后关闭
     * 90702-买家已撤销退款申请
     */
    private Integer couponRefundStatus;

    /**
     * 满减优惠券code
     */
    private String refundCouponCode;

    /**
     *
     * 退单履约类型:
     * 1-远程车控 REMOTE SERVICE
     * 2-PIVI Subscription
     * 3-实物商品
     * 4-组合商品
     * 5-优惠券商品
     */
    private Integer refundFulfilmentType;

    /**
     * 售后状态 (补充说明):
     * 1-券码逾期
     * 2-券码逾期 (部分)
     * 3-主动退款
     * 4-主动退款 (部分)
     */
    private Integer refundStatusSup;
}

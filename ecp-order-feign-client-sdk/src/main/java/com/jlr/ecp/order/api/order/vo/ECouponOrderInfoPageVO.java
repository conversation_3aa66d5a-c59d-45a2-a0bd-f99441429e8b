package com.jlr.ecp.order.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - lre order info VO")
public class ECouponOrderInfoPageVO {

    // t_order_info => parent_order_code
    @Schema(description = "父订单号")
    private String parentOrderCode;

    // t_order_info => order_code
    @Schema(description = "订单号")
    private String orderCode;

    // t_order_info => wx_phone_mix
    @Schema(description = "微信手机号-掩码")
    private String wxPhoneMix;

    // t_order_info => wx_phone_md5
    @Schema(description = "微信手机号-md5")
    private String wxPhone;

    // t_order_info => contact_phone_mix
    @Schema(description = "订单手机号-掩码")
    private String contactPhoneMix;

    // t_order_info => contact_phone_md5
    @Schema(description = "订单手机号-md5")
    private String contactPhone;

    // t_order_info => created_time
    @Schema(description = "订单创建时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime createdTime;

    // t_order_info => coupon_status
    @Schema(description = "订单状态")
    private Integer couponStatus;

    @Schema(description = "订单商品项")
    private List<ECouponOrderItemVO> orderItems;

    // t_order_item => order_item_code
    @Schema(description = "订单商品项编码")
    private String orderItemCodes;
}

package com.jlr.ecp.order.api.order.vo.feedback;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 单个环节下 不同的维度
 *  名称
 *  类型
 *  是否必填
 *  排序
 *  选项
 *  结果
 */
@Schema(description = "单个环节下 单个维度")
@Data
public class OrderFeedbackDimension {
    @Schema(description = "维度名称")
    private String name;

    @Schema(description = "维度类型 0：星级（5分制）；1：单选题；2：多选题")
    private Integer type;

    @Schema(description = "是否必填 0=否；1=是")
    private Integer mustInput;

    @Schema(description = "针对维度的 排序")
    private Integer sort;

    @Schema(description = "单个维度的 选项")
    private List<Option> optionJson;

    @Schema(description = "单个维度的 结果")
    private List<String> result;

}

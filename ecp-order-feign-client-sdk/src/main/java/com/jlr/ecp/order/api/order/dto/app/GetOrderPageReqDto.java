package com.jlr.ecp.order.api.order.dto.app;


import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.api.order.dto.app
 * @className: GetOrderPageReqDto
 * @author: gaoqig
 * @description: 订单列表查询参数
 * @date: 2025/3/8 12:03
 * @version: 1.0
 */
public class GetOrderPageReqDto extends PageParam {

    /***
     * @description 在LRE版本上之前的字段，新版本之后界面没有这个字段的赋值，所以弃用
     * @date 2025/3/8 12:06
    */

    @Schema(description = "eg.点击揽胜tab，这个品牌下的购买的订单对应的 List<series_code>", deprecated = true)
    List<String> seriesCodeList;

    /**
     * 这个自行从header中取，不用前端传
     */
    @Schema(description = "用户编码，后续可能拿 oneId去代替", hidden = true)
    String consumerCode;

    @Schema(description = "订单状态类型，1 - 待支付，5 - 售后(处理中)，不传查全部", hidden = true)
    String orderStatus;
}

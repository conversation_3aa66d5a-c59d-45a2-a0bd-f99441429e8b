package com.jlr.ecp.order.api.order.vo.brandcategory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端-我的订单 商品分类下 商品item list")
public class OrderBrandProductInfoVO {
    /**
     * 小程序端-我的订单 商品分类下 商品item list
     */
    @Schema(description = "商品item list")
    List<ProductBrandCategoriedItemInfoVO> productItemInfoList;
}

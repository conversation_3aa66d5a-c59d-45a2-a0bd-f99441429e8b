package com.jlr.ecp.order.api.unit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Data
@Schema(description = "管理后台 - 单位 Response VO")
@ToString(callSuper = true)
public class ProductUnitListRespVO {




    @Schema(description = "编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "编码不能为空")
    private String unitCode;

    /**
     * 单位
     */
    @Schema(description = "单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "单位不能为空")
    private String unitValue;



}

package com.jlr.ecp.order.api.refund.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "订单管理 - 退单申请DTO")
@Data
@ToString(callSuper = true)
@Validated
public class OrderRefundApplyDTO {

    @Schema(description = "退单类型 0:整单 1:部分")
    @NotNull(message = "退单类型不能为空")
    private Integer refundType;

    @Schema(description = "原始订单号")
    @NotBlank(message = "订单号不能为空")
    private String originOrderCode;

    @Schema(description = "附件")
    @NotNull(message = "附件不能为空")
    private List<String> attachmentUrls;


    @Schema(description = "是否退款")
    @NotNull(message = "是否退款不能为空")
    private Integer refundMoney;

    @Schema(description = "退款金额")
    private String refundMoneyAmount;

    /**
     * 主商品item list
     */
    @Schema(description = "主商品信息")
    @NotNull(message = "主商品信息不能为空")
    List<ProductItemInfoDTO> productItemInfoList;


    /**
     * 备注
     */
    @Schema(description = "备注")
    @Length(max = 100, message = "备注长度不能超过100个字符")
    private String remark;


}

package com.jlr.ecp.order.api.cart.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.internal.productprice
 * @className: PromotionalDto
 * @author: gaoqig
 * @description: 优惠策略入参
 * @date: 2025/3/6 16:39
 * @version: 1.0
 */
@Data
@Schema(description = " 购物车-个人优惠券列表- DTO")
@ToString(callSuper = true)
@Validated
public class PromotionDto {

    /**
     * 卡券券码
     */
    @Schema(description = "卡券编码")
    private String couponCode;

    /**
     * 卡券模版名称
     */
    @Schema(description = "卡券模版名称")
    private String couponModelName;

    /**
     * 卡券模版code
     */
    @Schema(description = "卡券模版code")
    private String couponModelCode;

    /**
     * 卡券模板类型：0-积分 1-兑换券 2-代金券 3-折扣券 4-满减券
     * CouponTypeEnum
     */
    @Schema(description = "券模板类型：0-积分(特殊) 1-兑换券 2-代金券 3-折扣券 4-满减券 CouponTypeEnum")
    private Integer couponModelClassify;

    /**
     * 积分数量
     */

    @Schema(description = "积分数量")
    private Integer points;
    /**
     * 满减类型：1-满金额减；2-满数量减
     * 如果是积分的话： 0表示用户选择了积分
     */
    @Schema(description = "满减类型：1-满金额减；2-满数量减")
    private Integer ruleType;

    /**
     * 触发满金额减和使用现金券时的金额要求（满减券）
     * 使用现金券时的场景目前不会用到，先不告诉前端
     * 单位 分， 在从接口获取时，要先*100，那边接口给的是元
     */
    @Schema(description = "触发满金额减的金额要求（满减券）")
    private String triggerMoney;

    /**
     * 触发满数量减时的数量要求（满减券）
     * 目前业务场景不会使用这种优惠券，隐藏
     */
    @Schema(description = "触发满数量减时的数量要求（满减券）", hidden = true)
    private Integer triggerNumber;

    /**
     * 满减券的优惠金额
     * 单位 分， 在从接口获取时，要先*100，那边接口给的是元
     */
    @Schema(description = "满减券的优惠金额")
    private String triggerAmount;

    /**
     * 代金券的金额
     * 单位 分， 在从接口获取时，要先*100，那边接口给的是元
     */
    @Schema(description = "代金券的金额")
    private String money;

    /**
     * 折扣券的折扣
     * e.g: 80——表示80%，所以折扣的金额是0.2*金额
     */
    @Schema(description = "折扣券的折扣")
    private Double discountPercent;

    /**
     * 使用规则
     */
    @Schema(description = "使用规则")
    private String useRule;

    /**
     * 使用说明
     */
    @Schema(description = "使用说明")
    private String useExplain;

    /**
     * 卡券适用范围
     */
    @Schema(description = "卡券适用范围")
    private String couponScope;

    /**
     * 生效时间
     */
    @Schema(description = "生效时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime validStartTime;

    /**
     * 失效时间
     */
    @Schema(description = "失效时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime validEndTime;

    /**
     * 模版图片链接
     */
    @Schema(description = "失效时间")
    private String couponImgLink;

    /**
     * 本次可优惠的金额
     */
    @Schema(description = "本优惠券本次可优惠金额")
    private String discountAmount;

    /**
     * 本次优惠后需要支付的金额
     */
    @Schema(description = "本优惠券本次优惠后需要支付的金额")
    private String costAmount;
}

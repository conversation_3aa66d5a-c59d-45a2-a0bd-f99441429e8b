package com.jlr.ecp.order.api.order;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.order.dto.*;
import com.jlr.ecp.order.api.order.vo.base.OrderItemBaseVO;
import com.jlr.ecp.order.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 订单管理")
public interface OrderAppApi {
    String PREFIX = ApiConstants.PREFIX + "/order";

    /**
     * 发起支付后 更新订单状态
     *
     * @param orderCode
     * @return
     */
    @PostMapping(PREFIX + "/status/update")
    @Operation(summary = "发起支付后，更新订单状态")
    @PermitAll
    CommonResult<Integer> updateOrderStatus(@RequestParam(name = "orderCode") @NotBlank(message = "orderCode不能为空") String orderCode);


    /**
     * 支付成功完成后 更新订单状态
     *
     * @param orderCode
     * @return
     */
    @PostMapping(PREFIX + "/status/success/update")
    @Operation(summary = "支付成功完成后，更新订单状态")
    @PermitAll
    CommonResult<Integer> updateOrderStatusOnSuccess(@RequestParam(name = "orderCode", required = true) @NotBlank(message = "订单号不能为空") String orderCode,
                                                     @RequestParam(name = "payFinishTime", required = false) String payFinishTime);

    /**
     * 远程调用商品服务，根据List<order_item_code> 查询商品信息，组装成Map<String carVin,orderItemDO对象>返回
     */
    @PostMapping(PREFIX + "/orderItem/list")
    @Operation(summary = "根据List<order_item_code> 查询商品信息，组装成Map<String carVin,orderItemDO对象>返回")
    @PermitAll
    CommonResult<List<OrderItemBaseVO>> getOrderItemInfo(@Validated @RequestBody OrderItemCodeListDTO dto);

//    /**
//     * /integration/getorder CRCAPIM
//     */
//    @GetMapping(PREFIX + "/integration/getorder")
//    @Operation(summary = "integration getorder")
//    @PermitAll
//    CommonResult<OrderIntegrationRespVO> getOrder(@RequestParam(name = "orderCode") String orderCode,
//                                                  @RequestParam(name = "jlrId ") @NotBlank(message = "jlrId不能为空") String jlrId);
//
//    /**
//     * /integration/getlatestorders CRCAPIM
//     */
//    @GetMapping(PREFIX + "/integration/getlatestorders")
//    @Operation(summary = "integration getlatestorders")
//    @PermitAll
//    CommonResult<PageResult<OrderIntegrationPageRespVO>> getlatestOrders(@RequestParam(name = "jlrId ") @NotBlank(message = "jlrId不能为空") String jlrId,
//                                                                         @RequestParam(value = "createdTimeSort", required = false) String createdTimeSort, //DESCASC
//                                                                         @RequestParam(name = "pageNo") Integer pageNo,
//                                                                         @RequestParam(name = "pageSize") Integer pageSize
//    );

    /**
     * tsdp退单调用
     *
     * @param orderRefundCode
     * @return
     */
    @GetMapping(PREFIX + "/tsdpRefundCallBack")
    @Operation(summary = "tsdp退单调用")
    CommonResult<Integer> tsdpRefundCallBack(@RequestParam("orderRefundCode") @NotBlank(message = "退单编码不能为空") String orderRefundCode
    ,@RequestParam(value = "updateStatus",required = false) Boolean updateStatus);

    /**
     * tsdp退单调用
     *
     * @param orderCode
     * @return
     */
    @GetMapping(PREFIX + "/tsdpCallBack")
    @Operation(summary = "tsdp退单调用")
    CommonResult<Integer> tsdpCallBack(@RequestParam("orderCode") @NotBlank(message = "订单编码不能为空") String orderCode,
                                       @RequestParam(value = "updateStatus",required = false) Boolean updateStatus);

        
    @PostMapping(PREFIX + "/phone/car/vin")
    @Operation(summary = "通过carvin获取手机号")
    @PermitAll
    CommonResult<List<OrderCarVinDTO>> getPhoneByCarVin(@RequestBody List<String> carVinList);

    @PostMapping(PREFIX + "/time")
    @Operation(summary = "通过orderCode获取订单信息")
    @PermitAll
    CommonResult<OrderInvoiceDTO> getOrderDoByOrderCode(@RequestParam("orderCode") String orderCode);

    @PostMapping(PREFIX + "/query/order/item")
    @Operation(summary = "通过orderCode获取orderItem的信息")
    @PermitAll
    CommonResult<List<OrderItemDTO>> getOrderItemListByOrderCode(@RequestParam("orderCode") String orderCode);


    @PostMapping(PREFIX + "/order/timeout/cancel")
    @Operation(summary = "订单超时取消的job")
    @PermitAll
    Integer orderTimeoutCancel(@RequestParam("days") Integer days);

    @PostMapping(PREFIX + "/order/getAfterSalesOrderCode")
    @Operation(summary = "根据List<orderCode>查询订单状态为售后处理中的orderCode")
    @PermitAll
    CommonResult<Set<String>> getAfterSalesOrderCode(@RequestBody List<String> orderCodeList);

    @GetMapping(PREFIX + "/checkOrderInTransit")
    @Operation(summary = "根据carVin和serviceType校验是否存在在途订单")
    @PermitAll
    CommonResult<Boolean> checkOrderInTransit(@RequestParam("carVin") String carVin, @RequestParam("serviceType") Integer serviceType);

    @PostMapping(PREFIX + "/checkOrderInTransitByVinList")
    @Operation(summary = "根据carVinList和serviceType校验是否存在在途订单")
    @PermitAll
    CommonResult<List<String>> checkOrderInTransitByVinList(@RequestBody OrderInTransitReqDTO reqDTO);


    @GetMapping(PREFIX + "/getPassTimeUnEnableFeedback")
    @Operation(summary = "查待处理的评价编码集合")
    @PermitAll
    CommonResult<List<String>> getPassTimeUnEnableFeedback( @RequestParam("pageSize") Integer pageSize);


    @GetMapping(PREFIX + "/handPassUnEnableFeedback")
    @Operation(summary = "查待处理的评价编码集合")
    @PermitAll
    CommonResult<Boolean> handPassUnEnableFeedback( @RequestParam("feedbackCode") String feedbackCode);

    /**
     * 确认收货定时任务
     */
    @PostMapping(PREFIX + "/confirmReceiptJob")
    @Operation(summary = "确认收货定时任务")
    @PermitAll
    CommonResult<Boolean> confirmReceiptList(@RequestBody ConfirmReceiptJobReqDTO reqDTO);
}


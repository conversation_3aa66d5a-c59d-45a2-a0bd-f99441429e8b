package com.jlr.ecp.order.api.order.vo.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - product item respVO")
public class ProductItemInfoAppVO extends ProductItemInfoVO{
    /**
     * 商品主图URL
     */
    @Schema(description = "商品主图URL")
    private String productImageUrl;

    /**
     * 主商品item list
     */
    @Schema(description = "子商品信息")
    List<ProductItemInfoAppVO> next;
}

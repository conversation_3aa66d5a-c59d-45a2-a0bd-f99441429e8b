package com.jlr.ecp.order.api.order.dto.address;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class OrderGiftAddressDTO {

    @Schema(description = "是否领取礼物 0否 1是 2无需")
    @NotNull(message = "是否领取礼物不能为空")
    private Integer needGift;

    @Schema(description = "中国行政区划代码 （Administrative Division Code）" +
            "eg.adCode: \"230110\"" +
            "23：省级代码（黑龙江省）" +
            "01：地市级代码（哈尔滨市）" +
            "10：区县级代码（香坊区）")
    private String adCode;

    @Schema(description = "省编码" +
            "取adCode前 2 位（省级代码）：取23")
    private String provinceCode;

    @Schema(description = "市编码" +
            "取adCod前 4 位（省+市）：2301")
    private String cityCode;

    @Schema(description = "区编码" +
            "直接使用完整adCode： 230110" )
    private String areaCode;

    @Schema(description = "详细地址")
    private String detailAddress;

    @Schema(description = "收件人")
    private String recipient;

    @Schema(description = "收件人电话")
    private String recipientPhone;

    /**
     * 参数校验方法
     */
    public boolean validate() {
        // needGift为0或2时，不做校验
        if(this.needGift == 0 || this.needGift == 2){
            return true;
        }
        if(this.needGift != 1){
            return true;
        }
        // 校验参数
        if(StrUtil.isBlank(this.provinceCode)){
            return false;
        }
        if(StrUtil.isBlank(this.cityCode)){
            return false;
        }
        if(StrUtil.isBlank(this.areaCode)){
            return false;
        }
        if(StrUtil.isBlank(this.detailAddress)){
            return false;
        }
        if(StrUtil.isBlank(this.recipient)){
            return false;
        }
        return !StrUtil.isBlank(this.recipientPhone);
    }
}
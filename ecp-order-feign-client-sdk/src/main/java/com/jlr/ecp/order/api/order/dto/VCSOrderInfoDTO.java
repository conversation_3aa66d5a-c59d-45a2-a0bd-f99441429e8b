package com.jlr.ecp.order.api.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class VCSOrderInfoDTO implements Serializable {

    /**
     * VCS订单编码;VCS订单编码
     */
    private String vcsOrderCode;

    /**
     * 订单编码
     */
    private String orderCode;

    /**
     * 订单明细编号;订单明细编号，by商品维度记录履约订单
     */
    private String orderItemCode;

    /**
     * 用户编码;用户编码
     */
    private String consumerCode;

    /**
     * incontrol账号;
     */
    private String incontrolId;

    /**
     * incontrol账号;
     */
    private String incontrolIdMd5;

    /**
     * inControl账号 半隐藏只展示@后面的部位
     */
    private String incontrolIdMix;


    /**
     * 车辆VIN;车辆VIN
     */
    private String carVin;

    /**
     * 车辆VIN;车辆VIN
     */
    private String carVinMd5;

    /**
     * 车辆VIN;车辆VIN 半隐藏只展示前6位
     */
    private String carVinMix;

    /**
     * 服务开始时间;服务开始时间，计算得出
     */
    private LocalDateTime serviceBeginDate;

    /**
     * 服务结束时间;服务结束时间，计算得出
     */
    private LocalDateTime serviceEndDate;

    /**
     * 品牌名;品牌名
     */
    private String seriesName;

    /**
     * 品牌code;品牌code
     */
    private String seriesCode;

    /**
     * 服务履约类型：1：远程车控Remote Service；2：PIVI Subscription Service；
     */
    private Integer serviceType;

    /**
     * 租户号
     */
    private Integer tenantId;
}

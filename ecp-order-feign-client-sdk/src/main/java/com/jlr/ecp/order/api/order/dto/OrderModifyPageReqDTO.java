package com.jlr.ecp.order.api.order.dto;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Schema(description = "订单管理 - 订单操作列表")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderModifyPageReqDTO extends PageParam {

    @Schema(description = "订单号")
    @NotBlank(message="订单号订单号不能为空")
    private String orderCode;

}

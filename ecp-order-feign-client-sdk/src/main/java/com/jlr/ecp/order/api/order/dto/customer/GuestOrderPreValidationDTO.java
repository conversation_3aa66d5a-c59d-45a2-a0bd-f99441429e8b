package com.jlr.ecp.order.api.order.dto.customer;

import com.jlr.ecp.order.api.order.dto.GlobalInfoDTO;
import com.jlr.ecp.order.api.order.dto.OrderInfoDTO;
import com.jlr.ecp.order.api.order.dto.address.OrderGiftAddressDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "待客下单 - 商品选购前置校验DTO")
@Data
public class GuestOrderPreValidationDTO{
    /**
     * 0.一些全局信息
     * 全局 品牌编码
     * 渠道编码
     * 用户编码
     */
    @Valid
    @NotNull(message = "一些全局信息不能为空")
    GlobalInfoDTO globalInfoDTO;

    /**
     * 0.购物车item基本信息
     */
    @Valid
    @NotNull(message = "购物车item基本信息不能为空")
    List<GuestOrderShopCarItemDTO> shopCarItemList;

    /**
     * 订单信息 包括
     * 1.登录信息 IN CONTROL ID
     * 2.联系人信息
     * 3.支付信息
     * 4.条款信息
     * <p>
     */
    @Valid
    @NotNull(message = "订单信息不能为空")
    OrderInfoDTO orderInfoDTO;

    @Valid
    @NotNull(message = "赠品信息不能为空")
    OrderGiftAddressDTO giftInfoDTO;
}
package com.jlr.ecp.order.api.cart.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 购物车创建DTO
 * <AUTHOR>
 */
@Data
@Schema(description = " 购物车创建- DTO")
@ToString(callSuper = true)
@Validated
public class BrandGoodsCarInfoDTO {

    /**
     * 商品SKU编码
     */
    @Schema(description = "商品SKU编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "商品SKU编码不能为空")
    private String productSkuCode;


    /**
     * 商品数量
     */
    @Schema(description = "商品数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品数量不能为空")
    private Integer quantity;

}

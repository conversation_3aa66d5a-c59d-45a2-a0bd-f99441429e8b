package com.jlr.ecp.order.api.refund;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.refund.dto.OrderRefundDto;
import com.jlr.ecp.order.api.refund.dto.OrderRefundItemDto;
import com.jlr.ecp.order.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 订单管理")
public interface RefundApi {

    String PREFIX = "/feign/v1/refund";

    /**
     * 发起支付后 更新订单状态
     * @param orderRefundCode
     * @return
     */
    @GetMapping(PREFIX + "/refundPaymentCallback")
    @Operation(summary = "退单支付回调")
    @PermitAll
    CommonResult<Integer> refundPaymentCallback(@RequestParam("orderRefundCode") @NotBlank(message = "退单编码不能为空") String orderRefundCode);

    @GetMapping(PREFIX + "/refundTransPaymentCallback")
    @Operation(summary = "分账退单支付回调")
    @PermitAll
    CommonResult<Integer> refundTransPaymentCallback(@RequestParam("orderRefundCode") @NotBlank(message = "退单编码不能为空") String orderRefundCode);

    /**
     * 根据 OrderCode 查询 OrderRefund
     *
     * @return List of OrderRefundDto
     */
    @PermitAll
    @GetMapping(PREFIX + "/getOrderRefundByOrderCode")
    @Operation(summary = "根据 OrderCode 查询 OrderRefund")
    CommonResult<List<OrderRefundDto>> getOrderRefundByOrderCode(
            @RequestParam("orderCode") @NotBlank(message = "OrderCode could not be empty") String orderCode);

    /**
     * 根据 List OrderRefundCode 查询 OrderRefundItem
     *
     * @return List of OrderRefundItemDto
     */
    @PermitAll
    @PostMapping(PREFIX + "/getOrderRefundItemByOrderRefundCode")
    @Operation(summary = "根据 根据 List OrderRefundCode 查询 OrderRefundItem")
    CommonResult<List<OrderRefundItemDto>> getOrderRefundItemByOrderRefundCode(
            @RequestBody List<String> orderRefundCodeList);
}

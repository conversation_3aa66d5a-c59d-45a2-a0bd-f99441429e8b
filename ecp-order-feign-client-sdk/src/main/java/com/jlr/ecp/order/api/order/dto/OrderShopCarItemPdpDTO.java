package com.jlr.ecp.order.api.order.dto;

import com.jlr.ecp.order.api.order.dto.customer.BaseOrderShopCarItemDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单创建-购物车item DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端 - 订单创建-购物车item DTO")
public class OrderShopCarItemPdpDTO extends BaseOrderShopCarItemDTO {
    /**
     * 校验小程序下单的实付价格
     */
    public void validatePayPrice() {
        BigDecimal salePrice = new BigDecimal(getSalePrice());
        BigDecimal payPrice = new BigDecimal(getPayPrice());

        if (payPrice.compareTo(salePrice) != 0) {
            throw new IllegalArgumentException("小程序下单时，实付价格必须等于销售价格");
        }
        // 可以在这里添加更多的校验逻辑
    }
}

package com.jlr.ecp.order.enums.refund;

import java.util.List;

/**
 * 退款物流状态枚举
 */
public enum RefundLogisticsStatusEnum {

    /**
     * 待退货审核
     */
    PENDING_RETURN_REVIEW(90101, "待退货审核", "买家已提交退货申请", "已提交退货申请","退货申请已提交,请耐心等待"),

    /**
     * 待商品寄回
     */
    PENDING_ITEM_RETURN(90102, "待商品寄回", "同意退货，待买家寄回商品", "待买家寄回商品","退货申请已同意,请尽快寄出商品"),

    /**
     * 待退款审核
     */
    PENDING_REFUND_REVIEW(90103, "待退款审核", "买家已寄回，退款待审核", "买家已寄回商品","商品已寄出,请耐心等待物流抵达"),

    /**
     * 分账退款中
     */
    SPLIT_REFUND_PROCESSING(90301, "分账退款中", "分账退款处理中", "分账退款中","分账退款中"),

    /**
     * 退款处理中
     */
    REFUND_PROCESSING(90302, "退款处理中", "退款处理中", "退款处理中","银行已受理,请耐心等待"),

    /**
     * 售后完成，已退款
     */
    REFUND_COMPLETED(90501, "售后完成，已退款", "售后关闭，已退款", "退款成功","钱款已原路返回"),

    /**
     * 售后关闭，拒绝退款申请
     */
    REFUND_CLOSED_REJECT(90701, "售后关闭，拒绝退款申请", "售后关闭，拒绝退款申请", "商家已拒绝","很抱歉,商家无法支持您本次退款申请,有问题请联系客服协助处理,祝您购物愉快"),

    /**
     * 售后关闭，买家已撤销申请
     */
    REFUND_CLOSED_CANCELLED(90702, "售后关闭，买家已撤销申请", "售后关闭，买家已撤销申请", "您已撤销此次售后申请","有问题请联系客服协助处理,祝您购物愉快"),
    RETURN_CLOSED_REJECT(90703, "售后关闭，拒绝退货申请", "售后关闭，拒绝退货申请", "商家已拒绝","很抱歉,商家无法支持您本次退货申请,有问题请联系客服协助处理,祝您购物愉快");

    private final int code;
    private final String name;
    private final String detailDescriptionOnFrontend;

    private final String nameOnApp;

    private final String textOnApp;

    RefundLogisticsStatusEnum(int code, String name, String detailDescriptionOnFrontend, String nameOnApp,String textOnApp) {
        this.code = code;
        this.name = name;
        this.detailDescriptionOnFrontend = detailDescriptionOnFrontend;
        this.nameOnApp = nameOnApp;
        this.textOnApp = textOnApp;
    }

    /**
     * 根据状态码获取枚举实例
     * @param code 状态编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static RefundLogisticsStatusEnum getByCode(int code) {
        for (RefundLogisticsStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        for (RefundLogisticsStatusEnum status : values()) {
            if (status.code == code) {
                return status.getName();
            }
        }
        return "";
    }

    public static String getFrontendNameByCode(int code) {
        for (RefundLogisticsStatusEnum status : values()) {
            if (status.code == code) {
                return status.getDetailDescriptionOnFrontend();
            }
        }
        return "";
    }

    public String getNameOnAppByCode(int code) {
        for (RefundLogisticsStatusEnum status : values()) {
            if (status.code == code) {
                return status.getNameOnApp();
            }
        }
        return "";
    }

    public String getTextOnAppByCode(int code){
        for (RefundLogisticsStatusEnum status : values()) {
            if (status.code == code) {
                return status.getTextOnApp();
            }
        }
        return "";
    }

    //获取不统计金额的状态集合
    public static List<Integer> getNotCountMoneyStatusList(){
        return List.of(RefundLogisticsStatusEnum.REFUND_CLOSED_REJECT.getCode(),RefundLogisticsStatusEnum.REFUND_CLOSED_CANCELLED.getCode(),RefundLogisticsStatusEnum.RETURN_CLOSED_REJECT.getCode());
    }


    /**
     * 根据code判断是否属于待退货审核或待商品寄回并返回布尔值
     * @return
     */
    public static boolean isPendingReturnOrPendingItemReturn(int code) {
        return code == PENDING_RETURN_REVIEW.getCode() || code == PENDING_ITEM_RETURN.getCode();
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDetailDescriptionOnFrontend() {
        return detailDescriptionOnFrontend;
    }

    public String getNameOnApp() {
        return nameOnApp;
    }

    public String getTextOnApp() {
        return textOnApp;
    }
}


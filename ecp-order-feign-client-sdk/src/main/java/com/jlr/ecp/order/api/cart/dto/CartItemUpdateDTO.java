package com.jlr.ecp.order.api.cart.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 购物车修改DTO
 * <AUTHOR>
 */
@Data
@Schema(description = " 购物车修改- DTO")
@ToString(callSuper = true)
public class CartItemUpdateDTO {

    @Schema(description = "数据ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer id;

    /**
     * 购物车code
     */
    @Schema(description = "购物车code", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cartCode;


    /**
     * 商品SKU编码
     */
    @Schema(description = "商品SKU编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "商品SKU编码不能为空")
    private String productSkuCode;


    /**
     * 商品数量
     */
    @Schema(description = "商品数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品数量不能为空")
    private Integer quantity;


}

package com.jlr.ecp.order.api.refund.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @createDate 11/03/2025 15:12
 **/
@Data
@Schema(description = "订单管理 - 退单编辑DTO")
public class OrderRefundEditDTO {

    @Schema(description = "退单订单号")
    @NotBlank(message = "退单订单号不能为空")
    private String refundOrderCode;

    @Schema(description = "运营人员备注")
    private String refundRemark;

    @Schema(description = "物流单号")
    private String logisticsCode;

    @Schema(description = "物流公司code")
    private String logisticsCompanyCode;

    @Schema(description = "物流公司名称")
    private String logisticsCompanyName;
}

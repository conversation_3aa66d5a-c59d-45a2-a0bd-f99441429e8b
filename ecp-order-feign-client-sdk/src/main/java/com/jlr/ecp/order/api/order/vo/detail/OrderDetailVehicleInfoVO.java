package com.jlr.ecp.order.api.order.vo.detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - OrderDetailVehicleInfoVO respVO")
public class OrderDetailVehicleInfoVO {
    /**
     * 车辆VIN密文
     */
    @Schema(description = "车辆VIN密文")
    private String carVin;

    /**
     * 车辆VIN半隐藏
     */
    @Schema(description = "车辆VIN半隐藏")
    private String carVinMix;

    /**
     * 品牌code
     */
    @Schema(description = "品牌code")
    private String brandCode;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    private String brandName;

    /**
     * 车辆型号 编码
     */
    @Schema(description = "车辆型号")
    private String seriesCode;

    /**
     * 车辆名称
     */
    @Schema(description = "车辆名称")
    private String seriesName;

    /**
     * 车型年款
     */
    @Schema(description = "车型年款")
    private String modelYear;

    /**
     * 配置code
     */
    @Schema(description = "配置code")
    private String configCode;

    /**
     * 配置名称
     */
    @Schema(description = "配置名称")
    private String configName;

}

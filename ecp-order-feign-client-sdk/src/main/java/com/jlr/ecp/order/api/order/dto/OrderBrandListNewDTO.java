package com.jlr.ecp.order.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * eg.
 * 点击揽胜tab，这个品牌下的购买的订单对应的 List<series_code>
 *
 * <AUTHOR>
 */
@Schema(description = "小程序端 - 前端传入的 当前用户所拥有的根据tab分类下的car_vin DTO")
@Data
public class OrderBrandListNewDTO {
//    @NotNull(message = "brandVehicles不能为空")
//    @Schema(description = "eg.点击揽胜tab，传来Map<揽胜, Map<car_vin, series_code:series_name> > map")
//    private Map<String, Map<String, String>> brandVehicles;

    @NotEmpty(message = "seriesCodeList不能为空")
    @Schema(description = "eg.点击揽胜tab，这个品牌下的购买的订单对应的 List<series_code>")
    List<String> seriesCodeList;

    @NotBlank(message = "consumerCode不能为空")
    @Schema(description = "用户编码，后续可能拿 oneId去代替")
    String consumerCode;
}

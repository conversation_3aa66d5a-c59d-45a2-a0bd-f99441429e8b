package com.jlr.ecp.order.api.refund.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @createDate 17/03/2025 15:14
 **/
@Data
@Schema(description = "退单订单商品行")
public class RefundOrderItemVO {
    @Schema(description = "退单订单编码")
    private String refundOrderCode;

    @Schema(description = "子订单号")
    private String orderCode;

    @Schema(description = "商品主图URL")
    private String productImageUrl;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品SKU编码")
    private String productSkuCode;

    @Schema(description = "订单商品SKU ID - 金蝶sku编码")
    private String kingdeeSkuCode;

    @Schema(description = "购买数量")
    private Integer productQuantity;

    @Schema(description = "售价")
    private String productSalePrice;

    @Schema(description = "实付金额")
    private String costAmount;

    @Schema(description = "实付积分")
    private Integer pointAmount;

    @Schema(description = "退款金额")
    private String refundMoney;

    @Schema(description = "退单实付积分")
    private Integer refundPoint;

    @Schema(description = "售后状态")
    private Integer afterSalesStatus;

    @Schema(description = "优惠券售后状态")
    private Integer couponRefundStatus;

    @Schema(description = "物流退单状态")
    private Integer logisticsRefundStatus;

    @Schema(description = "有效期开始时间")
    private String validStartTime;

    @Schema(description = "有效期结束时间")
    private String validEndTime;

    @Schema(description = "卡券code")
    private String discountCouponCode;

    @Schema(description = "卡券模版code")
    private String couponModelCode;
}

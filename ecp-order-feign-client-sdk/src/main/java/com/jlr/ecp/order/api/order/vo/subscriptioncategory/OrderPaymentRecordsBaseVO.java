package com.jlr.ecp.order.api.order.vo.subscriptioncategory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.api.order.vo.subscriptioncategory
 * @className: OrderPaymentRecordsBaseVO
 * @author: gaoqig
 * @description: 订单支付记录基础信息VO
 * @date: 2025/4/2 17:10
 * @version: 1.0
 */
@Data
public class OrderPaymentRecordsBaseVO {
    /**
     * 发起用户JLRID
     */
    @Schema(description = "发起用户JLRID")
    private String consumerCode;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderCode;

    /**
     * 交易流水号; 支付中心交易流水号
     */
    @Schema(description = "交易流水号; 支付中心交易流水号")
    private String payApplyNo;

    /**
     * 发起交易时间
     */
    @Schema(description = "发起交易时间")
    private LocalDateTime submitTime;

    /**
     * 支付状态; 1待支付 2已支付
     */
    @Schema(description = "支付状态; 1待支付 2已支付")
    private Integer payStatus;

    /**
     * 支付完成时间
     */
    @Schema(description = "支付完成时间")
    private LocalDateTime payFinishTime;
}

package com.jlr.ecp.order.api.cart.dto;


import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.internal.promotion.dto
 * @className: TmpDto
 * @author: gaoqig
 * @description: 临时类，之后要删除。用来伪表示商品与优惠券的对应关系
 * @date: 2025/3/6 20:52
 * @version: 1.0
 */
@Getter
@Setter
public class ProductCouponRelDto {
    private String productCode;

    private List<String> couponModuleCodeList;
}

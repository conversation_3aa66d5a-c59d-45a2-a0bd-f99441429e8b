package com.jlr.ecp.order.api.order.vo.detail;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - OrderStatusLis respVO")
public class OrderDetailOrderStatusVO {

    /**
     * 订单状态进度条
     */
    @Schema(description = "订单状态进度条")
    private List<OrderStatusProgress> orderStatusProgressList;



    /**
     * 订单状态
     */
    @Data
    public static class OrderStatusProgress {
        /**
         * 变更后订单状态枚举值
         */
        private Integer afterStatus;

        /**
         * 变更后订单状态描述
         */
        private String afterStatusDesc;

        /**
         * 变更时间
         */
        private String changeTime;

    }
}



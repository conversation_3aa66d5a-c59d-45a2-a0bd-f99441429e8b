package com.jlr.ecp.order.api.order.vo.bak;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - order status info VO")
public class OrderStatusInfoVO {
    @Schema(description = "订单状态")
    private String orderStatus;

    @Schema(description = "支付状态")
    private String paymentStatus;

    @Schema(description = "退款状态")
    private String refundStatus;

    @Schema(description = "订单状态")
    private String orderClosureReason;

    // 订单完成时间 = 成功激活时间
}

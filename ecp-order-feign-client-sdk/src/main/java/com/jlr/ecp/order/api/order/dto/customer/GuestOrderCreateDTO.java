package com.jlr.ecp.order.api.order.dto.customer;

import com.jlr.ecp.order.api.order.dto.GlobalInfoDTO;
import com.jlr.ecp.order.api.order.dto.OrderInfoDTO;
import com.jlr.ecp.order.api.order.dto.address.OrderGiftAddressDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 待客下单 - 订单创建-购物车item DTO
 *
 * <AUTHOR>
 */
@Schema(description = "待客下单 - 订单创建-购物车item DTO")
@Data
public class GuestOrderCreateDTO {
    /**
     * 0.一些全局信息
     * 全局 品牌编码
     * 渠道编码
     * 用户编码
     */
    @Valid
    @NotNull(message = "一些全局信息不能为空")
    GlobalInfoDTO globalInfoDTO;

    /**
     * 0.购物车item基本信息
     */
    @Valid
    @NotNull(message = "购物车item基本信息不能为空")
    List<GuestOrderShopCarItemDTO> shopCarItemList;

    /**
     * 订单信息 包括
     * 1.登录信息 IN CONTROL ID
     * 2.联系人信息
     * 3.支付信息
     * 4.条款信息
     * <p>
     */
    @Valid
    @NotNull(message = "订单信息不能为空")
    OrderInfoDTO orderInfoDTO;

    @Valid
    @NotNull(message = "赠品信息不能为空")
    OrderGiftAddressDTO giftInfoDTO;

    /**
     * 代客下单信息
     *
     * 接收付款短信手机号 不为空
     * 短信模板编码 不为空
     * 订单备注 不超过50位
     */
    @Valid
    @NotNull(message = "代客下单信息不能为空")
    GuestOrderCreateCustomerServiceDTO customerServiceOrderDTO;

    /**
     * 校验购物车项的品牌编码是否与全局品牌编码一致
     */
    public void validateBrandCodes() {
        String globalBrandCode = this.globalInfoDTO.getGlobalBrandCode();
        Set<String> itemBrandCodes = this.shopCarItemList.stream()
                .map(GuestOrderShopCarItemDTO::getItemBrandCode)
                .collect(Collectors.toSet());

        if ("JLR".equalsIgnoreCase(globalBrandCode)) {
            if (!itemBrandCodes.contains("LR") || !itemBrandCodes.contains("JA")) {
                throw new IllegalArgumentException("当全局品牌编码为JLR时，购物车中必须同时包含路虎（LR）和捷豹（JA）品牌的商品");
            }
        } else if ("LR".equalsIgnoreCase(globalBrandCode) || "JA".equalsIgnoreCase(globalBrandCode)) {
            if (itemBrandCodes.size() != 1 || !itemBrandCodes.contains(globalBrandCode)) {
                throw new IllegalArgumentException("当全局品牌编码为" + globalBrandCode + "时，购物车中所有商品的品牌编码必须与此一致");
            }
        } else {
            throw new IllegalArgumentException("未知的全局品牌编码：" + globalBrandCode);
        }
    }
}

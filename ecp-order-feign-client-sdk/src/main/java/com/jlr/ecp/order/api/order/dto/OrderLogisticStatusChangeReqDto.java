package com.jlr.ecp.order.api.order.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.api.order.dto
 * @className: OrderLogisticStatusChangeReqDto
 * @author: gaoqig
 * @description: 订单物流状态变化时请求参数
 * @date: 2025/4/3 11:11
 * @version: 1.0
 */
@Data
public class OrderLogisticStatusChangeReqDto {
    /**
     * 订单编号
     */
    @Schema(description = "订单编号", required = true)
    private String orderCode;

    @Schema(description = "是否拆分发货(1:整单发货 2:拆单发货)")
    private int split;

    List<OrderItemLogisticStatusChangeReqDto> orderItemLogisticStatusChangeList;

    @Data
    public static class OrderItemLogisticStatusChangeReqDto {
        /**
         * 订单行编号
         */
        @Schema(description = "订单行编号", required = true)
        private String orderItemCode;

        /**
         * 快递公司编码
         */
        @Schema(description = "快递公司编码", required = true)
        private String logisticsCode;

        /**
         * 快递公司名称
         */
        @Schema(description = "快递公司名称", required = true)
        private String logisticsName;

        /**
         * 快递运单号
         */
        @Schema(description = "快递运单号", required = true)
        private String trackingNumber;
    }
}

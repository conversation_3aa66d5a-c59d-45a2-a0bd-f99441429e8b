package com.jlr.ecp.order.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端 - 订单创建-订单信息 DTO")
public class BrandGoodsOrderInfoDTO {
    @Schema(description = "1.登录信息 IN CONTROL ID")
    private String incontrolId;

    @Valid
    @Schema(description = "2.联系人信息")
    private ContactInfoDTO contactInfoDTO;

    @Valid
    @Schema(description = "3.支付信息")
    @NotNull(message = "3.支付信息不能为空")
    private PaymentInfoDTO paymentInfoDTO;

    @Valid
    @Schema(description = "跟整个父订单关联的已经去重的条款编码list 这个productCode对应的A，B policy 下一个productCode对应的可能是B C，这个父订单productCodeList 对应的条款编码是 A B C", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "整个父订单关联的条款编码list不能为空")
    private List<String> allPolicyCodeList;

}

package com.jlr.ecp.order.api.refund.dto;


import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;

/**
 * <AUTHOR>
 * @createDate 09/03/2025 17:00
 **/
@Schema(description = "订单管理 - 退单列表入参DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderRefundPageReqNewDTO extends PageParam {

    @Schema(description = "退单订单号")
    private String refundOrderCode;

    @Schema(description = "主订单号")
    private String parentOrderCode;

    @Schema(description = "子订单号")
    private String orderCode;

    @Schema(description = "售后类型")
    private Integer refundOrderType;

    @Schema(description = "售后状态列表")
    private List<Integer> afterSalesStatusList;

    @Schema(description = "优惠券售后状态列表")
    private List<Integer> couponRefundStatusList;

    @Schema(description = "物流退单状态列表")
    private List<Integer> logisticsRefundStatusList;

    @Schema(description = "虚拟商品卡券编码")
    private String couponModelCode;

    @Schema(description = "买家手机号")
    private String recipientPhone;

    @Schema(description = "微信手机号")
    private String wxPhone;

    @Schema(description = "订单手机号")
    private String contactPhone;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "通过创建时间（添加日期）排序方式（asc：升序，desc：降序）")
    private String createdTimeSort;

    @Schema(description = "创建方")
    private List<Integer> refundSourceList;

    @Schema(description = "业务线")
    private String businessCode;

    @Schema(description = "订单商品SKU ID")
    private String productSkuCode;

    @Schema(description = "金蝶sku编码")
    private String kingdeeSkuCode;

    public void validateParameters() {
        if (startTime !=null && !startTime.isEmpty()){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
            LocalDateTime dateTime = null;
            try {
                dateTime = LocalDateTime.parse(startTime + " 00:00:00", formatter);
                // 日期字符串验证和清理成功，可以将dateTime对象用于后续操作
            } catch (DateTimeParseException e) {
                // 日期字符串验证和清理失败，处理异常
            }
            assert dateTime != null;
            this.startTime = dateTime.toString();
        }

        if (endTime !=null && !endTime.isEmpty()){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
            LocalDateTime dateTime = null;
            try {
                dateTime = LocalDateTime.parse(endTime + " 23:59:59", formatter);
                // 日期字符串验证和清理成功，可以将dateTime对象用于后续操作
            } catch (DateTimeParseException e) {
                // 日期字符串验证和清理失败，处理异常
            }
            assert dateTime != null;
            this.endTime = dateTime.toString();
        }
    }
}

package com.jlr.ecp.order.api.refund.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> Hongyi
 **/
@Data
@Schema(description = "退单管理 - 待状态的订单行")
public class OrderRefundItemWithStatusDTO {

    @Schema(description = "退单订单号")
    private String refundOrderCode;

    @Schema(description = "退单状态")
    private Integer refundOrderStatus;

    @Schema(description = "订单item编码")
    private String orderItemCode;

    @Schema(description = "退单实付金额")
    private Integer refundMoney;

    @Schema(description = "退单数量")
    private Integer refundQuantity;

    @Schema(description = "退单运费(单位分)")
    private Integer refundFreight;

}

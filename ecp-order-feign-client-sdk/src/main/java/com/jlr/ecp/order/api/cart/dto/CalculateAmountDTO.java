package com.jlr.ecp.order.api.cart.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 计算优惠价DTO
 * <AUTHOR>
 */
@Data
@Schema(description = " 计算优惠价- DTO", requiredProperties = {"cartItemCodeList","productSpuCodeList"})
@ToString(callSuper = true)
@Validated
public class CalculateAmountDTO {

    /**
     * 购物车栏目编码
     */
    @Schema(description = "购物车栏目编码，与productSkuCodeList必须传一个且只能传一个")
    private List<String> cartItemCodeList;

    /**
     * 商品Sku编码列表
     * 购物车栏目编码和本字段必须传一个，传这个字段时，表示不通过购物车结算跳转到提交订单页面
     */
    private List<ProductSpuSkuDTO> productItems;

    @Schema(description = "优惠券编码，cartItemCodeList")
    private String couponCode;

    @Schema(description = "优惠券类型：1-兑换券(暂不支持) 2-代金券 3-折扣券 4-满减券")
    private Integer couponType;

    /***
     * @description 是否忽略优惠（主动不使用优惠的场景）： 当前仅当传了true，就会才会不计算优惠
    */

    @Schema(description = "忽略优惠")
    private Boolean ignoreDiscount;

    @Schema(description = "商品行信息")
    private List<CartProductSkuInfo> productItemList;

    @Schema(description = "收件人地址编码")
    private String recipientAddressCode;

    @Schema(description = "优惠券类型：1-兑换券(暂不支持) 2-代金券 3-折扣券 4-满减券")
    private Integer paymentType;

    @Schema(description = "是否来自购物车页面,默认未否")
    private boolean fromShopCartPage = false;

    @Schema(description = "车辆识别码")
    private List<String> vinList;
}

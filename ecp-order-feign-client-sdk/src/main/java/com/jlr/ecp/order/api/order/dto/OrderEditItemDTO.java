package com.jlr.ecp.order.api.order.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - product item edit dto")
public class OrderEditItemDTO {
    /**
     * 商品SKU编码
     */
    @Schema(description = "商品SKU编码")
    @NotBlank(message = "商品SKU编码不能为空")
    private String productSkuCode;

    /**
     * 备注
     */
    @Schema(description = "商品备注")
    @Length(max = 100, message = "商品备注长度不能超过100个字符")
    private String remark;
}

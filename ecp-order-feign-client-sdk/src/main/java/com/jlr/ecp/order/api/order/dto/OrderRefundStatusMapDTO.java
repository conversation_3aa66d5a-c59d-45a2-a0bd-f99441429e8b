package com.jlr.ecp.order.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "后端 - 订单操作列表")
@Data
public class OrderRefundStatusMapDTO {

    @Schema(description = "订单项编码")
    private String orderItemCode;

    @Schema(description = "退款状态")
    private Integer refundOrderStatus;

    @Schema(description = "退款金额")
    private Integer refundAmount;

    @Schema(description = "退款数量")
    private Integer refundQuantity;
}

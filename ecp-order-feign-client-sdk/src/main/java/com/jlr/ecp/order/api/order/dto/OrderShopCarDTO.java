package com.jlr.ecp.order.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - 订单创建-购物车 DTO")
public class OrderShopCarDTO {
    /**
     * 用户编码;用户编码
     */
    @Schema(description = "用户编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户编码不能为空")
    private String consumerCode;

    /**
     * 购物车code;购物车code，雪花算法
     */
    @Schema(description = "购物车code", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "购物车code不能为空")
    private String cartCode;

    /**
     * 购物车原价;购物车原价，单位分
     */
    @Schema(description = "购物车原价", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "购物车原价不能为空")
    private String originalFeeTotal;

    /**
     * 购物车折扣价;购物车折扣价，单位分
     */
    @Schema(description = "购物车折扣价")
    private String discountFeeTotal;
}

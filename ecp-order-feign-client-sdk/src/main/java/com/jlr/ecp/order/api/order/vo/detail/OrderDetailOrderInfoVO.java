package com.jlr.ecp.order.api.order.vo.detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - OrderInfo respVO")
public class OrderDetailOrderInfoVO {
    /**
     * 商品履约类型0: 聚合父订单1：远程车控；2：实物商品；3：充电产品；4：组合商品; 5. 电子兑换券;
     */
    @Schema(description = "商品履约类型枚举值; 商品履约类型0: 聚合父订单1：远程车控；2：实物商品；3：充电产品； " +
            "用来判断是否是父单")
    private Integer orderType;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态枚举值")
    private Integer orderStatus;

    /**
     * 订单状态 - 前端显示用
     * 售后状态为 1-4 的，显示为 售后处理中（90501）
     */
    @Schema(description = "订单状态 - 前端显示用")
    private Integer displayOrderStatus;

    /**
     * 订单状态描述
     */
    @Schema(description = "订单状态描述")
    private String orderStatusDesc;

    /**
     * 支付状态
     */
    @Schema(description = "支付状态枚举值")
    private Integer paymentStatus;

    /**
     * 支付状态描述
     */
    @Schema(description = "支付状态描述")
    private String paymentStatusDesc;

    /**
     * 退款状态
     */
    @Schema(description = "退款状态枚举值")
    private Integer refundStatus;

    /**
     * 退款状态描述
     */
    @Schema(description = "退款状态描述")
    private String refundStatusDesc;

    /**
     * 主订单号
     */
    @Schema(description = "主订单号")
    private String parentOrderCode;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderCode;

    /**
     * 订单金额
     */
    @Schema(description = "订单金额,实付金额")
    private String costAmount;

    /**
     * 下单渠道枚举值
     */
    @Schema(description = "下单渠道枚举值")
    private Integer orderChannel;

    /**
     * 下单渠道描述
     */
    @Schema(description = "下单渠道描述")
    private String orderChannelDesc;

    /**
     * 客户留言
     */
    @Schema(description = "客户留言")
    private String customerRemark;

    /**
     * 订单备注
     */
    @Schema(description = "订单备注")
    private String operatorRemark;


    @Schema(description = "订单关闭原因")
    private String orderCloseReason;

    @Schema(description = "订单来源")
    private String orderSource;

    @Schema(description = "创建人员")
    private String orderCreator;

    @Schema(description = "代客下单 接受付款短信手机号 加密版")
    private String receivePhone;

    @Schema(description = "代客下单 接受付款短信手机号 隐藏手机号中间4位 替换字符为* eg.135****2889")
    private String receivePhoneMix;

    @Schema(description = "订单创建时间")
    private String orderCreateTime;

    @Schema(description = "订单完成时间")
    private String orderCompleteTime;
}

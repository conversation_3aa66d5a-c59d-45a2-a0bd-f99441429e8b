package com.jlr.ecp.order.api.order.dto.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "客服订单短信重发请求参数")
public class ResendMessageDTO {

    @Schema(description = "订单编号", required = true)
    @NotBlank(message = "订单编号不能为空")
    private String orderCode;

    @Schema(description = "重发手机号", required = true)
    @NotBlank(message = "重发手机号不能为空")
    private String receivePhone;

    @Schema(description = "商品服务名称", required = true)
    @NotBlank(message = "商品服务名称不能为空")
    private String serviceName;

    @Schema(description = "carVin", required = true)
    @NotBlank(message = "carVin不能为空")
    private String carVin;

    // 可以根据需要添加其他参数
}
package com.jlr.ecp.order.api.refund.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jlr.ecp.order.api.order.vo.detail.ProductItemInfoAppVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - order refund detail VO")
public class RefundDetailRespVO {

    @Schema(description = "退单订单号")
    private String refundOrderCode;

    @Schema(description = "原始订单号")
    private String originOrderCode;

    @Schema(description = "附件")
    private List<String> attachmentUrls;


    @Schema(description = "是否退款")
    private Integer refundMoney;

    @Schema(description = "退款金额")
    private String refundMoneyAmount;

    @Schema(description = "退单状态")
    private Integer refundOrderStatus;

    @Schema(description = "退单状态文本")
    private String refundOrderStatusText;

    @Schema(description = "提交人")
    private String submitUser;

    @Schema(description = "服务截止日期")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private LocalDateTime serviceEndDate;

    /**
     * 审批拒绝理由
     */
    @Schema(description = "审批拒绝理由")
    private String rejectReason;

    /**
     * 商品item list
     */
    @Schema(description = "商品信息")
    List<ProductItemInfoAppVO> productItemInfoList;


    /**
     * 备注
     */
    @Schema(description = "退款备注信息")
    private String refundRemark;
}

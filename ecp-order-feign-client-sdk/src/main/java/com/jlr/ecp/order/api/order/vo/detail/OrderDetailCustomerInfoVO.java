package com.jlr.ecp.order.api.order.vo.detail;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - OrderDetailCustomerInfoVO respVO")
public class OrderDetailCustomerInfoVO {
    /**
     * 客户微信昵称
     */
    @Schema(description = "客户微信昵称")
    private String wxNickName;

    /**
     * 微信授权手机号密文
     */
    @Schema(description = "微信授权手机号密文")
    private String wxPhone;

    /**
     * 微信授权手机号半隐藏
     */
    @Schema(description = "微信授权手机号半隐藏")
    private String wxPhoneMix;

    /**
     * 下单联系人手机号密文
     */
    @Schema(description = "下单联系人手机号密文")
    private String contactPhone;

    /**
     * 下单联系人手机号半隐藏
     */
    @Schema(description = "下单联系人手机号半隐藏")
    private String contactPhoneMix;


    /**
     * 买家名称
     */
    @Schema(description = "买家名称密文")
    private String recipient;

    /**
     * 买家手机号密文
     */
    @Schema(description = "买家手机号密文")
    private String recipientPhone;

    /**
     * 买家手机号半隐藏
     */
    @Schema(description = "买家手机号半隐藏")
    private String recipientPhoneMix;

    /**
     * 买家地址
     */
    @Schema(description = "买家地址密文")
    private String recipientAddress;

    /**
     * 买家地址
     */
    @Schema(description = "完整买家地址密文")
    private String fullRecipientAddress;

    /**
     * Incontrol账号密文
     */
    @Schema(description = "Incontrol账号密文")
    private String incontrolId;

    /**
     * Incontrol账号密文半隐藏
     */
    @Schema(description = "Incontrol账号密文半隐藏")
    private String incontrolIdMix;

    /**
     * 用户编码
     */
    @Schema(description = "用户编码")
    private String consumerCode;
}

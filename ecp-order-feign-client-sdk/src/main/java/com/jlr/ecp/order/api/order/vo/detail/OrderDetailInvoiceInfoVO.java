package com.jlr.ecp.order.api.order.vo.detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 开票信息 respVO")
@Data
public class OrderDetailInvoiceInfoVO {

    @Schema(description = "电子普票信息")
    private OrderDetailEInvoiceInfoVO eInvoiceInfo;

    @Schema(description = "纸质专票信息")
    private OrderDetailPaperInvoiceInfoVO paperInvoiceInfo;

    @Schema(description = "电子专票信息")
    private OrderDetailESpecialInvoiceInfoVO eSpecialInvoiceInfo;
}

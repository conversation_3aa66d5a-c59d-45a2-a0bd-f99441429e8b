package com.jlr.ecp.order.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 远程调用商品服务，根据List<order_item_code> 查询商品信息，组装成Map<String carVin,orderItemDO对象>返回
 *
 * <AUTHOR>
 */
@Schema(description = "远程调用商品服务，根据List<order_item_code> 查询商品信息，组装成Map<String carVin,orderItemDO对象>返回")
@Data
public class OrderItemCodeListDTO {

    @NotEmpty(message = "商品编码List不能为空")
    @Schema(description = "商品编码List")
    List<String> orderItemCodeList;
}

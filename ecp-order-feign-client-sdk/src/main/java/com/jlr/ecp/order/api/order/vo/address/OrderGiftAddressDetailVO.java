package com.jlr.ecp.order.api.order.vo.address;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class OrderGiftAddressDetailVO implements Serializable {

    @Schema(description = "是否有赠品")
    private String needGift;

    @Schema(description = "收件地址")
    private String addressMix;

    @Schema(description = "收件人")
    private String recipientMix;

    @Schema(description = "收件人电话")
    private String recipientPhoneMix;

    @Schema(description = "收件地址MD5")
    private String address;

    @Schema(description = "收件人MD5")
    private String recipient;

    @Schema(description = "收件人电话MD5")
    private String recipientPhone;
}
package com.jlr.ecp.order.api.refund.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "订单管理 - 退单审批DTO")
@Data
@ToString(callSuper = true)
@Validated
public class OrderRefundApproveDTO {

    @Schema(description = "审核状态 ")
    @NotNull(message = "审核状态不能为空")
    private Boolean approveStatus;

    @Schema(description = "退单订单号")
    @NotBlank(message = "订单号不能为空")
    private String refundOrderCode;

    @Schema(description = "拒绝理由")
    private String rejectReason;

}

package com.jlr.ecp.order.api.order.vo.brandcategory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端-我的订单 商品分类下 订单信息")
public class OrderBrandOrderInfoVO {
    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderCode;

    /**
     * 订单状态（t_order_info，order_status）
     * 新增一些value值定义，todo 未来补充
     */
    @Schema(description = "订单状态（t_order_info，order_status）")
    private Integer orderStatus;

    @Schema(description = "1:已开票 2:已红冲 3:已重开 4:未开票")
    private Integer invoiceStatus;

    @Schema(description = "1:已开票 2:已红冲 3:已重开 4:未开票")
    private String invoiceStatusDesc;

    /**
     * 小程序端显示的订单状态
     */
    @Schema(description = "小程序端显示的订单状态，订单详情页面-》为待支付时显示有个倒计时页面，其他状态不显示这个字段")
    private String customerOrderStatusView;

    /**
     * customer_after_sales_order_status_view（小程序售后状态），订单详情页面-》没有就不显示这个字段
     */
    @Schema(description = "customer_after_sales_order_status_view（小程序售后状态），没有则不显示这个字段")
    private String customerAfterSalesOrderStatusView;

    /**
     * 下单时间=订单提交时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    @Schema(description = "下单时间=订单提交时间")
    private LocalDateTime orderTime;

    /**
     * 支付状态 写这方便排查订单的支付状态
     */
    @Schema(description = "支付状态")
    private Integer paymentStatus;

    /**
     * 支付状态描述 写这方便排查订单的支付状态
     */
    @Schema(description = "支付状态描述")
    private String paymentStatusDesc;

    /**
     * 订单支付剩余时间时间戳
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    @Schema(description = "订单支付剩余时间时间戳 ")
    private Long paymentTimeout;

    /**
     * redis中缓存的 订单是否为支付中
     * 为支付中，订单不能被取消
     */
    @Schema(description = "redis中缓存的 订单是否为支付中 ，值为true代表此订单正在支付中，不能取消")
    private Boolean isPaying;

    /**
     * redis中缓存的 订单是否为支付中
     * 为支付中，订单不能被取消
     */
    @Schema(description = "redis中缓存的 订单支付回调是否完成 ，值为true代表此订单支付回调中，不能取消")
    private Boolean isPayDown;

    /**
     * 支付方式
     * TODO 支付时更新t_order_info？ 1：微信支付 2：支付宝支付 3：银联支付 4：现金支付 5：其他
     */
    @Schema(description = "支付方式")
    private String paymentMethod;

    /**
     * 当前展示总金额 为实付金额
     */
    @Schema(description = "当前展示总金额 为实付金额")
    private String costAmount;

    /**
     * 师傅金额含运费
     */
    @Schema(description = "实付金额+运费")
    private String costAmountIncludeShipping;

    /**
     * 采用Integer和BigDecimal来表示价格
     * 不和上面一致了
     */
    @Schema(description = "使用的积分")
    private Integer costPoints;

    @Schema(description = "运费")
    private String shipping;

    //订单原价
    @Schema(description = "订单原价")
    private String originalAmount;

    /**
     * 服务起始日期
     */
    @Schema(description = "服务起始日期")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private LocalDateTime serviceBeginDate;

    /**
     * 服务截止日期
     */
    @Schema(description = "服务截止日期")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private LocalDateTime serviceEndDate;

    /**
     * 客户留言信息
     */
    @Schema(description = "客户留言信息")
    private String customerRemark;

    /**
     * 订单类型
     */
    @Schema(description = "订单类型, 当订单类型==4时，为虚拟组合订单，不展示服务起止日期")
    private Integer orderType;

    /**
     * 下单渠道;下单渠道 1：捷豹小程序 2：路虎小程序 3：官网 5:代客下单
     */
    @Schema(description = "下单渠道")
    private Integer orderChannel;

    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    /**
     * 抵扣金额(包括积分抵扣和优惠券抵扣)
     */
    @Schema(description = "抵扣金额(包括积分抵扣和优惠券抵扣")
    private String discountAmount;

    @Schema(description = "是否使用了优惠券（不含包括分）")
    private boolean isCouponUsed;

    @Schema(description = "优惠券模版名称")
    private String couponModuleName;

    /**
     * 业务编码:BusinessIdEnum
     */
    @Schema(description = "业务编码")
    private String businessCode;

    @Schema(description = "订单完成时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime completedTime;

    @Schema(description = "订单关闭时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime closedTime;

    @Schema(description = "订单可开票金额")
    private String invocableAmount;
}

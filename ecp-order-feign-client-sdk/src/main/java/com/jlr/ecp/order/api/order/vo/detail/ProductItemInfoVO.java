package com.jlr.ecp.order.api.order.vo.detail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - product item respVO")
public class ProductItemInfoVO {

    /**
     * 订单item编码
     */
    @Schema(description = "订单item编码")
    private String orderItemCode;

    /**
     * 商品快照编码
     */
    @Schema(description = "商品快照编码")
    private String productVersionCode;

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    private String productCode;

    /**
     * 商品SKU编码
     */
    @Schema(description = "商品SKU编码")
    private String productSkuCode;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String productName;

    /**
     * 购买数量
     */
    @Schema(description = "购买数量")
    private Integer productQuantity;

    /**
     * stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI
     */
    @Schema(description = "stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI")
    private String productAttribute;

    /**
     * 标价
     */
    @Schema(description = "标价")
    private String productMarketPrice;

    /**
     * 售价
     */
    @Schema(description = "售价")
    private String productSalePrice;
    /**
     * 服务起始日期
     */
    @Schema(description = "服务起始日期")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private LocalDateTime serviceBeginDate;

    /**
     * 服务截止日期
     */
    @Schema(description = "服务截止日期")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private LocalDateTime serviceEndDate;

    /**
     * 服务截止日期
     */
    @Schema(description = "实际服务截止日期")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    private LocalDateTime actualEndDate;

    /**
     * 服务状态
     */
    @Schema(description = "服务状态")
    private Integer serviceStatus;

    /**
     * 服务状态描述
     */
    @Schema(description = "服务状态描述")
    private String serviceStatusDesc;

    /**
     * 取消状态描述
     * 后台：operation_origin_order_cancel_status_view(原单服务取消状态)
     */
    @Schema(description = "取消状态描述，后台：operation_origin_order_cancel_status_view(原单服务取消状态)")
    private String operationOriginOrderCancelStatusView;

    /**
     * 备注
     */
    @Schema(description = "商品备注")
    @Length(max = 100, message = "商品备注长度不能超过100个字符")
    private String remark;

    @Schema(description = "退单号")
    private String refundOrderCode;

    /**
     * 拒绝理由
     */
    @Schema(description = "退单拒绝理由")
    private String rejectReason;

    /**
     * 订单item商品的类型，1普通商品 2组合商品
     */
    @Schema(description = "订单item商品的类型")
    private Integer orderItemSpuType;

    /**
     * 实付金额
     */
    @Schema(description = "实付金额")
    private String costAmount;

    @Schema(description = "售后状态:1：售后处理中 2：售后已完成 3：售后关闭")
    private Integer aftersalesStatus;

    @Schema(description = "售后状态描述:1：售后处理中 2：售后已完成 3：售后关闭")
    private String aftersalesStatusDesc;
}

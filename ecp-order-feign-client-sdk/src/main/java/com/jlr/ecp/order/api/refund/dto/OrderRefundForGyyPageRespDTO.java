package com.jlr.ecp.order.api.refund.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "查询售后单 For 管易云出参")
@Data
public class OrderRefundForGyyPageRespDTO {

    @Schema(description = "售后单号")
    private String refundOrderCode;

    @Schema(description = "售后类型 1: 退货退款；2: 仅退款")
    private Integer refundOrderType;

    @Schema(description = "原始订单号（平台订单号）")
    private String originOrderCode;

    @Schema(description = "正向订单行号（子订单号）")
    private String orderItemCode;

    @Schema(description = "退款金额（订单行上，单位：分）")
    private Integer refundMoney;

    @Schema(description = "退单数量")
    private Integer refundQuantity;

    @Schema(description = "售后单状态")
    private Integer refundOrderStatus;

    /**
     * 枚举：RefundLogisticsStatusEnum
     */
    @Schema(description = "物流退单状态")
    private Integer logisticsRefundStatus;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "更新时间")
    private LocalDateTime updatedTime;

    @Schema(description = "退款原因")
    private Integer refundReason;

    @Schema(description = "退款原因名称")
    private String refundReasonName;

    @Schema(description = "物流公司code")
    private String logisticsCompanyCode;

    @Schema(description = "物流公司名称")
    private String logisticsCompanyName;

    @Schema(description = "退货物流单号")
    private String logisticsCode;

    @Schema(description = "退货物流单号")
    private Integer refundOrderFufilmentType;

}
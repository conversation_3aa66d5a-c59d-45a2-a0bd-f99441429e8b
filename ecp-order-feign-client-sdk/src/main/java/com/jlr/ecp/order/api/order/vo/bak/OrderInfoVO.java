package com.jlr.ecp.order.api.order.vo.bak;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - order info VO")
public class OrderInfoVO {
    @Schema(description = "父订单号")
    private String parentOrderCode;
    
    @Schema(description = "子订单号")
    private String orderCode;

    @Schema(description = "履约类型")
    private Integer fulfilmentType;
    
    @Schema(description = "业务线编码")
    private String businessCode;

    @Schema(description = "应付现金金额(含税)")
    private Integer costAmount;

    @Schema(description = "应付现金金额(不含税)")
    private Integer costAmountNoTax;

    @Schema(description = "退款金额")
    private String refundAmount;

    @Schema(description = "退款金额(不含税)")
    private String refundAmountNoTax;

    @Schema(description = "运费")
    private Integer freightAmount;

    @Schema(description = "运费费率")
    private BigDecimal freightTax;

    @Schema(description = "订单创建时间")
    private String createdTime;

    @Schema(description = "订单金额")
    private String orderAmount;

    @Schema(description = "下单渠道")
    private String orderChannel;

    @Schema(description = "客户留言")
    private String customerRemark;

    @Schema(description = "订单备注")
    private String operatorRemark;
}

package com.jlr.ecp.order.api.order.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.codec.binary.StringUtils;

import javax.validation.constraints.NotBlank;

/**
 * 一些全局信息，比如
 * 品牌编码
 * 渠道编码
 *
 * <AUTHOR>
 */
@Schema(description = "小程序端 - 前端传入的订单创建DTO")
@Data
public class BrandGoodsGlobalInfoDTO {

    @Schema(description = "用户编码 " +
            "代客下单时：传送常量值'CUSTOMER_SERVICE_ORDER' ", hidden = true, requiredMode = Schema.RequiredMode.REQUIRED)
    private String consumerCode;

    /**
     * 0.一些全局信息
     * 品牌编码
     * <p>
     * 生成 父 子 订单号时会用到
     * 路虎：LR
     * 捷豹：JA
     * 代客下单 可能同时下 捷豹、路虎品牌的车 此时父订单的枚举类 JLR
     */
    @Schema(description = "品牌编码" +
            "1.原有小程序传入brandCode保持不变;" +
            "2.待客下单传过来的实际购买商品存在可能属于不同品牌的情况 都属于路虎：LR、都属于捷豹：JA、既有捷豹又有路虎：JLR", requiredMode = Schema.RequiredMode.REQUIRED, example = "LR")
    String globalBrandCode;

    /**
     * 0.一些全局信息
     * 渠道编码
     * <p>
     * 渠道名 渠道编码
     * 小程序 M
     * 官网  S
     * APP  A
     * <p>
     * 客户服务 CS （CustomerService）: 待客下单
     * <p>
     */
    @Schema(description = "渠道编码 " +
            "路虎小程序：MLR; " +
            "捷豹小程序：MJA; " +
            "客户服务: CS （CustomerService）: 待客下单", requiredMode = Schema.RequiredMode.REQUIRED, example = "MLR")
    @NotBlank(message = "渠道编码不能为空")
    String channelCode;

    /**
     * 校验是否是代客下单请求
     */
    public boolean isGuestOrderRequest() {
        return "CS".equalsIgnoreCase(this.channelCode);
    }

    /**
     * 参数校验方法
     * 渠道编码 必须为下方
     * 路虎小程序：MLR
     * 捷豹小程序：MJA
     * 官网  S
     * APP  A
     * <p>
     * 不能引入 OrderChannelCodeEnum 枚举类 会导致循环依赖
     */
    public boolean validate() {
        // 对于有特定组合要求的品牌编码和渠道编码进行校验
        if ("LR".equalsIgnoreCase(this.globalBrandCode) && StringUtils.equals("MLR", this.channelCode)) {
            return true;
        }
        if ("JA".equalsIgnoreCase(this.globalBrandCode) && StringUtils.equals("MJA", this.channelCode)) {
            return true;
        }

        // 对于代客下单，允许品牌编码为 LR, JA 或 JLR
        if ("CS".equalsIgnoreCase(this.channelCode) &&
                ("LR".equalsIgnoreCase(this.globalBrandCode) ||
                        "JA".equalsIgnoreCase(this.globalBrandCode) ||
                        "JLR".equalsIgnoreCase(this.globalBrandCode))) {
            return true;
        }

        // 对于官网和APP暂时不做特定组合限制
//        if (StringUtils.equals("S", this.channelCode)) {
//            return true;
//        }
//        if (StringUtils.equals("A", this.channelCode)) {
//            return true;
//        }

        return false;
    }
}

package com.jlr.ecp.order.api.order.vo.detail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 电子专票信息 respVO")
@Data
public class OrderDetailESpecialInvoiceInfoVO {
    
    @Schema(description = "开票状态, 2:已开电子专票 3:已红冲电子专票")
    private Integer invoiceStatus;

    @Schema(description = "完成时间", example = "2024/02/02 10:30:00")
    private String finishTime;

    @Schema(description = "发票抬头名称")
    private String invoiceTitleName;

    @Schema(description = "抬头税号")
    private String titleTaxNo;

    @Schema(description = "企业电话")
    private String companyMobile;

    @Schema(description = "企业注册地址")
    private String companyAddress;

    @Schema(description = "开户银行")
    private String companyBankName;

    @Schema(description = "开户行账号")
    private String companyBankAccount;

    @Schema(description = "接收人姓名")
    private String recipientName;

    @Schema(description = "接收人电话")
    private String recipientPhone;

    @Schema(description = "接收人详细地址")
    private String recipientAddress;
    
}

package com.jlr.ecp.order.api.order.vo.bak;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "车辆信息")
public class CarInfoVO {
    @Schema(description = "车辆VIN码", required = true)
    private String vinNumber;

    @Schema(description = "品牌", required = true)
    private String brand;

    @Schema(description = "车型编码", required = true)
    private String seriesCode;

    @Schema(description = "车型名称", required = true)
    private String modelName;

    @Schema(description = "车型年款", required = true)
    private String modelYear;

    @Schema(description = "配置编码", required = true)
    private String configurationCode;

    @Schema(description = "配置名称", required = true)
    private String configurationName;

}

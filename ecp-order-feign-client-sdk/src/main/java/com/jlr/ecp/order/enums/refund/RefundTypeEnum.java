package com.jlr.ecp.order.enums.refund;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 * 0：整单取消
 * 1：部分取消
 */
@AllArgsConstructor
@Getter
public enum RefundTypeEnum {

    /**
     * 0：整单取消
     * 1：部分取消
     */
    FULL_REFUND(0, "整单取消"),
    PARTIAL_REFUND(1, "部分取消");

    /**
     * code
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;

}

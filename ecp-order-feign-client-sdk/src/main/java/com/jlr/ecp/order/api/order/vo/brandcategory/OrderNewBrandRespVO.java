package com.jlr.ecp.order.api.order.vo.brandcategory;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jlr.ecp.order.api.order.vo.base.OrderItemBaseVO;
import com.jlr.ecp.order.api.order.vo.orderlist.OrderSkuItemVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "小程序端 - 不同品牌下 订单列表的 Response VO")
@Data
public class OrderNewBrandRespVO {
    /**
     * 1.商品信息
     */
    @Schema(description = "商品信息")
    private OrderItemBaseVO productInfo;

    /**
     * 商品信息列表
     * 该字段与上述字段都会包括VCS的商品，除此之外该字段还会包括LRE甚至BG的商品信息
     */
    @Schema(description = "商品信息")
    private List<OrderSkuItemVo> productList;

    /**
     * 2.车型信息
     */
    @Schema(description = "车型信息")
    private OrderBrandVehicleInfoVO vehicleInfo;

    /**
     * 3.订单信息
     */
    @Schema(description = "订单信息")
    private OrderBrandOrderInfoVO orderInfo;

    /**
     * 4.订单号
     */
    @Schema(description = "订单号")
    private String orderCode;

    /**
     * 下单时间=订单提交时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    @Schema(description = "下单时间=订单提交时间")
    private LocalDateTime orderTime;

    /**
     * 订单标签信息
     */
    @Schema(description = "订单标签信息")
    private OrderTagInfoVO tagInfo;

    /**
     * 当前总价——金额
     */
    @Schema(description = "当前总价——金额")
    private BigDecimal totalAmount;
    /**
     * 当前总价——积分
     */
    @Schema(description = "当前总价——积分")
    private Integer totalPoints;

    @Schema(description = "业务线编码")
    private String businessCode;
}

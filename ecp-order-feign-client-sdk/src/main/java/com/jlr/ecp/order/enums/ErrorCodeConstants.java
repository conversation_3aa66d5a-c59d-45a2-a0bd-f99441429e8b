package com.jlr.ecp.order.enums;

import com.jlr.ecp.framework.common.exception.ErrorCode;


/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-002-000-000 段
 */
public class ErrorCodeConstants {

    public static final String TEMPLATE_PARAM_LOSE = "模板参数({})缺失";

    // ========== AUTH 模块 1002000000 ==========
    public static final ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1002000000, "登录失败，账号密码不正确");
    public static final ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1002000001, "登录失败，账号被禁用");
    public static final ErrorCode AUTH_LOGIN_CAPTCHA_CODE_ERROR = new ErrorCode(1002000004, "验证码不正确，原因：{}");
    public static final ErrorCode AUTH_THIRD_LOGIN_NOT_BIND = new ErrorCode(1002000005, "未绑定账号，需要进行绑定");
    public static final ErrorCode AUTH_TOKEN_EXPIRED = new ErrorCode(1002000006, "Token 已经过期");
    public static final ErrorCode AUTH_MOBILE_NOT_EXISTS = new ErrorCode(1002000007, "手机号不存在");

    // ========== 菜单模块 1002001000 ==========
    public static final ErrorCode MENU_NAME_DUPLICATE = new ErrorCode(1002001000, "已经存在该名字的菜单");
    public static final ErrorCode MENU_PARENT_NOT_EXISTS = new ErrorCode(1002001001, "父菜单不存在");
    public static final ErrorCode MENU_PARENT_ERROR = new ErrorCode(1002001002, "不能设置自己为父菜单");
    public static final ErrorCode MENU_NOT_EXISTS = new ErrorCode(1002001003, "菜单不存在");
    public static final ErrorCode MENU_EXISTS_CHILDREN = new ErrorCode(1002001004, "存在子菜单，无法删除");
    public static final ErrorCode MENU_PARENT_NOT_DIR_OR_MENU = new ErrorCode(1002001005, "父菜单的类型必须是目录或者菜单");

    // ========== 角色模块 1002002000 ==========
    public static final ErrorCode ROLE_NOT_EXISTS = new ErrorCode(1002002000, "角色不存在");
    public static final ErrorCode ROLE_NAME_DUPLICATE = new ErrorCode(1002002001, "已经存在名为【{}】的角色");
    public static final ErrorCode ROLE_CODE_DUPLICATE = new ErrorCode(1002002002, "已经存在编码为【{}】的角色");
    public static final ErrorCode ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE = new ErrorCode(1002002003, "不能操作类型为系统内置的角色");
    public static final ErrorCode ROLE_IS_DISABLE = new ErrorCode(1002002004, "名字为【{}】的角色已被禁用");
    public static final ErrorCode ROLE_ADMIN_CODE_ERROR = new ErrorCode(1002002005, "编码【{}】不能使用");

    // ========== 用户模块 1002003000 ==========
    public static final ErrorCode USER_USERNAME_EXISTS = new ErrorCode(1002003000, "用户账号已经存在");
    public static final ErrorCode USER_MOBILE_EXISTS = new ErrorCode(1002003001, "手机号已经存在");
    public static final ErrorCode USER_EMAIL_EXISTS = new ErrorCode(1002003002, "邮箱已经存在");
    public static final ErrorCode USER_NOT_EXISTS = new ErrorCode(1002003003, "用户不存在");
    public static final ErrorCode USER_IMPORT_LIST_IS_EMPTY = new ErrorCode(1002003004, "导入用户数据不能为空！");
    public static final ErrorCode USER_PASSWORD_FAILED = new ErrorCode(1002003005, "用户密码校验失败");
    public static final ErrorCode USER_IS_DISABLE = new ErrorCode(1002003006, "名字为【{}】的用户已被禁用");
    public static final ErrorCode USER_COUNT_MAX = new ErrorCode(1002003008, "创建用户失败，原因：超过租户最大租户配额({})！");

    // ========== 部门模块 1002004000 ==========
    public static final ErrorCode DEPT_NAME_DUPLICATE = new ErrorCode(1002004000, "已经存在该名字的部门");
    public static final ErrorCode DEPT_PARENT_NOT_EXITS = new ErrorCode(1002004001, "父级部门不存在");
    public static final ErrorCode DEPT_NOT_FOUND = new ErrorCode(1002004002, "当前部门不存在");
    public static final ErrorCode DEPT_EXITS_CHILDREN = new ErrorCode(1002004003, "存在子部门，无法删除");
    public static final ErrorCode DEPT_PARENT_ERROR = new ErrorCode(1002004004, "不能设置自己为父部门");
    public static final ErrorCode DEPT_EXISTS_USER = new ErrorCode(1002004005, "部门中存在员工，无法删除");
    public static final ErrorCode DEPT_NOT_ENABLE = new ErrorCode(1002004006, "部门({})不处于开启状态，不允许选择");
    public static final ErrorCode DEPT_PARENT_IS_CHILD = new ErrorCode(1002004007, "不能设置自己的子部门为父部门");

    // ========== 岗位模块 1002005000 ==========
    public static final ErrorCode POST_NOT_FOUND = new ErrorCode(1002005000, "当前岗位不存在");
    public static final ErrorCode POST_NOT_ENABLE = new ErrorCode(1002005001, "岗位({}) 不处于开启状态，不允许选择");
    public static final ErrorCode POST_NAME_DUPLICATE = new ErrorCode(1002005002, "已经存在该名字的岗位");
    public static final ErrorCode POST_CODE_DUPLICATE = new ErrorCode(1002005003, "已经存在该标识的岗位");

    // ========== 字典类型 1002006000 ==========
    public static final ErrorCode DICT_TYPE_NOT_EXISTS = new ErrorCode(1002006001, "当前字典类型不存在");
    public static final ErrorCode DICT_TYPE_NOT_ENABLE = new ErrorCode(1002006002, "字典类型不处于开启状态，不允许选择");
    public static final ErrorCode DICT_TYPE_NAME_DUPLICATE = new ErrorCode(1002006003, "已经存在该名字的字典类型");
    public static final ErrorCode DICT_TYPE_TYPE_DUPLICATE = new ErrorCode(1002006004, "已经存在该类型的字典类型");
    public static final ErrorCode DICT_TYPE_HAS_CHILDREN = new ErrorCode(1002006005, "无法删除，该字典类型还有字典数据");

    // ========== 字典数据 1002007000 ==========
    public static final ErrorCode DICT_DATA_NOT_EXISTS = new ErrorCode(1002007001, "当前字典数据不存在");
    public static final ErrorCode DICT_DATA_NOT_ENABLE = new ErrorCode(1002007002, "字典数据({})不处于开启状态，不允许选择");
    public static final ErrorCode DICT_DATA_VALUE_DUPLICATE = new ErrorCode(1002007003, "已经存在该值的字典数据");

    // ========== 通知公告 1002008000 ==========
    public static final ErrorCode NOTICE_NOT_FOUND = new ErrorCode(1002008001, "当前通知公告不存在");

    // ========== 短信渠道 1002011000 ==========
    public static final ErrorCode SMS_CHANNEL_NOT_EXISTS = new ErrorCode(1002011000, "短信渠道不存在");
    public static final ErrorCode SMS_CHANNEL_DISABLE = new ErrorCode(1002011001, "短信渠道不处于开启状态，不允许选择");
    public static final ErrorCode SMS_CHANNEL_HAS_CHILDREN = new ErrorCode(1002011002, "无法删除，该短信渠道还有短信模板");

    // ========== 短信模板 1002012000 ==========
    public static final ErrorCode SMS_TEMPLATE_NOT_EXISTS = new ErrorCode(1002012000, "短信模板不存在");
    public static final ErrorCode SMS_TEMPLATE_CODE_DUPLICATE = new ErrorCode(1002012001, "已经存在编码为【{}】的短信模板");

    // ========== 短信发送 1002013000 ==========
    public static final ErrorCode SMS_SEND_MOBILE_NOT_EXISTS = new ErrorCode(1002013000, "手机号不存在");
    public static final ErrorCode SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS = new ErrorCode(1002013001, TEMPLATE_PARAM_LOSE);
    public static final ErrorCode SMS_SEND_TEMPLATE_NOT_EXISTS = new ErrorCode(1002013002, "短信模板不存在");

    // ========== 短信验证码 1002014000 ==========
    public static final ErrorCode SMS_CODE_NOT_FOUND = new ErrorCode(1002014000, "验证码不存在");
    public static final ErrorCode SMS_CODE_EXPIRED = new ErrorCode(1002014001, "验证码已过期");
    public static final ErrorCode SMS_CODE_USED = new ErrorCode(1002014002, "验证码已使用");
    public static final ErrorCode SMS_CODE_NOT_CORRECT = new ErrorCode(1002014003, "验证码不正确");
    public static final ErrorCode SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY = new ErrorCode(1002014004, "超过每日短信发送数量");
    public static final ErrorCode SMS_CODE_SEND_TOO_FAST = new ErrorCode(1002014005, "短信发送过于频率");
    public static final ErrorCode SMS_CODE_IS_EXISTS = new ErrorCode(1002014006, "手机号已被使用");
    public static final ErrorCode SMS_CODE_IS_UNUSED = new ErrorCode(1002014007, "验证码未被使用");

    // ========== 租户信息 1002015000 ==========
    public static final ErrorCode TENANT_NOT_EXISTS = new ErrorCode(1002015000, "租户不存在");
    public static final ErrorCode TENANT_DISABLE = new ErrorCode(1002015001, "名字为【{}】的租户已被禁用");
    public static final ErrorCode TENANT_EXPIRE = new ErrorCode(1002015002, "名字为【{}】的租户已过期");
    public static final ErrorCode TENANT_CAN_NOT_UPDATE_SYSTEM = new ErrorCode(1002015003, "系统租户不能进行修改、删除等操作！");
    public static final ErrorCode TENANT_NAME_DUPLICATE = new ErrorCode(1002015004, "名字为【{}】的租户已存在");

    // ========== 租户套餐 1002016000 ==========
    public static final ErrorCode TENANT_PACKAGE_NOT_EXISTS = new ErrorCode(1002016000, "租户套餐不存在");
    public static final ErrorCode TENANT_PACKAGE_USED = new ErrorCode(1002016001, "租户正在使用该套餐，请给租户重新设置套餐后再尝试删除");
    public static final ErrorCode TENANT_PACKAGE_DISABLE = new ErrorCode(1002016002, "名字为【{}】的租户套餐已被禁用");

    // ========== 错误码模块 1002017000 ==========
    public static final ErrorCode ERROR_CODE_NOT_EXISTS = new ErrorCode(1002017000, "错误码不存在");
    public static final ErrorCode ERROR_CODE_DUPLICATE = new ErrorCode(1002017001, "已经存在编码为【{}】的错误码");

    // ========== 社交用户 1002018000 ==========
    public static final ErrorCode SOCIAL_USER_AUTH_FAILURE = new ErrorCode(1002018000, "社交授权失败，原因是：{}");
    public static final ErrorCode SOCIAL_USER_UNBIND_NOT_SELF = new ErrorCode(1002018001, "社交解绑失败，非当前用户绑定");
    public static final ErrorCode SOCIAL_USER_NOT_FOUND = new ErrorCode(1002018002, "社交授权失败，找不到对应的用户");

    // ========== 系统敏感词 1002019000 =========
    public static final ErrorCode SENSITIVE_WORD_NOT_EXISTS = new ErrorCode(1002019000, "系统敏感词在所有标签中都不存在");
    public static final ErrorCode SENSITIVE_WORD_EXISTS = new ErrorCode(1002019001, "系统敏感词已在标签中存在");

    // ========== OAuth2 客户端 1002020000 =========
    public static final ErrorCode OAUTH2_CLIENT_NOT_EXISTS = new ErrorCode(1002020000, "OAuth2 客户端不存在");
    public static final ErrorCode OAUTH2_CLIENT_EXISTS = new ErrorCode(1002020001, "OAuth2 客户端编号已存在");
    public static final ErrorCode OAUTH2_CLIENT_DISABLE = new ErrorCode(1002020002, "OAuth2 客户端已禁用");
    public static final ErrorCode OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS = new ErrorCode(1002020003, "不支持该授权类型");
    public static final ErrorCode OAUTH2_CLIENT_SCOPE_OVER = new ErrorCode(1002020004, "授权范围过大");
    public static final ErrorCode OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH = new ErrorCode(1002020005, "无效 redirect_uri: {}");
    public static final ErrorCode OAUTH2_CLIENT_CLIENT_SECRET_ERROR = new ErrorCode(1002020006, "无效 client_secret: {}");

    // ========== OAuth2 授权 1002021000 =========
    public static final ErrorCode OAUTH2_GRANT_CLIENT_ID_MISMATCH = new ErrorCode(1002021000, "client_id 不匹配");
    public static final ErrorCode OAUTH2_GRANT_REDIRECT_URI_MISMATCH = new ErrorCode(1002021001, "redirect_uri 不匹配");
    public static final ErrorCode OAUTH2_GRANT_STATE_MISMATCH = new ErrorCode(**********, "state 不匹配");
    public static final ErrorCode OAUTH2_GRANT_CODE_NOT_EXISTS = new ErrorCode(**********, "code 不存在");

    // ========== OAuth2 授权 ********** =========
    public static final ErrorCode OAUTH2_CODE_NOT_EXISTS = new ErrorCode(**********, "code 不存在");
    public static final ErrorCode OAUTH2_CODE_EXPIRE = new ErrorCode(**********, "code 已过期");

    // ========== 邮箱账号 ********** ==========
    public static final ErrorCode MAIL_ACCOUNT_NOT_EXISTS = new ErrorCode(**********, "邮箱账号不存在");
    public static final ErrorCode MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS = new ErrorCode(**********, "无法删除，该邮箱账号还有邮件模板");

    // ========== 邮件模版 ********** ==========
    public static final ErrorCode MAIL_TEMPLATE_NOT_EXISTS = new ErrorCode(**********, "邮件模版不存在");
    public static final ErrorCode MAIL_TEMPLATE_CODE_EXISTS = new ErrorCode(**********, "邮件模版 code({}) 已存在");

    // ========== 邮件发送 ********** ==========
    public static final ErrorCode MAIL_SEND_TEMPLATE_PARAM_MISS = new ErrorCode(**********, TEMPLATE_PARAM_LOSE);
    public static final ErrorCode MAIL_SEND_MAIL_NOT_EXISTS = new ErrorCode(**********, "邮箱不存在");

    // ========== 站内信模版 ********** ==========
    public static final ErrorCode NOTIFY_TEMPLATE_NOT_EXISTS = new ErrorCode(**********, "站内信模版不存在");
    public static final ErrorCode NOTIFY_TEMPLATE_CODE_DUPLICATE = new ErrorCode(**********, "已经存在编码为【{}】的站内信模板");

    // ========== 站内信模版 1002027000 ==========

    // ========== 站内信发送 1002028000 ==========
    public static final ErrorCode NOTIFY_SEND_TEMPLATE_PARAM_MISS = new ErrorCode(1002028000, TEMPLATE_PARAM_LOSE);
    // ========== bpm Http接口调用 1002029000 ==========
    public static final ErrorCode REST_TEMPLATE_ERROR = new ErrorCode(1002029000, "Http 接口调用失败");
    // ========== 参数配置 1001000000 ==========
    public static final ErrorCode CONFIG_NOT_EXISTS = new ErrorCode(1001000001, "参数配置不存在");
    public static final ErrorCode CONFIG_KEY_DUPLICATE = new ErrorCode(1001000002, "参数配置 key 重复");
    public static final ErrorCode CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE = new ErrorCode(1001000003, "不能删除类型为系统内置的参数配置");
    public static final ErrorCode CONFIG_GET_VALUE_ERROR_IF_VISIBLE = new ErrorCode(1001000004, "获取参数配置失败，原因：不允许获取不可见配置");

    // ========== 定时任务 1001001000 ==========
    public static final ErrorCode JOB_NOT_EXISTS = new ErrorCode(1001001000, "定时任务不存在");
    public static final ErrorCode JOB_HANDLER_EXISTS = new ErrorCode(1001001001, "定时任务的处理器已经存在");
    public static final ErrorCode JOB_CHANGE_STATUS_INVALID = new ErrorCode(1001001002, "只允许修改为开启或者关闭状态");
    public static final ErrorCode JOB_CHANGE_STATUS_EQUALS = new ErrorCode(1001001003, "定时任务已经处于该状态，无需修改");
    public static final ErrorCode JOB_UPDATE_ONLY_NORMAL_STATUS = new ErrorCode(1001001004, "只有开启状态的任务，才可以修改");
    public static final ErrorCode JOB_CRON_EXPRESSION_VALID = new ErrorCode(1001001005, "CRON 表达式不正确");

    // ========== API 错误日志 1001002000 ==========
    public static final ErrorCode API_ERROR_LOG_NOT_FOUND = new ErrorCode(1001002000, "API 错误日志不存在");
    public static final ErrorCode API_ERROR_LOG_PROCESSED = new ErrorCode(1001002001, "API 错误日志已处理");

    // ========= 文件相关 1001003000=================
    public static final ErrorCode FILE_PATH_EXISTS = new ErrorCode(1001003000, "文件路径已存在");
    public static final ErrorCode FILE_NOT_EXISTS = new ErrorCode(1001003001, "文件不存在");
    public static final ErrorCode FILE_IS_EMPTY = new ErrorCode(1001003002, "文件为空");

    // ========== 代码生成器 1001004000 ==========
    public static final ErrorCode CODEGEN_TABLE_EXISTS = new ErrorCode(1003001000, "表定义已经存在");
    public static final ErrorCode CODEGEN_IMPORT_TABLE_NULL = new ErrorCode(1003001001, "导入的表不存在");
    public static final ErrorCode CODEGEN_IMPORT_COLUMNS_NULL = new ErrorCode(1003001002, "导入的字段不存在");
    public static final ErrorCode CODEGEN_TABLE_NOT_EXISTS = new ErrorCode(1003001004, "表定义不存在");
    public static final ErrorCode CODEGEN_COLUMN_NOT_EXISTS = new ErrorCode(1003001005, "字段义不存在");
    public static final ErrorCode CODEGEN_SYNC_COLUMNS_NULL = new ErrorCode(1003001006, "同步的字段不存在");
    public static final ErrorCode CODEGEN_SYNC_NONE_CHANGE = new ErrorCode(1003001007, "同步失败，不存在改变");
    public static final ErrorCode CODEGEN_TABLE_INFO_TABLE_COMMENT_IS_NULL = new ErrorCode(1003001008, "数据库的表注释未填写");
    public static final ErrorCode CODEGEN_TABLE_INFO_COLUMN_COMMENT_IS_NULL = new ErrorCode(1003001009, "数据库的表字段({})注释未填写");

    // ========== 字典类型（测试）1001005000 ==========
    public static final ErrorCode TEST_DEMO_NOT_EXISTS = new ErrorCode(1001005000, "测试示例不存在");

    // ========== 文件配置 1001006000 ==========
    public static final ErrorCode FILE_CONFIG_NOT_EXISTS = new ErrorCode(1001006000, "文件配置不存在");
    public static final ErrorCode FILE_CONFIG_DELETE_FAIL_MASTER = new ErrorCode(1001006001, "该文件配置不允许删除，原因：它是主配置，删除会导致无法上传文件");

    // ========== 数据源配置 1001007000 ==========
    public static final ErrorCode DATA_SOURCE_CONFIG_NOT_EXISTS = new ErrorCode(1001007000, "数据源配置不存在");
    public static final ErrorCode DATA_SOURCE_CONFIG_NOT_OK = new ErrorCode(1001007001, "数据源配置不正确，无法进行连接");

    // ========== ProductService标准接口-商品配置接口 101001 ==========

    public static final ErrorCode SERIES_CREATE_FAIL = new ErrorCode(105001, "车型年款配置创建失败");
    public static final ErrorCode SERIES_VALUE_EXIST_ERROR = new ErrorCode(105002, "该车型已存在，请重新输入");
    public static final ErrorCode SERIES_UPDATE_FAIL = new ErrorCode(105003, "车型年款配置修改失败");
    public static final ErrorCode SERIES_UPDATE_REVISION_ERROR = new ErrorCode(105004, "车型年款配置已被其他人修改");
    public static final ErrorCode SERIES_DELETE_FAIL = new ErrorCode(105006, "该车型年款配置刪除失败");
    public static final ErrorCode SERIES_MODEL_YEAR_EXISTS_ERROR = new ErrorCode(105007, "车型年款有重复");

    // ========== ProductService标准接口-服务包配置接口 105020 ==========
    public static final ErrorCode PACKAGE_CREATE_FAIL = new ErrorCode(105020, "服务包配置创建失败");
    public static final ErrorCode PACKAGE_CODE_ALREADY_EXIST = new ErrorCode(105021, "该服务包编号已存在，请重新输入");
    public static final ErrorCode PACKAGE_DELETE_FAIL = new ErrorCode(105022, "服务包配置删除失败");
    public static final ErrorCode PACKAGE_NOT_FOUND = new ErrorCode(105023, "服务包配置不存在");

    public static final ErrorCode FILE_SIZE_EXCEED_LIMIT = new ErrorCode(105030, "此文件大小超过2MB");
    public static final ErrorCode PACKAGE_BATCH_UPLOAD_FAIL = new ErrorCode(105031, "批量上传服务包编号失败");
    public static final ErrorCode SERVER_ERROR= new ErrorCode(105032, "无法获取模板文件的URL");


    public static final ErrorCode CART_ITEM_COUNT_ERROR = new ErrorCode(102000, "购物车商品数量异常");
    public static final ErrorCode CART_CREATE_FAIL = new ErrorCode(102001, "加入购物车失败");
    public static final ErrorCode CART_SKU_ERROR = new ErrorCode(102002, "该商品信息错误，请重新选择");
    public static final ErrorCode CART_UPDATE_FAIL = new ErrorCode(102003, "该商品信息错误，修改失败");
    public static final ErrorCode NOT_FOUND_SKU = new ErrorCode(102004, "未查询到商品信息");
    public static final ErrorCode CART_ITEM_EMPTY = new ErrorCode(102005, "购物车商品数量为空");
    public static final ErrorCode NOT_SUPPORT_COUPON = new ErrorCode(102006, "暂不支持的优惠方式");
    public static final ErrorCode NOT_SUPPORT_MULTI_COUPON = new ErrorCode(102007, "暂不支持的同时使用多张优惠券");
    public static final ErrorCode EXPIRED_COUPON = new ErrorCode(102008, "该优惠券不再有效期内");
    public static final ErrorCode NOT_FOUND_COUPON = new ErrorCode(102009, "所选优惠券不满足使用条件或不存在");
    public static final ErrorCode NOT_ENOUGH_MONEY = new ErrorCode(102010, "优惠券使用条件不满足");
    public static final ErrorCode NOT_CLEAR_COUPON_TYPE = new ErrorCode(102013, "优惠券类型不明确");
    public static final ErrorCode NOT_ENOUGH_POINTS = new ErrorCode(102011, "积分不足");
    public static final ErrorCode GET_POINTS_FAILED = new ErrorCode(1020115, "积分获取失败");
    public static final ErrorCode PRODUCT_NOT_SUPPORT_POINTS = new ErrorCode(102012, "存在商品不支持积分支付");
    public static final ErrorCode NOT_EXIST_PRODUCT_SUPPORT = new ErrorCode(102014, "不存在商品支持该优惠券");
    public static final ErrorCode CART_DELETE_EXISTS_ERROR = new ErrorCode(101016, "该销售单位正在使用中，不能删除");
    public static final ErrorCode CART_DELETE_FAIL = new ErrorCode(101017, "该商品刪除失败");
    public static final ErrorCode INTERNAL_EXCEPTION = new ErrorCode(101018, "内部系统异常: 获取锁失败");
    public static final ErrorCode CART_SKU_QUANTITY_LIMITED_ERROR = new ErrorCode(101019, "该商品不能购买更多，将为您调整至最大可购数量。");
    public static final ErrorCode QUERY_SKU_STOCK_FAILED = new ErrorCode(101020, "获取库存信息失败");
    public static final ErrorCode CART_SKU_QUANTITY_ERROR = new ErrorCode(101021, "商品加购件数(含已加购件数)已超过库存");

    public static final ErrorCode CART_SKU_QUANTITY_ZERO_ERROR = new ErrorCode(101022, "该商品已售罄");
    public static final ErrorCode GET_SKU_SNAPSHOT_FAILED = new ErrorCode(101023, "商品快照信息获取失败");
    public static final ErrorCode PRODUCT_FULFILMENT_ERROR = new ErrorCode(101024, "商品履约年份配置错误");

    // ========== OrderService标准接口-订单配置接口 102001 ==========
    public static final ErrorCode ORDER_CREATE_FAIL =  new ErrorCode(102030, "订单创建失败");
    public static final ErrorCode SHOP_CAR_ITEM_EMPTY = new ErrorCode(102031,"要购买的商品为空");

    public static final ErrorCode ORDER_UPDATE_FAIL = new ErrorCode(102032, "订单编辑失败");
    public static final ErrorCode ORDER_NOT_EXISTS = new ErrorCode(102033, "订单不存在");
    public static final ErrorCode ORDER_ITEM_NOT_FOUND = new ErrorCode(102133, "订单明细信息不存在");

    public static final ErrorCode ORDER_NOT_PAYABLE  = new ErrorCode(102034, "订单不可支付");

    public static final ErrorCode PAY_ERROR = new ErrorCode(102035, "支付异常，请重试");

    public static final ErrorCode ORDER_ITEM_REPEAT = new ErrorCode(102036,"要购买的商品类型重复");

    public static final ErrorCode QUANTITY_INVALID = new ErrorCode(102037,"商品数量必须为正整数");
    public static final ErrorCode BUSINESS_INVALID = new ErrorCode(102038,"当前商品需与现在选中的商品分开下单");
    public static final ErrorCode ORDER_INFO_LOST = new ErrorCode(102039, "订单信息缺失");
    public static final ErrorCode ORDER_NOT_SUPPORT_VCS = new ErrorCode(102040, "暂不支持查看该订单");
    public static final ErrorCode ORDER_PRODUCT_STOCK_QUANTITY_ERROR = new ErrorCode(102041, "商品库存不足");
    public static final ErrorCode ORDER_DEDUCT_ERROR = new ErrorCode(102042, "扣减库存失败");
    public static final ErrorCode STOCK_SYNC_ERROR = new ErrorCode(102043, "库存同步失败");

    public static final ErrorCode ORDER_CANCEL_ERROR = new ErrorCode(102080, "取消订单失败");
    public static final ErrorCode ORDER_CANNOT_MANUAL_COMPLETED = new ErrorCode(102081, "当前订单不符合手动完成订单的条件");

    // ========== OrderService标准接口-订单配置接口 102001 ==========
    public static final ErrorCode REFUND_APPLY_FAIL = new ErrorCode(102040, "取消订单提交失败");
    public static final ErrorCode REFUND_MONEY_EMPTY = new ErrorCode(102041,"退款金额不能为空");
    public static final ErrorCode REFUND_MONEY_OVER_TOTAL = new ErrorCode(102042, "退款金额不能大于总支付金额");
    public static final ErrorCode REFUND_ALL_ERROR = new ErrorCode(102043, "已经存在申请,整单取消申请失败");
    public static final ErrorCode REFUND_PART_ERROR = new ErrorCode(102044, "已经存在申请,部分取消申请失败");
    public static final ErrorCode REFUND_ORDER_STATUS_ERROR = new ErrorCode(102045, "当前订单状态不能取消订单");
    public static final ErrorCode REFUND_APPROVE_FAIL = new ErrorCode(102040, "取消订单审核失败");

    public static final ErrorCode REFUND_APPROVE_SEND_REFUND = new ErrorCode(102040, "调用退款支付异常");
    public static final ErrorCode REFUND_APPLY_FULFILMENT_ERROR = new ErrorCode(102046, "服务查询失败，请稍后再试");
    public static final ErrorCode REFUND_APPLY_FULFILMENT_FAIL = new ErrorCode(102047, "服务正在激活中，请稍后再试");
    public static final ErrorCode REFUND_MONEY_ZERO = new ErrorCode(102048,"退款金额不能为0");
    public static final ErrorCode REFUND_END_TIME_OUT_RANGE = new ErrorCode(102049, "请选择正确的实际服务截止日期");

    public static final ErrorCode ORDER_CANCEL_REQ_NULL = new ErrorCode(102050, "取消订单入参为空");

    public static final ErrorCode CANCEL_ORDER_CODE_NULL = new ErrorCode(102051, "取消订单号为空");

    public static final ErrorCode CANCEL_ORDER_INFO_NULL = new ErrorCode(102052, "取消订单,查询当前订单为空");

    public static final ErrorCode INVALID_ORDER = new ErrorCode(102053, "无效订单，该订单编码，没有下单记录");

    public static final ErrorCode CHANNEL_CODE_ERROR = new ErrorCode(102054, "品牌编码与渠道编码不匹配，请确认输入信息是否正确 or 渠道编码无效，仅接受MLR或MJA作为路虎小程序或捷豹小程序渠道编码" +
            ";代客下单时，品牌编码必须为LR、JA或JLR，且渠道编码必须为CS");

    public static final ErrorCode CANCEL_ORDER_STATUE_ERROR = new ErrorCode(102055, "手动取消订单失败，订单状态不是已下单");
    public static final ErrorCode CANCEL_ORDER_ERROR_CAN_RETRY = new ErrorCode(102056, "取消订单失败");

    public static final ErrorCode CANCEL_ORDER_PAY_ERROR = new ErrorCode(102056, "手动取消订单失败，当前订单正在支付中");
    public static final ErrorCode COMPANY_ADDRESS_NULL = new ErrorCode(102057, "企业注册地址不能为空");
    public static final ErrorCode COMPANY_MOBILE_NULL = new ErrorCode(102058, "企业电话不能为空");
    public static final ErrorCode COMPANY_BANK_NAME = new ErrorCode(102059, "开户行不能为空");
    public static final ErrorCode COMPANY_BANK_ACCOUNT = new ErrorCode(102060, "银行账号不能为空");
    public static final ErrorCode INVOICE_COMPANY_TAX_NUMBER_NULL = new ErrorCode(102061, "发票抬头税号不能为空");
    public static final ErrorCode RECIPIENT_NAME_NULL = new ErrorCode(102062, "接收人姓名不能为空");
    public static final ErrorCode RECIPIENT_PHONE_NULL = new ErrorCode(102063, "接收人电话不能为空");
    public static final ErrorCode RECIPIENT_ADDRESS_NULL = new ErrorCode(102064, "接收人详细地址不能为空");
    public static final ErrorCode RECIPIENT_PHONE_INVALID = new ErrorCode(102065, "手机号格式错误");
    public static final ErrorCode INVOICE_GET_EXCEPTION = new ErrorCode(1020666, "获取发票信息失败");
    public static final ErrorCode RECEIVER_INFO_NOT_FOUND = new ErrorCode(102081, "收货信息不存在");
    public static final ErrorCode ORDER_STATUS_EXCEPTION = new ErrorCode(102082, "订单状态异常");

    public static final ErrorCode REFUND_APPROVE_STATUS_ERROR = new ErrorCode(102083, "退款单状态不为带退款审核");

    public static final ErrorCode RETURN_APPROVE_STATUS_ERROR = new ErrorCode(102084, "退款单状态不为待退货审核");

    public static final ErrorCode VCS_ORDER_NOT_NEAREST = new ErrorCode(102033, "该VIN存在多个该服务的订单，请先取消最新订单");
    public static final ErrorCode REFUND_APPROVE_IS_COMPLETE = new ErrorCode(102066, "该订单已被审核");

    public static final ErrorCode CHECK_ICR_CONSUMER_ERROR = new ErrorCode(102067, "ICR和用户编码不合法");

    public static final ErrorCode PRODUCT_ITEM_INFO_EMPTY = new ErrorCode(102068, "商品信息不能为空");

    public static final ErrorCode REPEAT_SUBMISSION = new ErrorCode(102069, "重复提交");

    public static final ErrorCode GIFT_ADDRESS_INVALID = new ErrorCode(102070, "收货信息参数非法");

    // ========== OrderService标准接口-代客下单 ==========
    // 订单状态非已下单，无法提交重发短信请求
    public static final ErrorCode ORDER_STATUS_NOT_PLACED = new ErrorCode(102071, "订单状态非已下单，无法提交重发短信请求");
    // 订单来源非代客下单时，无法提交重发短信请求
    public static final ErrorCode ORDER_SOURCE_NOT_GUEST = new ErrorCode(102072, "订单来源非代客下单，无法提交重发短信请求");
    // 订单为聚合父订单，无法提交重发短信请求
    public static final ErrorCode ORDER_IS_AGGREGATE = new ErrorCode(102073, "订单为聚合父订单，无法提交重发短信请求");

    // 请求头需要传递jlrId
    public static final ErrorCode JLR_ID_NOT_EXIST = new ErrorCode(102074, "请求头需要传递jlrId");

    // 订单状态非已下单，无法进行回绑操作
    public static final ErrorCode ORDER_STATUS_NOT_PLACED_BIND = new ErrorCode(102075, "订单状态非已下单，无法进行回绑操作");

    // 订单为聚合父订单，无法进行回绑操作
    public static final ErrorCode ORDER_IS_AGGREGATE_BIND = new ErrorCode(102076, "订单为聚合父订单，无法进行回绑操作");

    // 订单来源非代客下单，无法进行回绑操作
    public static final ErrorCode ORDER_SOURCE_NOT_GUEST_BIND = new ErrorCode(102077, "订单来源非代客下单，无法进行回绑操作");

    // 该订单已绑定
    public static final ErrorCode ORDER_IS_BIND = new ErrorCode(102078, "该订单已绑定");

    // 在客户服务关系订单表中 不存在此订单
    public static final ErrorCode ORDER_NOT_EXISTS_IN_CUSTOMER_SERVICE_RELATION = new ErrorCode(102079, "在客户服务关系订单表中 不存在此订单");

    public static final ErrorCode CHECK_ORDER_AMOUNT_ERROR = new ErrorCode(102080, "订单金额校验失败");
    public static final ErrorCode CHECK_ORDER_CART_SKU_ERROR = new ErrorCode(102080, "购物车内有重复的商品，不能一起下单");

    public static final ErrorCode FEEDBACK_CREATE_FAIL = new ErrorCode(102091, "评价配置创建失败");
    public static final ErrorCode FEEDBACK_UPDATE_FAIL = new ErrorCode(102092, "评价配置修改失败");
    public static final ErrorCode FEEDBACK_DIMENSIONS_OVER_SIZE_ERROR = new ErrorCode(102093, "最多设置10个维度");
    public static final ErrorCode FEEDBACK_DELETE_FAIL = new ErrorCode(102094, "评价配置删除失败");
    public static final ErrorCode FEEDBACK_OPTIONS_OVER_SIZE_ERROR = new ErrorCode(102095, "最多设置10个选项");
    public static final ErrorCode FEEDBACK_OPTIONS_EXIST_ERROR = new ErrorCode(102096, "维度配置选项不能为空");
    public static final ErrorCode FEEDBACK_REVISION_ERROR = new ErrorCode(102097, "该评价配置已被其他人修改");
    public static final ErrorCode FEEDBACK_ENABLE_STATUS_ERROR = new ErrorCode(102098, "该评价配置是启用状态，请先停用");
    public static final ErrorCode FEEDBACK_ENABLE_EXISTS_ERROR = new ErrorCode(102099, "该评价配置已经启用过，无法删除");
    public static final ErrorCode FEEDBACK_ENABLE_ERROR = new ErrorCode(102100, "该评价配置启用失败");
    public static final ErrorCode FEEDBACK_DISABLE_ERROR = new ErrorCode(102101, "该评价配置停用失败");
    public static final ErrorCode FEEDBACK_ENABLE_CHECK_ERROR = new ErrorCode(102102, "已有该环节的评价模板在启用状态，请停用现有评价后，再启用该套评价");
    public static final ErrorCode FEEDBACK_RECORDS_SINGLE_CHECK_ERROR = new ErrorCode(102103, "单选结果校验错误");
    public static final ErrorCode FEEDBACK_RECORDS_MULTIPLE_CHECK_ERROR = new ErrorCode(102104, "多选结果校验错误");
    public static final ErrorCode FEEDBACK_SAME_ENABLE_TIME_ERROR = new ErrorCode(102105, "已有该环节评价问卷在相同启用时间，请重新设置启用时间");

    public static final ErrorCode INVOICE_CHECK_FAILED = new ErrorCode(102106, "发票校验失败,请先退回发票再进行退款!");

    public static final ErrorCode ORDER_ITEMS_CAN_NOT_EMPTY = new ErrorCode(102107, "订单行号不能为空!");

    public static final ErrorCode COUPON_CODE_NOT_EXISTS = new ErrorCode(102108, "找不到该退货申请下的有效电子卷!");

    public static final ErrorCode REMAIN_AMOUNT_NOT_ENOUGH = new ErrorCode(102109, "剩余退款金额不足");

    public static final ErrorCode OPERATION_TYPE_CAN_NOT_NULL = new ErrorCode(102110, "operationType不能为空");

    public static final ErrorCode ORDER_ITEM_STATUS_NOT_PENDING_VERIFY = new ErrorCode(102111, "订单行状态不为待核销");

    public static final ErrorCode ORDER_INDEPENDENT_PROCESSING = new ErrorCode(102112, "该订单的分账正在进行中");

    public static final ErrorCode ORDER_ITEM_STATUS_NOT_PENDING_VERIFY_OR_NOT_RECALL = new ErrorCode(102113, "订单行状态不为待核销或已回收");

    public static final ErrorCode OPERATION_SUBMIT_NOT_REFUND_ONLY = new ErrorCode(102114, "运营人员只能发起仅退款");

    public static final ErrorCode ORDER_STATUS_IS_PENDING_SHIPMENT = new ErrorCode(102115, "订单行状态为待发货");

    public static final ErrorCode ORDER_REFUND_TYPE_EMPTY = new ErrorCode(102116, "实物类型退单退款类型不能为空");

    public static final ErrorCode USER_CANCEL_STATUS_ERROR = new ErrorCode(102117, "用户取消退款时,退单状态不为待退货审核或待商品寄回");

    public static final ErrorCode ORDER_ITEM_REFUND_RPOCESSING = new ErrorCode(102118, "该订单行退款已在进行中");

    // ========== 手动续费校验 ==========
    public static final ErrorCode SUBSCRIPTION_SERVICE_ERROR = new ErrorCode(103001, "调用订阅服务异常");

    public static final ErrorCode ORDER_IN_TRANSIT_ERROR = new ErrorCode(103002, "已有在途订单，请稍后再试");


    // ========== 仪表盘 ==========
    public static final ErrorCode START_TIME_GREATER_THAN_END_TIME = new ErrorCode(104001, "开始日期不能大于结束日期");

    public static final ErrorCode TYPE_FILTER_ERROR = new ErrorCode(104002, "类型筛选错误");

    public static final ErrorCode ORDER_CHANNEL_ERROR = new ErrorCode(104003, "下单渠道参数错误");

    public static final ErrorCode VEHICLE_MODEL_ERROR = new ErrorCode(104004, "车型不能为空");

    public static final ErrorCode NO_ORDERS_AT_THIS_TIME = new ErrorCode(104005, "该时间段暂无订单");

    // ========== 订单退款 ==========
    public static final ErrorCode ORDER_REFUND_NOT_EXIST = new ErrorCode(103003, "退款单不存在");

    public static final ErrorCode ORDER_PAYMENT_NOT_EXIST = new ErrorCode(103004, "支付单不存在");

    public static final ErrorCode INVALID_REFUND_AMOUNT = new ErrorCode(103005, "运营人员发起的退款,退款金额不能为空或者小于0");

    public static final ErrorCode ORDER_REFUND_ITEM_NOT_EXIST = new ErrorCode(103006, "退款单订单行不存在");
    // ========== 分账 ==========
    public static final ErrorCode INDEPENDENT_FAIL = new ErrorCode(103101, "分账失败");
    public static final ErrorCode INDEPENDENT_CALLBACK_FAIL = new ErrorCode(103102, "分账结果回调失败");

    // ========== 确认收货 ==========
    public static final ErrorCode ORDER_ITEM_REFUNDING = new ErrorCode(103201, "订单存在售后中商品，请稍后再试");
    public static final ErrorCode ORDER_LOGISTICS_STATUS_EXCEPTION = new ErrorCode(103202, "订单不存在已发货商品, 不能发起确认收货");
}

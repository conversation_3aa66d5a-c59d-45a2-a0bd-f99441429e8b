package com.jlr.ecp.order.api.cart.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 购物车创建DTO
 * <AUTHOR>
 */
@Data
@Schema(description = " 购物车创建- DTO")
@ToString(callSuper = true)
@Validated
public class CarInfoDTO {




    /**
     * 车型编码
     */
    @Schema(description = "车型编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车型编码不能为空")
    private String seriesCode;

    /**
     * 车型
     */
    @Schema(description = "车型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车型不能为空")
    private String seriesName;

    /**
     * 车辆VIN码
     */
    @Schema(description = "车架号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车架号不能为空")
    private String carVin;


    /**
     * 购物车商品类型
     */
    @Schema(description = "购物车商品类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "购物车商品类型不能为空")
    private Integer cartItemType;


    /**
     * 商品SKU编码
     */
    @Schema(description = "商品SKU编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "商品SKU编码不能为空")
    private String productSkuCode;


    /**
     * 商品数量
     */
    @Schema(description = "商品数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品数量不能为空")
    private Integer quantity;

}

package com.jlr.ecp.order.api.order.dto;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Locale;

import com.jlr.ecp.order.enums.common.SortOrder;

/**
 * <AUTHOR>
 */
@Schema(description = "后台管理 - 电子券（LRE）订单列表请求参数")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ECouponOrderPageReqDTO extends PageParam {
    
    @Schema(description = "主订单号")
    private String parentOrderCode;

    @Schema(description = "子订单号")
    private String orderCode;

    @Schema(description = "订单状态")
    private String couponStatus;

    @Schema(description = "售后状态")
    private String afterSalesStatus;

    @Schema(description = "虚拟商品卡券(模版)编码")
    private String couponModelCode;

    @Schema(description = "微信手机号")
    private String wxPhone;

    @Schema(description = "订单手机号")
    private String contactPhone;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "通过创建时间（添加日期）排序方式（asc：升序，desc：降序）")
    private String createdTimeSort;

    @Schema(hidden = true)
    private List<Integer> couponStatusList;
    
    @Schema(hidden = true)
    private List<Integer> afterSalesStatusList;
    
    private static final String DATE_FORMAT_PATTERN = "yyyy/MM/dd HH:mm:ss";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN);

    /**
     * 准备查询参数
     * 在执行查询前调用此方法处理所有参数
     */
    public void prepareForQuery() {
        validateAndProcessDates();
        validateAndProcessSortingParams();
        // 可以添加其他参数处理逻辑
    }

    /**
     * 验证并处理日期参数
     */
    private void validateAndProcessDates() {
        this.startTime = parseAndFormatDate(startTime, "00:00:00");
        this.endTime = parseAndFormatDate(endTime, "23:59:59");
    }

    /**
     * 验证并处理排序参数
     */
    private void validateAndProcessSortingParams() {
        if (this.createdTimeSort != null) {
            String normalizedSort = this.createdTimeSort.toLowerCase(Locale.ROOT);
            
            boolean isValid = false;
            for (SortOrder sortOrder : SortOrder.values()) {
                if (sortOrder.getCode().equals(normalizedSort)) {
                    isValid = true;
                    break;
                }
            }
            
            if (isValid) {
                this.createdTimeSort = normalizedSort;
            } else {
                this.createdTimeSort = SortOrder.DESCENDING.getCode();
            }            
        }
    }
    
    /**
     * 解析并格式化日期字符串
     * @param dateStr 日期字符串
     * @param timeStr 时间字符串
     * @return 格式化后的日期时间字符串，如果输入无效则返回null
     */
    private String parseAndFormatDate(String dateStr, String timeStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateStr + " " + timeStr, DATE_FORMATTER);
            return dateTime.toString();
        } catch (DateTimeParseException e) {
            // 记录错误或考虑抛出业务异常
            // logger.error("Failed to parse date: " + dateStr, e);
            return null;
        }
    }
}
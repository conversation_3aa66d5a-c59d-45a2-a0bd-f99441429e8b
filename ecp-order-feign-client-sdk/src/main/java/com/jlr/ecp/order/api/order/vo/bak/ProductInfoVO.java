package com.jlr.ecp.order.api.order.vo.bak;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "商品信息")
public class ProductInfoVO {
    @Schema(description = "购买商品名称", required = true)
    private String displayProductName;

    @Schema(description = "购买数量", required = true)
    private Integer purchasedAmount;

    @Schema(description = "属性", required = true)
    private String attributeValue;

    @Schema(description = "标价", required = true)
    private Double listPrice;

    @Schema(description = "售价", required = false)
    private Double promotionalPrice;

}

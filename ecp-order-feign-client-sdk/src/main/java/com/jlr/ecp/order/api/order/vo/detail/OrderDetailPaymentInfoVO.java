package com.jlr.ecp.order.api.order.vo.detail;

import java.time.LocalDateTime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 支付信息 respVO")
@Data
public class OrderDetailPaymentInfoVO {
    
    @Schema(description = "优惠方式")
    private String paymentMethod;

    @Schema(description = "实付现金金额")
    private String costAmount;

    @Schema(description = "实付积分")
    private Integer pointAmount;

    @Schema(description = "运费")
    private String freightAmount;

    @Schema(description = "卡券类型")
    private String couponType;

    @Schema(description = "卡券名称")
    private String couponName;
    
    @Schema(description = "卡券券码")
    private String couponCode;

    @Schema(description = "付款时间")
    private String paymentTime;
}

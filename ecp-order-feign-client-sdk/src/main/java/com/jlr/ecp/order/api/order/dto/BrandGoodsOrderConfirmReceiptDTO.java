package com.jlr.ecp.order.api.order.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @createDate 07/04/2025 14:10
 **/
@Schema(description = "确认收货DTO")
@Data
public class BrandGoodsOrderConfirmReceiptDTO {

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderCode;
}

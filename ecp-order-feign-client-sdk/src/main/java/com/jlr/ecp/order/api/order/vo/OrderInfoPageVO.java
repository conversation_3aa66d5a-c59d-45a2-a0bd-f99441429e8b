package com.jlr.ecp.order.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - order info VO")
public class OrderInfoPageVO {

    @Schema(description = "订单金额")
    private String orderCode;

    @Schema(description = "inControl账号")
    private String inControlId;

    @Schema(description = "inControl账号加密信息")
    private String icrCT;

    @Schema(description = "VIN")
    private String carVin;

    @Schema(description = "VIN密文")
    private String carVinCT;


    @Schema(description = "联系电话")
    private String contactPhone;


    @Schema(description = "联系电话密文")
    private String contactPhoneCT;

    @Schema(description = "订单金额")
    private String costAmount;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "通过创建时间排序方式（asc：升序，desc：降序）")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime createdTime;

    @Schema(description = "订单日志状态")
    private Integer afterStatus;

    @Schema(description = "订单日志状态文本")
    private String statusText;

    @Schema(description = "创建人员")
    private String orderCreator;

    @Schema(description = "订单来源")
    private String orderSource;
}

package com.jlr.ecp.order.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端 - 未支付订单-微信用户关系 DTO")
public class OrderUnpaidRelaDTO {

        @Schema(description = "未支付的微信用户 consumerCode")
        private String consumerCode;

        @Schema(description = "未支付订单号")
        private String orderCode;

        @Schema(description = "订单状态")
        private Integer orderStatus;

        @Schema(description = "退款状态")
        private Integer refundStatus;
}

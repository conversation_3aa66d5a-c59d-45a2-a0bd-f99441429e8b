package com.jlr.ecp.order.api.order.vo.apporderdetail;

import com.jlr.ecp.order.api.order.vo.address.AppOrderGiftAddressDetailVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.OrderBrandOrderInfoVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.OrderBrandVehicleInfoVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.ProductBrandCategoriedItemInfoVO;
import com.jlr.ecp.order.api.order.vo.feedback.OrderFeedbackInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "小程序端 - 订单详情的 Resp VO")
@Data
public class OrderAppDetailPage {
    /**
     * 1.单个商品信息
     */
    @Schema(description = "单个商品信息")
    private ProductBrandCategoriedItemInfoVO productItemInfo;

    /**
     * 商品列表信息（订单行纬度）
     */
    private List<ProductBrandCategoriedItemInfoVO> orderItemList;

    /**
     * 2.车型信息
     */
    @Schema(description = "车型信息")
    private OrderBrandVehicleInfoVO vehicleInfo;

    /**
     * 3.订单信息
     */
    @Schema(description = "订单信息")
    private OrderBrandOrderInfoVO orderInfo;

    /**
     * 4.赠品信息
     */
    @Schema(description = "赠品信息")
    private AppOrderGiftAddressDetailVO giftInfo;

    /**
     * sprint47 订单-评价信息
     */
    @Schema(description = "订单-评价信息")
    private List<OrderFeedbackInfo> feedbackInfos;
}

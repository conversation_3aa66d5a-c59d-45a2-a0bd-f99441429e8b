package com.jlr.ecp.order.api.refund;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.refund.dto.OrderRefundForGyyPageReqDTO;
import com.jlr.ecp.order.api.refund.dto.OrderRefundForGyyPageRespDTO;
import com.jlr.ecp.order.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.security.PermitAll;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 订单管理 For 管易云")
public interface OrderRefundForGyyApi {

    String PREFIX = ApiConstants.PREFIX + "/refund";

    @PostMapping(PREFIX + "/page")
    @Operation(summary = "查询售后单For管易云")
    @PermitAll
    CommonResult<PageResult<OrderRefundForGyyPageRespDTO>> pageOrderRefund(@RequestBody OrderRefundForGyyPageReqDTO req);

}

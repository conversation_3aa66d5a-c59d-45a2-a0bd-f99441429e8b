package com.jlr.ecp.order.api.order.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * OrderInvoiceDTO
 *
 * <AUTHOR> <PERSON>
 * @since 2025-03-13 20:57:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderInvoiceDTO {

    /**
     * 用户编码;用户编码
     */
    private String consumerCode;

    /**
     * 订单编码;订单编码，编码规则：业务线code+时间戳(日期+时分秒)+6位随机数
     */
    private String orderCode;

    /**
     * 原始订单金额;原始订单金额，单位分
     */
    private Integer originalFeeTotalAmount;

    /**
     * 应付金额;订单金额，单位分
     */
    private Integer feeTotalAmount;

    /**
     * 实付金额;实付金额，单位分
     */
    private Integer costAmount;

    /**
     * 折扣金额;折扣金额，单位分
     */
    private Integer discountTotalAmount;

    /**
     * 运费金额;运费金额，虚拟商品为0
     */
    private Integer freightAmount;

    /**
     * 父订单号;父订单号，用于标记多个子订单从属于哪个订单，当只有一个订单时为本单号
     */
    private String parentOrderCode;

    /**
     * 订单状态;订单状态；1：已下单 2：已支付 3：订单完成 4：订单关闭  5：发起整单退款申请 6：发起部分退款申请  7：订单整单取消
     */
    private Integer orderStatus;

    /**
     * 支付状态;支付状态；0:未支付 1：已支付
     */
    private Integer paymentStatus;

    /**
     * 支付时间;支付时间
     */
    private LocalDateTime paymentTime;

    /**
     * 订单提交时间;订单提交时间
     */
    private LocalDateTime orderTime;

    /**
     * 0：聚合父订单
     * 订单类型;订单类型；1：VCS，2：Brand_goods，3：Charging
     */
    private Integer orderType;

    /**
     * 下单渠道;下单渠道 1：捷豹小程序 2：路虎小程序 3：官网
     */
    private Integer orderChannel;

    /**
     * 微信昵称
     */
    private String wxNickName;

    /**
     * 微信授权手机
     */
    private String wxPhone;

    /**
     * 客户留言信息;客户留言信息
     */
    private String customerRemark;

    /**
     * 客户联系手机;客户联系手机
     */
    private String contactPhone;

    /**
     * 操作员备注
     */
    private String operatorRemark;

    /**
     * 退款状态;退款状态  0：未退款 1：部分退款 2：全退款
     */
    private Integer refundStatus;

    /**
     * 订单关闭原因;订单退款状态
     */
    private String orderCloseReason;

    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 支付流水号
     */
    private String payApplyNo;

    /**
     * 业务线编号
     */
    private String businessCode;

    /**
     * 税收编码
     */
    private String freightCode;

    /**
     * 运费费率
     */
    private BigDecimal freightTax;

    /**
     * 运费费率更新时间
     */
    private LocalDateTime freightTaxUpdateTime;


}

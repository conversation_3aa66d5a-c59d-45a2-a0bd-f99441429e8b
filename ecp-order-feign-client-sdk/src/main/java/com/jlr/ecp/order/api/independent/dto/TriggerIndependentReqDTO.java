package com.jlr.ecp.order.api.independent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "RPC 服务 - 触发分账申请入参")
@Data
public class TriggerIndependentReqDTO {

    @Schema(description = "要触发分账的订单（重试或非重试都适用）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<String> orderCodes;

    @Schema(description = "BG订单完成时间（非重试适用）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime bgCompletedTimeLimit;

    @Schema(description = "LRE订单完成时间（非重试适用）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime lreCompletedTimeLimit;

    @Schema(description = "业务线编码（非重试适用）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String businessCode;

    @Schema(description = "用于控制是走到分账任务重试的逻辑里还是正常分账任务里", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean compensate = Boolean.FALSE;

}

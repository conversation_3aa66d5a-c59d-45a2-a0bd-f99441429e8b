package com.jlr.ecp.order.api.order.dto.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "consumerCode 回绑订单 请求参数")
public class OrderBindDTO {

    @Schema(description = "订单号", required = true)
    @NotBlank(message = "订单号不能为空")
    private String orderNumber;

    @Schema(description = "微信授权手机", required = true)
    @NotBlank(message = "微信授权手机不能为空")
    private String wxPhone;

    // 其他必要的字段可以根据实际需求添加
}
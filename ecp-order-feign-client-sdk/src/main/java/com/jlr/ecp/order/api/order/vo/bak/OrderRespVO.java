package com.jlr.ecp.order.api.order.vo.bak;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 订单详情的 Response VO")
@Data
public class OrderRespVO {
    /**
     * 订单状态
     */
    @Schema(description = "订单状态")
    private OrderStatusInfoVO orderStatusInfo;

    /**
     * 订单信息
     */
    @Schema(description = "订单信息")
    private OrderInfoVO orderInfo;

    /**
     * 客户信息
     */
    @Schema(description = "客户信息")
    private CustomerInfoVO customerInfo;

    /**
     * 车辆信息
     */
    @Schema(description = "车辆信息")
    private CarInfoVO carInfo;

    /**
     * 开票信息TBD
     */
    //TODO
    @Schema(description = "开票信息")
    private InvoiceInfoVO invoiceInfo;

    /**
     * 商品信息
     */
    @Schema(description = "商品信息")
    private List<ProductInfoVO> productInfoVOList;
}

package com.jlr.ecp.order.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端 - 订单创建-联系人信息 DTO")
public class ContactInfoDTO {

    /**
     * 手机号
     */
    @Schema(description = "手机号" +
            "待客下单时 为ICR注册手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contactPhone;

    /**
     * 客户留言
     */
    @Schema(description = "客户留言")
    @Length(max = 100,message = "客户留言长度不能超过100个字符")
    private String operatorRemark;

    /**
     * BG客户留言
     */
    @Schema(description = "BG客户留言")
    @Length(max = 100,message = "客户留言长度不能超过100个字符")
    private String operatorBgRemark;

    /**
     * 微信昵称
     */
    @Schema(description = "微信昵称")
//    @Length(max = 50,message = "微信昵称长度不能超过50个字符")
    private String wxNickName;

    /**
     * 微信授权手机
     */
    @Schema(description = "微信授权手机")
//    @Length(max = 20,message = "微信授权手机长度不能超过20个字符")
    private String wxPhone;
}

package com.jlr.ecp.order.api.order.vo.coupon;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "管理后台 - 电子券订单详情VO")
public class ECouponOrderDetailVO {
    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderCode;

    /**
     * 订单明细编号
     */
    @Schema(description = "订单明细编号")
    private String orderItemCode;

    /**
     * 卡券模板code
     */
    @Schema(description = "卡券模板code")
    private String couponModelCode;

    /**
     * 卡券code
     */
    @Schema(description = "卡券code")
    private String couponCode;

    /**
     * 卡券状态: 1-入库; 2-未生效; 3-待使用; 4-已作废; 5-已核销; 6-已过期
     */
    @Schema(description = "卡券状态: 1-入库; 2-未生效; 3-待使用; 4-已作废; 5-已核销; 6-已过期")
    private Integer status;

    /**
     * 有效期开始时间
     */
    @Schema(description = "有效期开始时间")
    private String validStartTime;

    /**
     * 有效期结束时间
     */
    @Schema(description = "有效期结束时间")
    private String validEndTime;

    /**
     * 核销时间
     */
    @Schema(description = "核销时间")
    private String usedTime;

    /**
     * 发券时间
     */
    @Schema(description = "发券时间")
    private String sendTime;
}

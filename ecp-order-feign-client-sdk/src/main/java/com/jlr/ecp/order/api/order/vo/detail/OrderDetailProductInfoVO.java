package com.jlr.ecp.order.api.order.vo.detail;

import com.jlr.ecp.order.api.order.vo.ECouponOrderInfoPageVO;
import com.jlr.ecp.order.api.order.vo.ECouponOrderItemVO;
import com.jlr.ecp.order.api.order.vo.BrandGoodsOrderItemVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.ProductBrandCategoriedItemInfoVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - OrderDetailProductInfoVO respVO")
public class OrderDetailProductInfoVO {

    /**
     * 商品item list
     */
    @Schema(description = "商品item list")
    List<ProductBrandCategoriedItemInfoVO> productItemInfoList;

    /**
     * LRE 商品item list
     */
    @Schema(description = "LRE 商品item list")
    List<ECouponOrderItemVO> eCouponProductItemInfoList;

    /**
     * BG 商品item list
     */
    @Schema(description = "BG 商品item list")
    List<BrandGoodsOrderItemVO> brandGoodsProductItemInfoList;
}

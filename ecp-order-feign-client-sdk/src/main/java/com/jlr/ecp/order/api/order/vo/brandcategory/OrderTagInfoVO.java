package com.jlr.ecp.order.api.order.vo.brandcategory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "订单标签信息，标识订单是否属于‘退款/售后’；‘待评价’ ")
public class OrderTagInfoVO {
    /**
     * 是否属于退款/售后
     * <p>如果订单有相关的逆向流程，则该值为true。</p>
     */
    @Schema(description = "是否属于退款/售后 - 如果订单有相关的逆向流程，则该值为true", example = "true")
    private Boolean isRefundOrAfterSale;

    /**
     * 是否属于待评价
     * <p>订单状态为 已支付、订单完成、订单整单取消 且 处于当前环节该订单无评价，则该值为true。</p>
     */
    @Schema(description = "是否属于待评价 - 订单状态为 已支付、订单完成、订单整单取消 且 处于当前环节该订单无评价，则该值为true", example = "false")
    private Boolean isPendingEvaluation;

    // 可以根据需要添加更多属性，例如：
    /*
    @Schema(description = "额外的标签信息或其他状态标识")
    private String additionalTag;
    */
}
package com.jlr.ecp.order.api.refund.dto;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "订单管理 - 订单列表")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderRefundPageReqDTO extends PageParam {

    @Schema(description = "退单订单号")
    private String refundOrderCode;

    @Schema(description = "原始订单号")
    private String originOrderCode;

    @Schema(description = "inControl账号")
    private String inControlId;

    @Schema(description = "VIN码")
    private String vin;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "订单状态")
    private String refundOrderStatus;

    @Schema(description = "通过创建时间（添加日期）排序方式（asc：升序，desc：降序）")
    private String createdTimeSort;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "订单类型列表, 0: 聚合父订单, 1：远程车控, 2：实物商品, 3：充电产品")
    private Integer orderType;

    private List<Integer> statusList;

    @Schema(description = "订单来源")
    private Integer orderSource;

    @Schema(description = "创建人员")
    private String orderCreator;

    private List<Integer> orderChannelList;

    @Schema(description = "Business Code")
    private String businessCode;

    public void validateParameters() {
        if (startTime !=null && !startTime.isEmpty()){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
            LocalDateTime dateTime = null;
            try {
                 dateTime = LocalDateTime.parse(startTime + " 00:00:00", formatter);
                // 日期字符串验证和清理成功，可以将dateTime对象用于后续操作
            } catch (DateTimeParseException e) {
                // 日期字符串验证和清理失败，处理异常
            }
            assert dateTime != null;
            this.startTime = dateTime.toString();
        }

        if (endTime !=null && !endTime.isEmpty()){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
            LocalDateTime dateTime = null;
            try {
                dateTime = LocalDateTime.parse(endTime + " 23:59:59", formatter);
                // 日期字符串验证和清理成功，可以将dateTime对象用于后续操作
            } catch (DateTimeParseException e) {
                // 日期字符串验证和清理失败，处理异常
            }
            assert dateTime != null;
            this.endTime = dateTime.toString();
        }
    }
}

package com.jlr.ecp.order.api.order.vo.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "从t_order_item获取与商品有关的 订单item基础信息，order_item_code唯一")
public class OrderItemBaseVO {
    /**
     * 订单item编码
     */
    @Schema(description = "订单item编码")
    private String orderItemCode;


    /**
     * 商品快照编码
     */
    @Schema(description = "商品快照编码")
    private String productVersionCode;

    /**
     * 商品编码
     */
    @Schema(description = "商品编码")
    private String productCode;

    /**
     * 商品SKU编码
     */
    @Schema(description = "商品SKU编码")
    private String productSkuCode;

    /**
     * 商品名称
     */
    @Schema(description = "商品名称")
    private String productName;

    /**
     * 商品主图URL
     */
    @Schema(description = "商品主图URL")
    private String productImageUrl;

    /**
     * stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI
     */
    @Schema(description = "stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI")
    private String productAttribute;

    /**
     * 标价
     */
    @Schema(description = "标价")
    private String productMarketPrice;

    /**
     * 售价
     */
    @Schema(description = "售价")
    private String productSalePrice;

    /**
     * 购买数量
     */
    @Schema(description = "购买数量")
    private Integer productQuantity;
}

package com.jlr.ecp.order.api.order.dto;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.codec.digest.DigestUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Locale;
import java.util.Arrays;
import java.util.Collections;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import org.apache.commons.codec.digest.DigestUtils;

import com.jlr.ecp.order.enums.common.SortOrder;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@Schema(description = "后台管理 - 电子券（BG）订单列表请求参数")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrandGoodsOrderPageReqDTO extends PageParam {
    
    @Schema(description = "主订单号")
    private String parentOrderCode;

    @Schema(description = "子订单号")
    private String orderCode;

    @Schema(description = "订单状态")
    private String logisticsOrderStatus;

    @Schema(description = "售后状态")
    private String afterSalesStatus;

    @Schema(description = "物流状态")
    private String deliveryStatus;

    @Schema(description = "商品SKU编码")
    private String productSkuCode;

    @Schema(description = "订单商品SKU ID - 金蝶sku编码")
    private String kingdeeSkuCode;

    @Schema(description = "微信手机号")
    private String wxPhone;

    @Schema(description = "订单手机号")
    private String contactPhone;

    @Schema(description = "买家手机号")
    private String recipientPhone;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "开始更新时间")
    private String startUpdateTime;

    @Schema(description = "结束更新时间")
    private String endUpdateTime;

    @Schema(description = "通过创建时间（添加日期）排序方式（asc：升序，desc：降序）")
    private String createdTimeSort;

    @Schema(description = "通过修改时间（添加日期）排序方式（asc：升序，desc：降序）")
    private String updateTimeSort;

    @Schema(hidden = true)
    private List<Integer> logisticsOrderStatusList;

    // 售后状态
    @Schema(hidden = true)
    private List<Integer> afterSalesStatusList;

    @Schema(hidden = true)
    private List<Integer> deliveryStatusList;
    /**
     * 退货原因
     */
    @Schema(hidden = true)
    private boolean closedWithNoPay;

    /**
     * 是否需要查询售后处理中的订单
     */
    @Schema(hidden = true)
    private Boolean needQueryAfterSales;

    /**
     * 售后处理中状态码
     */
    private static final int AFTER_SALE_PROCESSING_STATUS = 90501;
    
    private static final String DATE_FORMAT_PATTERN = "yyyy/MM/dd HH:mm:ss";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN);

    /**
     * 准备查询参数
     * 在执行查询前调用此方法处理所有参数
     */
    public void prepareForQuery() {
        validateAndProcessDates();
        validateAndProcessSortingParams();
        
        parseCommaSeparatedIntegers(logisticsOrderStatus, this::setLogisticsOrderStatusList);
        parseCommaSeparatedIntegers(afterSalesStatus, this::setAfterSalesStatusList);
        parseCommaSeparatedIntegers(deliveryStatus, this::setDeliveryStatusList);
        applyMd5IfNotBlank(wxPhone, this::setWxPhone);
        applyMd5IfNotBlank(contactPhone, this::setContactPhone);
        applyMd5IfNotBlank(recipientPhone, this::setRecipientPhone);
        
        // 初始化 needQueryAfterSales 为 false
        this.needQueryAfterSales = false;
        
        // 检查是否需要查询售后处理中的订单
        if (logisticsOrderStatusList != null && logisticsOrderStatusList.contains(AFTER_SALE_PROCESSING_STATUS)) {
            this.needQueryAfterSales = true;
        }
    }

    /**
     * 验证并处理日期参数
     */
    private void validateAndProcessDates() {
        this.startTime = parseAndFormatDate(startTime, "00:00:00");
        this.endTime = parseAndFormatDate(endTime, "23:59:59");
        this.startUpdateTime = parseAndFormatDate(startUpdateTime, "00:00:00");
        this.endUpdateTime = parseAndFormatDate(endUpdateTime, "23:59:59");
    }

    /**
     * 验证并处理排序参数
     */
    private void validateAndProcessSortingParams() {
        if (this.createdTimeSort != null) {
            String normalizedSort = this.createdTimeSort.toLowerCase(Locale.ROOT);
            
            boolean isValid = false;
            for (SortOrder sortOrder : SortOrder.values()) {
                if (sortOrder.getCode().equals(normalizedSort)) {
                    isValid = true;
                    break;
                }
            }
            
            if (isValid) {
                this.createdTimeSort = normalizedSort;
            } else {
                this.createdTimeSort = SortOrder.DESCENDING.getCode();
            }            
        }
    }
    
    /**
     * 解析并格式化日期字符串
     * @param dateStr 日期字符串
     * @param timeStr 时间字符串
     * @return 格式化后的日期时间字符串，如果输入无效则返回null
     */
    private String parseAndFormatDate(String dateStr, String timeStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateStr + " " + timeStr, DATE_FORMATTER);
            return dateTime.toString();
        } catch (DateTimeParseException e) {
            // 记录错误或考虑抛出业务异常
            // logger.error("Failed to parse date: " + dateStr, e);
            return null;
        }
    }

    private void parseCommaSeparatedIntegers(String commaSeparatedStr, Consumer<List<Integer>> listSetter) {
        if (commaSeparatedStr != null && !commaSeparatedStr.isEmpty()) {
            try {
                List<Integer> intList = Arrays.stream(commaSeparatedStr.split(","))
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                listSetter.accept(intList);
            } catch (NumberFormatException e) {
                listSetter.accept(Collections.emptyList());
            }
        }
    }

    private void applyMd5IfNotBlank(String value, Consumer<String> setter) {
        if (value != null && !value.isEmpty()) {
            setter.accept(DigestUtils.md5Hex(value));
        }
    }
}
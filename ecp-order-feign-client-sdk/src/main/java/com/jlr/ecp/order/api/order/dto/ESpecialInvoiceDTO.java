package com.jlr.ecp.order.api.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Schema(description = "管理后台 - 订单编辑-电子专票 DTO")
@Data
public class ESpecialInvoiceDTO {

    @Schema(description = "开票状态,1:开票中 2:已开电子专票 3:已红冲电子专票", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "开票状态不能为空")
    private Integer invoiceStatus;

    @Schema(description = "用户编码")
    @NotBlank(message = "用户编码不能为空")
    private String consumerCode;

    @Schema(description = "发票抬头名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceTitleName;

    @Schema(description = "抬头税号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String titleTaxNo;

    @Schema(description = "企业电话", requiredMode = Schema.RequiredMode.REQUIRED)
    private String companyMobile;

    @Schema(description = "企业注册地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String companyAddress;

    @Schema(description = "开户银行", requiredMode = Schema.RequiredMode.REQUIRED)
    private String companyBankName;

    @Schema(description = "开户行账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String companyBankAccount;

    @Schema(description = "接收人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String recipientName;

    @Schema(description = "接收人电话", requiredMode = Schema.RequiredMode.REQUIRED)
    private String recipientPhone;

    @Schema(description = "接收人详细地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String recipientAddress;
}

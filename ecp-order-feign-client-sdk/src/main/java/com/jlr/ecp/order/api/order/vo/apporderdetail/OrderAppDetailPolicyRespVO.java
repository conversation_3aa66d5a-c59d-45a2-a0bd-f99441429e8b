package com.jlr.ecp.order.api.order.vo.apporderdetail;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "小程序端订单详情-购买条款与条件 Resp VO")
@Data
public class OrderAppDetailPolicyRespVO {
    @Schema(description = "商品服务条款code;条款ID")
    private String policyCode;

    @Schema(description = "信息名称")
    private String policyName;

    @Schema(description = "信息内容（富文本）")
    private String policyContent;
}

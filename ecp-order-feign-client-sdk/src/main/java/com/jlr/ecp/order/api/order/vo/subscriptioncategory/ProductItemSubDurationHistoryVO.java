package com.jlr.ecp.order.api.order.vo.subscriptioncategory;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "小程序端-我的订阅 商品item下 商品历史订阅时间段")
public class ProductItemSubDurationHistoryVO {
    @Schema(description = "历史订阅开始时间")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    LocalDateTime subHistoryStartTime;

    @Schema(description = "历史订阅结束时间")
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    LocalDateTime subHistoryEndTime;


}

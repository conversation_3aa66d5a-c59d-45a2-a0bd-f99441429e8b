package com.jlr.ecp.order.api.refund.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "订单管理 - 电子券退单申请DTO")
@Data
@ToString(callSuper = true)
@Validated
public class BaseOrderRefundApplyDTO {


    @Schema(description = "原始订单号")
    @NotBlank(message = "订单号不能为空")
    private String originOrderCode;

    @Schema(description = "原始订单行号")
    @NotBlank(message = "原始订单行号不能为空")
    private String orderItemCode;

    @Schema(description = "附件")
    private List<String> attachmentUrls;

    @Schema(description = "手动退款金额,由运营人员手动录入.没有则不传")
    private String refundMoneyAmount;

    //退款原因 RefundReasonEnum
    @Schema(description = "退款原因")
    @NotBlank(message = "退款原因不能为空")
    private Integer refundReason;

    //售后类型 RefundOrderTypeEnum
    @Schema(description = "售后类型 1-退货退款 2-仅退款")
    private Integer refundOrderType;
    /**
     * 备注
     */
    @Schema(description = "运营人员描述")
    @Length(max = 100, message = "描述不能超过100个字符")
    private String remark;


    /**
     * 补充描述;补充描述
     */
    @Schema(description = "补充描述")
    @Length(max = 100, message = "描述不能超过100个字符")
    private String supDesc;

    /**
     * 用户JLR ID 小程序端需要传 其他端不传
     */
    @Schema(description = "用户JLR ID 小程序端需要传 其他端不传")
    private String jlrId;

    /**
     *
     * 退单履约类型;
     * 1：远程车控REMOTE SERVICE； 2：PIVI Subscription   3: 实物商品；4：组合商品 5：优惠券商品
     */
    @Schema(description = "履约方式")
    private Integer refundFulfilmentType;


}

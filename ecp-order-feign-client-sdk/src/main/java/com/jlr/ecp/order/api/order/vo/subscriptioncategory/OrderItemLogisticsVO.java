package com.jlr.ecp.order.api.order.vo.subscriptioncategory;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.api.order.vo.subscriptioncategory
 * @className: OrderItemLogisticsVO
 * @author: gaoqig
 * @description: 订单物流信息VO
 * @date: 2025/4/2 16:51
 * @version: 1.0
 */
@Data
public class OrderItemLogisticsVO {
    /**
     * 订单编码
     */
    @Schema(description = "订单编码")
    private String orderCode;

    /**
     * 订单明细编码
     */
    @Schema(description = "订单明细编码")
    private String orderItemCode;

    /**
     * 快递状态：1-待发货，2-已发货，3-已签收，4-确认收货
     */
    @Schema(description = "快递状态：1-待发货，2-已发货，3-已签收，4-确认收货")
    private Integer logisticsStatus;

    /**
     * 物流公司名称;物流公司
     */
    @Schema(description = "物流公司名称;物流公司")
    private String logisticsCompany;

    /**
     * 物流单号;物流单号
     */
    @Schema(description = "物流单号;物流单号")
    private String logisticsNo;

    /**
     * 收件人;收件人
     */
    @Schema(description = "收件人;收件人")
    private String recipient;

    /**
     * 收件人联系手机;
     */
    @Schema(description = "收件人联系手机")
    private String recipientPhone;

    /**
     * 收件省;收件省
     */
    @Schema(description = "收件省;收件省")
    private String provinceCode;

    /**
     * 收件市;收件市
     */
    @Schema(description = "收件市;收件市")
    private String cityCode;

    /**
     * 收件区;收件区
     */
    @Schema(description = "收件区;收件区")
    private String areaCode;

    /**
     * 收件详细地址;收件详细地址
     */
    @Schema(description = "收件详细地址;收件详细地址")
    private String detailAddress;

    /**
     * 邮编地址;邮编地址
     */
    @Schema(description = "邮编地址;邮编地址")
    private String postCode;
    /**
     * 发货时间
     */
    @Schema(description = "发货时间")
    private LocalDateTime deliveryTime;
}

package com.jlr.ecp.order.enums.refund;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 *
 * 退单订单状态：
 * 1：发起整单退款申请
 * 2：发起部分退款申请
 * 3：同意整单退款申请
 * 4：同意部分退款申请
 * 5：订单整单退款完成
 * 6：订单部分退款完成
 * 7：拒绝整单退单申请
 * 8：拒绝部分退款申请
 */
@AllArgsConstructor
@Getter
public enum OrderRefundStatusEnum {

    /****
     * 退单订单状态：
     * 1：发起整单退款申请
     * 2：发起部分退款申请
     * 3：同意整单退款申请
     * 4：同意部分退款申请
     * 5：订单整单退款完成
     * 6：订单部分退款完成
     * 7：拒绝整单退单申请
     * 8：拒绝部分退款申请
     */
    FULL_REFUND_APPLY(1, "发起整单退款申请"),
    PARTIAL_REFUND_APPLY(2, "发起部分退款申请"),
    FULL_REFUND_APPROVE(3, "同意整单退款申请"),
    PARTIAL_REFUND_APPROVE(4, "同意部分退款申请"),
    FULL_REFUND_COMPLETED(5, "订单整单退款完成"),
    PARTIAL_REFUND_COMPLETED(6, "订单部分退款完成"),
    FULL_REFUND_REFUSE(7, "拒绝整单退单申请"),
    PARTIAL_REFUND_REFUSE(8, "拒绝部分退款申请");
    
    /**
     * code
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;

    public static OrderRefundStatusEnum getEnumByCode(Integer code) {
        for (OrderRefundStatusEnum status : OrderRefundStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
       return null;
    }

    public static String getDescriptionCode(Integer code) {
        for (OrderRefundStatusEnum status : OrderRefundStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        return null;
    }

    /**
     * 判断支付单完态
     */
    public static boolean isApproved(Integer code){
        List<Integer> enumList = Arrays.asList(
                OrderRefundStatusEnum.FULL_REFUND_APPROVE.getCode(),
                OrderRefundStatusEnum.PARTIAL_REFUND_APPROVE.getCode(),
                OrderRefundStatusEnum.FULL_REFUND_REFUSE.getCode(),
                OrderRefundStatusEnum.PARTIAL_REFUND_REFUSE.getCode()
        );
        return enumList.contains(code);
    }

    /**
     * 判断是否在售后中
     */
    public static boolean isRefunding(Integer code){
        List<Integer> enumList = Arrays.asList(
                OrderRefundStatusEnum.FULL_REFUND_APPLY.getCode(),
                OrderRefundStatusEnum.PARTIAL_REFUND_APPLY.getCode(),
                OrderRefundStatusEnum.FULL_REFUND_APPROVE.getCode(),
                OrderRefundStatusEnum.PARTIAL_REFUND_APPROVE.getCode()
        );
        return enumList.contains(code);
    }

    /**
     * 判断是否售后完成
     */
    public static boolean isRefunded(Integer code){
        List<Integer> enumList = Arrays.asList(
                OrderRefundStatusEnum.FULL_REFUND_COMPLETED.getCode()
        );
        return enumList.contains(code);
    }

}

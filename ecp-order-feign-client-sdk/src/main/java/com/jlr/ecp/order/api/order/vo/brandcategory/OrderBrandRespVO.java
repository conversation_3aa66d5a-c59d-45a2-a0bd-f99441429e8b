package com.jlr.ecp.order.api.order.vo.brandcategory;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "小程序端 - 不同品牌下 订单列表的 Response VO")
@Data
public class OrderBrandRespVO {
    /**
     * 1.商品信息
     */
    @Schema(description = "商品信息")
    private OrderBrandProductInfoVO productInfo;

    /**
     * 2.车型信息
     */
    @Schema(description = "车型信息")
    private OrderBrandVehicleInfoVO vehicleInfo;

    /**
     * 3.订单信息
     */
    @Schema(description = "订单信息")
    private OrderBrandOrderInfoVO orderInfo;


    /**
     * 4.订单号
     */
    @Schema(description = "订单号")
    private String orderCode;

    /**
     * 下单时间=订单提交时间
     */
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    @Schema(description = "下单时间=订单提交时间")
    private LocalDateTime orderTime;


}

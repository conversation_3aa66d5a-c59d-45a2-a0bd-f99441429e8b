package com.jlr.ecp.order.api.order.vo.fegincall;

import com.jlr.ecp.order.api.order.vo.base.OrderItemBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "远程调用商品服务，根据List<order_item_code> 查询商品信息，组装成Map<String carVin,orderItemDO对象>返回\n")
public class OrderItemDOListRespVO {

    List<OrderItemBaseVO> OrderItemBaseVOList;
}

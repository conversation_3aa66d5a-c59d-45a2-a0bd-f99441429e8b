package com.jlr.ecp.order.api.refund.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * OrderRefundItemDto
 *
 * <AUTHOR> <PERSON>
 * @since 2025-03-24 11:18:32
 */
@Data
public class OrderRefundItemDto {

    /**
     * 退单订单号;退单订单号
     */
    private String refundOrderCode;

    /**
     * 订单item号;订单item号
     */
    private String orderItemCode;

    /**
     * 订单item退款号;订单item退款号
     * 用于取消卡券的时候传入进行安全验证
     * 生成规则为R+order_item_code
     */
    private String orderRefundItemCode;

    /**
     *  指定服务过期时间
     * */
    private LocalDateTime serviceEndDate;

    /**
     * 退单实付金额
     */
    private Integer refundMoney;

    /**
     * 退单实付积分
     */
    private Integer refundPoint;

    /**
     * 退单数量
     */
    private Integer refundQuantity;

}

package com.jlr.ecp.order.api.order.vo.address;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class AppOrderGiftAddressDetailVO implements Serializable {

    @Schema(description = "是否有赠品")
    private Boolean needGift;

    @Schema(description = "中国行政区划代码 （Administrative Division Code）" +
            "eg.adCode: \"230110\"" +
            "23：省级代码（黑龙江省）" +
            "01：地市级代码（哈尔滨市）" +
            "10：区县级代码（香坊区）")
    private String adCode;

    @Schema(description = "省")
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市")
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区")
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "详细地址")
    private String detailAddress;

    @Schema(description = "收件人")
    private String recipient;

    @Schema(description = "收件人电话")
    private String recipientPhone;
}
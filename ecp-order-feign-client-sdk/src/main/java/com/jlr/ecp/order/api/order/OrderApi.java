package com.jlr.ecp.order.api.order;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.order.dto.OrderLogisticStatusChangeReqDto;
import com.jlr.ecp.order.api.order.dto.vcs.VcsOrderDecodeVinDTO;
import com.jlr.ecp.order.api.order.vo.BrandGoodsOrderInfoPageVO;
import com.jlr.ecp.order.api.order.vo.bak.OrderRespVO;
import com.jlr.ecp.order.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.security.PermitAll;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 订单管理")
public interface OrderApi {
    String PREFIX = ApiConstants.PREFIX + "/order";

    /**
     * 订单viewAPI接口
     *
     * @param orderCode
     * @return
     */
    @GetMapping(PREFIX + "/view")
    @Operation(summary = "订单详情")
    CommonResult<OrderRespVO> viewOrderInfo(@RequestParam(value = "orderCode") String orderCode);

    /**
     * 订单分页查询
     */
    @GetMapping("/orders/page")
    @Operation(summary = "订单列表分页查询接口")
    @Parameters({
            @Parameter(name = "orderNumber", description = "订单号，根据订单编号逻辑进行验证"),
            @Parameter(name = "logisticsOrderStatusList", description = "订单状态，可以选择一个或多个状态进行过滤"),
            @Parameter(name = "updateStartTime", description = "修改的起始时间，格式为yyyy/MM/dd HH:mm:ss"),
            @Parameter(name = "updateEndTime", description = "修改的结束时间，格式为yyyy/MM/dd HH:mm:ss"),
            @Parameter(name = "updateTimeSort", description = "通过创建时间排序方式（asc：升序，desc：降序）", example = "desc"),
            @Parameter(name = "pageNo", description = "页码", required = true, example = "1"),
            @Parameter(name = "pageSize", description = "页大小", required = true, example = "10")
    })
    @PermitAll
    CommonResult<PageResult<BrandGoodsOrderInfoPageVO>> pageOrders(
            @RequestParam(value = "orderNumber", required = false) String orderNumber,
            @RequestParam(value = "logisticsOrderStatusList", required = false) List<Integer> logisticsOrderStatusList,
            @RequestParam(value = "updateStartTime", required = false) @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(value = "updateEndTime", required = false) @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(value = "updateTimeSort", required = false) String updateTimeSort,
            @RequestParam(value = "closedWithNoPay", required = false, defaultValue = "false") boolean closedWithNoPay,
            @RequestParam(name = "pageNo") Integer pageNo,
            @RequestParam(name = "pageSize") Integer pageSize
    );

    /***
     * <AUTHOR>
     * @description 修改订单状态
     * @date 2025/4/3 11:15
     * @param reqDto: 订单状态修改请求对象
     * @return: com.jlr.ecp.framework.common.pojo.CommonResult<java.lang.Boolean>
    */

    @PostMapping(PREFIX + "/updateOrderLogisticStatus")
    @Operation(summary = "修改订单物流发货状态")
    CommonResult<Boolean> updateOrderStatusOnLogisticChange(@RequestBody OrderLogisticStatusChangeReqDto reqDto);

    /***
     * <AUTHOR>
     * @description 订单物流签收更新JOB
     * @date 2025/4/8 11:15
     */
    @PostMapping(PREFIX + "/updateOrderLogisticSign")
    @Operation(summary = "订单物流签收更新JOB")
    @PermitAll
    Integer updateOrderLogisticSign();

    @PostMapping(PREFIX + "/syncGuanyiOrder")
    @Operation(summary = "同步管易云状态更新")
    @PermitAll
    CommonResult<String> syncGuanyiOrder(@RequestBody List<String> orderCodeList);

    @PostMapping(PREFIX + "/getAllVcsOrderInfoId")
    @Operation(summary = "获取全部的VcsOrderInfo的id")
    CommonResult<List<Long>> getAllVcsOrderInfoId(@RequestBody VcsOrderDecodeVinDTO decodeVinDTO);

    @PostMapping(PREFIX + "/decodeVinByVcsOrderInfo")
    @Operation(summary = "解密VcsOrderInfo的vin")
    CommonResult<Boolean> decodeVinByVcsOrderInfo(@RequestBody List<Long> vcsOrderIdList);
}

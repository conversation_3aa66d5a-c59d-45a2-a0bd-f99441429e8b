package com.jlr.ecp.order.api.order.dto.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description 代客下单 - 订单创建信息：接受付款短信手机号；短信模版code；订单备注
 */
@Data
@Schema(description = "代客下单 - 订单创建信息：接受付款短信手机号；短信模版code；订单备注")
public class GuestOrderCreateCustomerServiceDTO {

    /**
     * 接收付款短信手机号
     */
    @Schema(description = "接收付款短信手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "12345678901")
    @NotBlank(message = "接收付款短信手机号不能为空")
    private String receivePhone;

    /**
     * 短信模板编码
     */
    @Schema(description = "短信模板编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "TEMPLATE_CODE_001")
    @NotBlank(message = "短信模板编码不能为空")
    private String messageTemplateCode;

    /**
     * 客服订单备注信息
     */
    @Schema(description = "客服订单备注信息", example = "这是一个备注示例")
    @Length(max = 50,message = "订单备注长度不能超过50个字符")
    private String customerServiceRemark;
}
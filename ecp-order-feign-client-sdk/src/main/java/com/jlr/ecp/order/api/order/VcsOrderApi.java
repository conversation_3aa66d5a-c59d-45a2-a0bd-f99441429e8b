package com.jlr.ecp.order.api.order;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.order.dto.VCSOrderInfoDTO;
import com.jlr.ecp.order.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.security.PermitAll;
import java.util.List;
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - VCS订单管理")
public interface VcsOrderApi {
    String PREFIX = ApiConstants.PREFIX + "/vcsOrder";
    @GetMapping(PREFIX + "/query/vcsOrderDTO")
    @Operation(summary = "使用carVin查询PIVI订单")
    @PermitAll
    CommonResult<List<VCSOrderInfoDTO>> queryPIVIVcsOrderByCarVin(@RequestParam("carVin") String carVin);
}

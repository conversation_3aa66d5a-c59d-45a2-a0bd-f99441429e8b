  pipeline {
    agent {
    kubernetes {
    yamlFile 'mainbuildpod.yaml' // 使用您的 mainbuildpod.yaml 文件来创建 Kubernetes Pod
    }
    }
    stages {
    stage('Checkout') {
    steps {
    // 在此阶段中，从代码仓库中检出源代码
    git 'https://github.com/your-repo/your-project.git'
    }
    }
    stage('Build') {
    steps {
    // 使用 Maven 构建项目
    container('maven') {
    sh 'mvn clean package'
    }
    }
    }
    stage('Docker Build') {
    steps {
    // 在 Docker 容器中构建 Docker 镜像
    container('docker') {
    sh 'docker build -t your-image-name .'
    }
    }
    }
    stage('Kubernetes Deploy') {
    steps {
    // 使用 kubectl 部署到 Kubernetes 集群
    container('kubectl') {
    sh 'kubectl apply -f your-deployment.yaml'
    }
    }
    }
    }
    post {
    success {
    // 构建成功后的操作
    echo 'Build and deployment successful!'
    }
    failure {
    // 构建失败后的操作
    echo 'Build or deployment failed!'
    }
    }
  }
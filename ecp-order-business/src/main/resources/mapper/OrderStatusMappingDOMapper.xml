<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.order.dal.mysql.order.OrderStatusMappingDOMapper">

    <resultMap id="BaseResultMap" type="com.jlr.ecp.order.dal.dataobject.order.OrderStatusMappingDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
            <result property="refundOrderStatus" column="refund_order_status" jdbcType="INTEGER"/>
            <result property="customerOrderStatusView" column="customer_order_status_view" jdbcType="VARCHAR"/>
            <result property="customerAfterSalesOrderStatusView" column="customer_after_sales_order_status_view" jdbcType="VARCHAR"/>
            <result property="operationOriginOrderStatusView" column="operation_origin_order_status_view" jdbcType="VARCHAR"/>
            <result property="operationOriginOrderCancelStatusView" column="operation_origin_order_cancel_status_view" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
            <result property="revision" column="revision" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_status,refund_order_status,
        customer_order_status_view,customer_after_sales_order_status_view,operation_origin_order_status_view,
        operation_origin_order_cancel_status_view,tenant_id,created_by,
        created_time,updated_by,updated_time,
        is_deleted,revision
    </sql>
</mapper>

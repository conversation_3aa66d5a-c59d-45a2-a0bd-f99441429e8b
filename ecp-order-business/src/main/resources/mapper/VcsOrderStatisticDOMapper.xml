<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.order.dal.mysql.order.VcsOrderStatisticDOMapper">

    <resultMap id="BaseResultMap" type="com.jlr.ecp.order.dal.dataobject.order.VcsOrderStatisticDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="consumerCode" column="consumer_code" jdbcType="VARCHAR"/>
            <result property="seriesName" column="series_name" jdbcType="VARCHAR"/>
            <result property="seriesCode" column="series_code" jdbcType="VARCHAR"/>
            <result property="orderCount" column="order_count" jdbcType="INTEGER"/>
            <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
            <result property="revision" column="revision" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,consumer_code,series_name,
        series_code,order_count,tenant_id,
        created_by,created_time,updated_by,
        updated_time,is_deleted,revision
    </sql>
</mapper>

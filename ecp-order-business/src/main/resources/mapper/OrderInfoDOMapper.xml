<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper">

    <resultMap id="BaseResultMap" type="com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="consumerCode" column="consumer_code" jdbcType="VARCHAR"/>
        <result property="orderCode" column="order_code" jdbcType="VARCHAR"/>
        <result property="originalFeeTotalAmount" column="original_fee_total_amount" jdbcType="INTEGER"/>
        <result property="feeTotalAmount" column="fee_total_amount" jdbcType="INTEGER"/>
        <result property="costAmount" column="cost_amount" jdbcType="INTEGER"/>
        <result property="discountTotalAmount" column="discount_total_amount" jdbcType="INTEGER"/>
        <result property="freightAmount" column="freight_amount" jdbcType="INTEGER"/>
        <result property="parentOrderCode" column="parent_order_code" jdbcType="VARCHAR"/>
        <result property="orderStatus" column="order_status" jdbcType="INTEGER"/>
        <result property="paymentStatus" column="payment_status" jdbcType="INTEGER"/>
        <result property="paymentTime" column="payment_time" jdbcType="TIMESTAMP"/>
        <result property="orderTime" column="order_time" jdbcType="TIMESTAMP"/>
        <result property="orderType" column="order_type" jdbcType="INTEGER"/>
        <result property="orderChannel" column="order_channel" jdbcType="INTEGER"/>
        <result property="customerRemark" column="customer_remark" jdbcType="VARCHAR"/>
        <result property="contactPhone" column="contact_phone" jdbcType="VARCHAR"/>
        <result property="operatorRemark" column="operator_remark" jdbcType="VARCHAR"/>
        <result property="refundStatus" column="refund_status" jdbcType="INTEGER"/>
        <result property="orderCloseReason" column="order_close_reason" jdbcType="VARCHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,consumer_code,order_code,
        original_fee_total_amount,fee_total_amount,cost_amount,
        discount_total_amount,freight_amount,parent_order_code,
        order_status,payment_status,payment_time,
        order_time,order_type,order_channel,
        customer_remark,contact_phone,operator_remark,
        refund_status,order_close_reason,tenant_id,
        created_by,created_time,updated_by,
        updated_time,is_deleted,revision
    </sql>

    <select id="getPage" resultType="com.jlr.ecp.order.api.order.vo.OrderInfoPageVO">
        SELECT
        oi.order_code AS orderCode, voi.incontrol_id_mix AS inControlId,voi.incontrol_id as icrCT, voi.car_vin_mix AS carVin,voi.car_vin AS carVinCT,
        oi.contact_phone_mix AS contactPhone,oi.contact_phone AS contactPhoneCT, FORMAT(oi.cost_amount / 100.0, 2) AS costAmount,
        oi.order_status AS orderStatus, oi.created_time AS createdTime,
        tosl.after_status AS afterStatus,
        IFNULL(cso.create_operator, '-') as orderCreator,
        CASE
        WHEN oi.order_channel IN (1, 2) THEN '小程序下单'
        WHEN oi.order_channel = 5 THEN '客服下单'
        ELSE '-' END AS orderSource
        FROM t_order_info oi
        LEFT JOIN t_vcs_order_info voi ON voi.order_code = oi.order_code AND
        voi.id = (SELECT MAX(id) FROM t_vcs_order_info WHERE order_code = voi.order_code)
        LEFT JOIN t_order_status_log tosl
        ON oi.order_code = tosl.order_code AND tosl.id = (SELECT MAX(id) FROM t_order_status_log WHERE order_code = voi.order_code)
        LEFT JOIN t_customer_service_order cso ON cso.order_code = oi.order_code
        WHERE oi.order_type > 0
        <if test="dto.orderCreator!=null and dto.orderCreator!=''">
            and cso.create_operator = #{dto.orderCreator}
        </if>
        <if test="dto.orderCode!=null and dto.orderCode!=''">
            and oi.parent_order_code = #{dto.orderCode}
        </if>
        <if test="dto.vin!=null and dto.vin!=''">
            <!--and #{dto.vin}  like CONCAT('%',SUBSTR(voi.car_vin, -6))-->
            and voi.car_vin_md5 = #{dto.vin}
        </if>
        <if test="dto.orderStatus!=null and dto.orderStatus!=''">
            and tosl.after_status in
            <foreach item="item" collection="dto.statusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.orderChannelList!=null and dto.orderChannelList.size() > 0">
            and oi.order_channel in
            <foreach item="item" collection="dto.orderChannelList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.orderType!=null">
            and oi.order_type = #{dto.orderType}
        </if>
        <if test="dto.inControlId!=null and dto.inControlId!=''">
            and voi.incontrol_id_md5 = #{dto.inControlId}
        </if>
        <if test="dto.mobile!=null and dto.mobile!=''">
            and (oi.contact_phone_md5 = #{dto.mobile}  or oi.wx_phone_md5 = #{dto.mobile})
        </if>
        <if test="dto.startTime!=null and dto.startTime!=''">
            and oi.created_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime!=null and dto.endTime!=''">
            and oi.created_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.businessCode!=null and dto.businessCode!=''">
            and oi.business_code = #{dto.businessCode}
        </if>
        AND oi.is_deleted = false
        <if test="dto.createdTimeSort!=null and dto.createdTimeSort!='' and dto.createdTimeSort=='asc'">
            order by oi.created_time asc, oi.id asc
        </if>
        <if test="dto.createdTimeSort!=null and dto.createdTimeSort!='' and dto.createdTimeSort=='desc'">
            order by oi.created_time desc, oi.id desc
        </if>
    </select>

    <select id="getValetPage" resultType="com.jlr.ecp.order.api.order.vo.OrderInfoPageVO">
        SELECT
        oi.order_code AS orderCode, voi.incontrol_id_mix AS inControlId,voi.incontrol_id as icrCT, voi.car_vin_mix AS carVin,voi.car_vin AS carVinCT,
        oi.contact_phone_mix AS contactPhone,oi.contact_phone AS contactPhoneCT, FORMAT(oi.cost_amount / 100.0, 2) AS costAmount,
        oi.order_status AS orderStatus, oi.created_time AS createdTime,
        tosl.after_status AS afterStatus,
        IFNULL(cso.create_operator, '-') as orderCreator,
        CASE
        WHEN oi.order_channel IN (1, 2) THEN '小程序下单'
        WHEN oi.order_channel = 5 THEN '客服下单'
        ELSE '-' END AS orderSource
        FROM t_order_info oi
        LEFT JOIN t_vcs_order_info voi ON voi.order_code = oi.order_code AND
        voi.id = (SELECT MAX(id) FROM t_vcs_order_info WHERE order_code = voi.order_code)
        LEFT JOIN t_order_status_log tosl
        ON oi.order_code = tosl.order_code AND tosl.id = (SELECT MAX(id) FROM t_order_status_log WHERE order_code = voi.order_code)
        LEFT JOIN t_customer_service_order cso ON cso.order_code = oi.order_code
        WHERE oi.order_type > 0
        <if test="dto.orderCreator!=null and dto.orderCreator!=''">
            and cso.create_operator = #{dto.orderCreator}
        </if>
        <if test="dto.orderCode!=null and dto.orderCode!=''">
            and oi.parent_order_code = #{dto.orderCode}
        </if>
        <if test="dto.vin!=null and dto.vin!=''">
            <!--and #{dto.vin}  like CONCAT('%',SUBSTR(voi.car_vin, -6))-->
            and voi.car_vin_md5 = #{dto.vin}
        </if>
        <if test="dto.orderStatus!=null and dto.orderStatus!=''">
            and tosl.after_status in
            <foreach item="item" collection="dto.statusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.orderChannelList!=null and dto.orderChannelList.size() > 0">
            and oi.order_channel in
            <foreach item="item" collection="dto.orderChannelList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.orderType!=null">
            and oi.order_type = #{dto.orderType}
        </if>
        <if test="dto.inControlId!=null and dto.inControlId!=''">
            and voi.incontrol_id_md5 = #{dto.inControlId}
        </if>
        <if test="dto.mobile!=null and dto.mobile!=''">
            and (oi.contact_phone_md5 = #{dto.mobile}  or oi.wx_phone_md5 = #{dto.mobile})
        </if>
        <if test="dto.startTime!=null and dto.startTime!=''">
            and oi.created_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime!=null and dto.endTime!=''">
            and oi.created_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.businessCode!=null and dto.businessCode!=''">
            and oi.business_code = #{dto.businessCode}
        </if>
        <if test="dto.orderCreator != null and dto.orderCreator !=''">
            and oi.created_by = #{dto.orderCreator}
        </if>
        AND oi.order_channel = 5
        AND oi.is_deleted = false
        <if test="dto.createdTimeSort!=null and dto.createdTimeSort!='' and dto.createdTimeSort=='asc'">
            order by oi.created_time asc, oi.id asc
        </if>
        <if test="dto.createdTimeSort!=null and dto.createdTimeSort!='' and dto.createdTimeSort=='desc'">
            order by oi.created_time desc, oi.id desc
        </if>
    </select>

    <select id="selectUnpaidOrAfterSalesOrdersForVCS" resultType="com.jlr.ecp.order.api.order.dto.OrderUnpaidRelaDTO">
        SELECT toi.order_code,toi.consumer_code,toi.order_status,toi.refund_status
        FROM t_vcs_order_info tvoi
        JOIN t_order_info toi ON tvoi.order_code = toi.order_code
        WHERE
        <foreach item="item" index="index" collection="carVinAndServiceTypeList" open="(" separator="or" close=")">
            tvoi.car_vin_md5 = #{item.carVin} and tvoi.service_type = #{item.serviceType}
        </foreach>
        AND toi.order_status in (1,2,5)
        AND tvoi.is_deleted = 0 AND toi.is_deleted = 0
    </select>

    <select id="queryPhoneListByCarVin" resultType="com.jlr.ecp.order.api.order.dto.OrderCarVinDTO">
        SELECT
            toi.tenant_id, toi.order_code, toi.wx_phone, toi.customer_remark, toi.contact_phone,  tvoi.incontrol_id, tvoi.car_vin
        FROM t_vcs_order_info tvoi
        JOIN t_order_info toi ON tvoi.order_code = toi.order_code
        WHERE tvoi.car_vin_md5 IN
        <foreach item="item" index="index" collection="carVinList" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND tvoi.is_deleted = 0 AND toi.is_deleted = 0
    </select>


    <select id="queryCarVinAndServiceTypeByItemCodes" resultType="com.jlr.ecp.order.api.order.dto.CarVinAndServiceTypeDTO">
        SELECT distinct voi.car_vin_md5 as carVin ,voi.service_type as serviceType FROM t_vcs_order_info voi
        Left Join t_order_item oi on oi.order_item_code = voi.order_item_code
        where voi.is_deleted = 0
        and oi.is_deleted = 0
        and voi.order_item_code in
        <foreach item="item" index="index" collection="orderItemCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryOrderDoByOrderCodeList" resultType="com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO">
        SELECT * from t_order_info
        where order_code in
        <foreach item="item" index="index" collection="orderCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_deleted = 0
        order by order_time ASC
    </select>

    <select id="findOrderCodeByCarVinAndServiceType" resultType="java.lang.String">
        SELECT  oi.order_code as orderCode
        FROM t_vcs_order_info voi
                 Left Join t_order_item oi on oi.order_item_code = voi.order_item_code
                 left join t_order_info toi on oi.order_code = toi.order_code
        where voi.is_deleted = 0
          and oi.is_deleted = 0
          and voi.car_vin_md5 =  #{dto.carVin}
          and voi.service_type = #{dto.serviceType}
          and toi.order_status not in (4,6,7)
        order by voi.id desc limit 1
    </select>

    <select id="selectOrderInTransit" resultType="com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO">
        SELECT toi.order_code,toi.consumer_code,toi.order_status
        FROM t_vcs_order_info tvoi
        JOIN t_order_info toi ON tvoi.order_code = toi.order_code
        WHERE
        tvoi.car_vin_md5 = #{carVinMd5}
        AND
        tvoi.service_type = #{serviceType}
        AND
        toi.order_status NOT IN
        <foreach item="item" index="index" collection="orderStatusList" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND tvoi.is_deleted = 0 AND toi.is_deleted = 0
    </select>

    <select id="selectOrderInTransitByVinSet" resultType="java.lang.String">
        SELECT tvoi.car_vin_md5
        FROM t_vcs_order_info tvoi
        JOIN t_order_info toi ON tvoi.order_code = toi.order_code
        WHERE
        tvoi.car_vin_md5 IN
        <foreach item="item" index="index" collection="carVinSet" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND
        tvoi.service_type = #{serviceType}
        AND
        toi.order_status NOT IN
        <foreach item="item" index="index" collection="orderStatusList" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND tvoi.is_deleted = 0 AND toi.is_deleted = 0
    </select>

    <select id="selectOrderItemInfo" resultType="com.jlr.ecp.order.dal.mysql.order.po.OrderItemRefundVcsPo">
        select toi.order_code, toi.order_item_spu_type,toi.item_fufilement_type,toi.aftersales_status,
        toi.order_item_code,toi.product_version_code,toi.product_code,toi.product_sku_code,toi.product_name,toi.product_image_url,toi.product_attribute,toi.cost_amount,
        toi.product_market_price,toi.product_sale_price, toi.product_quantity
        from t_order_item toi
        where toi.is_deleted = 0
        and toi.order_code in
        <foreach item="item" index="index" collection="orderCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY toi.id ASC
    </select>

    <select id="selectConsumerOrderPage" resultType="com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO">
        select toi.* from t_order_info toi
        where consumer_code = #{consumerCode}
           and toi.is_deleted = 0
        AND ((
            toi.business_code = 'BUSINESS:001' and order_type != 0
        ) or (
            (toi.business_code is null or toi.business_code = 'LRE' or toi.business_code = 'BrandedGoods') and (
            (order_status = 1 AND order_type = 0)
            OR (order_status = 1 AND order_type != 0 AND parent_order_code = order_code)
            OR (order_status != 1 AND order_type != 0)
            )
        ))
        <choose>
            <when test="orderStatus == 1">
                AND order_status = 1
            </when>
            <when test="orderStatus == 5">
                AND EXISTS( SELECT 1
                FROM t_order_refund tor
                WHERE tor.origin_order_code = toi.order_code
                AND tor.is_deleted = 0
                AND tor.tenant_id = 1)
            </when>
        </choose>
        order by created_time desc
    </select>

    <!-- 查询符合条件的订单基本信息, 并包含满足条件的order_item_code -->
    <select id="getEcouponOrdersWithPaging" resultType="com.jlr.ecp.order.api.order.vo.ECouponOrderInfoPageVO">
        <if test="dto.afterSalesStatusList!=null and dto.afterSalesStatusList.size() > 0">
            WITH refun_only_last_items AS (
                SELECT *, '1' as tenant_id FROM (
                    SELECT t2.coupon_refund_status, t1.order_item_code,
                        ROW_NUMBER() OVER (PARTITION BY t1.order_item_code ORDER BY t1.id desc) AS row_num
                    FROM t_order_refund_item t1
                    JOIN t_order_refund t2 ON t1.refund_order_code = t2.refund_order_code
                ) only_last where row_num = 1
            )
        </if>
        SELECT
            oi.parent_order_code AS parentOrderCode,
            oi.order_code AS orderCode,
            oi.wx_phone_mix AS wxPhoneMix,
            oi.wx_phone AS wxPhone,
            oi.contact_phone_mix AS contactPhoneMix,
            oi.contact_phone AS contactPhone,
            oi.created_time AS createdTime,
            oi.coupon_status AS couponStatus
        FROM t_order_info oi
        WHERE oi.business_code = 'LRE' and oi.order_type > 0 and oi.is_deleted = 0
        <if test="dto.parentOrderCode != null and dto.parentOrderCode != ''">
            AND oi.parent_order_code = #{dto.parentOrderCode}
        </if>
        <if test="dto.orderCode != null and dto.orderCode != ''">
            AND oi.order_code = #{dto.orderCode}
        </if>
        <if test="dto.couponModelCode != null and dto.couponModelCode != ''">
            AND EXISTS (
                SELECT 1 FROM t_order_item as item
                WHERE oi.order_code = item.order_code AND item.is_deleted = 0
                AND item.coupon_model_code = #{dto.couponModelCode}
            )
        </if>
        <if test="dto.wxPhone != null and dto.wxPhone != ''">
            AND oi.wx_phone_md5 = #{dto.wxPhone}
        </if>
        <if test="dto.contactPhone != null and dto.contactPhone != ''">
            AND oi.contact_phone_md5 = #{dto.contactPhone}
        </if>
        <if test="dto.startTime!=null and dto.startTime!=''">
            AND oi.created_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime!=null and dto.endTime!=''">
            AND oi.created_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.couponStatusList!=null and dto.couponStatusList.size() > 0">
            AND oi.coupon_status IN
            <foreach item="item" collection="dto.couponStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.afterSalesStatusList!=null and dto.afterSalesStatusList.size() > 0">
            AND EXISTS (
                SELECT 1 FROM t_order_item AS tor
                INNER JOIN refun_only_last_items AS ri ON tor.order_item_code = ri.order_item_code
                WHERE tor.order_code = oi.order_code
                AND ri.coupon_refund_status IN
                <foreach item="afterSalesStatus" collection="dto.afterSalesStatusList" open="(" separator="," close=")">
                    #{afterSalesStatus}
                </foreach>
            )
        </if>
        <if test="dto.createdTimeSort!=null and dto.createdTimeSort!='' and dto.createdTimeSort=='asc'">
            ORDER BY oi.created_time ASC, oi.id ASC
        </if>
        <if test="dto.createdTimeSort!=null and dto.createdTimeSort!='' and dto.createdTimeSort=='desc'">
            ORDER BY oi.created_time DESC, oi.id DESC
        </if>
    </select>

    <!-- 根据订单编码列表获取订单详情 -->
    <select id="getEcouponOrderDetailsByCodes" resultType="com.jlr.ecp.order.api.order.vo.ECouponOrderItemVO">
        SELECT
        item.order_code AS orderCode,
        item.order_item_code AS orderItemCode,
        item.product_image_url AS productImageUrl,
        item.product_name AS productName,
        item.coupon_model_code AS couponModelCode,
        (SELECT valid_start_time FROM t_order_coupon_detail WHERE order_item_code = item.order_item_code AND is_deleted = 0 LIMIT 1) AS validStartTime,
        (SELECT valid_end_time FROM t_order_coupon_detail WHERE order_item_code = item.order_item_code AND is_deleted = 0 LIMIT 1) AS validEndTime,
        item.product_quantity AS productQuantity,
        FORMAT(item.product_sale_price / 100.0, 2) AS productSalePrice,
        FORMAT(item.cost_amount / 100.0, 2) AS costAmount,
        item.point_amount AS pointAmount,
        (SELECT tor.coupon_refund_status 
         FROM t_order_refund tor 
         WHERE tor.refund_order_code = (
             SELECT refund_order_code 
             FROM t_order_refund_item 
             WHERE order_item_code = item.order_item_code 
             AND is_deleted = 0 
             ORDER BY id DESC LIMIT 1
         ) 
         AND tor.is_deleted = 0
        ) AS afterSalesStatus,
        item.item_status AS itemStatus,
        FORMAT(odd.discount_amount / 100.0, 2) AS discountAmount,
        odd.coupon_code AS discountCouponCode,
        (SELECT refund_order_code FROM t_order_refund_item  WHERE order_item_code = item.order_item_code AND is_deleted = 0 ORDER BY id DESC LIMIT 1) AS refundOrderCode
        FROM t_order_item item
        LEFT JOIN t_order_discount_detail odd ON item.order_item_code = odd.order_item_code AND odd.is_deleted = 0
        WHERE item.order_item_code IN
        <foreach item="item" collection="orderItemCodes" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND item.is_deleted = 0
    </select>

    
    <!-- 查询符合条件的订单基本信息, 并包含满足条件的order_item_code -->
    <select id="getBrandGoodsOrdersWithPaging" resultType="com.jlr.ecp.order.api.order.vo.BrandGoodsOrderInfoPageVO">
        <if test="dto.afterSalesStatusList!=null and dto.afterSalesStatusList.size() > 0">
            WITH refun_only_last_items AS (
                SELECT *, '1' as tenant_id FROM (
                    SELECT t2.logistics_refund_status, t1.order_item_code,
                    ROW_NUMBER() OVER (PARTITION BY t1.order_item_code ORDER BY t1.id desc) AS row_num
                    FROM t_order_refund_item t1
                    JOIN t_order_refund t2 ON t1.refund_order_code = t2.refund_order_code
                ) only_last where row_num = 1
            )
        </if>
        SELECT
            oi.parent_order_code AS parentOrderCode,
            oi.order_code AS orderCode,
            oi.order_status AS orderStatus,
            oi.wx_phone_mix AS wxPhoneMix,
            oi.wx_phone AS wxPhone,
            oi.contact_phone_mix AS contactPhoneMix,
            oi.contact_phone AS contactPhone,
            oi.created_time AS createdTime,
            oi.updated_time AS updatedTime,
            oi.logistics_status AS logisticsOrderStatus,
            oi.wx_nick_name AS wxNickName,
            oi.erp_sync AS erpSync,
            FORMAT(oi.cost_amount / 100.0, 2) AS costAmount,
            FORMAT(oi.discount_total_amount / 100.0, 2) AS discountTotalAmount,
            FORMAT(oi.freight_amount / 100.0, 2) AS freightAmount,
            oi.point_amount AS pointAmount,
            oi.customer_remark AS customerRemark,
            oi.operator_remark AS operatorRemark,
            CASE WHEN order_status = 4 THEN '微信支付' ELSE NULL END AS paymentType,
            oi.order_time AS orderTime,
            oi.payment_time AS paymentTime
        FROM t_order_info oi
        WHERE oi.order_type > 0 AND oi.is_deleted = 0 AND oi.business_code = "BrandedGoods"
        <if test="dto.parentOrderCode != null and dto.parentOrderCode != ''">
            AND oi.parent_order_code = #{dto.parentOrderCode}
        </if>
        <if test="dto.orderCode != null and dto.orderCode != ''">
            AND oi.order_code = #{dto.orderCode}
        </if>
        <if test="(dto.productSkuCode != null and dto.productSkuCode != '') or (dto.kingdeeSkuCode != null and dto.kingdeeSkuCode != '')">
            AND EXISTS (
                SELECT 1 FROM t_order_item as item where oi.order_code = item.order_code AND item.is_deleted = 0
                <if test="dto.productSkuCode != null and dto.productSkuCode != ''">
                    AND item.product_sku_code = #{dto.productSkuCode}
                </if>
                <if test="dto.kingdeeSkuCode != null and dto.kingdeeSkuCode != ''">
                    AND item.kingdee_sku_code = #{dto.kingdeeSkuCode}
                </if>
            )
        </if>
        <if test="dto.wxPhone != null and dto.wxPhone != ''">
            AND oi.wx_phone_md5 = #{dto.wxPhone}
        </if>
        <if test="dto.contactPhone != null and dto.contactPhone != ''">
            AND oi.contact_phone_md5 = #{dto.contactPhone}
        </if>

        <if test="(dto.recipientPhone != null and dto.recipientPhone != '') or (dto.deliveryStatusList!=null and dto.deliveryStatusList.size() > 0)">
            AND EXISTS (
                SELECT 1 FROM t_order_item_logistics as oil
                WHERE oi.order_code = oil.order_code AND oil.is_deleted = 0
                <if test="dto.recipientPhone != null and dto.recipientPhone != ''">
                    AND oil.recipient_phone_md5 = #{dto.recipientPhone}
                </if>
                <if test="dto.deliveryStatusList!=null and dto.deliveryStatusList.size() > 0">
                    AND oil.logistics_status IN
                    <foreach item="item" collection="dto.deliveryStatusList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            )
        </if>
        <if test="dto.startTime!=null and dto.startTime!=''">
            AND oi.created_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime!=null and dto.endTime!=''">
            AND oi.created_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.startUpdateTime!=null and dto.startUpdateTime!=''">
            AND oi.updated_time &gt;= #{dto.startUpdateTime}
        </if>
        <if test="dto.endUpdateTime!=null and dto.endUpdateTime!=''">
            AND oi.updated_time &lt;= #{dto.endUpdateTime}
        </if>
        <if test="dto.logisticsOrderStatusList != null and dto.logisticsOrderStatusList.size() > 0 or dto.needQueryAfterSales">
            AND (
                <if test="dto.logisticsOrderStatusList != null and dto.logisticsOrderStatusList.size() > 0">
                    oi.logistics_status IN
                    <foreach item="item" collection="dto.logisticsOrderStatusList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.needQueryAfterSales">
                    OR EXISTS (
                        SELECT 1 FROM t_order_refund tor
                        WHERE tor.origin_order_code = oi.order_code
                        AND tor.refund_order_status IN (1, 2, 3, 4)
                        AND tor.is_deleted = 0
                    )
                </if>
                <if test="!dto.needQueryAfterSales">
                    AND NOT EXISTS (
                        SELECT 1 FROM t_order_refund tor 
                        WHERE tor.origin_order_code = oi.order_code
                        AND tor.refund_order_status IN (1, 2, 3, 4)
                        AND tor.is_deleted = 0
                    )
                </if>
            )
        </if>
        <if test="dto.afterSalesStatusList!=null and dto.afterSalesStatusList.size() > 0">
            AND EXISTS (
                SELECT 1 FROM t_order_item AS tor
                INNER JOIN refun_only_last_items AS ri ON tor.order_item_code = ri.order_item_code
                WHERE tor.order_code = oi.order_code
                AND ri.logistics_refund_status IN
                <foreach item="afterSalesStatus" collection="dto.afterSalesStatusList" open="(" separator="," close=")">
                    #{afterSalesStatus}
                </foreach>
            )
        </if>
        <if test="dto.closedWithNoPay !=null and dto.closedWithNoPay">
            AND (oi.order_status in (2,3,4,5,6,7) and payment_status = 1)
        </if>
        <choose>
            <when test="dto.createdTimeSort!=null and dto.createdTimeSort!='' and dto.createdTimeSort=='asc'">ORDER BY oi.created_time ASC, oi.id ASC</when>
            <when test="dto.createdTimeSort!=null and dto.createdTimeSort!='' and dto.createdTimeSort=='desc'">ORDER BY oi.created_time DESC, oi.id DESC</when>
            <when test="dto.updateTimeSort!=null and dto.updateTimeSort!='' and dto.updateTimeSort=='asc'">ORDER BY oi.updated_time ASC, oi.id ASC</when>
            <when test="dto.updateTimeSort!=null and dto.updateTimeSort!='' and dto.updateTimeSort=='desc'">ORDER BY oi.updated_time DESC, oi.id DESC</when>
        </choose>
    </select>

    <!-- 根据订单编码列表获取订单详情 -->
    <select id="getBrandGoodsOrderDetailsByCodes" resultType="com.jlr.ecp.order.api.order.vo.BrandGoodsOrderItemVO">
        SELECT
        item.order_code AS orderCode,
        item.order_item_code AS orderItemCode,
        item.product_image_url AS productImageUrl,
        item.product_name AS productName,
        item.product_code AS productCode,
        item.product_sku_code AS productSkuCode,
        item.kingdee_sku_code AS kingdeeSkuCode,
        item.product_attribute AS productAttribute,
        item.aftersales_status AS itemAfterSalesStatus,
        (SELECT valid_start_time FROM t_order_coupon_detail WHERE order_item_code = item.order_item_code AND is_deleted = 0 LIMIT 1) AS validStartTime,
        (SELECT valid_end_time FROM t_order_coupon_detail WHERE order_item_code = item.order_item_code AND is_deleted = 0 LIMIT 1) AS validEndTime,
        item.product_quantity AS productQuantity,
        FORMAT(item.product_sale_price / 100.0, 2) AS productSalePrice,
        FORMAT(item.cost_amount / 100.0, 2) AS costAmount,
        item.point_amount AS pointAmount,
        refund_info.logistics_refund_status AS afterSalesStatus,
        refund_info.refund_order_status AS refundOrderStatus,
        item.item_status AS itemStatus,
        FORMAT(odd.discount_amount / 100.0, 2) AS discountAmount,
        odd.coupon_code AS discountCouponCode,
        oil.logistics_no AS logistics_no,
        oil.logistics_status AS logisticsStatus,
        refund_info.refund_order_code AS refundOrderCode
        FROM t_order_item item
        LEFT JOIN t_order_discount_detail odd ON item.order_item_code = odd.order_item_code AND odd.is_deleted = 0
        LEFT JOIN t_order_item_logistics oil ON item.order_item_code = oil.order_item_code AND oil.is_deleted = 0
        LEFT JOIN (
            SELECT 
                tori.order_item_code,
                tor.refund_order_code,
                tor.logistics_refund_status,
                tor.refund_order_status
            FROM t_order_refund_item tori
            JOIN t_order_refund tor ON tor.refund_order_code = tori.refund_order_code AND tor.is_deleted = 0
            WHERE tori.is_deleted = 0
            AND tori.id = (
                SELECT MAX(id) 
                FROM t_order_refund_item 
                WHERE order_item_code = tori.order_item_code 
                AND is_deleted = 0
            )
        ) refund_info ON refund_info.order_item_code = item.order_item_code
        WHERE item.order_item_code IN
        <foreach item="item" collection="orderItemCodes" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND item.is_deleted = 0
        ORDER BY item.id ASC
    </select>

    <select id="getSalesTrendByChannel" parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT order_type AS orderType, DATE_FORMAT(order_time, #{dateFormat}) AS label,
        COUNT(*) AS quantity,
        SUM(cost_amount) AS amount
        FROM t_order_info
        WHERE business_code = #{businessCode}
        AND order_time BETWEEN #{startDate} and #{endDate}
        <if test="orderChannel!=null and orderChannel!=''">
            AND order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
        <if test="kpi!=1">
            AND payment_status = 1
        </if>
        AND order_type in (1,2,4)
        AND is_deleted = 0
        GROUP BY order_type, label
    </select>

    <select id="getSalesSummaryGroupChannel" parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SummarySqlResultDTO">
        SELECT SUBSTRING(order_code, 1, 2) AS code, DATE_FORMAT(order_time, #{dateFormat}) AS label,
        COUNT(*) AS quantity,
        SUM(cost_amount) AS amount
        FROM t_order_info
        WHERE business_code = #{businessCode}
        AND order_time BETWEEN #{startDate} and #{endDate}
        <if test="orderChannel!=null and orderChannel!=''">
            AND order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
        <if test="kpi!=1">
            AND payment_status = 1
        </if>
        AND order_type in (1,2,4)
        AND is_deleted = 0
        GROUP BY SUBSTRING(order_code, 1, 2), label
    </select>

    <select id="getSalesTrendByChannelByValet"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT
        order_type AS orderType,
        DATE_FORMAT(order_time, #{dateFormat}) AS label,
        COUNT(*) AS quantity,
        SUM(cost_amount) AS amount
        FROM t_order_info
        WHERE business_code = #{businessCode}
        AND order_time BETWEEN #{startDate} and #{endDate}
        <if test="orderChannel!=null and orderChannel!=''">
            AND order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
        <if test="kpi!=1">
            AND payment_status = 1
        </if>
        AND order_channel = 5
        AND order_type in (1,2,4)
        AND is_deleted = 0
        GROUP BY order_type, label
    </select>

    <select id="countOrderByChannel"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.ValetDashboardTempDTO">
        SELECT

        COALESCE(SUM(CASE
        WHEN order_channel = 5
        THEN 1
        ELSE 0
        END), 0) AS valetOrderCount,

        COALESCE(COUNT(order_code),0) AS totalOrderCount

        FROM t_order_info
        WHERE
        is_deleted = 0
        AND order_type != 0
        AND business_code = 'BUSINESS:001'
        AND created_time BETWEEN #{start} AND #{end}
        -- 处理渠道过滤
        <if test="orderChannel != null and orderChannel != ''">
            AND order_code LIKE
            <choose>
                <when test="orderChannel == 'LR'">CONCAT('LR%', '')</when>
                <when test="orderChannel == 'JA'">CONCAT('JA%', '')</when>
            </choose>
        </if>
    </select>

    <select id="countTotalOrderCount"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.ValetDashboardTempDTO">
        SELECT

        COALESCE(COUNT(order_code),0) AS totalOrderCount

        FROM t_order_info
        WHERE
        is_deleted = 0
        AND order_type != 0
        AND created_time BETWEEN #{start} AND #{end}
        <if test="businessCode != null and businessCode != ''">
            AND business_code = #{businessCode}
        </if>
    </select>

    <select id="countOrderByPaymentStatus"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.ValetDashboardTempDTO">
        SELECT

        -- 3.代客下单成交数（已支付）
        COALESCE(SUM(CASE
        WHEN order_channel = 5
        THEN 1
        ELSE 0
        END), 0) AS valetPaidCount,
        -- 4.已支付总订单数
        COALESCE(COUNT(order_code),0) AS totalPaidCount,

        -- 5.代客下单交易额（含税）
        COALESCE(SUM(CASE
        WHEN order_channel = 5
        THEN cost_amount
        ELSE 0
        END), 0) AS valetGmv,
        -- 6.所有订单交易额
        COALESCE(SUM(cost_amount), 0) AS totalGmv

        FROM t_order_info
        WHERE
        is_deleted = 0
        AND order_type != 0
        AND payment_status = 1
        AND business_code = 'BUSINESS:001'
        AND created_time BETWEEN #{start} AND #{end}
        -- 处理渠道过滤
        <if test="orderChannel != null and orderChannel != ''">
            AND order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
    </select>

    <select id="countTotalOrderByPaymentStatus"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.ValetDashboardTempDTO">
        SELECT

        -- 4.已支付总订单数
        COALESCE(COUNT(order_code),0) AS totalPaidCount,
        -- 6.所有订单交易额
        COALESCE(SUM(cost_amount), 0) AS totalGmv

        FROM t_order_info
        WHERE
        is_deleted = 0
        AND order_type != 0
        AND payment_status = 1
        AND created_time BETWEEN #{start} AND #{end}
        <if test="businessCode != null and businessCode != ''">
            AND business_code = #{businessCode}
        </if>
    </select>

    <select id="countOrderItemByChannel"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.ValetDashboardTempDTO">
        SELECT

        -- 代客下单商品总数
        COALESCE(SUM(
        CASE
        WHEN o.order_channel = 5 THEN
        CASE
        WHEN o.order_type = 4 AND it.order_item_spu_type = 2 THEN it.product_quantity
        WHEN o.order_type IN (1,2) THEN it.product_quantity
        ELSE 0
        END
        ELSE 0
        END
        ), 0) AS valetProductCount,

        -- 所有订单商品总数
        COALESCE(SUM(
        CASE
        WHEN o.order_type = 4 AND it.order_item_spu_type = 2 THEN it.product_quantity
        WHEN o.order_type IN (1,2) THEN it.product_quantity
        ELSE 0
        END
        ), 0) AS totalProductCount

        FROM t_order_item it
        JOIN t_order_info o
        ON it.order_code = o.order_code
        AND o.payment_status = 1
        AND o.is_deleted = 0
        AND o.order_type IN (1,2,4)
        AND o.business_code = 'BUSINESS:001'
        WHERE
        it.is_deleted = 0
        AND o.created_time BETWEEN #{start} AND #{end}
        -- 渠道过滤
        <if test="orderChannel != null and orderChannel != ''">
            AND o.order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
    </select>

    <select id="countTotalOrderItemQuantity"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.ValetDashboardTempDTO">
        SELECT

        -- 所有订单商品总数
        COALESCE(SUM(
        CASE
        WHEN o.order_type = 4 AND it.order_item_spu_type = 2 THEN it.product_quantity
        WHEN o.order_type IN (1,2) THEN it.product_quantity
        ELSE 0
        END
        ), 0) AS totalProductCount

        FROM t_order_item it
        JOIN t_order_info o
        ON it.order_code = o.order_code
        AND o.payment_status = 1
        AND o.is_deleted = 0
        AND o.order_type IN (1,2,4)
        WHERE
        it.is_deleted = 0
        AND o.created_time BETWEEN #{start} AND #{end}
        <if test="businessCode != null and businessCode != ''">
            AND o.business_code = #{businessCode}
        </if>
    </select>

    <select id="getSalesProportionByChannel"
            parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT toi.order_type AS orderType,
        CASE
        WHEN oit.product_attribute LIKE '%:一年%' THEN 'One Year'
        WHEN oit.product_attribute LIKE '%:三年%' THEN 'Three Years'
        WHEN oit.product_attribute LIKE '%:六年%' THEN 'Six Years'
        ELSE NULL
        END AS label,
        SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN 1
        WHEN toi.order_type IN (1,2) THEN 1
        ELSE 0
        END
        ) AS quantity,
        SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN toi.cost_amount
        WHEN toi.order_type IN (1,2) THEN toi.cost_amount
        ELSE 0
        END
        ) AS amount
        FROM t_order_info toi
        INNER JOIN t_order_item oit
        ON toi.order_code = oit.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="orderChannel!=null and orderChannel!=''">
            AND toi.order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
        <if test="kpi!=1">
            AND toi.payment_status = 1
        </if>
        AND toi.order_type in (1,2,4)
        AND toi.is_deleted = 0
        AND oit.is_deleted = 0
        GROUP BY toi.order_type, label
        HAVING
        label IS NOT NULL
    </select>

    <select id="getSalesTrendBySeriesCode" parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT toi.order_type AS orderType, DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        COUNT(*) AS quantity,
        SUM(toi.cost_amount) AS amount
        FROM t_order_info toi
        INNER JOIN t_vcs_order_info voi
        ON toi.order_code = voi.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="vehicleModel!=null and vehicleModel!=''">
            AND voi.series_code = #{vehicleModel}
        </if>
        <if test="kpi!=1">
            AND toi.payment_status = 1
        </if>
        AND
        (toi.order_type in (1,2)
        OR
        toi.order_type = 4 and voi.service_type = 1)
        AND toi.is_deleted = 0
        AND voi.is_deleted = 0
        GROUP BY toi.order_type, label
    </select>


    <select id="getSalesSummaryGroupSeriesCode" parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SummarySqlResultDTO">
        SELECT voi.series_code AS code, DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        COUNT(*) AS quantity,
        SUM(toi.cost_amount) AS amount
        FROM t_order_info toi
        INNER JOIN t_vcs_order_info voi
        ON toi.order_code = voi.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="kpi!=1">
            AND toi.payment_status = 1
        </if>
        AND
        (toi.order_type in (1,2)
        OR
        toi.order_type = 4 and voi.service_type = 1)
        AND toi.is_deleted = 0
        AND voi.is_deleted = 0
        GROUP BY voi.series_code, label
    </select>

    <select id="getSalesSummaryGroupProductName"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SummarySqlResultDTO">
        SELECT toItem.product_name AS code, DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        COUNT(*) AS quantity,
        SUM(toi.cost_amount) AS amount
        FROM t_order_info toi
        INNER JOIN t_order_item toItem
        ON toi.order_code = toItem.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="kpi!=1">
            AND toi.payment_status = 1
        </if>
        AND
        (
        toi.order_type IN (1, 2)
        OR
        (toi.order_type = 4 AND toItem.order_item_spu_type = 2)
        )
        AND toi.is_deleted = 0
        AND toItem.is_deleted = 0
        GROUP BY toItem.product_name, label
    </select>

    <select id="getSalesProportionBySeriesCode"
            parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT toi.order_type AS orderType,
        CASE
        WHEN oit.product_attribute LIKE '%:一年%' THEN 'One Year'
        WHEN oit.product_attribute LIKE '%:三年%' THEN 'Three Years'
        WHEN oit.product_attribute LIKE '%:六年%' THEN 'Six Years'
        ELSE NULL
        END AS label,
        SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN 1
        WHEN toi.order_type IN (1,2) THEN 1
        ELSE 0
        END
        ) AS quantity,
        SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN toi.cost_amount
        WHEN toi.order_type IN (1,2) THEN toi.cost_amount
        ELSE 0
        END
        ) AS amount
        FROM t_order_info toi
        INNER JOIN t_order_item oit
        ON toi.order_code = oit.order_code
        INNER JOIN t_vcs_order_info voi
        ON voi.order_code = toi.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="vehicleModel!=null and vehicleModel!=''">
            AND voi.series_code = #{vehicleModel}
        </if>
        <if test="kpi!=1">
            AND toi.payment_status = 1
        </if>
        AND
        (toi.order_type in (1,2)
        OR
        toi.order_type = 4 and voi.service_type = 1)
        AND toi.is_deleted = 0
        AND oit.is_deleted = 0
        AND voi.is_deleted = 0
        GROUP BY toi.order_type, label
        HAVING
        label IS NOT NULL
    </select>

    <select id="getSalesTrendRefundByChannel" parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT toi.order_type AS orderType, DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        SUM(refund_money_amount) AS amount
        FROM t_order_info toi
        INNER JOIN t_order_refund tor
        ON toi.order_code = tor.origin_order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="orderChannel!=null and orderChannel!=''">
            AND toi.order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
        AND toi.payment_status = 1
        AND toi.order_type in (1,2,4)
        AND toi.order_status in (6,7)
        AND toi.is_deleted = 0
        AND tor.refund_order_status in (5,6)
        AND tor.is_deleted = 0
        GROUP BY toi.order_type, label
    </select>


    <select id="getSalesSummaryRefund" parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SummarySqlResultDTO">
        SELECT SUBSTRING(toi.order_code, 1, 2) AS code, DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        SUM(refund_money_amount) AS amount
        FROM t_order_info toi
        INNER JOIN t_order_refund tor
        ON toi.order_code = tor.origin_order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="orderChannel!=null and orderChannel!=''">
            AND toi.order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
        AND toi.payment_status = 1
        AND toi.order_type in (1,2,4)
        AND toi.order_status in (6,7)
        AND toi.is_deleted = 0
        AND tor.refund_order_status in (5,6)
        AND tor.is_deleted = 0
        GROUP BY SUBSTRING(toi.order_code, 1, 2) , label
    </select>

    <select id="getSalesTrendRefundByChannelByValet"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT
        toi.order_type AS orderType,
        DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        SUM(refund_money_amount) AS amount

        FROM t_order_info toi
        INNER JOIN t_order_refund tor
        ON toi.order_code = tor.origin_order_code

        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="orderChannel!=null and orderChannel!=''">
            AND toi.order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
        AND toi.order_channel = 5
        AND toi.payment_status = 1
        AND toi.order_type in (1,2,4)
        AND toi.order_status in (6,7)
        AND toi.is_deleted = 0
        AND tor.refund_order_status in (5,6)
        AND tor.is_deleted = 0
        GROUP BY toi.order_type, label
    </select>

    <select id="getSalesProportionRefundByChannel"
            parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT toi.order_type AS orderType,
        CASE
        WHEN oit.product_attribute LIKE '%:一年%' THEN 'One Year'
        WHEN oit.product_attribute LIKE '%:三年%' THEN 'Three Years'
        WHEN oit.product_attribute LIKE '%:六年%' THEN 'Six Years'
        ELSE NULL
        END AS label,
        SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN tor.refund_money_amount
        WHEN toi.order_type IN (1,2) THEN tor.refund_money_amount
        ELSE 0
        END
        ) AS amount
        FROM t_order_info toi
        INNER JOIN t_order_item oit
        ON toi.order_code = oit.order_code
        INNER JOIN t_order_refund tor
        ON toi.order_code = tor.origin_order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="orderChannel!=null and orderChannel!=''">
            AND toi.order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
        AND toi.payment_status = 1
        AND toi.order_type in (1,2,4)
        AND toi.order_status in (6,7)
        AND toi.is_deleted = 0
        AND oit.is_deleted = 0
        AND tor.refund_order_status in (5,6)
        AND tor.is_deleted = 0
        GROUP BY toi.order_type, label
        HAVING
        label IS NOT NULL
    </select>

    <select id="getSalesTrendRefundBySeriesCode" parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT toi.order_type AS orderType, DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        SUM(tor.refund_money_amount) AS amount
        FROM t_order_info toi
        INNER JOIN t_vcs_order_info voi
        ON toi.order_code = voi.order_code
        INNER JOIN t_order_refund tor
        ON toi.order_code = tor.origin_order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="vehicleModel!=null and vehicleModel!=''">
            AND voi.series_code = #{vehicleModel}
        </if>
        AND toi.payment_status = 1
        AND
        (toi.order_type in (1,2)
        OR
        toi.order_type = 4 and voi.service_type = 1)
        AND toi.order_status in (6,7)
        AND toi.is_deleted = 0
        AND tor.refund_order_status in (5,6)
        AND tor.is_deleted = 0
        AND voi.is_deleted = 0
        GROUP BY toi.order_type, label
    </select>


    <select id="getSalesSummaryRefundGroupSeriesCode" parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SummarySqlResultDTO">
        SELECT voi.series_code AS code, DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        SUM(tor.refund_money_amount) AS amount
        FROM t_order_info toi
        INNER JOIN t_vcs_order_info voi
        ON toi.order_code = voi.order_code
        INNER JOIN t_order_refund tor
        ON toi.order_code = tor.origin_order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        AND toi.payment_status = 1
        AND
        (toi.order_type in (1,2)
        OR
        toi.order_type = 4 and voi.service_type = 1)
        AND toi.order_status in (6,7)
        AND toi.is_deleted = 0
        AND tor.refund_order_status in (5,6)
        AND tor.is_deleted = 0
        AND voi.is_deleted = 0
        GROUP BY  voi.series_code, label
    </select>

    <select id="getSalesSummaryRefundGroupProductName"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SummarySqlResultDTO">
        SELECT toItem.product_name AS code, DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
               SUM(tor.refund_money_amount) AS amount
        FROM t_order_info toi
                 INNER JOIN t_order_item toItem
                            ON toi.order_code = toItem.order_code
                 INNER JOIN t_order_refund tor
                            ON toi.order_code = tor.origin_order_code
        WHERE toi.business_code = #{businessCode}
          AND toi.order_time BETWEEN #{startDate} and #{endDate}
          AND toi.payment_status = 1
          AND
            (toi.order_type in (1,2)
                OR
             toi.order_type = 4 and toItem.order_item_spu_type = 2)
          AND toi.order_status in (6,7)
          AND toi.is_deleted = 0
          AND tor.refund_order_status in (5,6)
          AND tor.is_deleted = 0
          AND toItem.is_deleted = 0
        GROUP BY  toItem.product_name, label
    </select>


    <select id="getSalesProportionRefundBySeriesCode"
            parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT toi.order_type AS orderType,
        CASE
        WHEN oit.product_attribute LIKE '%:一年%' THEN 'One Year'
        WHEN oit.product_attribute LIKE '%:三年%' THEN 'Three Years'
        WHEN oit.product_attribute LIKE '%:六年%' THEN 'Six Years'
        ELSE NULL
        END AS label,
        SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN tor.refund_money_amount
        WHEN toi.order_type IN (1,2) THEN tor.refund_money_amount
        ELSE 0
        END
        ) AS amount
        FROM t_order_info toi
        INNER JOIN t_order_item oit
        ON toi.order_code = oit.order_code
        INNER JOIN t_order_refund tor
        ON toi.order_code = tor.origin_order_code
        INNER JOIN t_vcs_order_info voi
        ON toi.order_code = voi.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="vehicleModel!=null and vehicleModel!=''">
            AND voi.series_code = #{vehicleModel}
        </if>
        AND toi.payment_status = 1
        AND
        (toi.order_type in (1,2)
        OR
        toi.order_type = 4 and voi.service_type = 1)
        AND toi.order_status in (6,7)
        AND toi.is_deleted = 0
        AND oit.is_deleted = 0
        AND tor.refund_order_status in (5,6)
        AND tor.is_deleted = 0
        AND voi.is_deleted = 0
        GROUP BY toi.order_type, label
        HAVING
        label IS NOT NULL
    </select>

    <select id="getSalesTrendProductCountByChannel" parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT toi.order_type AS orderType,
        DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN oit.product_quantity
        WHEN toi.order_type IN (1,2) THEN oit.product_quantity
        ELSE 0
        END
        ) AS quantity
        FROM t_order_info toi
        INNER JOIN t_order_item oit
        ON toi.order_code = oit.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="orderChannel!=null and orderChannel!=''">
            AND toi.order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
        AND toi.payment_status = 1
        AND toi.order_type in (1,2,4)
        AND toi.is_deleted = 0
        AND oit.is_deleted = 0
        GROUP BY toi.order_type, label
    </select>


    <select id="getSalesSummaryProductCountGroupChannel" parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SummarySqlResultDTO">
        SELECT SUBSTRING(toi.order_code, 1, 2) AS code,
        DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        COALESCE(SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN oit.product_quantity
        WHEN toi.order_type IN (1,2) THEN oit.product_quantity
        ELSE 0
        END
        ), 0) AS quantity
        FROM t_order_info toi
        INNER JOIN t_order_item oit
        ON toi.order_code = oit.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="orderChannel!=null and orderChannel!=''">
            AND toi.order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
        AND toi.payment_status = 1
        AND toi.order_type in (1,2,4)
        AND toi.is_deleted = 0
        AND oit.is_deleted = 0
        GROUP BY SUBSTRING(toi.order_code, 1, 2), label
    </select>

    <select id="getSalesTrendProductCountByChannelByValet"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT
        toi.order_type AS orderType,
        DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        COALESCE(SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN oit.product_quantity
        WHEN toi.order_type IN (1,2) THEN oit.product_quantity
        ELSE 0
        END
        ), 0) AS quantity
        FROM t_order_info toi
        INNER JOIN t_order_item oit
        ON toi.order_code = oit.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="orderChannel!=null and orderChannel!=''">
            AND toi.order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
        AND toi.order_channel = 5
        AND toi.payment_status = 1
        AND toi.order_type in (1,2,4)
        AND toi.is_deleted = 0
        AND oit.is_deleted = 0
        GROUP BY toi.order_type, label
    </select>

    <select id="getSalesProportionProductCountByChannel"
            parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT toi.order_type AS orderType,
        CASE
        WHEN oit.product_attribute LIKE '%:一年%' THEN 'One Year'
        WHEN oit.product_attribute LIKE '%:三年%' THEN 'Three Years'
        WHEN oit.product_attribute LIKE '%:六年%' THEN 'Six Years'
        ELSE NULL
        END AS label,
        SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN oit.product_quantity
        WHEN toi.order_type IN (1,2) THEN oit.product_quantity
        ELSE 0
        END
        ) AS quantity
        FROM t_order_info toi
        INNER JOIN t_order_item oit
        ON toi.order_code = oit.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="orderChannel!=null and orderChannel!=''">
            AND toi.order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
        AND toi.payment_status = 1
        AND toi.order_type in (1,2,4)
        AND toi.is_deleted = 0
        AND oit.is_deleted = 0
        GROUP BY toi.order_type, label
        HAVING
        label IS NOT NULL
    </select>

    <select id="getSalesTrendProductCountBySeriesCode" parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT toi.order_type AS orderType,
        DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN oit.product_quantity
        WHEN toi.order_type IN (1,2) THEN oit.product_quantity
        ELSE 0
        END
        ) AS quantity
        FROM t_order_info toi
        INNER JOIN t_vcs_order_info voi
        ON toi.order_code = voi.order_code
        INNER JOIN t_order_item oit
        ON toi.order_code = oit.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="vehicleModel!=null and vehicleModel!=''">
            AND voi.series_code = #{vehicleModel}
        </if>
        AND toi.payment_status = 1
        AND
        (toi.order_type in (1,2)
        OR
        toi.order_type = 4 and voi.service_type = 1)
        AND toi.is_deleted = 0
        AND voi.is_deleted = 0
        AND oit.is_deleted = 0
        GROUP BY toi.order_type, label
    </select>

    <select id="getSalesSummaryProductCountGroupSeriesCode" parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SummarySqlResultDTO">
        SELECT voi.series_code AS code,
        DATE_FORMAT(toi.order_time, #{dateFormat}) AS label,
        COALESCE(SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN oit.product_quantity
        WHEN toi.order_type IN (1,2) THEN oit.product_quantity
        ELSE 0
        END
        ), 0) AS quantity
        FROM t_order_info toi
        INNER JOIN t_vcs_order_info voi
        ON toi.order_code = voi.order_code
        INNER JOIN t_order_item oit
        ON toi.order_code = oit.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}

        AND toi.payment_status = 1
        AND
        (toi.order_type in (1,2)
        OR
        toi.order_type = 4 and voi.service_type = 1)
        AND toi.is_deleted = 0
        AND voi.is_deleted = 0
        AND oit.is_deleted = 0
        GROUP BY voi.series_code, label
    </select>

    <select id="getSalesProportionProductCountBySeriesCode"
            parameterType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO">
        SELECT toi.order_type AS orderType,
        CASE
        WHEN oit.product_attribute LIKE '%:一年%' THEN 'One Year'
        WHEN oit.product_attribute LIKE '%:三年%' THEN 'Three Years'
        WHEN oit.product_attribute LIKE '%:六年%' THEN 'Six Years'
        ELSE NULL
        END AS label,
        SUM(
        CASE
        WHEN toi.order_type = 4 AND oit.order_item_spu_type = 2 THEN oit.product_quantity
        WHEN toi.order_type IN (1,2) THEN oit.product_quantity
        ELSE 0
        END
        ) AS quantity
        FROM t_order_info toi
        INNER JOIN t_order_item oit
        ON toi.order_code = oit.order_code
        INNER JOIN t_vcs_order_info voi
        ON voi.order_code = toi.order_code
        WHERE toi.business_code = #{businessCode}
        AND toi.order_time BETWEEN #{startDate} and #{endDate}
        <if test="vehicleModel!=null and vehicleModel!=''">
            AND voi.series_code = #{vehicleModel}
        </if>
        AND toi.payment_status = 1
        AND
        (toi.order_type in (1,2)
        OR
        toi.order_type = 4 and voi.service_type = 1)
        AND toi.is_deleted = 0
        AND oit.is_deleted = 0
        AND voi.is_deleted = 0
        GROUP BY toi.order_type, label
        HAVING
        label IS NOT NULL
    </select>
</mapper>

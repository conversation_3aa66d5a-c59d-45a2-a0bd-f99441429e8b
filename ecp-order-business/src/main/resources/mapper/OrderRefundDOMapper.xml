<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper">

    <resultMap id="BaseResultMap" type="com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="refundOrderCode" column="refund_order_code" jdbcType="VARCHAR"/>
            <result property="originOrderCode" column="origin_order_code" jdbcType="VARCHAR"/>
            <result property="refundOrderStatus" column="refund_order_status" jdbcType="INTEGER"/>
            <result property="submitUser" column="submit_user" jdbcType="VARCHAR"/>
            <result property="refundMoney" column="refund_money" jdbcType="INTEGER"/>
            <result property="refundMoneyAmount" column="refund_money_amount" jdbcType="INTEGER"/>
            <result property="refundRemark" column="refund_remark" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
            <result property="revision" column="revision" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,refund_order_code,origin_order_code,
        refund_order_status,submit_user,refund_money,
        refund_money_amount,refund_remark,tenant_id,
        created_by,created_time,updated_by,
        updated_time,is_deleted,revision
    </sql>

    <select id="getPage" resultType="com.jlr.ecp.order.api.refund.vo.OrderRefundPageVO">
        select
        tor.refund_order_code , tor.origin_order_code,
        voi.incontrol_id_mix inControlId,
        voi.incontrol_id icrCT,
        voi.car_vin_mix carVin,
        voi.car_vin carVinCT,
        <!--oi.contact_phone contactPhone,oi.wx_phone as wxPhone,-->
        oi.contact_phone_mix contactPhone,
        oi.contact_phone contactPhoneCT,
        oi.wx_phone_mix as wxPhone,
        tor.refund_order_status refundOrderStatus ,tor.created_time createdTime,
        IFNULL(cso.create_operator, '-') as orderCreator,
        CASE
        WHEN oi.order_channel IN (1, 2) THEN '小程序下单'
        WHEN oi.order_channel = 5 THEN '客服下单'
        ELSE '-' END AS orderSource
        from t_order_refund as tor
        left join t_order_info oi on tor.origin_order_code = oi.order_code
        left join t_vcs_order_info voi on voi.order_code = oi.order_code
        and voi.id = (SELECT MAX(id) FROM t_vcs_order_info WHERE order_code = voi.order_code)
        LEFT JOIN t_customer_service_order cso ON cso.order_code = oi.order_code
        where oi.is_deleted = false
        <if test="dto.orderCreator!=null and dto.orderCreator!=''">
            and cso.create_operator = #{dto.orderCreator}
        </if>
        <if test="dto.orderChannelList!=null and dto.orderChannelList.size() > 0">
            and oi.order_channel in
            <foreach item="item" collection="dto.orderChannelList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.orderType!=null">
            and oi.order_type = #{dto.orderType}
        </if>
        <if test="dto.refundOrderCode!=null and dto.refundOrderCode!=''">
            and tor.refund_order_code = #{dto.refundOrderCode}
        </if>
        <if test="dto.originOrderCode!=null and dto.originOrderCode!=''">
            and oi.parent_order_code = #{dto.originOrderCode}
        </if>
        <if test="dto.vin!=null and dto.vin!=''">
            <!--and #{dto.vin}  like CONCAT('%',SUBSTR(voi.car_vin, -6))-->
            and voi.car_vin_md5 = #{dto.vin}
        </if>
        <if test="dto.refundOrderStatus!=null and dto.refundOrderStatus!=''">
            and tor.refund_order_status in
            <foreach item="item" collection="dto.statusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.inControlId!=null and dto.inControlId!=''">
            and voi.incontrol_id_md5 = #{dto.inControlId}
        </if>
        <if test="dto.mobile!=null and dto.mobile!=''">
            and (oi.contact_phone_md5 = #{dto.mobile}  or oi.wx_phone_md5 = #{dto.mobile})
        </if>
        <if test="dto.startTime!=null and dto.startTime!=''">
            and tor.created_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime!=null and dto.endTime!=''">
            and tor.created_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.businessCode!=null and dto.businessCode!=''">
            and oi.business_code = #{dto.businessCode}
        </if>
        <if test="dto.createdTimeSort!=null and dto.createdTimeSort!='' and dto.createdTimeSort=='asc'">
            order by tor.created_time asc, tor.id asc
        </if>
        <if test="dto.createdTimeSort!=null and dto.createdTimeSort!='' and dto.createdTimeSort=='desc'">
            order by tor.created_time desc, tor.id desc
        </if>
    </select>

    <select id="getPageNew" resultType="com.jlr.ecp.order.api.refund.vo.OrderRefundPageRespNewVO">
        select
        tor.id, tor.refund_order_code as refundOrderCode, oi.parent_order_code as parentOrderCode,
        tor.refund_order_type as refundOrderType, tor.created_time createdTime, tor.created_by createdBy,
        tor.refund_source as refundSource,tor.refund_freight,

        oi.wx_phone as wxPhone,
        oi.wx_phone_mix as wxPhoneMix,
        oi.contact_phone_mix contactPhoneMix,
        oi.contact_phone contactPhone,
        oi.business_code,
        oi.freight_amount,

        oil.recipient_phone_mix AS recipientPhoneMix,
        oil.recipient_phone AS recipientPhone

        from t_order_refund as tor
        left join t_order_info oi on tor.origin_order_code = oi.order_code AND oi.is_deleted = 0
        left join t_order_refund_item tori on tor.refund_order_code = tori.refund_order_code AND tori.is_deleted = 0
        left join t_order_item item ON tori.order_item_code = item.order_item_code AND item.is_deleted = 0
        left join t_order_item_logistics oil on tor.origin_order_code = oil.order_code AND oil.is_deleted = 0

        where tor.is_deleted = 0
        <if test="dto.parentOrderCode != null and dto.parentOrderCode != ''">
            AND oi.parent_order_code = #{dto.parentOrderCode}
        </if>
        <if test="dto.orderCode != null and dto.orderCode != ''">
            AND oi.order_code = #{dto.orderCode}
        </if>
        <if test="dto.couponModelCode != null and dto.couponModelCode != ''">
            AND item.coupon_model_code = #{dto.couponModelCode}
        </if>
        <if test="dto.wxPhone != null and dto.wxPhone != ''">
            AND oi.wx_phone_md5 = #{dto.wxPhone}
        </if>
        <if test="dto.contactPhone != null and dto.contactPhone != ''">
            AND oi.contact_phone_md5 = #{dto.contactPhone}
        </if>
        <if test="dto.recipientPhone != null and dto.recipientPhone != ''">
            AND oil.recipient_phone_md5 = #{dto.recipientPhone}
        </if>
        <if test="dto.afterSalesStatusList!=null and dto.afterSalesStatusList.size() > 0">
            AND item.aftersales_status IN
            <foreach item="item" collection="dto.afterSalesStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.couponRefundStatusList!=null and dto.couponRefundStatusList.size() > 0">
            AND tor.coupon_refund_status IN
            <foreach item="item" collection="dto.couponRefundStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.logisticsRefundStatusList!=null and dto.logisticsRefundStatusList.size() > 0">
            AND tor.logistics_refund_status IN
            <foreach item="item" collection="dto.logisticsRefundStatusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.refundSourceList!=null and dto.refundSourceList.size() > 0">
            AND tor.refund_source IN
            <foreach item="item" collection="dto.refundSourceList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.refundOrderCode!=null and dto.refundOrderCode!=''">
            and tor.refund_order_code = #{dto.refundOrderCode}
        </if>
        <if test="dto.startTime!=null and dto.startTime!=''">
            and tor.created_time &gt;= #{dto.startTime}
        </if>
        <if test="dto.endTime!=null and dto.endTime!=''">
            and tor.created_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.couponModelCode!=null and dto.couponModelCode!=''">
            and item.coupon_model_code = #{dto.couponModelCode}
        </if>
        <if test="dto.businessCode!=null and dto.businessCode!=''">
            and oi.business_code = #{dto.businessCode}
        </if>
        <if test="dto.productSkuCode!=null and dto.productSkuCode!=''">
            and item.product_sku_code = #{dto.productSkuCode}
        </if>
        <if test="dto.kingdeeSkuCode!=null and dto.kingdeeSkuCode!=''">
            and item.kingdee_sku_code = #{dto.kingdeeSkuCode}
        </if>
        <if test="dto.refundOrderType!=null and dto.refundOrderType!=''">
            and tor.refund_order_type = #{dto.refundOrderType}
        </if>
        group by tor.refund_order_code
        <if test="dto.createdTimeSort!=null and dto.createdTimeSort!='' and dto.createdTimeSort=='asc'">
            order by tor.created_time asc, tor.id asc
        </if>
        <if test="dto.createdTimeSort!=null and dto.createdTimeSort!='' and dto.createdTimeSort=='desc'">
            order by tor.created_time desc, tor.id desc
        </if>
    </select>

    <select id="pageForGyy" resultType="com.jlr.ecp.order.api.refund.dto.OrderRefundForGyyPageRespDTO">
        select
            tor.refund_order_code,
            tor.refund_order_type,
            tor.origin_order_code,
            tor.refund_order_status,
            tor.logistics_refund_status,
            tor.refund_reason,
            tor.logistics_company_code,
            tor.logistics_code,
            tor.refund_order_fufilment_type,
            tor.created_time,
            tor.updated_time,
            tori.order_item_code,
            tori.refund_money,
            tori.refund_quantity
        from t_order_refund as tor
        left join t_order_refund_item as tori on tor.refund_order_code = tori.refund_order_code AND tori.is_deleted = 0
        where tor.is_deleted = 0
        <if test="dto.startTime !=null">
            and tor.updated_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime !=null">
            and tor.updated_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.refundOrderCode != null and dto.refundOrderCode != ''">
            and tor.refund_order_code = #{dto.refundOrderCode}
        </if>
        <if test="dto.logisticsRefundStatusList != null and dto.logisticsRefundStatusList.size() > 0">
            and tor.logistics_refund_status in
            <foreach item="status" collection="dto.logisticsRefundStatusList" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="dto.filfullments != null and dto.filfullments.size() > 0">
            and tor.refund_order_fufilment_type in
            <foreach item="filfullment" collection="dto.filfullments" open="(" separator="," close=")">
                #{filfullment}
            </foreach>
        </if>
        <if test="dto.businessCodes != null and dto.businessCodes.size() > 0">
            and exists (
                select 1 from t_order_info as toi
                where toi.order_code = tor.origin_order_code and toi.business_code in
                <foreach item="businessCode" collection="dto.businessCodes" open="(" separator="," close=")">
                    #{businessCode}
                </foreach>
            )
        </if>
    </select>

</mapper>
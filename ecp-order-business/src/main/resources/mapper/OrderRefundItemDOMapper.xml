<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper">

    <resultMap id="BaseResultMap" type="com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="refundOrderCode" column="refund_order_code" jdbcType="VARCHAR"/>
            <result property="orderItemCode" column="order_item_code" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="INTEGER"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
            <result property="revision" column="revision" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="OrderRefundItemResultMap" type="com.jlr.ecp.order.api.order.dto.OrderRefundStatusMapDTO">
        <result property="refundOrderStatus" column="refund_order_status" jdbcType="VARCHAR"/>
        <result property="orderItemCode" column="order_item_code" jdbcType="VARCHAR"/>
        <result property="refundAmount" column="refund_money_amount" jdbcType="INTEGER"/>
        <result property="refundQuantity" column="refund_quantity" jdbcType="INTEGER"/>
    </resultMap>



    <sql id="Base_Column_List">
        id,refund_order_code,order_item_code,
        tenant_id,created_by,created_time,
        updated_by,updated_time,is_deleted,
        revision
    </sql>

    <select id="getRefundOrderStatusByOrderItemCode" resultType="java.lang.Integer">
        SELECT r.refund_order_status
            FROM t_order_refund_item ri
            LEFT JOIN t_order_refund r ON r.refund_order_code = ri.refund_order_code
            WHERE ri.id IN (
                SELECT MAX(id)
                FROM t_order_refund_item
              where is_deleted = 0 and tenant_id = 1
                GROUP BY order_item_code
            )
            and ri.is_deleted = 0 and r.is_deleted = 0 and ri.tenant_id = 1 and ri.order_item_code = #{orderItemCode}
    </select>

    <select id="getOnlyRefundTotalMoneyByOrderCode" resultType="java.lang.Integer">
        select IFNULL(sum(tori.refund_money),0)
        from t_order_info info
        inner join t_order_item item on info.order_code = item.order_code
        inner join t_order_refund_item tori on tori.order_item_code =item.order_item_code
        inner join t_order_refund tor on tor.refund_order_code   = tori.refund_order_code
        where tor.refund_order_type = 2 and info.order_code = #{orderCode}
        and tor.logistics_refund_status  =  #{refundLogisticsStatus}

    </select>

    <select id="getLogisticsAlreadyRefundMoney" resultType="java.lang.Integer">
        select IFNULL(sum(tori.refund_money),0)
        from t_order_refund_item tori
        inner join t_order_refund tor on tor.refund_order_code   = tori.refund_order_code
        where tori.is_deleted = 0 and tor.is_deleted = 0 and tori.order_item_code = #{orderItemCode}
        and tor.logistics_refund_status = #{refundLogisticsStatus}
        <if test="orderRefundItemId != null and orderRefundItemId != ''">
            and tori.id != #{orderRefundItemId}
        </if>


    </select>

    <select id="getLogisticsCodeByOrderItemCode" resultType="java.lang.String">
        select max(logistics_code)
        from t_order_refund_item tori
        inner join t_order_refund tor on tor.refund_order_code   = tori.refund_order_code
        where tori.is_deleted = 0 and tor.is_deleted = 0 and tori.order_item_code = #{orderItemCode}
    </select>

    <select id="getAllLogisticsAlreadyRefundMoney" resultType="java.lang.Integer">
        select IFNULL(sum(tori.refund_money),0)
        from t_order_refund_item tori
        inner join t_order_refund tor on tor.refund_order_code   = tori.refund_order_code
        where tori.is_deleted = 0 and tor.is_deleted = 0
        and tor.logistics_refund_status = #{refundLogisticsStatus}
        <if test="orderItemCodeList != null and orderItemCodeList.size() > 0">
            and tori.order_item_code  in
            <foreach item="item" collection="orderItemCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="getAllLogisticsAlreadyRefundMoneyIncludeFreight" resultType="java.lang.Integer">
        select IFNULL(sum(tori.refund_money),0)+IFNULL(sum(tori.refund_freight),0)
        from t_order_refund_item tori
        inner join t_order_refund tor on tor.refund_order_code   = tori.refund_order_code
        where tori.is_deleted = 0 and tor.is_deleted = 0
        and tor.logistics_refund_status = #{refundLogisticsStatus}
        <if test="orderItemCodeList != null and orderItemCodeList.size() > 0">
            and tori.order_item_code  in
            <foreach item="item" collection="orderItemCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>


    <select id="getRefundOrderStatusByOrderItemCodeList" resultMap="OrderRefundItemResultMap">
        SELECT ri.order_item_code,r.refund_order_status, r.refund_money_amount, ri.refund_quantity
            FROM t_order_refund_item ri
            LEFT JOIN t_order_refund r ON r.refund_order_code = ri.refund_order_code
            WHERE ri.id IN (
                SELECT MAX(id)
                FROM t_order_refund_item
              where is_deleted = 0 and tenant_id = 1
                GROUP BY order_item_code
            )
            and ri.is_deleted = 0 and r.is_deleted = 0 and ri.tenant_id = 1 and ri.order_item_code in
            <foreach item="item" collection="orderItemCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <select id="getRefundInfoByOrderItemCodeList" resultMap="OrderRefundItemResultMap">
        SELECT ri.order_item_code,r.refund_order_status, r.refund_money_amount, ri.refund_quantity
        FROM t_order_refund_item ri
        LEFT JOIN t_order_refund r ON r.refund_order_code = ri.refund_order_code
        WHERE ri.is_deleted = 0
          and r.is_deleted = 0
          and ri.tenant_id = 1
          and ri.order_item_code in
        <foreach item="item" collection="orderItemCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by ri.id
    </select>

    <select id="getRefundAmounts"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.ValetDashboardTempDTO">
        SELECT
        COALESCE(SUM(CASE WHEN o.order_channel = 5 THEN r.refund_money_amount ELSE 0 END), 0) AS valetRefundAmount,
        COALESCE(SUM(r.refund_money_amount), 0) AS totalRefundAmount
        FROM t_order_info o
        JOIN t_order_refund r
        ON r.origin_order_code = o.order_code  -- 仅保留连接条件
        WHERE
        -- 主表过滤条件
        o.payment_status = 1
        AND o.order_type != 0
        AND o.order_status IN (6, 7)
        AND o.is_deleted = 0
        AND o.business_code = 'BUSINESS:001'

        -- 关联表过滤条件
        AND r.is_deleted = 0
        AND r.refund_order_status IN (5, 6)

        -- 时间范围
        AND o.created_time BETWEEN #{start} AND #{end}

        -- 渠道过滤
        <if test="orderChannel != null and orderChannel != ''">
            AND o.order_code LIKE CONCAT(#{orderChannel}, '%')
        </if>
    </select>
    <select id="getTotalRefundAmounts"
            resultType="com.jlr.ecp.order.controller.admin.dashboard.dto.ValetDashboardTempDTO">
        SELECT
        COALESCE(SUM(r.refund_money_amount), 0) AS totalRefundAmount
        FROM t_order_info o
        JOIN t_order_refund r
        ON r.origin_order_code = o.order_code  -- 仅保留连接条件
        WHERE
        -- 主表过滤条件
        o.payment_status = 1
        AND o.order_type != 0
        AND o.order_status IN (6, 7)
        AND o.is_deleted = 0

        -- 关联表过滤条件
        AND r.is_deleted = 0
        AND r.refund_order_status IN (5, 6)

        -- 时间范围
        AND o.created_time BETWEEN #{start} AND #{end}
        <if test="businessCode != null and businessCode != ''">
            AND o.business_code = #{businessCode}
        </if>
    </select>
    <select id="getRefundOrderItemList"
            resultType="com.jlr.ecp.order.api.refund.vo.RefundOrderItemVO">
        SELECT
        tori.refund_point AS refundPoint,
        tori.refund_order_code AS refundOrderCode,
        tori.refund_money AS refundMoney,
        item.order_code AS orderCode,
        item.order_item_code AS orderItemCode,
        item.product_image_url AS productImageUrl,
        item.product_sku_code AS productSkuCode,
        item.product_name AS productName,
        item.coupon_model_code AS couponModelCode,
        item.kingdee_sku_code AS kingdeeSkuCode,
        coupon.valid_start_time AS validStartTime,
        coupon.valid_end_time AS validEndTime,
        item.product_quantity AS productQuantity,
        item.product_sale_price AS productSalePrice,
        item.cost_amount AS costAmount,
        item.point_amount AS pointAmount,
        item.aftersales_status AS afterSalesStatus,
        odd.discount_amount AS discountAmount,
        odd.coupon_code AS discountCouponCode,
        tor.coupon_refund_status AS couponRefundStatus,
        tor.logistics_refund_status AS logisticsRefundStatus
        FROM t_order_refund_item tori
        LEFT JOIN t_order_refund tor ON tori.refund_order_code = tor.refund_order_code AND tor.is_deleted = 0
        LEFT JOIN t_order_item item on tori.order_item_code = item.order_item_code AND item.is_deleted = 0
        LEFT JOIN t_order_coupon_detail coupon ON item.order_item_code = coupon.order_item_code AND coupon.is_deleted = 0
        LEFT JOIN t_order_discount_detail odd ON item.order_item_code = odd.order_item_code AND odd.is_deleted = 0
        WHERE tori.refund_order_code IN
        <foreach item="item" collection="refundOrderCodeList" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND item.is_deleted = 0
        group by tori.refund_order_code, tori.order_refund_item_code
    </select>

    <select id="listRefundByOrderCode" resultType="com.jlr.ecp.order.api.refund.dto.OrderRefundItemWithStatusDTO">
        select
            tori.refund_money,
            tori.order_item_code,
            tori.refund_order_code,
            tori.refund_quantity,
            tori.refund_freight,
            tor.refund_order_status
        from t_order_refund_item tori
        inner join t_order_refund tor on tori.refund_order_code = tor.refund_order_code
        where tor.origin_order_code in
        <foreach item="orderCode" collection="orderCodes" open="(" separator="," close=")">
            #{orderCode}
        </foreach>
        <if test="refundStatusList!=null and refundStatusList.size() > 0">
            and tor.refund_order_status in
            <foreach item="refundStatus" collection="refundStatusList" open="(" separator="," close=")">
                #{refundStatus}
            </foreach>
        </if>
    </select>

</mapper>
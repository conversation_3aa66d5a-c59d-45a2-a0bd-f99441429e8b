spring:
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务
  # Sleuth 配置
  sleuth:
    # 是否启用 Sleuth
    enabled: true
    # 链路追踪采样率，1.0 表示 100% 采样
    sampler:
      probability: 1.0
    # Web 配置
    web:
      # 是否启用 Web 链路追踪
      enabled: true
  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********.401，而是直接 **********401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: false # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: false # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui.html

knife4j:
  enable: false # 2.2 是否开启 Swagger 文档的 Knife4j UI 界面
  setting:
    language: zh_cn

# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      # 重要说明：如果将配置放到 Nacos 时，请注意将 id-type 设置为对应 DB 的类型，否则会报错；详细见 https://gitee.com/zhijiantianya/lc-cloud/issues/I5W2N0 讨论
      id-type: AUTO # "智能"模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
#      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
#      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
#      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  type-aliases-package: ${lc.info.base-package}.dal.dataobject

--- #################### RPC 远程调用相关配置 ####################

--- #################### MQ 消息队列相关配置 ####################

spring:
  cloud:
    # Spring Cloud Stream 配置项，对应 BindingServiceProperties 类
    stream:
      function:
        definition: busConsumer;smsSendConsumer;mailSendConsumer
      # Binding 配置项，对应 BindingProperties Map
      bindings:
        smsSend-out-0:
          destination: system_sms_send
        smsSendConsumer-in-0:
          destination: system_sms_send
          group: system_sms_send_consumer_group
        mailSend-out-0:
          destination: system_mail_send
        mailSendConsumer-in-0:
          destination: system_mail_send
          group: system_mail_send_consumer_group
      # Spring Cloud Stream RocketMQ 配置项
      rocketmq:
        # RocketMQ Binder 配置项，对应 RocketMQBinderConfigurationProperties 类
        binder:
          name-server: 121.36.251.176:9876 # RocketMQ Namesrv 地址
        default: # 默认 bindings 全局配置
          producer: # RocketMQ Producer 配置项，对应 RocketMQProducerProperties 类
            group: system_producer_group # 生产者分组
            send-type: SYNC # 发送模式，SYNC 同步
        bindings:
          springCloudBusInput:
            consumer:
              message-model: BROADCASTING # 重要，解决 Spring Cloud Bus RocketMQ 默认不是 BROADCASTING 广播消费的问题

    # Spring Cloud Bus 配置项，对应 BusProperties 类
    bus:
      enabled: true # 是否开启，默认为 true
      id: ${spring.application.name}:${server.port} # 编号，Spring Cloud Alibaba 建议使用"应用:端口"的格式
      destination: springCloudBus # 目标消息队列，默认为 springCloudBus

--- #################### 定时任务相关配置 ####################

xxl:
  job:
    executor:
      appname: ${spring.application.name} # 执行器 AppName
      logpath: ${user.home}/logs/xxl-job/${spring.application.name} # 执行器运行日志文件存储磁盘路径
    accessToken: default_token # 执行器通讯TOKEN

--- #################### 验证码相关配置 ####################

aj:
  captcha:
    jigsaw: classpath:images/jigsaw # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    pic-click: classpath:images/pic-click # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    cache-type: redis # 缓存 local/redis...
    cache-number: 1000 # local 缓存的阈值,达到这个值，清除缓存
    timing-clear: 180 # local定时清除过期缓存(单位秒),设置为0代表不执行
    type: blockPuzzle # 验证码类型 default两种都实例化。 blockPuzzle 滑块拼图 clickWord 文字点选
    water-mark: RR低代码 # 右下角水印文字(我的水印)，可使用 https://tool.chinaz.com/tools/unicode.aspx 中文转 Unicode，Linux 可能需要转 unicode
    interference-options: 0 # 滑动干扰项(0/1/2)
    req-frequency-limit-enable: false # 接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 5 # 验证失败5次，get接口锁定
    req-get-lock-seconds: 10 # 验证失败后，锁定时间间隔
    req-get-minute-limit: 30 # get 接口一分钟内请求数限制
    req-check-minute-limit: 60 # check 接口一分钟内请求数限制
    req-verify-minute-limit: 60 # verify 接口一分钟内请求数限制

--- #################### RR低代码相关配置 ####################

lc:
  info:
    version: 1.0.0
    base-package: com.jlr.ecp.order
  web:
    admin-ui:
      url: http://dashboard.roselife.com # Admin 管理后台 UI 的地址
  swagger:
    title: 管理后台
    description: 提供管理员管理的所有功能
    version: ${lc.info.version}
    base-package: ${lc.info.base-package}
  captcha:
    enable: false # 验证码的开关，默认为 true；
  error-code: # 错误码相关配置项
    constants-class-list:
      - com.jlr.ecp.product.enums.ErrorCodeConstants
  tenant: # 多租户相关配置项
    enable: true
    ignore-urls:
      - /admin-api/system/tenant/get-id-by-name # 基于名字获取租户，不许带租户编号
      - /admin-api/system/captcha/get-image # 获取图片验证码，和租户无关
      - /admin-api/system/captcha/get # 获取图片验证码，和租户无关
      - /admin-api/system/captcha/check # 校验图片验证码，和租户无关
      - /admin-api/system/sms/callback/* # 短信回调接口，无法带上租户编号
      - /rpc-api/system/tenant/valid # 防止递归。避免调用 /rpc-api/system/tenant/valid 接口时，又去触发 /rpc-api/system/tenant/valid 去校验
      - /rpc-api/system/tenant/id-list # 获得租户列表的时候，无需传递租户编号
      - /rpc-api/system/error-code/* # 错误码的自动创建与下载的接口，无法带上租户编号
      - /rpc-api/system/oauth2/token/check # 访问令牌校验时，无需传递租户编号；主要解决上传文件的场景，前端不会传递 tenant-id！
    ignore-tables:
      - system_tenant
      - system_tenant_package
      - system_dict_data
      - system_dict_type
      - system_error_code
      - system_menu
      - system_sms_channel
      - system_sms_template
      - system_sms_log
      - system_sensitive_word
      - system_oauth2_client
      - system_mail_account
      - system_mail_template
      - system_mail_log
      - system_notify_template
  sms-code: # 短信验证码相关的配置项
    expire-times: 10m
    send-frequency: 1m
    send-maximum-quantity-per-day: 10
    begin-code: 9999 # 这里配置 9999 的原因是，测试方便。
    end-code: 9999 # 这里配置 9999 的原因是，测试方便。
  encrypt:
    private-key:  ${RSA_2048_PRIVATE}
    public-key:  ${RSA_2048_PUBLIC}
debug: false
dataCenterFlag: product-data-center
workFlag: product-work

template:
  url: https://ecp-static-dev.jaguarlandrover.cn/file/RemoteServicePackageCodeUploadTemplate.xlsx # 上传模板的地址

taskCode:

  order:
    success: order-successful-code #下单成功，通知任务code
    cancel: order-cancel-code    #订单取消成功，通知任务code
    ecoupon:
      refund: order-ecoupon-refund-code #LRE订单退款成功，通知任务code
  service:
    activate: service-activate-code #服务激活，通知任务code

# 精选好物通知配置
branded-goods:
  kafka:
    topics:
      partial-delivery: branded-goods-partial-delivery-topic
      return-refund: branded-goods-return-refund-topic
      refund-only: branded-goods-refund-only-topic
      payment-success: branded-goods-payment-success-topic

confirm:
  receipt:
    maxTime: 2
    minTime: 15
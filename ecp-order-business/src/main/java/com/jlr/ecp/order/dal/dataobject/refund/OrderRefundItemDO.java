package com.jlr.ecp.order.dal.dataobject.refund;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_order_refund_item
 *
 * <AUTHOR>
 * @TableName t_order_refund_item
 */
@TableName(value = "t_order_refund_item")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderRefundItemDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 退单订单号;退单订单号
     */
    @TableField(value = "refund_order_code")
    private String refundOrderCode;

    /**
     * 订单item号;订单item号
     */
    @TableField(value = "order_item_code")
    private String orderItemCode;

    /**
     * 订单item退款号;订单item退款号
     * 用于取消卡券的时候传入进行安全验证
     * 生成规则为R+order_item_code
     */
    @TableField(value = "order_refund_item_code")
    private String orderRefundItemCode;

    /**
     *  指定服务过期时间
     * */
    @TableField(value = "service_end_date")
    private LocalDateTime serviceEndDate;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

    /**
     * 退单实付金额(单位分)
     */
    @TableField(value = "refund_money")
    private Integer refundMoney;

    /**
     * 退单实付积分
     */
    @TableField(value = "refund_point")
    private Integer refundPoint;

    /**
     * 退单数量
     */
    @TableField(value = "refund_quantity")
    private Integer refundQuantity;

    /**
     * 退单运费(单位分)
     */
    @TableField(value = "refund_freight")
    private Integer refundFreight;
}
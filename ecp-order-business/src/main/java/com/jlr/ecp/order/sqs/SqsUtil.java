package com.jlr.ecp.order.sqs;


import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.core.internal.http.loader.DefaultSdkHttpClientBuilder;
import software.amazon.awssdk.http.SdkHttpConfigurationOption;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.*;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.auth.StsAssumeRoleCredentialsProvider;
import software.amazon.awssdk.utils.AttributeMap;

import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.sqs
 * @className: SqsService
 * @author: gaoqig
 * @description: SQS工具
 * @date: 2025/3/17 14:19
 * @version: 1.0
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "spring.cloud.aws.sqs.enabled", havingValue = "true", matchIfMissing = true)
public class SqsUtil {
    @Value("${sqs.coupon.status.queue.host}${sqs.coupon.status.queue.path}")
    String sqlQueueUrl;

    @Value("${sqs.arn}")
    String sqsArn;

    @Value("${sqs.role.arn}")
    String userRoleArn;

    @Value("${cloud.aws.region.static}")
    String awsRegion;


    private static SqsClient sqsClient = null;

    public SqsClient getSqsClient() {
        if (sqsClient != null){
            return sqsClient;
        }
        log.info("LRE Status Listening:开始初始化sqs client, role arn->{} \n sqs arn->{}", userRoleArn, sqsArn);
        return SqsClient.builder()
                // Private Network in Landing Zone
//                .endpointOverride(URI.create(SQS_PRIVATE_ENDPOINT))
                // Public Network
                .endpointOverride(URI.create(sqlQueueUrl))
                .region(Region.of(awsRegion))
                .httpClient(new DefaultSdkHttpClientBuilder().buildWithDefaults(
                        AttributeMap.builder()
                                .put(SdkHttpConfigurationOption.TRUST_ALL_CERTIFICATES, true)
                                .build()
                ))
                // Assume role
                .credentialsProvider(loadCredentialsWithDoubleAssumeRole(userRoleArn, sqsArn))
                .build();
    }

    /**
     * Receive message from SQS
     * Applicable for Standard, FIFO and Dead Letter Queue(DLQ) queue.
     * @param queueUrl
     * @param maxNumberOfMessages
     * @return List<Message>
     */
    public List<Message> receiveMessage(String queueUrl, int maxNumberOfMessages, int waitTimeSeconds, SqsClient client) {

        try {
            ReceiveMessageRequest receiveMessageRequest = ReceiveMessageRequest.builder()
                    .queueUrl(queueUrl)
                    .maxNumberOfMessages(maxNumberOfMessages)
                    .waitTimeSeconds(waitTimeSeconds)
                    .build();
            return client.receiveMessage(receiveMessageRequest).messages();
        } catch (SqsException e) {
            log.info("LRE Status Listening: Receive message from SQS exception:{}", e.awsErrorDetails().errorMessage());
            throw e;
        }
    }

    /**
     * Delete message from SQS
     * @param queueUrl
     * @param m
     */
    public void deleteMessage(String queueUrl, Message m, SqsClient client) {
        try {
            DeleteMessageRequest deleteMessageRequest = DeleteMessageRequest.builder()
                    .queueUrl(queueUrl)
                    .receiptHandle(m.receiptHandle())
                    .build();
            client.deleteMessage(deleteMessageRequest);

        } catch (SqsException e) {
            log.info("Delete message from SQS exception：{}", e.awsErrorDetails().errorMessage());
        }
    }

    /**
     * Delete message batch from SQS
     * @param queueUrl
     * @param messages
     */
    public void deleteMessageBatch(String queueUrl, List<Message> messages, SqsClient client) {
        try {
            List<DeleteMessageBatchRequestEntry> entries = new ArrayList<>();
            messages.forEach(m -> entries.add(DeleteMessageBatchRequestEntry.builder()
                    .receiptHandle(m.receiptHandle())
                    .id(m.messageId())
                    .build()));

            if (CollUtil.isEmpty(entries)) {
                return;
            }
            DeleteMessageBatchRequest deleteMessageBatchRequest = DeleteMessageBatchRequest.builder()
                    .queueUrl(queueUrl)
                    .entries(entries)
                    .build();
            client.deleteMessageBatch(deleteMessageBatchRequest);
        } catch (SqsException e) {
            log.info("Delete message batch from SQS exception：{}", e.awsErrorDetails().errorMessage());
        }
    }

    public static StsAssumeRoleCredentialsProvider loadCredentialsWithDoubleAssumeRole(String userRoleArn, String sqsRoleArn) {
        // 创建初始的 STS 客户端
        StsClient initialStsClient = StsClient.builder()
                .region(Region.CN_NORTHWEST_1)
                // 如果需要使用非 AWS 的 AK-SK 凭证，取消下面这行的注释
//                 .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(ACCESS_KEY_ID, SECRET_ACCESS_KEY)))
                .build();

        // 第一次角色切换：获取 A role 的凭证
        StsAssumeRoleCredentialsProvider roleACredentials = StsAssumeRoleCredentialsProvider.builder()
                .stsClient(initialStsClient)
                .refreshRequest(r -> r.roleArn(userRoleArn).roleSessionName("session-" + UUID.randomUUID()))
                .build();

        // 使用 A role 的凭证创建新的 STS 客户端
        StsClient stsClientWithRoleA = StsClient.builder()
                .region(Region.CN_NORTHWEST_1)
                .credentialsProvider(roleACredentials)
                .build();

        // 第二次角色切换：使用 A role 的凭证获取 B role 的凭证
        return StsAssumeRoleCredentialsProvider.builder()
                .stsClient(stsClientWithRoleA)
                .refreshRequest(r -> r.roleArn(sqsRoleArn).roleSessionName("session-sns-" + UUID.randomUUID()))
                .build();
    }
}

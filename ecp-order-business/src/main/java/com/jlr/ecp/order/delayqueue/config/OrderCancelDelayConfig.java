package com.jlr.ecp.order.delayqueue.config;


import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 订单取消延迟时间配置
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderCancelDelayConfig {
    
    /**
     * BG+LRE业务线：15分钟
     */
    private static final long BG_LRE_DELAY_MILLS = Constants.BG_LRE_PAY_TIMEOUT_MILLS;
    
    /**
     * VCS业务线：2.5小时
     */
    private static final long VCS_DELAY_MILLS = Constants.VCS_PAY_TIMEOUT_MILLS;
    
    /**
     * 判断是否为VCS业务线
     *
     * @param businessCode 业务线编码
     * @return true-VCS业务线，false-其他业务线
     */
    public boolean isVcsBusinessLine(String businessCode) {
        if (StringUtils.isEmpty(businessCode)) {
            return false;
        }
        return BusinessIdEnum.VCS.getCode().equals(businessCode);
    }

    /**
     * 判断是否为BG业务线
     *
     * @param businessCode 业务线编码
     * @return true-BG业务线，false-其他业务线
     */
    public boolean isBgBusinessLine(String businessCode) {
        if (StringUtils.isEmpty(businessCode)) {
            return false;
        }
        return BusinessIdEnum.BRAND_GOODS.getCode().equals(businessCode);
    }

    /**
     * 判断是否为LRE业务线
     *
     * @param businessCode 业务线编码
     * @return true-LRE业务线，false-其他业务线
     */
    public boolean isLreBusinessLine(String businessCode) {
        if (StringUtils.isEmpty(businessCode)) {
            return false;
        }
        return BusinessIdEnum.LRE.getCode().equals(businessCode);
    }
    
    /**
     * 根据业务线编码获取延迟时间
     * 
     * @param businessCode 业务线编码
     * @return 延迟时间（毫秒）
     */
    public long getDelayTime(String businessCode) {
        if (isVcsBusinessLine(businessCode)) {
            return VCS_DELAY_MILLS; // VCS: 2小时
        } else {
            return BG_LRE_DELAY_MILLS; // BG和LRE: 15分钟
        }
    }
    
    /**
     * 计算实际需要延迟的时间
     * 
     * @param businessCode 业务线编码
     * @param sendTime 发送时间
     * @return 实际延迟时间（毫秒）
     */
    public long calculateActualDelay(String businessCode, LocalDateTime sendTime) {
        long totalDelayTime = getDelayTime(businessCode);
        long elapsedTime = Duration.between(sendTime, LocalDateTime.now()).toMillis();
        long actualDelay = Math.max(0, totalDelayTime - elapsedTime);
        
        log.info("业务线: {}, 总延迟时间: {}ms, 已过时间: {}ms, 实际延迟: {}ms",
                businessCode, totalDelayTime, elapsedTime, actualDelay);
        
        return actualDelay;
    }
    
    /**
     * 获取业务线描述
     *
     * @param businessCode 业务线编码
     * @return 业务线描述
     */
    public String getBusinessLineDescription(String businessCode) {
        if (isVcsBusinessLine(businessCode)) {
            return BusinessIdEnum.VCS.getName() + "(2个半小时)";
        } else if (isBgBusinessLine(businessCode)) {
            return BusinessIdEnum.BRAND_GOODS.getName() + "(15分钟)";
        } else if (isLreBusinessLine(businessCode)) {
            return BusinessIdEnum.LRE.getName() + "(15分钟)";
        } else {
            return "未知业务线(默认15分钟)";
        }
    }
}

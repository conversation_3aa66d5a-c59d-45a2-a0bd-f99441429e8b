package com.jlr.ecp.order.service.order.customer.order;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.order.dal.dataobject.customer.order.CustomerServiceOrderDO;

/**
 * <AUTHOR>
 * @description 针对表【t_customer_service_order(t_customer_service_order)】的数据库操作Service
 * @createDate 2023-12-20 10:41:04
 */
public interface CustomerServiceOrderDOService extends IService<CustomerServiceOrderDO> {
    // 您可以在这里定义额外的业务方法
}
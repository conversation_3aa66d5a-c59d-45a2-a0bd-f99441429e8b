package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * t_order_info
 *
 * <AUTHOR>
 * @TableName t_order_info
 */
@TableName(value = "t_order_info")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderInfoDO extends BaseDO {
    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 用户编码;用户编码
     */
    @TableField(value = "consumer_code")
    private String consumerCode;

    /**
     * 订单编码;订单编码，编码规则：业务线code+时间戳(日期+时分秒)+6位随机数
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 原始订单金额;原始订单金额，单位分
     */
    @TableField(value = "original_fee_total_amount")
    private Integer originalFeeTotalAmount;

    /**
     * 应付金额;订单金额，单位分
     */
    @TableField(value = "fee_total_amount")
    private Integer feeTotalAmount;

    /**
     * 实付金额;实付金额，单位分
     */
    @TableField(value = "cost_amount")
    private Integer costAmount;

    /**
     * 折扣金额;折扣金额，单位分
     */
    @TableField(value = "discount_total_amount")
    private Integer discountTotalAmount;

    /**
     * 运费金额;运费金额，虚拟商品为0
     */
    @TableField(value = "freight_amount")
    private Integer freightAmount;

    /**
     * 不含税总金额
     */
    @TableField(value = "exclude_tax_amount")
    private Integer excludeTaxAmount;

    /**
     * 税费总金额
     */
    @TableField(value = "tax_amount")
    private Integer taxAmount;

    /**
     * 父订单号;父订单号，用于标记多个子订单从属于哪个订单，当只有一个订单时为本单号
     */
    @TableField(value = "parent_order_code")
    private String parentOrderCode;

    /**
     * 主单状态
     * 订单状态;订单状态；1：已下单 2：已支付 3：订单完成 4：订单关闭  5：发起整单退款申请 6：发起部分退款申请  7：订单整单取消
     */
    @TableField(value = "order_status")
    private Integer orderStatus;

    /**
     * 支付状态;支付状态；0:未支付 1：已支付
     */
    @TableField(value = "payment_status")
    private Integer paymentStatus;

    /**
     * 支付时间;支付时间
     */
    @TableField(value = "payment_time")
    private LocalDateTime paymentTime;

    /**
     * 订单提交时间;订单提交时间
     */
    @TableField(value = "order_time")
    private LocalDateTime orderTime;

    /**
     * 0：聚合父订单
     * 订单类型;订单类型；1：VCS，2：PIVI 3：Brand_goods，4：虚拟组合
     */
    @TableField(value = "order_type")
    private Integer orderType;

    /**
     * 下单渠道;下单渠道 1：捷豹小程序 2：路虎小程序 3：官网
     */
    @TableField(value = "order_channel")
    private Integer orderChannel;


    /**
     * 微信昵称
     */
    @TableField(value = "wx_nick_name")
    private String wxNickName;

    /**
     * 微信授权手机
     */
    @TableField(value = "wx_phone")
    private String wxPhone;

    /**
     * 微信授权手机
     */
    @TableField(value = "wx_phone_mix")
    private String wxPhoneMix;

    /**
     * 微信授权手机
     */
    @TableField(value = "wx_phone_md5")
    private String wxPhoneMd5;


    /**
     * 客户留言信息;客户留言信息
     */
    @TableField(value = "customer_remark")
    private String customerRemark;

    /**
     * 客户联系手机;客户联系手机
     */
    @TableField(value = "contact_phone")
    private String contactPhone;

    /**
     * 客户联系手机;客户联系手机
     */
    @TableField(value = "contact_phone_mix")
    private String contactPhoneMix;

    /**
     * 客户联系手机;客户联系手机
     */
    @TableField(value = "contact_phone_md5")
    private String contactPhoneMd5;

    /**
     * 操作员备注
     */
    @TableField(value = "operator_remark")
    private String operatorRemark;

    /**
     * 业务线编码
     */
    @TableField(value = "business_code")
    private String businessCode;

    /**
     * 退款状态;退款状态  0：未退款 1：部分退款 2：全退款
     */
    @TableField(value = "refund_status")
    private Integer refundStatus;

    /**
     * 订单关闭原因;订单退款状态
     */
    @TableField(value = "order_close_reason")
    private String orderCloseReason;
    
    /**
     * 领取礼物地址：0否 1是 2无需
     */
    @TableField(value = "gift_address")
    private Integer giftAddress;


    /**
     * bg订单 实物状态
     * 90101：待支付  90301：订单完成  90401：订单关闭  90501：售后处理中，90201：待发货，90202：部分发货，90203：全部发货
     */
    @TableField(value = "logistics_status")
    private Integer logisticsStatus;

    /**
     * lre 订单
     * 90101：待支付 90401：订单关闭  90501：售后处理中，90201：待发放，90202：待核销，90301：已核销
     */
    @TableField(value = "coupon_status")
    private Integer couponStatus;

    /**
     * 订单完成时间
     */
    @TableField(value = "completed_time")
    private LocalDateTime completedTime;

    /**
     * 积分
     */
    @TableField(value = "point_amount")
    private Integer pointAmount;

    /**
     * 分账状态 0-无需分账 1-待分账 2-分账中 3-已分账
     */
    @TableField(value = "independent_status")
    private Integer independentStatus;

    /**
     * 订单关闭时间
     */
    @TableField(value = "closed_time")
    private LocalDateTime closedTime;

    /**
     * 税收编码
     */
    @TableField(value = "freight_code")
    private String freightCode;

    /**
     * 运费费率
     */
    @TableField(value = "freight_tax")
    private BigDecimal freightTax;

    /**
     * 运费费率更新时间
     */
    @TableField(value = "freight_tax_update_time")
    private LocalDateTime freightTaxUpdateTime;

    /**
     * 售价不含税订单金额
     */
    @TableField(value = "original_exclude_tax_amount")
    private Integer originalExcludeTaxAmount;

    /**
     * 商品数量
     */
    @TableField(value = "product_quantity")
    private Integer productQuantity;

    /**
     * 是否同步给erp系统
     * 1：已同步 0：未同步》
     */
    @TableField(value = "erp_sync")
    private Integer erpSync;
}
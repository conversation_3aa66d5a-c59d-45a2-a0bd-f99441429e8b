package com.jlr.ecp.order.dal.mysql.refund;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.order.api.refund.dto.OrderRefundForGyyPageReqDTO;
import com.jlr.ecp.order.api.refund.dto.OrderRefundForGyyPageRespDTO;
import com.jlr.ecp.order.api.refund.dto.OrderRefundPageReqDTO;
import com.jlr.ecp.order.api.refund.dto.OrderRefundPageReqNewDTO;
import com.jlr.ecp.order.api.refund.vo.OrderRefundPageRespNewVO;
import com.jlr.ecp.order.api.refund.vo.OrderRefundPageVO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【t_order_refund(t_order_refund)】的数据库操作Mapper
* @createDate 2024-01-15 11:28:44
* @Entity com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO
*/
@Mapper
public interface OrderRefundDOMapper extends BaseMapper<OrderRefundDO> {

    /**
     * 分页列表
     * @param page 分页
     * @param dto 参数
     * @return 返回 OrderRefundPageVO
     */
    Page<OrderRefundPageVO> getPage(Page<OrderRefundDO> page, @Param("dto") OrderRefundPageReqDTO dto);

    /**
     * 退单列表分页(LRE)
     * @param page
     * @param dto
     * @return
     */
    Page<OrderRefundPageRespNewVO> getPageNew(Page<OrderRefundDO> page, @Param("dto") OrderRefundPageReqNewDTO dto);

    Page<OrderRefundForGyyPageRespDTO> pageForGyy(Page<OrderRefundDO> page, @Param("dto") OrderRefundForGyyPageReqDTO dto);

}
package com.jlr.ecp.order.service.logistic;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil;
import com.jlr.ecp.logistics.api.packageinfo.dto.PackageInfoQueryRespDTO;
import com.jlr.ecp.order.api.order.dto.OrderLogisticStatusChangeReqDto;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemLogisticsDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemLogisticsDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.logistic.DeliveryNotificationEnum;
import com.jlr.ecp.order.enums.order.BgOrderItemLogisticsStatusEnum;
import com.jlr.ecp.order.enums.order.OrderItemLogisticsStatusEnum;
import com.jlr.ecp.order.enums.order.OrderLogisticsStatusEnum;
import com.jlr.ecp.order.service.order.OrderItemLogisticsDOService;
import com.jlr.ecp.order.util.PIPLDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.utils.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.logistic
 * @className: OrderItemLogisticService
 * @author: gaoqig
 * @description: 订单行物流信息服务
 * @date: 2025/4/3 17:36
 * @version: 1.0
 */
@Service
@Slf4j
public class OrderItemLogisticService {
    @Resource
    private OrderItemLogisticsDOService orderItemLogisticsDOService;
    @Resource
    private OrderItemLogisticsDOMapper orderItemLogisticsDOMapper;
    @Resource
    private OrderItemDOMapper orderItemDOMapper;
    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;
    @Resource
    private PIPLDataUtil piplDataUtil;

    @Transactional(rollbackFor = Exception.class)
    public void doUpdateLogisticStatus(OrderInfoDO orderInfoDO, List<OrderItemDO> orderItemDOList,
                                        List<OrderItemLogisticsDO> orderItemLogisticsDOS){
        orderInfoDOMapper.updateById(orderInfoDO);
        orderItemDOMapper.updateBatch(orderItemDOList);
        orderItemLogisticsDOMapper.updateBatch(orderItemLogisticsDOS);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer updateOrderLogisticSign(){
        LocalDateTime now = LocalDateTime.now();
        // 只更新180天内的数据
        LocalDateTime sendTimeMin = now.minusDays(180);
        // 获取已发货的订单物流数据
        List<OrderItemLogisticsDO> orderItemLogisticsList = orderItemLogisticsDOMapper.getNotSignList(sendTimeMin);
        if (CollUtil.isEmpty(orderItemLogisticsList)) {
            log.info("获取订单信息为空，订单自动更新签收任务完成");
            return 0;
        }

        List<String> orderItemCodeList = new ArrayList<>();
        List<OrderItemLogisticsDO> updateLogisticsList = new ArrayList<>();
        for (OrderItemLogisticsDO logisticsDo: orderItemLogisticsList) {
            log.info("签收任务logistics数据ID：{}", logisticsDo.getId());
            // 参数为空则忽略
            if (StrUtil.isBlankIfStr(logisticsDo.getLogisticsCompany()) || StrUtil.isBlankIfStr(logisticsDo.getLogisticsNo()) || StrUtil.isBlankIfStr(logisticsDo.getRecipientPhone())) {
                continue;
            }
            // 获取快递数据
            this.obtainLogisticSignData(now, logisticsDo, orderItemCodeList, updateLogisticsList);
        }
        // orderItemLogistics数据更新
        if (!CollUtil.isEmpty(updateLogisticsList)) {
            orderItemLogisticsDOMapper.updateBatch(updateLogisticsList);
        }

        // 更新orderItem数据
        if (!CollUtil.isEmpty(orderItemCodeList)) {
            List<OrderItemDO> orderItemList = orderItemDOMapper.getByOrderItemCodeList(orderItemCodeList);
            List<OrderItemDO> updateOrderItemList = new ArrayList<>();
            for (OrderItemDO orderItem: orderItemList) {
                OrderItemDO updateOrderItem = new OrderItemDO();
                updateOrderItem.setId(orderItem.getId());
                // 更新状态
                updateOrderItem.setItemStatus(OrderItemLogisticsStatusEnum.DELIVERED.getCode());
                updateOrderItem.setUpdatedTime(now);
                updateOrderItemList.add(updateOrderItem);
            }

            orderItemDOMapper.updateBatch(updateOrderItemList);
        }
        log.info("订单自动更新签收任务完成, 增加操作日志数量:{}", updateLogisticsList.size());
        return updateLogisticsList.size();
    }

    private void obtainLogisticSignData(LocalDateTime now, OrderItemLogisticsDO logisticsDo, List<String> orderItemCodeList, List<OrderItemLogisticsDO> updateLogisticsList){
        // 密文解密
        String recipientPhone = piplDataUtil.getDecodeText(logisticsDo.getRecipientPhone());
        // 查询快递信息
        PackageInfoQueryRespDTO packageInfo = orderItemLogisticsDOService.getLogisticsInfo(logisticsDo.getLogisticsCompany(), logisticsDo.getLogisticsNo(), recipientPhone);
        // 签收
        if (packageInfo != null && Constants.KUAIDI100_STATE_SIGN.equals(packageInfo.getState())) {
            OrderItemLogisticsDO updateLogistics = new OrderItemLogisticsDO();
            updateLogistics.setId(logisticsDo.getId());
            // 更新状态 已发货才会更新状态
            if (OrderItemLogisticsStatusEnum.SHIPPED.getCode().equals(logisticsDo.getLogisticsStatus())) {
                orderItemCodeList.add(logisticsDo.getOrderItemCode());

                updateLogistics.setLogisticsStatus(OrderItemLogisticsStatusEnum.DELIVERED.getCode());
            }
            // 获取结果数组中最新时间
            LocalDateTime signTime = now;
            if (!StringUtils.isBlank(packageInfo.getData().get(0).getTime())) {
                signTime = DateUtil.parseLocalDateTime(packageInfo.getData().get(0).getTime(), DatePattern.NORM_DATETIME_PATTERN);
            }

            updateLogistics.setSignTime(signTime);
            // 无理由退货时间+7
            updateLogistics.setNoReasonReturnsTime(signTime.plusDays(7));
            // 签收任务执行标识
            updateLogistics.setIsSignTask(1);
            updateLogistics.setUpdatedTime(now);
            updateLogisticsList.add(updateLogistics);
            log.warn("快递物流签收任务-正常签收，orderCode={}，orderItemCode={}", logisticsDo.getOrderCode(), logisticsDo.getOrderItemCode());
        } else if (logisticsDo.getIsSignTask() == 0) {
            // 对于未执行过任务的数据更新 签收任务的标识
            OrderItemLogisticsDO updateLogistics = new OrderItemLogisticsDO();
            updateLogistics.setId(logisticsDo.getId());
            updateLogistics.setIsSignTask(1);
            updateLogistics.setUpdatedTime(now);
            updateLogisticsList.add(updateLogistics);
            log.warn("快递物流签收任务-首次执行，orderCode={}，orderItemCode={}", logisticsDo.getOrderCode(), logisticsDo.getOrderItemCode());
        }
    }

    /***
     * <AUTHOR>
     * @description 更新拆单场景下的发货状态
     * @date 2025/4/11 12:13
     * @param reqDto:
     * @param orderInfoDO:
     * @param orderItemDOList:
     * @param orderItemLogisticsDOS:
     * @return: boolean 是否已经全部发货完成
    */

    public DeliveryNotificationEnum checkAndBuildInfoStatus(OrderLogisticStatusChangeReqDto reqDto, OrderInfoDO orderInfoDO, List<OrderItemDO> orderItemDOList, List<OrderItemLogisticsDO> orderItemLogisticsDOS) {
        List<String> hasUpdateOrderItemCode = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        //取出没有更新的数量
        long needUpdateCount = orderItemLogisticsDOS.stream().filter(x -> x.getLogisticsStatus() == null || BgOrderItemLogisticsStatusEnum.IN_TRANSIT.getCode() == x.getLogisticsStatus()).count();
        boolean dealAtLeastOne = false; //至少处理过一条订单行（这种情况要发送短信通知，如果一条都没有处理过则不更新）
        boolean atLeastUpdate = false; //至少需要更新数据标识，优先级低于dealAtLeastOne,
        for (OrderLogisticStatusChangeReqDto.OrderItemLogisticStatusChangeReqDto item : reqDto.getOrderItemLogisticStatusChangeList()) {
            OrderItemDO orderItemDO = orderItemDOList.stream().filter(x -> x.getOrderItemCode().equals(item.getOrderItemCode())).findFirst().orElse(null);
            OrderItemLogisticsDO orderItemLogisticsDO = orderItemLogisticsDOS.stream().filter(x -> x.getOrderItemCode().equals(item.getOrderItemCode())).findFirst().orElse(null);
            if (orderItemDO == null || orderItemLogisticsDO == null){
                log.warn("同步发货信息，orderCode={}，orderItemCode={}", orderInfoDO.getOrderCode(), item.getOrderItemCode());
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_ITEM_NOT_FOUND);
            }

            needUpdateCount --;

            if (checkStatus(orderInfoDO, item, hasUpdateOrderItemCode)) {
                continue;
            }

            atLeastUpdate = true;
            if (orderItemLogisticsDO.getLogisticsStatus() == null || BgOrderItemLogisticsStatusEnum.IN_TRANSIT.getCode() == orderItemLogisticsDO.getLogisticsStatus()){//等于空才更新
                orderItemLogisticsDO.setLogisticsStatus(BgOrderItemLogisticsStatusEnum.COLLECTED.getCode());
                orderItemLogisticsDO.setLogisticsCompany(item.getLogisticsCode());
                orderItemLogisticsDO.setLogisticsNo(item.getTrackingNumber());
                orderItemLogisticsDO.setSendTime(now);
                orderItemLogisticsDO.setUpdatedTime(now);
                orderItemDO.setItemStatus(OrderItemLogisticsStatusEnum.SHIPPED.getCode());
                orderItemDO.setUpdatedTime(now);
                dealAtLeastOne = true;
            }else {
                //有可能用户会提前收货，也可能签收的消息比同步发货来得更早，这种情况下也要更新物流信息，但是不能更新状态。
                orderItemLogisticsDO.setLogisticsCompany(item.getLogisticsCode());
                orderItemLogisticsDO.setLogisticsNo(item.getTrackingNumber());
                orderItemLogisticsDO.setSendTime(now);
                orderItemLogisticsDO.setUpdatedTime(now);
            }
            hasUpdateOrderItemCode.add(item.getOrderItemCode());
        }

        //只要至少处理过一条，而且大订单状态时待发货和已发货的才更新大订单状态以及发送短信
        if (dealAtLeastOne
                && List.of(OrderLogisticsStatusEnum.PENDING_SHIPMENT.getCode(), OrderLogisticsStatusEnum.PARTIALLY_SHIPPED.getCode())
                            .contains(orderInfoDO.getLogisticsStatus())) {
            orderInfoDO.setUpdatedTime(now);
            return getDeliveryNotificationEnum(orderInfoDO, needUpdateCount);
        }else if (atLeastUpdate){
            return DeliveryNotificationEnum.NONE_BUT_UPDATE;
        }else{
            return DeliveryNotificationEnum.NONE;
        }
    }

    @NotNull
    private static DeliveryNotificationEnum getDeliveryNotificationEnum(OrderInfoDO orderInfoDO, long needUpdateCount) {
        if (needUpdateCount == 0) {
            //如果全部更新成功，则更新订单状态为已发货
            orderInfoDO.setLogisticsStatus(OrderLogisticsStatusEnum.FULLY_SHIPPED.getCode());
            return DeliveryNotificationEnum.FULLY_SHIPPED;
        } else {
            orderInfoDO.setLogisticsStatus(OrderLogisticsStatusEnum.PARTIALLY_SHIPPED.getCode());
            return DeliveryNotificationEnum.PARTIALLY_SHIPPED;
        }
    }

    private static boolean checkStatus(OrderInfoDO orderInfoDO, OrderLogisticStatusChangeReqDto.OrderItemLogisticStatusChangeReqDto item, List<String> hasUpdateOrderItemCode) {
        if (hasUpdateOrderItemCode.contains(item.getOrderItemCode())){
            log.warn("同步发货信息，orderCode={}，订单行状态已经处理过，orderItemCode={},不进行物流状态更新", orderInfoDO.getOrderCode(), item.getOrderItemCode());
            return true;
        }
        return false;
    }
}

package com.jlr.ecp.order.controller.admin.dashboard;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.controller.admin.dashboard.dto.KpiQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.QueryReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SalesSummaryReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SummaryQueryReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.SalesSummaryRespVo;
import com.jlr.ecp.order.service.dashboard.SalesOverviewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "后台管理端 - 销售总览仪表盘")
@RestController
@RequestMapping("/v1/dashboard/salesOverview")
@Validated
@Slf4j
public class SalesOverviewController {

    @Resource
    private SalesOverviewService salesOverviewService;

    @Operation(summary = "获取销售总览数据")
    @PostMapping("/summary")
    @PreAuthorize("@ss.hasPermission('dashboard:sales:index')")
    public CommonResult<SalesSummaryRespVo> getSalesSummary(@RequestBody @Valid SalesSummaryReqDTO reqDTO) {
        log.info("获取销售总览数据请求参数: {}", JSON.toJSONString(reqDTO));
        SalesSummaryRespVo respVo = salesOverviewService.getSalesSummary(reqDTO);
        return CommonResult.success(respVo);
    }

    /**
     * 产品销售趋势
     *
     * @param dto 参数
     * @return ProductSalesTrendRespVo
     */
    @PostMapping("/salesSummaryCharts")
    @Operation(summary = "销售总览视图")
    @PreAuthorize("@ss.hasPermission('dashboard:sales:index')")
    CommonResult<ProductSalesTrendRespVo> salesSummaryCharts(@RequestBody @Validated SummaryQueryReqDTO dto) {
        return salesOverviewService.querySalesSummaryCharts(dto);
    }


    /**
     * 销售总览KPI列表
     *
     */
    @GetMapping("/salesKpiList")
    @Operation(summary = "销售总览KPI列表")
    CommonResult<List<KpiQueryDTO>> salesKpiList() {
        return salesOverviewService.salesKpiList();
    }

}
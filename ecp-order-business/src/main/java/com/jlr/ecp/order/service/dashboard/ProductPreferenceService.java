package com.jlr.ecp.order.service.dashboard;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.controller.admin.dashboard.dto.KpiQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.QueryReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesProportionByAttributesRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;

import java.util.List;

/**
 * 商品偏好服务
 *
 */
public interface ProductPreferenceService {

    /**
     * 车型列表
     */
    CommonResult<List<String>> vehicleModelList();

    /**
     * 产品销售趋势KPI列表
     */
    CommonResult<List<KpiQueryDTO>> salesTrendKpiList();

    /**
     * 产品及不同属性销售占比KPI列表
     */
    CommonResult<List<KpiQueryDTO>> salesProportionByAttributesKpiList();

    /**
     * 产品销售趋势
     * @param dto 入参
     * @return ProductSalesTrendRespVo
     */
    CommonResult<ProductSalesTrendRespVo> querySalesTrend(QueryReqDTO dto);

    /**
     * 产品及不同属性销售占比
     * @param dto 入参
     * @return ProductSalesTrendRespVo
     */
    CommonResult<ProductSalesProportionByAttributesRespVo> querySalesProportionByAttributes(QueryReqDTO dto);
}


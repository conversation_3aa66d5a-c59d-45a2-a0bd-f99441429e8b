package com.jlr.ecp.order.api.order;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.common.util.json.JsonUtils;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.inventory.api.InventoryOrderApi;
import com.jlr.ecp.inventory.dto.qry.InventoryOrderUpdateDTO;
import com.jlr.ecp.order.api.order.dto.BrandGoodsOrderPageReqDTO;
import com.jlr.ecp.order.api.order.dto.OrderLogisticStatusChangeReqDto;
import com.jlr.ecp.order.api.order.dto.vcs.VcsOrderDecodeVinDTO;
import com.jlr.ecp.order.api.order.vo.BrandGoodsOrderInfoPageVO;
import com.jlr.ecp.order.api.order.vo.bak.OrderInfoVO;
import com.jlr.ecp.order.api.order.vo.bak.OrderRespVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemLogisticsDO;
import com.jlr.ecp.order.dal.dataobject.order.VCSOrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemLogisticsDOMapper;
import com.jlr.ecp.order.dal.mysql.order.VcsOrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.logistic.DeliveryNotificationEnum;
import com.jlr.ecp.order.enums.order.OrderCouponStatusEnum;
import com.jlr.ecp.order.enums.order.OrderLogisticsStatusEnum;
import com.jlr.ecp.order.enums.phone.PhoneNumberDecodeUtil;
import com.jlr.ecp.order.kafka.producer.BrandedGoodsNotificationProducer;
import com.jlr.ecp.order.service.logistic.OrderItemLogisticService;
import com.jlr.ecp.order.service.order.BrandGoodsOrderInfoDOService;
import com.jlr.ecp.order.service.order.handler.ShortLinkHandler;
import com.jlr.ecp.order.util.PIPLDataUtil;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class OrderApiImpl implements OrderApi {
    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;

    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Resource
    private OrderRefundItemDOMapper orderRefundItemDOMapper;

    @Resource
    private OrderItemLogisticsDOMapper orderItemLogisticsDOMapper;

    @Resource
    private OrderRefundDOMapper orderRefundDOMapper;

    @Resource
    private OrderItemLogisticService orderItemLogisticService;

    @Resource
    private BrandGoodsOrderInfoDOService brandGoodsOrderInfoDOService;

    @Resource
    private BrandedGoodsNotificationProducer notificationProducer;

    @Resource
    private ShortLinkHandler shortLinkHandler;

    @Resource
    private PhoneNumberDecodeUtil phoneNumberDecodeUtil;

    @Resource
    InventoryOrderApi inventoryOrderApi;

    @Resource
    private VcsOrderInfoDOMapper vcsOrderInfoDOMapper;

    @Resource
    private PIPLDataUtil piplDataUtil;

    @Override
    public CommonResult<OrderRespVO> viewOrderInfo(String orderCode) {
        OrderInfoDO orderInfoDO = orderInfoDOMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>().eq(OrderInfoDO::getOrderCode, orderCode));
        OrderInfoVO orderInfoVO = new OrderInfoVO();
        orderInfoVO.setParentOrderCode(orderInfoDO.getParentOrderCode());
        orderInfoVO.setOrderCode(orderInfoDO.getOrderCode());
        orderInfoVO.setFulfilmentType(orderInfoDO.getOrderType());
        orderInfoVO.setCostAmount(orderInfoDO.getCostAmount());
        orderInfoVO.setCostAmountNoTax(orderInfoDO.getExcludeTaxAmount());
        orderInfoVO.setCreatedTime(orderInfoDO.getCreatedTime().toString());
        orderInfoVO.setFreightAmount(orderInfoDO.getFreightAmount());
        orderInfoVO.setFreightTax(orderInfoDO.getFreightTax());
        orderInfoVO.setBusinessCode(orderInfoDO.getBusinessCode());

        // 计算退款金额
        RefundAmountResult refundResult = calculateRefundAmount(orderCode);
        orderInfoVO.setRefundAmount(refundResult.getTotalRefundAmount().toString());
        orderInfoVO.setRefundAmountNoTax(refundResult.getTotalRefundAmountNoTax().toString());

        OrderRespVO orderRespVO = new OrderRespVO();
        orderRespVO.setOrderInfo(orderInfoVO);
        return CommonResult.success(orderRespVO);
    }

    /**
     * 计算订单的退款金额
     * 通过关联 t_order_refund、t_order_refund_item 和 t_order_item 表
     * 筛选 logistics_refund_status 为 90501 的记录
     * 计算 refund_money_amount 的总和
     */
    private RefundAmountResult calculateRefundAmount(String orderCode) {
        // 获取订单信息以判断业务类型
        OrderInfoDO orderInfo = orderInfoDOMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(OrderInfoDO::getIsDeleted, false));

        if (orderInfo == null) {
            return new RefundAmountResult(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        // 构建查询条件
        LambdaQueryWrapperX<OrderRefundDO> refundWrapper = new LambdaQueryWrapperX<OrderRefundDO>()
                .eq(OrderRefundDO::getOriginOrderCode, orderCode)
                .eq(OrderRefundDO::getIsDeleted, false);

        // 根据业务类型设置不同的状态条件
        if (BusinessIdEnum.LRE.getCode().equals(orderInfo.getBusinessCode())) {
            refundWrapper.eq(OrderRefundDO::getCouponRefundStatus, OrderCouponStatusEnum.AFTER_SALE_PROCESSING.getCode());
        } else if (BusinessIdEnum.BRAND_GOODS.getCode().equals(orderInfo.getBusinessCode())) {
            refundWrapper.eq(OrderRefundDO::getLogisticsRefundStatus, OrderLogisticsStatusEnum.AFTER_SALE_PROCESSING.getCode());
        } else {
            return new RefundAmountResult(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        // 查询符合条件的退款记录
        List<OrderRefundDO> refundList = orderRefundDOMapper.selectList(refundWrapper);
        
        if (CollUtil.isEmpty(refundList)) {
            return new RefundAmountResult(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        // 获取所有退款订单号
        List<String> refundOrderCodes = refundList.stream()
                .map(OrderRefundDO::getRefundOrderCode)
                .collect(Collectors.toList());

        // 查询退款项
        List<OrderRefundItemDO> refundItems = orderRefundItemDOMapper.selectList(
                new LambdaQueryWrapperX<OrderRefundItemDO>()
                        .in(OrderRefundItemDO::getRefundOrderCode, refundOrderCodes)
                        .eq(OrderRefundItemDO::getIsDeleted, false)
        );

        // 获取所有订单项编码
        List<String> orderItemCodes = refundItems.stream()
                .map(OrderRefundItemDO::getOrderItemCode)
                .collect(Collectors.toList());

        // 查询订单项以获取税率
        List<OrderItemDO> orderItems = orderItemDOMapper.selectList(
                new LambdaQueryWrapperX<OrderItemDO>()
                        .in(OrderItemDO::getOrderItemCode, orderItemCodes)
                        .eq(OrderItemDO::getIsDeleted, false)
        );

        // 创建订单项编码到税率的映射
        Map<String, BigDecimal> itemCodeToTaxRate = orderItems.stream()
                .collect(Collectors.toMap(
                        OrderItemDO::getOrderItemCode,
                        item -> item.getTaxRate() != null ? item.getTaxRate() : BigDecimal.ZERO
                ));

        // 计算总退款金额和总不含税退款金额
        BigDecimal totalRefundAmount = BigDecimal.ZERO;
        BigDecimal totalRefundAmountNoTax = BigDecimal.ZERO;

        for (OrderRefundItemDO refundItem : refundItems) {
            if (refundItem.getRefundMoney() != null && refundItem.getRefundMoney() > 0) {
                BigDecimal refundMoney = new BigDecimal(refundItem.getRefundMoney());
                totalRefundAmount = totalRefundAmount.add(refundMoney);

                // 获取税率
                BigDecimal taxRate = itemCodeToTaxRate.getOrDefault(refundItem.getOrderItemCode(), BigDecimal.ZERO);
                
                // 计算不含税金额：含税金额 / (1 + 税率/100)
                BigDecimal divisor = BigDecimal.ONE.add(taxRate.divide(new BigDecimal(100), 4, RoundingMode.HALF_UP));
                BigDecimal itemRefundAmountNoTax = refundMoney.divide(divisor, 2, RoundingMode.HALF_UP);
                totalRefundAmountNoTax = totalRefundAmountNoTax.add(itemRefundAmountNoTax);
            }
        }

        return new RefundAmountResult(totalRefundAmount, totalRefundAmountNoTax);
    }

    /**
     * 退款金额结果类
     */
    private static class RefundAmountResult {
        private final BigDecimal totalRefundAmount;
        private final BigDecimal totalRefundAmountNoTax;

        public RefundAmountResult(BigDecimal totalRefundAmount, BigDecimal totalRefundAmountNoTax) {
            this.totalRefundAmount = totalRefundAmount;
            this.totalRefundAmountNoTax = totalRefundAmountNoTax;
        }

        public BigDecimal getTotalRefundAmount() {
            return totalRefundAmount;
        }

        public BigDecimal getTotalRefundAmountNoTax() {
            return totalRefundAmountNoTax;
        }
    }

    @Override
    public CommonResult<PageResult<BrandGoodsOrderInfoPageVO>> pageOrders(String orderNumber, List<Integer> logisticsOrderStatusList,
                                                                          LocalDateTime startTime, LocalDateTime endTime, String updateTimeSort,boolean closedWithNoPay, Integer pageNo, Integer pageSize) {
        PageResult<BrandGoodsOrderInfoPageVO> pageResult = new PageResult<>();
        BrandGoodsOrderPageReqDTO reqDTO = new BrandGoodsOrderPageReqDTO();
        reqDTO.setPageNo(pageNo);
        reqDTO.setPageSize(pageSize);
        reqDTO.setOrderCode(orderNumber);
        reqDTO.setLogisticsOrderStatusList(logisticsOrderStatusList);
        if (startTime != null) {
            reqDTO.setStartUpdateTime(startTime.toString());
        }
        if (endTime != null) {
            reqDTO.setEndUpdateTime(endTime.toString());
        }
        reqDTO.setUpdateTimeSort(updateTimeSort);
        reqDTO.setClosedWithNoPay(closedWithNoPay);

        PageResult<BrandGoodsOrderInfoPageVO> page = brandGoodsOrderInfoDOService.getPage(reqDTO);
        if (page == null || CollUtil.isEmpty(page.getList())){
            return CommonResult.success(pageResult);
        }

        return CommonResult.success(page);
    }

    @Override
    public CommonResult<Boolean> updateOrderStatusOnLogisticChange(OrderLogisticStatusChangeReqDto reqDto) {
        log.info("同步发货信息开始，orderCode={}，req={}", reqDto.getOrderCode(), JsonUtils.toJsonString(reqDto));
        OrderInfoDO orderInfoDO = getOrderInfoDo(reqDto);

        List<OrderItemDO> orderItemDOList = getOrderItemDOS(reqDto);

        List<OrderItemLogisticsDO> orderItemLogisticsDOS = getOrderItemLogisticsDOS(reqDto);

        if(!needUpdateOrderStatus(orderInfoDO)){
            log.info("同步发货信息，订单状态已经无需更新发货状态，orderCode={}, logisticsStatus={}", orderInfoDO.getOrderCode(), orderInfoDO.getLogisticsStatus());
            //如果不需要更新，则直接返回成功
            return CommonResult.success(true);
        }

        DeliveryNotificationEnum dealResult = orderItemLogisticService.checkAndBuildInfoStatus(reqDto, orderInfoDO, orderItemDOList, orderItemLogisticsDOS);

        if (DeliveryNotificationEnum.NONE != dealResult){
            orderItemLogisticService.doUpdateLogisticStatus(orderInfoDO, orderItemDOList, orderItemLogisticsDOS);
            if (DeliveryNotificationEnum.NONE_BUT_UPDATE != dealResult){
                // 解密手机号
                String phone = phoneNumberDecodeUtil.getDecodePhone(orderInfoDO.getContactPhone());
                String wxUrl = shortLinkHandler.getBGOrderDetailShortLink(reqDto.getOrderCode());
                String packageNoListStr = reqDto.getOrderItemLogisticStatusChangeList().stream()
                        .map(OrderLogisticStatusChangeReqDto.OrderItemLogisticStatusChangeReqDto::getTrackingNumber)
                        .distinct()
                        .collect(Collectors.joining("、"));
                log.info("同步发货信息，订单状态更新完成，还需要发送通知，orderCode={}, logisticsStatus={}", orderInfoDO.getOrderCode(), orderInfoDO.getLogisticsStatus());
                notificationProducer.sendPartialDeliveryNotification(reqDto.getOrderCode(), packageNoListStr, phone, wxUrl);
            }

        }
        return CommonResult.success(true);
    }

    @Override
    public Integer updateOrderLogisticSign() {
        return orderItemLogisticService.updateOrderLogisticSign();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> syncGuanyiOrder(List<String> orderCodeList) {
        if (CollUtil.isEmpty(orderCodeList)){
            log.info("同步管易云订单状态，订单列表为空");
            return CommonResult.success("待同步订单列表为空");
        }

        InventoryOrderUpdateDTO inventoryOrderUpdateDTO = new InventoryOrderUpdateDTO();
        inventoryOrderUpdateDTO.setOrderCodeList(orderCodeList);
        CommonResult<String> syncResult = inventoryOrderApi.orderSync(inventoryOrderUpdateDTO);
        log.info("同步订单库存状态，请求参数:{},响应结果:{}",orderCodeList, syncResult);
        if (!syncResult.isSuccess()){
            //如果同步失败，则不返回结果给管易云
            log.info("订单库存更新失败，参数:{},响应:{}", inventoryOrderUpdateDTO, syncResult);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.STOCK_SYNC_ERROR, syncResult.getMsg());
        }

        // MyBatis-Plus 批量更新字段方法示例（假设更新 user 表的 status 字段）
        LambdaUpdateWrapper<OrderInfoDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(OrderInfoDO::getOrderCode, orderCodeList)
                .set(OrderInfoDO::getErpSync, 1);
        orderInfoDOMapper.update(null, updateWrapper);
        return CommonResult.success("同步完成");
    }

    /**
     * 获取所有需要解密VcsOrderInfo记录的Id
     *
     * @return CommonResult<Boolean> 返回一个通用结果对象，包含一个布尔值来表示操作是否成功
     */
    @Override
    public CommonResult<List<Long>> getAllVcsOrderInfoId(VcsOrderDecodeVinDTO decodeVinDTO) {
        log.info("获取所有需要解密VcsOrderInfo记录的Id, decodeVinDTO:{}", decodeVinDTO);
        LambdaQueryWrapperX<VCSOrderInfoDO> queryWrapper = new LambdaQueryWrapperX<>();
        if (Objects.nonNull(decodeVinDTO.getStartTime())) {
            queryWrapper.ge(VCSOrderInfoDO::getCreatedTime, decodeVinDTO.getStartTime());
        }
        if (Objects.nonNull(decodeVinDTO.getEndTime())) {
            queryWrapper.le(VCSOrderInfoDO::getCreatedTime, decodeVinDTO.getEndTime());
        }
        List<VCSOrderInfoDO> vcsOrderInfoDOList = vcsOrderInfoDOMapper.selectList();
        log.info("获取所有需要解密VcsOrderInfo记录的Id数量：{}", vcsOrderInfoDOList.size());
        List<Long> vcsOrderIdList = vcsOrderInfoDOList.stream().map(VCSOrderInfoDO::getId).collect(Collectors.toList());
        return CommonResult.success(vcsOrderIdList);
    }

    /**
     * 根据VCS订单信息解码VIN
     *
     * @return 解码是否成功的通用结果对象
     */
    @Override
    public CommonResult<Boolean> decodeVinByVcsOrderInfo(List<Long> vcsOrderIdList) {
        if (CollUtil.isEmpty(vcsOrderIdList)) {
            log.info("根据VCS订单信息解码VIN, vcsOrderIdList为空");
            return CommonResult.success(true);
        }
        LambdaQueryWrapperX<VCSOrderInfoDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.in(VCSOrderInfoDO::getId, vcsOrderIdList);
        List<VCSOrderInfoDO> vcsOrderInfoDOList = vcsOrderInfoDOMapper.selectList(queryWrapper);
        log.info("根据VCS订单信息解码VIN, vcsOrderIdList数量：{}, vcsOrderInfoDOList数量：{}", vcsOrderIdList.size(), vcsOrderInfoDOList.size());
        List<String> carVinList = vcsOrderInfoDOList.stream().map(VCSOrderInfoDO::getCarVin).collect(Collectors.toList());
        Map<String, String> decodeVinMap = piplDataUtil.getDecodeListText(carVinList);
        log.info("根据VCS订单信息解码VIN, carVinList数量:{}, decodeVinMap数量:{}", carVinList.size(), decodeVinMap.size());
        for (VCSOrderInfoDO vcsOrderInfoDO : vcsOrderInfoDOList) {
            String carVinView = decodeVinMap.get(vcsOrderInfoDO.getCarVin());
            if (StringUtils.isBlank(carVinView)) {
                log.info("根据VCS订单信息解码VIN, 解密为空, vcsOrderCode:{}", vcsOrderInfoDO.getOrderCode());
                continue;
            }
            vcsOrderInfoDO.setCarVinView(carVinView);
        }
        vcsOrderInfoDOMapper.updateBatch(vcsOrderInfoDOList);
        return CommonResult.success(true);
    }



    private boolean needUpdateOrderStatus(OrderInfoDO orderInfoDO){
        if (orderInfoDO.getLogisticsStatus() == null){
            log.warn("订单状态为空, orderCode:{}", orderInfoDO.getOrderCode());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_STATUS_EXCEPTION);
        }
        if (OrderLogisticsStatusEnum.PENDING_PAYMENT.getCode() == orderInfoDO.getLogisticsStatus()){
            log.warn("订单状态为待支付状态, orderCode:{}", orderInfoDO.getOrderCode());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_STATUS_EXCEPTION);
        }
        //订单完成的订单，也有可能会同步发货信息过来
        List<Integer> needUpdateStatus = List.of(OrderLogisticsStatusEnum.PENDING_SHIPMENT.getCode(), OrderLogisticsStatusEnum.PARTIALLY_SHIPPED.getCode(), OrderLogisticsStatusEnum.ORDER_COMPLETED.getCode());
        return needUpdateStatus.contains(orderInfoDO.getLogisticsStatus());
    }

    private List<OrderItemDO> getOrderItemDOS(OrderLogisticStatusChangeReqDto reqDto) {
        List<OrderItemDO> orderItemDOList = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, reqDto.getOrderCode())
                .eq(OrderItemDO::getIsDeleted, false));

        if (CollUtil.isEmpty(orderItemDOList)){
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_ITEM_NOT_FOUND);
        }
        return orderItemDOList;
    }

    private List<OrderItemLogisticsDO> getOrderItemLogisticsDOS(OrderLogisticStatusChangeReqDto reqDto) {
        List<OrderItemLogisticsDO> orderItemLogisticsDOS = orderItemLogisticsDOMapper.selectList(new LambdaQueryWrapperX<OrderItemLogisticsDO>()
                .eq(OrderItemLogisticsDO::getOrderCode, reqDto.getOrderCode())
                .eq(OrderItemLogisticsDO::getIsDeleted, false));
        if (CollUtil.isEmpty(orderItemLogisticsDOS)){
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.RECEIVER_INFO_NOT_FOUND);
        }

        return orderItemLogisticsDOS;
    }

    private OrderInfoDO getOrderInfoDo(OrderLogisticStatusChangeReqDto reqDto) {
        //检查order info是否正常
        OrderInfoDO orderInfoDO = orderInfoDOMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, reqDto.getOrderCode())
                .eq(OrderInfoDO::getIsDeleted, false));
        if (orderInfoDO == null){
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        return orderInfoDO;
    }
}
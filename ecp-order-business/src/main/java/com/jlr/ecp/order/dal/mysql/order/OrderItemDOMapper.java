package com.jlr.ecp.order.dal.mysql.order;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【t_order_item(t_order_item)】的数据库操作Mapper
* @createDate 2023-12-20 10:41:04
* @Entity com.jlr.ecp.order.dal.dataobject.order.OrderItemDO
*/
@Mapper
public interface OrderItemDOMapper extends BaseMapperX<OrderItemDO> {

    /**
     * 查询订单下的所有订单行，并按照ID升序
     */
    default Map<String, List<OrderItemDO>> mapByOrderCode(List<String> orderCodes) {
        LambdaQueryWrapper<OrderItemDO> wrapper = Wrappers.lambdaQuery(OrderItemDO.class)
                .in(OrderItemDO::getOrderCode, orderCodes)
                .eq(OrderItemDO::getIsDeleted, Boolean.FALSE)
                .orderByAsc(OrderItemDO::getId);
        List<OrderItemDO> orderItems = selectList(wrapper);
        Map<String, List<OrderItemDO>> orderItemMap = orderItems.stream()
                .collect(Collectors.groupingBy(OrderItemDO::getOrderCode));
        return orderItemMap;
    }

    default OrderItemDO getByOrderItemCode(String orderItemCode) {
        LambdaQueryWrapper<OrderItemDO> wrapper = Wrappers.lambdaQuery(OrderItemDO.class)
                .eq(OrderItemDO::getOrderItemCode, orderItemCode)
                .eq(OrderItemDO::getIsDeleted, Boolean.FALSE);
        return selectOne(wrapper);
    }

    default List<OrderItemDO> getByOrderItemCodeList(List<String> orderItemCodeList) {
        LambdaQueryWrapper<OrderItemDO> wrapper = Wrappers.lambdaQuery(OrderItemDO.class)
                .in(OrderItemDO::getOrderItemCode, orderItemCodeList)
                .eq(OrderItemDO::getIsDeleted, Boolean.FALSE);
        return selectList(wrapper);
    }

    /**
     * 查询订单下的所有订单行
     */
    default List<OrderItemDO> listByOrderCode(List<String> orderCodes) {
        LambdaQueryWrapper<OrderItemDO> wrapper = Wrappers.lambdaQuery(OrderItemDO.class)
                .in(OrderItemDO::getOrderCode, orderCodes)
                .eq(OrderItemDO::getIsDeleted, Boolean.FALSE);
        return selectList(wrapper);
    }

}
package com.jlr.ecp.order.controller.app.order.vo.Integration.crcQueryOrderInfo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "小程序端 - 为CRC提供可查询BG VCS的订单及商品信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderIntegrationRespVO {
    /**
     * VCS订单 相关信息 eg.车、商品
     */
    @Schema(description = "VCS订单 相关信息 eg.车、商品")
    private VcsInfo vcsInfo;

    /**
     * BG 相关信息 eg.首商品信息、订单信息、商品列表item信息
     */
    @Schema(description = "BG 相关信息 eg.首商品信息、订单信息、商品列表item信息")
    private BgInfo bgInfo;

    /**
     * 订单编码
     */
    @Schema(description = "订单编码")
    private String orderCode;

    /**
     * 下单时间
     */
    @Schema(description = "下单时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime orderTime;

    /**
     * 查询手机号 通过consumer_code 查询 t_consumer_info 表中phone_encrypt 进行解密
     */
    @Schema(description = "查询手机号 通过consumer_code 查询 t_consumer_info 表中phone_encrypt 进行解密")
    private String phoneNumber;


}


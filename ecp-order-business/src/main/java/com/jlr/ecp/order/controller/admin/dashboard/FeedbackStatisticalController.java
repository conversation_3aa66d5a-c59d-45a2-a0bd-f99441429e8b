package com.jlr.ecp.order.controller.admin.dashboard;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.controller.admin.dashboard.dto.FeedbackStatisticQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;
import com.jlr.ecp.order.service.feedback.FeedbackRecordsDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

@Tag(name = "后台管理端 - 客户评价仪表盘")
@RestController
@RequestMapping("v1/dashboard/feedback")
@Validated
public class FeedbackStatisticalController {

    @Resource
    private FeedbackRecordsDOService feedbackRecordsDOService;




    /**
     * 评价分数
     *
     * @param dto 参数
     * @return ProductSalesTrendRespVo
     */
    @PostMapping("/scoreCount")
    @Operation(summary = "评价统计")
    @PreAuthorize("@ss.hasPermission('dashboard:feedback:index')")
    CommonResult<Map<String, Object>> scoreCount(@RequestBody @Validated FeedbackStatisticQueryDTO dto) {
        return CommonResult.success(feedbackRecordsDOService.scoreCount(dto));
    }

}

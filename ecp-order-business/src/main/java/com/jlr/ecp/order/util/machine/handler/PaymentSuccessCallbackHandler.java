package com.jlr.ecp.order.util.machine.handler;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.enums.order.*;
import org.springframework.stereotype.Component;

/**
 * 支付成功回调 事件处理
 * <AUTHOR>
 */
@Component
public class PaymentSuccessCallbackHandler implements EventHandler {
    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO) {
        orderInfoDO.setOrderStatus(OrderStatusEnum.PAID.getCode());
        orderInfoDO.setPaymentStatus(PaymentStatusEnum.PAID.getCode());
        if (OrderTypeEnum.BRAND_GOOD.getCode().equals(orderInfoDO.getOrderType())) {
            orderInfoDO.setLogisticsStatus(OrderLogisticsStatusEnum.PENDING_SHIPMENT.getCode());
        } else if (OrderTypeEnum.ELECTRONIC_COUPON.getCode().equals(orderInfoDO.getOrderType())) {
            orderInfoDO.setCouponStatus(OrderCouponStatusEnum.PENDING_ISSUANCE.getCode());
        }
    }

}

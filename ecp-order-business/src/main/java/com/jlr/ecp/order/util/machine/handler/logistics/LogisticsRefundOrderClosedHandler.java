package com.jlr.ecp.order.util.machine.handler.logistics;

import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusLogDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.order.OrderItemAftersalesStatusEnum;
import com.jlr.ecp.order.enums.order.OrderLogisticsStatusEnum;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import com.jlr.ecp.order.enums.refund.RefundLogisticsStatusEnum;
import com.jlr.ecp.order.service.coupon.status.dto.OrderStatusChangedKafkaDto;
import com.jlr.ecp.order.util.machine.handler.LogisticsEventHandler;
import com.jlr.ecp.order.util.order.OrderAssembleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 关闭主订单
 */
@Component
@Slf4j
public class LogisticsRefundOrderClosedHandler implements LogisticsEventHandler {

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;
    @Resource
    private OrderStatusLogDOMapper statusLogMapper;

    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Resource
    private OrderRefundItemDOMapper orderRefundItemDOMapper;

    @Resource
    private ProducerTool producerTool;


    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO) {
        //打印各个参数到日志中
        LocalDateTime current = LocalDateTime.now();

        log.info("LogisticsRefundOrderClosedHandler handleEvent orderInfoDO before:{},orderRefundDO:{},orderItemDO:{}",orderInfoDO,orderRefundDO,orderItemDO);
        Integer orderStatus = calculateOrderStatus(orderInfoDO);
        log.info("LogisticsRefundOrderClosedHandler handleEvent orderStatus :{}",orderStatus);
        OrderStatusLogDO logDO = OrderAssembleUtil.assembleOrderStatusLogDO(orderInfoDO.getOrderCode(),orderInfoDO.getOrderStatus(),orderStatus);
        orderInfoDO.setOrderStatus(orderStatus);
        if(OrderStatusEnum.COMPLETED.getCode().equals(orderStatus)){
            orderInfoDO.setLogisticsStatus(OrderLogisticsStatusEnum.ORDER_COMPLETED.getCode());
            orderInfoDO.setCompletedTime(current);
        }else{
            orderInfoDO.setLogisticsStatus(OrderLogisticsStatusEnum.ORDER_CLOSED.getCode());
            orderInfoDO.setClosedTime(current);
        }
        orderInfoDO.setUpdatedTime(current);
        orderInfoDOMapper.updateById(orderInfoDO);
        statusLogMapper.insert(logDO);
        orderItemDO.setAftersalesStatus(OrderItemAftersalesStatusEnum.COMPLETED.getCode());
        orderItemDOMapper.updateById(orderItemDO);
        log.info("LogisticsRefundOrderClosedHandler handleEvent orderInfoDO after:{},orderRefundDO:{},orderItemDO:{}",orderInfoDO,orderRefundDO,orderItemDO);
        if(OrderStatusEnum.COMPLETED.getCode().equals(orderStatus)){
            // 注册事务同步器，确保事务提交后执行
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 事务提交后执行的代码，发送kafka消息
                    sendOrderStatusKafkaMessage(orderInfoDO);
                }
            });
        }
    }

    //如果全部款项已经退回(包含运费),则设置主订单为已关闭,否则设置为已完成
    private Integer calculateOrderStatus(OrderInfoDO orderInfoDO){
        log.info("LogisticsRefundOrderClosedHandler handleEvent  calculateOrderStatus orderInfoDO:{}",orderInfoDO);
        //根据orderCode查询出所有子订单行
        List<OrderItemDO>  orderItemDOList = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, orderInfoDO.getOrderCode())
                .eq(OrderItemDO::getIsDeleted, Boolean.FALSE)
        );
        log.info("LogisticsRefundOrderClosedHandler handleEvent  calculateOrderStatus orderItemDOList:{}",orderItemDOList);
        if(CollectionUtils.isNotEmpty(orderItemDOList)){
            List<String> itemCodeList = orderItemDOList.stream().map(OrderItemDO::getOrderItemCode).collect(Collectors.toList());
            //根据itemCodeList 查询出orderRefundItemDOMapper所有的退单子订单行
            List<OrderRefundItemDO> orderRefundItemDOList = orderRefundItemDOMapper.selectList(new LambdaQueryWrapperX<OrderRefundItemDO>()
                    .in(OrderRefundItemDO::getOrderItemCode,itemCodeList)
                    .eq(OrderRefundItemDO::getIsDeleted,Boolean.FALSE)
            );
            if(CollectionUtils.isNotEmpty(orderRefundItemDOList)){
                Integer costAmount = getIntWithDefault(orderInfoDO.getCostAmount());
                Integer freightAmount = getIntWithDefault(orderInfoDO.getFreightAmount());
                Integer paymentMoney = costAmount + freightAmount;
                Integer refundMoney  = orderRefundItemDOMapper.getAllLogisticsAlreadyRefundMoneyIncludeFreight(itemCodeList, RefundLogisticsStatusEnum.REFUND_COMPLETED.getCode());
                log.info("LogisticsRefundOrderClosedHandler handleEvent  calculateOrderStatus paymentMoney:{},refundMoney:{}",paymentMoney,refundMoney);
                if(!(paymentMoney.compareTo(refundMoney) == 0)){//如果钱款没有全部退回(包含运费)则订单完成,反之订单关闭
                     return OrderStatusEnum.COMPLETED.getCode();
                }else{
                    return OrderStatusEnum.CLOSED.getCode();
                }
            }
        }
            return OrderStatusEnum.COMPLETED.getCode();
    }

    private int getIntWithDefault(Integer item) {
        return item != null ? item : 0;
    }

    private void sendOrderStatusKafkaMessage(OrderInfoDO orderInfoDO){
        log.info("LogisticsRefundOrderClosedHandler handleEvent sendOrderStatusKafkaMessage orderInfoDO:{}",orderInfoDO);
        //所有消息状态都要向kafka发送
        String kafkaMessage = null;
        try {
            kafkaMessage = OrderStatusChangedKafkaDto.getOrderStatusChangedKafkaDto(orderInfoDO);
            producerTool.sendMsg(KafkaConstants.ORDER_STATUS_CHANGE_TOPIC,"", kafkaMessage);
            log.info("LogisticsRefundOrderClosedHandler handleEvent sendOrderStatusKafkaMessage，优惠券code:{},消息内容{}", orderInfoDO.getOrderCode(), kafkaMessage);
        } catch (Exception e) {
            log.info("LogisticsRefundOrderClosedHandler handleEvent sendOrderStatusKafkaMessage，优惠券code:{},消息内容{}", orderInfoDO.getOrderCode(), kafkaMessage);

        }
    }

}

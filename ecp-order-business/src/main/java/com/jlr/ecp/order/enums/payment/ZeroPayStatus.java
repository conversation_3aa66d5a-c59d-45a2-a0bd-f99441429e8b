package com.jlr.ecp.order.enums.payment;

import lombok.Getter;

/**
 * ZeroPayStatus
 *
 * <AUTHOR> <PERSON>
 * @since 2025-03-18 20:27:49
 */
@Getter
public enum ZeroPayStatus {

    SUCCESS("SUCCESS", "Zero pay success"),

    FAIL("FAIL", "Zero pay fail"),

    NA("NA", "Not applicable");

    ZeroPayStatus(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    private final String status;

    private final String desc;

}

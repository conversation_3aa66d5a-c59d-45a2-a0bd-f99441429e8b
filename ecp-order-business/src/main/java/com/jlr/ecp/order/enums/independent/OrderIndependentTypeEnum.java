package com.jlr.ecp.order.enums.independent;

import lombok.Getter;

/**
 * 订单分账类型
 */
@Getter
public enum OrderIndependentTypeEnum {

    ORDER_SUCCESS("ORDER_SUCCESS", "订单完成"),
    COUPON_USED("COUPON_USED", "券核销");

    /**
     * 类型值
     */
    private final String type;

    /**
     * 类型名称
     */
    private final String name;

    OrderIndependentTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

}

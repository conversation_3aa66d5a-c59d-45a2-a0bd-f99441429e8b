package com.jlr.ecp.order.config;

import cn.hutool.core.thread.BlockPolicy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.ThreadPoolExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池的配置类
 *
 * <AUTHOR>
 * */
@Configuration
public class ThreadPoolConfiguration {

    /**
     * 取消订单线程池
     * */
    @Bean("cancelOrderThreadPool")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        int corePoolSize = Runtime.getRuntime().availableProcessors();

        // 核心线程数
        executor.setCorePoolSize(corePoolSize);
        // 最大线程数
        executor.setMaxPoolSize(corePoolSize * 2);
        // 线程存活时间
        executor.setKeepAliveSeconds(300); // 时间单位是秒
        // 阻塞队列容量
        executor.setQueueCapacity(1000);
        // 线程前缀
        executor.setThreadNamePrefix("cancel-order-thread-pool");

        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 初始化线程池
        executor.initialize();
        return executor;
    }

        /**
         * 取消订单线程池
         * */
    @Bean("cancelOrderSchedulerPool")
    public ThreadPoolTaskScheduler scheduledExecutor() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        // 获取当前服务器可用处理器数量作为核心线程数
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        // 设置线程池中核心线程数
        scheduler.setPoolSize(corePoolSize);
        // 设置线程名称前缀，便于在日志中识别
        scheduler.setThreadNamePrefix("cancel-order-thread-pool");
        // 在应用启动时立即启动线程池
        scheduler.setDaemon(true);
        // 等待所有任务执行完成再关闭线程池
        scheduler.setAwaitTerminationSeconds(60);
        // 设置任务拒绝策略
        scheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 启动调度器
        scheduler.initialize();
        return scheduler;
    }
}

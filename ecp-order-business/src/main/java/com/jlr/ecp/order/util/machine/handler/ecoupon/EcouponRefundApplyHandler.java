package com.jlr.ecp.order.util.machine.handler.ecoupon;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusLogDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.order.OrderCouponStatusEnum;
import com.jlr.ecp.order.enums.order.OrderItemAftersalesStatusEnum;
import com.jlr.ecp.order.enums.order.RefundCouponStatusEnum;
import com.jlr.ecp.order.enums.order.RefundStatusEnum;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import com.jlr.ecp.order.util.machine.handler.EcouponEventHandler;
import com.jlr.ecp.order.util.machine.handler.EventHandler;
import com.jlr.ecp.order.util.order.OrderAssembleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 发起退款申请
 */
@Component
@Slf4j
public class EcouponRefundApplyHandler implements EcouponEventHandler {

    @Resource
    private OrderRefundDOMapper orderRefundDOMapper;

    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Resource
    private OrderStatusLogDOMapper statusLogMapper;


    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO) {
        //打印各个参数到日志中
        log.info("EcouponRefundApplyHandler handleEvent orderInfoDO:{},orderRefundDO:{},orderItemDO:{}",orderInfoDO,orderRefundDO,orderItemDO);
        orderItemDO.setAftersalesStatus(OrderItemAftersalesStatusEnum.PROCESSING.getCode());
        orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.FULL_REFUND_APPLY.getCode());
        OrderStatusLogDO logDO =OrderAssembleUtil.assembleOrderStatusLogDO(orderRefundDO.getRefundOrderCode(),null,RefundCouponStatusEnum.INITIATE_REFUND_REQUEST.getCode());
        orderRefundDO.setCouponRefundStatus(RefundCouponStatusEnum.INITIATE_REFUND_REQUEST.getCode());
        orderRefundDOMapper.updateById(orderRefundDO);
        orderItemDOMapper.updateById(orderItemDO);
        statusLogMapper.insert(logDO);
    }
}

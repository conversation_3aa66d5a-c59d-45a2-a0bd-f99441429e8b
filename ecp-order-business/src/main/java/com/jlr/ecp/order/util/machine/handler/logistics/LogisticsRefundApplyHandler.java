package com.jlr.ecp.order.util.machine.handler.logistics;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusLogDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.enums.order.OrderItemAftersalesStatusEnum;
import com.jlr.ecp.order.enums.refund.RefundLogisticsStatusEnum;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import com.jlr.ecp.order.util.machine.handler.LogisticsEventHandler;
import com.jlr.ecp.order.util.order.OrderAssembleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 发起退款申请
 */
@Component
@Slf4j
public class LogisticsRefundApplyHandler implements LogisticsEventHandler {

    @Resource
    private OrderRefundDOMapper orderRefundDOMapper;

    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Resource
    private OrderStatusLogDOMapper statusLogMapper;


    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO) {
        //打印各个参数到日志中
        log.info("LogisticsRefundApplyHandler handleEvent orderInfoDO:{},orderRefundDO:{},orderItemDO:{}",orderInfoDO,orderRefundDO,orderItemDO);
        orderItemDO.setAftersalesStatus(OrderItemAftersalesStatusEnum.PROCESSING.getCode());
        orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.FULL_REFUND_APPLY.getCode());
        OrderStatusLogDO logDO =OrderAssembleUtil.assembleOrderStatusLogDO(orderRefundDO.getRefundOrderCode(),null,RefundLogisticsStatusEnum.PENDING_RETURN_REVIEW.getCode());
        orderRefundDO.setLogisticsRefundStatus(RefundLogisticsStatusEnum.PENDING_RETURN_REVIEW.getCode());
        orderRefundDOMapper.updateById(orderRefundDO);
        orderItemDOMapper.updateById(orderItemDO);
        statusLogMapper.insert(logDO);
    }
}

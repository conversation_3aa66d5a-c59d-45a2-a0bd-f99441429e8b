package com.jlr.ecp.order.service.feedback;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.admin.feedback.dto.*;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedBackConfigVO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedbackDimensionsVO;
import com.jlr.ecp.order.controller.app.feedback.dto.FeedbackQueryDTO;
import com.jlr.ecp.order.controller.app.feedback.vo.FeedbackDimensionsAppVO;
import com.jlr.ecp.order.controller.app.feedback.vo.FeedbackSettingVO;
import com.jlr.ecp.order.dal.dataobject.feedback.*;
import com.jlr.ecp.order.dal.mysql.feedback.*;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.feedback.*;
import com.jlr.ecp.order.enums.feedback.modifyLog.EvaluationDimensionFieldEnum;
import com.jlr.ecp.order.enums.redis.DelayQueuePrefixEnum;
import com.jlr.ecp.order.enums.redis.RedisDelayQueueEnum;
import com.jlr.ecp.order.util.common.TimeFormatUtil;
import com.jlr.ecp.order.util.redis.RedisDelayQueueUtil;
import com.jlr.ecp.order.util.refund.CodeGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.invalidParamException;
import static com.jlr.ecp.order.enums.feedback.modifyLog.EvaluationDimensionFieldEnum.*;
import static com.jlr.ecp.order.util.common.TimeFormatUtil.formatter_7;

/**
 * 评价设置表ServiceImpl
 */
@Service
@Slf4j
public class FeedbackConfigDOServiceImpl extends ServiceImpl<FeedbackConfigDOMapper, FeedbackConfigDO> implements FeedbackConfigDOService {

    @Resource
    private FeedbackConfigDOMapper feedbackConfigDOMapper;

    @Resource
    private FeedbackDimensionsDOMapper feedbackDimensionsDOMapper;

    @Resource
    private FeedbackModifyLogDOMapper modifyLogDOMapper;

    @Resource
    private FeedbackSnapshotDOMapper feedbackSnapshotDOMapper;

    @Resource
    private FeedbackRecordsDOMapper feedbackRecordsDOMapper;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createFeedback(FeedBackCreateDTO feedBackCreateDTO) {
        //转换为DO
        FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();

        List<FeedbackDimensionsDTO> feedbackDimensions = feedBackCreateDTO.getDimensionsList();

        //判断评价维度是否超过10个
        if(feedbackDimensions.size()>10){
            throw exception(ErrorCodeConstants.FEEDBACK_DIMENSIONS_OVER_SIZE_ERROR);
        }

        BeanUtil.copyProperties(feedBackCreateDTO,feedbackConfigDO);

        //设置feedbackCode
        setMaxConfigCodeDO(feedbackConfigDO);
        //启用状态为：待启用
        feedbackConfigDO.setEnableStatus(FeedBackEnableStatusEnum.WAIT.getCode());
        feedbackConfigDO.setLastModifyUser(WebFrameworkUtils.getLoginUserName());
        //插入评价维度
        insertFeedbackDimensions(feedbackConfigDO,feedbackDimensions);

        int insert = feedbackConfigDOMapper.insert(feedbackConfigDO);
        return insert>0;
    }

    @Override
    public FeedBackConfigVO getOneByFeedBackCode(String feedbackCode) {
        FeedBackConfigVO feedBackConfigVO = new FeedBackConfigVO();
        FeedbackConfigDO feedbackConfigDO = feedbackConfigDOMapper.selectOneByFeedBackCode(feedbackCode);
        //未查询到直接返回
        if(feedbackConfigDO == null){
            return feedBackConfigVO;
        }

        //填充基本字段
        BeanUtil.copyProperties(feedbackConfigDO,feedBackConfigVO);
        feedBackConfigVO.setFeedbackDimensionsText(FeedBackTypeEnum.fromCode(feedbackConfigDO.getFeedbackDimensions()));

        //查询评价维度
        List<FeedbackDimensionsDO> feedbackDimensionsList = feedbackDimensionsDOMapper.selectListByFeedBackCode(feedbackCode);
        //组装FeedbackDimensionsVO
        List<FeedbackDimensionsVO> feedbackDimensionsVOList = new ArrayList<>();
        viewAssembleFeedbackDimensions(feedbackDimensionsList,feedbackDimensionsVOList);
        feedBackConfigVO.setDimensionsList(feedbackDimensionsVOList);


        return feedBackConfigVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(FeedBackUpdateDTO updateDTO) {
        log.info("修改评价配置信息, FeedBackUpdateDTO updateDTO：{}", JSON.toJSONString(updateDTO));

        FeedbackConfigDO feedbackConfigDO = feedbackConfigDOMapper.selectOneByFeedBackCode(updateDTO.getFeedbackCode());
        //查不到数据直接返回false
        if(feedbackConfigDO == null){
            return false;
        }
        //乐观锁校验
        if (!feedbackConfigDO.getRevision().equals(updateDTO.getRevision())){
            throw exception(ErrorCodeConstants.FEEDBACK_REVISION_ERROR);
        }
        //启用状态为启用无法修改
        if(FeedBackEnableStatusEnum.ENABLE.getCode().equals(feedbackConfigDO.getEnableStatus())){
            throw exception(ErrorCodeConstants.FEEDBACK_ENABLE_STATUS_ERROR);
        }

        // 保存 old feedbackConfigDO 用于日志记录
        FeedbackConfigDO oldFeedbackConfigDO = new FeedbackConfigDO();
        BeanUtils.copyProperties(feedbackConfigDO,oldFeedbackConfigDO);
        log.info("修改评价配置信息，old feedbackConfigDO：{}", JSON.toJSONString(oldFeedbackConfigDO));

        BeanUtil.copyProperties(updateDTO,feedbackConfigDO);
        if(!FeedBackEnableStatusEnum.WAIT.getCode().equals(feedbackConfigDO.getEnableStatus())){
            //启用状态重置为：待启用
            feedbackConfigDO.setEnableStatus(FeedBackEnableStatusEnum.WAIT.getCode());
            feedbackConfigDO.setScheduleEnableTime(null);
            feedbackConfigDO.setDowntime(null);
            feedbackConfigDO.setEnableTime(null);
        }

        feedbackConfigDO.setLastModifyUser(WebFrameworkUtils.getLoginUserName());
        feedbackConfigDO.setUpdatedTime(LocalDateTime.now());


        List<FeedbackDimensionsDTO> feedbackDimensions = updateDTO.getDimensionsList();
        //判断评价维度是否超过10个
        if(feedbackDimensions.size()>10){
            throw exception(ErrorCodeConstants.FEEDBACK_DIMENSIONS_OVER_SIZE_ERROR);
        }
        //查询原来的评价维度
        List<FeedbackDimensionsDO> feedbackDimensionsList = feedbackDimensionsDOMapper.selectListByFeedBackCode(feedbackConfigDO.getFeedbackCode());
        //将feedbackDimensionsList转换为 List<FeedbackDimensionsDTO> 类型
        List<FeedbackDimensionsDTO> feedbackDimensionsDTOList = new ArrayList<>();
        for (FeedbackDimensionsDO feedbackDimensionsDO : feedbackDimensionsList) {
            FeedbackDimensionsDTO feedbackDimensionsDTO = new FeedbackDimensionsDTO();
            BeanUtil.copyProperties(feedbackDimensionsDO,feedbackDimensionsDTO);
            feedbackDimensionsDTO.setOptionList(JSONUtil.toList(feedbackDimensionsDO.getOptionJson(), FeedbackOptionsDTO.class));
            feedbackDimensionsDTOList.add(feedbackDimensionsDTO);
        }
        // 使用CollUtil.isEquals比较两个列表的内容
        if (!CollUtil.isEqualList(feedbackDimensionsDTOList, feedbackDimensions)) {
            // 内容不相同，进行修改操作
            // 1. 删除之前的评价维度(逻辑删除)
            deleteFeedbackDimensions(feedbackConfigDO.getFeedbackCode());
            // 2. 插入新的评价维度
            insertFeedbackDimensions(feedbackConfigDO, feedbackDimensions);
        }

        recordEditLogs(
                oldFeedbackConfigDO.getFeedbackCode(),
                updateDTO,
                oldFeedbackConfigDO,
                feedbackDimensions,
                feedbackDimensionsDTOList
        );

        return updateById(feedbackConfigDO);
    }

    /**
     * 编辑操作日志记录方法（方法架子）
     *
     * @param feedbackCode  评价编码
     * @param updateDTO     新数据DTO
     * @param oldConfigDO   旧配置对象
     * @param newDimensions 新评价维度列表
     * @param oldDimensions 旧评价维度列表
     *
     * checkAndLogDimensionsEdit 方法逻辑
     * 如果 旧维度列表：[A, B, C，D, E]（sort: 1,2,3,4,5）
     * 操作1: 经过删除维度A\B\E ,D维度sort上移，新增G维度
     * 操作2: 改变D维度名称，改变C维度类型
     * 变成新维度列表[D，C，G]（sort: 1,2,3）
     *
     * checkAndLogDimensionsAdd 方法逻辑
     * 只判断newDims.size() > oldDims.size()不对
     * 如果 旧维度列表：[A, B, C，D, E]
     * 操作1 删除 维度C\D\E
     * 操作2 新增 维度G
     * 变成新维度列表：[A，B，G]
     */
    private void recordEditLogs(String feedbackCode,
                                FeedBackUpdateDTO updateDTO,
                                FeedbackConfigDO oldConfigDO,
                                List<FeedbackDimensionsDTO> newDimensions,
                                List<FeedbackDimensionsDTO> oldDimensions) {
        log.info("编辑评价配置信息，feedbackCode：{}，updateDTO：{}，oldConfigDO：{}，newDimensions：{}，oldDimensions：{}",
                JSON.toJSONString(feedbackCode), JSON.toJSONString(updateDTO), JSON.toJSONString(oldConfigDO), JSON.toJSONString(newDimensions), JSON.toJSONString(oldDimensions));

        // 必须先处理删除/修改，再处理新增
        // 评价维度模块日志 - 编辑评价维度 module
        checkAndLogDimensionsEdit(feedbackCode, newDimensions, oldDimensions);
        // 评价维度模块日志 - 添加评价维度 module
        checkAndLogDimensionsAdd(feedbackCode, newDimensions, oldDimensions);

        // 基础信息模块日志 - 编辑评价输入框配置 module
        checkAndLogInputConfigEdit(feedbackCode, updateDTO, oldConfigDO);
        // 基础信息模块日志 - 编辑评价问卷说明 module
        checkAndLogDescriptionEdit(feedbackCode, updateDTO, oldConfigDO);

        // 新增：处理启用状态修改日志（已停用 情况下 去编辑会变成 待启用）
        checkAndLogEnableStatusEdit(feedbackCode, oldConfigDO, updateDTO);

        // 其他模块日志（根据实际业务添加）
    }

    private void checkAndLogEnableStatusEdit(String feedbackCode,
                                             FeedbackConfigDO oldConfigDO,
                                             FeedBackUpdateDTO newConfigDTO) {
        Integer oldStatus = oldConfigDO.getEnableStatus();

        // 仅当旧状态是“已停用”且新状态是“待启用”时记录日志
        if (FeedBackEnableStatusEnum.DISABLE.getCode().equals(oldStatus)) {

            Map<String, Object> oldValues = new HashMap<>();
            Map<String, Object> newValues = new HashMap<>();
            AtomicInteger modifyCount = new AtomicInteger(1); // 状态变化计数为1

            // 记录状态名称（如“已停用”→“待启用”）
            oldValues.put(EDIT_ENABLE_STATUS.getFieldName(), FeedBackEnableStatusEnum.DISABLE.getName());
            newValues.put(EDIT_ENABLE_STATUS.getFieldName(), FeedBackEnableStatusEnum.WAIT.getName());

            modifyLogDOMapper.insertModifyLog(
                    feedbackCode,
                    EDIT_ENABLE_STATUS.getModuleName(),
                    modifyCount.get(),
                    oldValues,
                    newValues
            );
        }
    }

    private void checkAndLogDimensionsEdit(String feedbackCode,
                                           List<FeedbackDimensionsDTO> newDims,
                                           List<FeedbackDimensionsDTO> oldDims) {
        // 将旧维度转换为Map（key: dimensionsCode）
        Map<String, FeedbackDimensionsDTO> oldDimMap = oldDims.stream()
                .filter(d -> d.getDimensionsCode() != null)
                .collect(Collectors.toMap(
                        FeedbackDimensionsDTO::getDimensionsCode,
                        d -> d,
                        (existing, replacement) -> existing
                ));
        log.info("checkAndLogDimensionsEdit，feedbackCode:{}，oldDimMap:{}", feedbackCode, JSON.toJSONString(oldDimMap));

        Map<String, Object> oldValues = new HashMap<>();
        Map<String, Object> newValues = new HashMap<>();
        AtomicInteger modifyCount = new AtomicInteger();

        // 处理修改和排序变更
        newDims.forEach(newDim -> {
            log.info("checkAndLogDimensionsEdit，feedbackCode:{}，newDim:{}", feedbackCode, JSON.toJSONString(newDim));
            if (newDim.getDimensionsCode() != null
                    && oldDimMap.containsKey(newDim.getDimensionsCode())) {
                FeedbackDimensionsDTO oldDim = oldDimMap.get(newDim.getDimensionsCode());

                // 比较各个字段
                checkFieldChange(EDIT_DIMENSION_NAME, newDim.getName(), oldDim.getName(), modifyCount, oldValues, newValues);
//                checkFieldChange(EDIT_DIMENSION_TYPE, newDim.getType(), oldDim.getType(), modifyCount, oldValues, newValues);
                checkFieldChange(EDIT_REQUIRED_1, newDim.getMustInput(), oldDim.getMustInput(), modifyCount, oldValues, newValues);
                checkFieldChange(EDIT_REMARK, newDim.getRemark(), oldDim.getRemark(), modifyCount, oldValues, newValues);
                checkFieldChange(SORT_DIMENSION, newDim.getSort(), oldDim.getSort(), modifyCount, oldValues, newValues);

                // 新增逻辑：比较类型和选项列表
                boolean typeChanged = !Objects.equals(newDim.getType(), oldDim.getType());
                boolean optionsChanged = false;
                if(CollUtil.isNotEmpty(newDim.getOptionList())  && CollUtil.isNotEmpty(oldDim.getOptionList())){
                    optionsChanged = !CollUtil.isEqualList(newDim.getOptionList(), oldDim.getOptionList());
                }
                // 只要类型或选项列表变化，就强制触发 EDIT_DIMENSION_TYPE 的修改日志
                if (typeChanged || optionsChanged) {
                    // 传递一个占位符（如空字符串）表示逻辑变更，而非实际值比较
                    checkFieldChange(EDIT_DIMENSION_TYPE, "", "", modifyCount, oldValues, newValues);
                }

                // 从旧map中移除已处理的维度
                oldDimMap.remove(newDim.getDimensionsCode());
            }
        });

        // 处理删除的维度（oldDimMap中剩余的维度）；oldDimMap为空，则没有删除的维度
        if (!oldDimMap.isEmpty()) {
            List<String> deletedNames = oldDimMap.values().stream()
                    .map(FeedbackDimensionsDTO::getName)
                    .collect(Collectors.toList());
            modifyCount.incrementAndGet();
            oldValues.put(DELETE_DIMENSION.getFieldName(), String.join(", ", deletedNames));
            newValues.put(DELETE_DIMENSION.getFieldName(), "");
        }

        // 插入日志
        if (modifyCount.get() > 0) {
            modifyLogDOMapper.insertModifyLog(
                    feedbackCode,
                    EDIT_DIMENSION_NAME.getModuleName(),
                    modifyCount.get(),
                    oldValues,
                    newValues
            );
        }
    }

    private <T> void checkFieldChange(EvaluationDimensionFieldEnum fieldEnum,
                                      T newValue, T oldValue,
                                      AtomicInteger modifyCount,
                                      Map<String, Object> oldValues,
                                      Map<String, Object> newValues) {
        // 如果是 EDIT_DIMENSION_TYPE，直接标记为已修改（无需比较值）
        if (fieldEnum == EDIT_DIMENSION_TYPE) {
            if (!oldValues.containsKey(fieldEnum.getFieldName())) {
                modifyCount.incrementAndGet();
                oldValues.put(fieldEnum.getFieldName(), "");
                newValues.put(fieldEnum.getFieldName(), "");
            }
            return;
        }

        // 原有逻辑：其他字段的比较
        if (!Objects.equals(newValue, oldValue)) {
            if (!oldValues.containsKey(fieldEnum.getFieldName())) {
                modifyCount.incrementAndGet();
                // 对于需要记录详情的字段，记录实际值
//                if (fieldEnum == SORT_DIMENSION) {
//                    oldValues.put(fieldEnum.getFieldName(), oldValue);
//                    newValues.put(fieldEnum.getFieldName(), newValue);
//                } else {
//                    oldValues.put(fieldEnum.getFieldName(), "");
//                    newValues.put(fieldEnum.getFieldName(), "");
//                }
                oldValues.put(fieldEnum.getFieldName(), "");
                newValues.put(fieldEnum.getFieldName(), "");
            }
        }
    }

    private void checkAndLogDimensionsAdd(String feedbackCode,
                                          List<FeedbackDimensionsDTO> newDims,
                                          List<FeedbackDimensionsDTO> oldDims) {
        // 新增维度：没有dimensionsCode或不在旧列表中
        List<FeedbackDimensionsDTO> addedDims = newDims.stream()
                .filter(newDim ->
                        newDim.getDimensionsCode() == null
                                || oldDims.stream()
                                .noneMatch(oldDim ->
                                        Objects.equals(oldDim.getDimensionsCode(), newDim.getDimensionsCode()
                                        )
                                )
                )
                .collect(Collectors.toList());

        if (!addedDims.isEmpty()) {
            Map<String, Object> oldVal = new HashMap<>();
            oldVal.put(ADD_DIMENSION.getFieldName(), "");

            Map<String, Object> newVal = new HashMap<>();
            newVal.put(ADD_DIMENSION.getFieldName(),
                    addedDims.stream()
                            .map(FeedbackDimensionsDTO::getName)
                            .collect(Collectors.joining(", ")));

            modifyLogDOMapper.insertModifyLog(
                    feedbackCode,
                    ADD_DIMENSION.getModuleName(),
                    1, // 根据PRD要求，新增始终计1次
                    oldVal,
                    newVal
            );
        }
    }

    private void checkAndLogInputConfigEdit(String feedbackCode,
                                    FeedBackUpdateDTO updateDTO,
                                    FeedbackConfigDO oldConfig) {
        Map<String, Object> oldValues = new HashMap<>();
        Map<String, Object> newValues = new HashMap<>();
        AtomicInteger modifyCount = new AtomicInteger();

        // 是否设置输入框
        if (!Objects.equals(oldConfig.getEnableInput(), updateDTO.getEnableInput())) {
            modifyCount.incrementAndGet();
            String fieldName = EDIT_ENABLE_INPUT.getFieldName();
            oldValues.put(fieldName, FeedBackMustInputEnum.fromCode(oldConfig.getEnableInput()));
            newValues.put(fieldName, FeedBackMustInputEnum.fromCode(updateDTO.getEnableInput()));
        }

        // 输入框必填
        if (!Objects.equals(oldConfig.getMustInput(), updateDTO.getMustInput())) {
            modifyCount.incrementAndGet();
            String fieldName = EDIT_REQUIRED_2.getFieldName();
            oldValues.put(fieldName, FeedBackMustInputEnum.fromCode(oldConfig.getMustInput()));
            newValues.put(fieldName, FeedBackMustInputEnum.fromCode(updateDTO.getMustInput()));
        }

        // 输入框提示文案
        if (!Objects.equals(oldConfig.getInputText(), updateDTO.getInputText())) {
            modifyCount.incrementAndGet();
            String fieldName = EDIT_INPUT_PLACEHOLDER.getFieldName();
            oldValues.put(fieldName, oldConfig.getInputText());
            newValues.put(fieldName, updateDTO.getInputText());
        }

        // 插入日志（仅当有修改时）
        if (modifyCount.get() > 0) {
            modifyLogDOMapper.insertModifyLog(
                    feedbackCode,
                    EDIT_ENABLE_INPUT.getModuleName(),
                    modifyCount.get(),
                    oldValues,
                    newValues
            );
        }
    }

    private void checkAndLogDescriptionEdit(String feedbackCode,
                                    FeedBackUpdateDTO updateDTO,
                                    FeedbackConfigDO oldConfig) {
        if (!Objects.equals(oldConfig.getRemark(), updateDTO.getRemark())) {
            Map<String, Object> oldVal = new HashMap<>();
            String moduleName = EDIT_DESCRIPTION.getModuleName();
            String fieldName = EDIT_DESCRIPTION.getFieldName();
            oldVal.put(fieldName, oldConfig.getRemark());

            Map<String, Object> newVal = new HashMap<>();
            newVal.put(fieldName, updateDTO.getRemark());

            modifyLogDOMapper.insertModifyLog(
                    feedbackCode,
                    moduleName,
                    1,
                    oldVal,
                    newVal
            );
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(String feedbackCode) {
        FeedbackConfigDO feedbackConfigDO = feedbackConfigDOMapper.selectOneByFeedBackCode(feedbackCode);
        //查不到数据直接返回false
        if(feedbackConfigDO == null){
            return false;
        }
        //启用状态为启用无法删除
        if(FeedBackEnableStatusEnum.ENABLE.getCode().equals(feedbackConfigDO.getEnableStatus())){
            throw exception(ErrorCodeConstants.FEEDBACK_ENABLE_STATUS_ERROR);
        }
        //查询快照表是否有数据、有说明启用过无法被删除
        if(feedbackSnapshotDOMapper.selectCount(new LambdaQueryWrapperX<FeedbackSnapshotDO>()
                .eq(FeedbackSnapshotDO::getFeedbackCode, feedbackCode))>0){
            throw exception(ErrorCodeConstants.FEEDBACK_ENABLE_EXISTS_ERROR);
        }

        //执行逻辑删除 修改isDeleted字段为false
        feedbackConfigDO.setIsDeleted(true);
        feedbackConfigDO.setUpdatedTime(LocalDateTime.now());
        feedbackConfigDO.setLastModifyUser(WebFrameworkUtils.getLoginUserName());
        //删除之前的评价维度
        deleteFeedbackDimensions(feedbackConfigDO.getFeedbackCode());


        return updateById(feedbackConfigDO);
    }

    @Override
    public PageResult<FeedBackConfigVO> selectFeedBackPage(PageParam dto) {
        PageResult<FeedbackConfigDO> pageResult = baseMapper.selectPage(dto, new LambdaQueryWrapperX<FeedbackConfigDO>()
                .eq(FeedbackConfigDO::getIsDeleted, false)
                .orderByDesc(FeedbackConfigDO::getEnableStatus)
                .orderByDesc(BaseDO::getUpdatedTime));
        List<FeedBackConfigVO> list = pageResult.getList().stream().map(item -> {
            FeedBackConfigVO vo = new FeedBackConfigVO();
            BeanUtils.copyProperties(item, vo);
            vo.setFeedbackDimensionsText(FeedBackTypeEnum.fromCode(item.getFeedbackDimensions()));
            vo.setEnableStatusText(FeedBackEnableStatusEnum.fromCode(item.getEnableStatus()));
            return vo;
        }).collect(Collectors.toList());
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateEnableStatus(FeedBackEnableStatusDTO dto) {
        log.info("启用评价问卷，updateEnableStatus，FeedBackEnableStatusDTO:{}", JSON.toJSONString(dto));

        FeedbackConfigDO feedbackConfigDO = feedbackConfigDOMapper.selectOneByFeedBackCode(dto.getFeedbackCode());
        log.info("updateEnableStatus，feedbackConfigDO:{}", feedbackConfigDO);
        //查不到数据直接返回false
        if (feedbackConfigDO == null) {
            return false;
        }

        // 保存旧状态用于日志记录
        Integer oldEnableStatus = feedbackConfigDO.getEnableStatus();

        feedbackConfigDO.setLastModifyUser(WebFrameworkUtils.getLoginUserName());
        feedbackConfigDO.setUpdatedTime(LocalDateTime.now());
        //获取租户编号
        Long tenantId = TenantContextHolder.getTenantId();

        String value = DelayQueuePrefixEnum.FEEDBACK_UP.getCode() +
                tenantId + ":" +
                feedbackConfigDO.getFeedbackCode();

        //判断是否是定时启用 如果不是定时启用 则直接修改启用状态
        if (StringUtils.isEmpty(dto.getEnableTime())) {
            //判断是否有当前环节是否有已经启用的配置
            checkEnableStatus(feedbackConfigDO);
            feedbackConfigDO.setEnableTime(LocalDateTime.now());
            feedbackConfigDO.setEnableStatus(dto.getEnableStatus());
            feedbackConfigDO.setDowntime(null);
            feedbackConfigDO.setScheduleEnableTime(null);
            //移除队列
            redisDelayQueueUtil.remove(value, RedisDelayQueueEnum.ECP_ORDER_FEEDBACK_SCHEDULED_LAUNCH.getCode());

            //修改为启用状态并且生成快照插入
            insertSnapshot(feedbackConfigDO);

        } else {
            //定时启用逻辑
            LocalDateTime enableTime = TimeFormatUtil.stringToLocalDate(dto.getEnableTime());
            feedbackConfigDO.setScheduleEnableTime(enableTime);
            //校验同环节是否有相同时间上架的评价配置
            checkSameDimensionsEnableTime(feedbackConfigDO);

            if (enableTime != null && enableTime.isBefore(LocalDateTime.now())) {
                throw invalidParamException("定时时间应在当前时间之后");
            }
            long delay = Duration.between(LocalDateTime.now(), enableTime).getSeconds();
            feedbackConfigDO.setEnableTime(enableTime);
            feedbackConfigDO.setEnableStatus(FeedBackEnableStatusEnum.WAIT.getCode());

            redisDelayQueueUtil.remove(value, RedisDelayQueueEnum.ECP_ORDER_FEEDBACK_SCHEDULED_LAUNCH.getCode());
            redisDelayQueueUtil.addDelayQueue(value, delay, TimeUnit.SECONDS, RedisDelayQueueEnum.ECP_ORDER_FEEDBACK_SCHEDULED_LAUNCH.getCode());

        }

        log.info("updateEnableStatus，开始调用recordModifyLog");
        recordModifyLog(
                feedbackConfigDO.getFeedbackCode(),
                EvaluationDimensionFieldEnum.EDIT_ENABLE_STATUS.getModuleName(),
                EvaluationDimensionFieldEnum.EDIT_ENABLE_STATUS.getFieldName(),
                FeedBackEnableStatusEnum.fromCode( oldEnableStatus),
                FeedBackEnableStatusEnum.fromCode(dto.getEnableStatus()),
                1
        );

        return updateById(feedbackConfigDO);
    }

    /**
     * 记录修改日志
     *
     * @param feedbackCode 评价问卷编码
     * @param moduleName   模块名称
     * @param fieldName    字段名称
     * @param oldValue     旧值
     * @param newValue     新值
     */
    private void recordModifyLog(String feedbackCode, String moduleName, String fieldName, Object oldValue, Object newValue, int modifyFieldCount) {
        log.info("记录修改日志，feedbackCode:{}, moduleName:{}, fieldName:{}, oldValue:{}, newValue:{}, modifyFieldCount:{}",
                feedbackCode, moduleName, fieldName, oldValue, newValue, modifyFieldCount);

        Map<String, Object> oldValues = new HashMap<>();
        oldValues.put(fieldName, oldValue == null ? "" : oldValue);

        Map<String, Object> newValues = new HashMap<>();
        newValues.put(fieldName, newValue == null ? "" : newValue);

        modifyLogDOMapper.insertModifyLog(feedbackCode, moduleName, modifyFieldCount, oldValues, newValues);
    }

    /**
     * 已有该环节评价问卷在相同启用时间，请重新设置启用时间
     * @param feedbackConfigDO 评价配置
     */
    private void checkSameDimensionsEnableTime(FeedbackConfigDO feedbackConfigDO) {
        FeedbackConfigDO configDO= feedbackConfigDOMapper
                .selectOneByDimensionsAndStatus(
                        feedbackConfigDO.getFeedbackDimensions(),
                        FeedBackEnableStatusEnum.WAIT.getCode(),
                        feedbackConfigDO.getScheduleEnableTime());
        if(configDO != null){
            throw exception(ErrorCodeConstants.FEEDBACK_SAME_ENABLE_TIME_ERROR);
        }
    }

    /**
     * 插入快照
     * @param feedbackConfigDO 评价配置
     */
    private void insertSnapshot(FeedbackConfigDO feedbackConfigDO) {
        FeedbackSnapshotDO feedbackSnapshotDO = new FeedbackSnapshotDO();
        //设置快照编码

        BeanUtil.copyProperties(feedbackConfigDO,feedbackSnapshotDO,"id");
        String code =feedbackConfigDO.getFeedbackCode()+"_"+TimeFormatUtil.timeToStringByFormat(LocalDateTime.now(),formatter_7);
        feedbackSnapshotDO.setSnapshotCode(code);
        //查询评价维度
        List<FeedbackDimensionsDO> feedbackDimensionsList = feedbackDimensionsDOMapper.selectListByFeedBackCode(feedbackConfigDO.getFeedbackCode());
        List<FeedbackDimensionsVO>  feedbackDimensionsVOList = new ArrayList<>(feedbackDimensionsList.size());
        viewAssembleFeedbackDimensions(feedbackDimensionsList,feedbackDimensionsVOList);
        feedbackSnapshotDO.setSnapshotJson(JSONUtil.toJsonStr(feedbackDimensionsVOList));
        feedbackSnapshotDOMapper.insert(feedbackSnapshotDO);
    }

    private void checkEnableStatus(FeedbackConfigDO feedbackConfigDO) {
        FeedbackConfigDO checkDO = baseMapper.selectOne(new LambdaQueryWrapperX<FeedbackConfigDO>()
                .eq(FeedbackConfigDO::getFeedbackDimensions, feedbackConfigDO.getFeedbackDimensions())
                .eq(FeedbackConfigDO::getEnableStatus, FeedBackEnableStatusEnum.ENABLE.getCode())
                .eq(BaseDO::getIsDeleted,false)
                .orderByDesc(FeedbackConfigDO::getId)
                .last(Constants.LIMIT_ONE));
        if(checkDO != null){
            throw exception(ErrorCodeConstants.FEEDBACK_ENABLE_CHECK_ERROR);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDisableStatus(FeedBackEnableStatusDTO dto) {
        log.info("updateDisableStatus，开始 FeedBackEnableStatusDTO dto:{}", JSON.toJSONString(dto));

        FeedbackConfigDO feedbackConfigDO = feedbackConfigDOMapper.selectOneByFeedBackCode(dto.getFeedbackCode());
        log.info("updateDisableStatus，feedbackConfigDO:{}", JSON.toJSONString(feedbackConfigDO));
        //查不到数据直接返回false
        if(feedbackConfigDO == null){
            return false;
        }

        // 保存旧状态用于日志记录
        Integer oldEnableStatus = feedbackConfigDO.getEnableStatus();

        feedbackConfigDO.setLastModifyUser(WebFrameworkUtils.getLoginUserName());
        feedbackConfigDO.setScheduleEnableTime(null);
        feedbackConfigDO.setUpdatedTime(LocalDateTime.now());
        feedbackConfigDO.setDowntime(LocalDateTime.now());
        if (FeedBackEnableStatusEnum.WAIT.getCode().equals(feedbackConfigDO.getEnableStatus())){
            feedbackConfigDO.setEnableTime(null);
        }
        feedbackConfigDO.setEnableStatus(dto.getEnableStatus());
        handleSnapshot(feedbackConfigDO);

        // 插入操作记录（改造后的日志记录）
        recordModifyLog(
                feedbackConfigDO.getFeedbackCode(),
                EvaluationDimensionFieldEnum.EDIT_ENABLE_STATUS.getModuleName(),
                EvaluationDimensionFieldEnum.EDIT_ENABLE_STATUS.getFieldName(),
                FeedBackEnableStatusEnum.fromCode(oldEnableStatus),
                FeedBackEnableStatusEnum.fromCode(dto.getEnableStatus()),
                1 // 修改字段数量
        );

        //获取租户编号
        Long tenantId = TenantContextHolder.getTenantId();

        String value = DelayQueuePrefixEnum.FEEDBACK_UP.getCode() +
                tenantId + ":" +
                feedbackConfigDO.getFeedbackCode();

        redisDelayQueueUtil.remove(value, RedisDelayQueueEnum.ECP_ORDER_FEEDBACK_SCHEDULED_LAUNCH.getCode());

        //更新配置表
        return  updateById(feedbackConfigDO);
    }

    private void handleSnapshot(FeedbackConfigDO feedbackConfigDO) {
        //查询快照表最近一条记录，并且recods表进行统计数量回写
        FeedbackSnapshotDO item = feedbackSnapshotDOMapper.selectOne(new LambdaQueryWrapperX<FeedbackSnapshotDO>()
                .eq(FeedbackSnapshotDO::getFeedbackCode, feedbackConfigDO.getFeedbackCode())
                .orderByDesc(FeedbackSnapshotDO::getId)
                .last(Constants.LIMIT_ONE));

        if(item != null){
            log.info("handleSnapshot 快照表 item:{}",item);
            Long aLong = feedbackRecordsDOMapper.selectCount(new LambdaQueryWrapperX<FeedbackRecordsDO>()
                    .eq(BaseDO::getIsDeleted, false)
                    .eq(FeedbackRecordsDO::getSnapshotCode, item.getSnapshotCode()));
            item.setSubmitNum(aLong.intValue());
            item.setDowntime(feedbackConfigDO.getDowntime());
            item.setUpdatedTime(LocalDateTime.now());
            //更新快照表
            feedbackSnapshotDOMapper.updateById(item);
        }

    }

    @Override
    public FeedbackSettingVO getFeedBackByDimensions(FeedbackQueryDTO dto) {
        FeedbackSettingVO feedBackConfigVO = new FeedbackSettingVO();
        FeedbackConfigDO feedbackConfigDO = feedbackConfigDOMapper.selectOneByDimensions(dto.getFeedbackDimensions());
        if(feedbackConfigDO == null){
            log.info("getFeedBackByDimensions 查询feedbackConfigDO为空，入参FeedbackQueryDTO:{}",dto);
            return feedBackConfigVO;
        }

        //填充基本字段
        BeanUtil.copyProperties(feedbackConfigDO,feedBackConfigVO);
        feedBackConfigVO.setFeedbackDimensionsText(FeedBackTypeEnum.fromCode(feedbackConfigDO.getFeedbackDimensions()));

        //查询评价维度
        List<FeedbackDimensionsDO> feedbackDimensionsList = feedbackDimensionsDOMapper.selectListByFeedBackCode(feedbackConfigDO.getFeedbackCode());
        //组装FeedbackDimensionsVO
        List<FeedbackDimensionsAppVO> feedbackDimensionsVOList = new ArrayList<>();
        if(CollUtil.isNotEmpty(feedbackDimensionsList)){
            feedbackDimensionsList.forEach(feedbackDimensionsDO -> {
                FeedbackDimensionsAppVO feedbackDimensionsVO = new FeedbackDimensionsAppVO();
                BeanUtil.copyProperties(feedbackDimensionsDO,feedbackDimensionsVO,"optionJson");
                feedbackDimensionsVO.setMustInputText(FeedBackMustInputEnum.fromCode(feedbackDimensionsDO.getMustInput()));
                feedbackDimensionsVO.setTypeText(FeedDimensionsTypeEnum.fromCode(feedbackDimensionsDO.getType()));
                //设置JSON
                if(StringUtils.isNotBlank(feedbackDimensionsDO.getOptionJson())){
                    feedbackDimensionsVO.setOptionJson(JSONUtil.parseArray(feedbackDimensionsDO.getOptionJson()).toList(FeedbackOptionsDTO.class));
                }
                feedbackDimensionsVOList.add(feedbackDimensionsVO);
            });
        }
        feedBackConfigVO.setDimensionsContent(feedbackDimensionsVOList);
        //查询最近一条快照
        FeedbackSnapshotDO feedbackSnapshotDO = feedbackSnapshotDOMapper.selectOne(new LambdaQueryWrapperX<FeedbackSnapshotDO>()
                .eq(FeedbackSnapshotDO::getFeedbackCode, feedbackConfigDO.getFeedbackCode())
                 .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(FeedbackSnapshotDO::getId)
                .last(Constants.LIMIT_ONE));
        feedBackConfigVO.setSnapshotCode(feedbackSnapshotDO.getSnapshotCode());
        return feedBackConfigVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void launchFeedback(String feedbackCode) {
        FeedbackConfigDO feedbackConfigDO = feedbackConfigDOMapper.selectOneByFeedBackCode(feedbackCode);

        //查不到数据直接返回false
        if(feedbackConfigDO == null){
            log.info("定时启用---未查到对应的评价配置配置,feedbackCode:{}",feedbackCode);
            return ;
        }
        if(FeedBackEnableStatusEnum.ENABLE.getCode().equals(feedbackConfigDO.getEnableStatus())){
            log.info("定时启用---该评价配置已经是启用状态,feedbackConfigDO:{}",feedbackConfigDO);
            return ;
        }
        if(feedbackConfigDO.getScheduleEnableTime() == null){
            log.info("定时启用---该评价配置定时时间字段为null 应该是被停用,feedbackConfigDO:{}",feedbackConfigDO);
            return;
        }
        LocalDateTime enableTime = feedbackConfigDO.getEnableTime();
        //查询当前环节是否有已经启用的数据
        FeedbackConfigDO configDO = feedbackConfigDOMapper
                .selectOneByDimensionsAndStatus(
                        feedbackConfigDO.getFeedbackDimensions(),
                        FeedBackEnableStatusEnum.ENABLE.getCode(),null);
        if(configDO != null){
            log.info("定时启用---已有的配置信息configDO:{}",configDO);
            //设置为停用
            configDO.setEnableStatus(FeedBackEnableStatusEnum.DISABLE.getCode());
            configDO.setDowntime(enableTime);
            configDO.setLastModifyUser(feedbackConfigDO.getLastModifyUser());
            configDO.setScheduleEnableTime(null);
            configDO.setUpdatedTime(enableTime);
            handleSnapshot(configDO);
            updateById(configDO);
        }
        feedbackConfigDO.setEnableStatus(FeedBackEnableStatusEnum.ENABLE.getCode());
        feedbackConfigDO.setUpdatedTime(feedbackConfigDO.getScheduleEnableTime());
        feedbackConfigDO.setDowntime(null);
        feedbackConfigDO.setScheduleEnableTime(null);


        //修改为启用状态并且生成快照插入
        insertSnapshot(feedbackConfigDO);
        updateById(feedbackConfigDO);

    }

    /**
     * 获取超时未启用的评价配置
     * @param pageSize 一次处理条数
     * @return feedbackCodes
     */
    @Override
    public List<String> getPassTimeUnEnableFeedback(Integer pageSize) {
        //查询 ScheduleEnableTime不为null 并且ScheduleEnableTime <当前时间的feedbackConfigDO
        List<FeedbackConfigDO> feedbackConfigDOS = feedbackConfigDOMapper.selectList(new LambdaQueryWrapperX<FeedbackConfigDO>()
                .eq(FeedbackConfigDO::getEnableStatus, FeedBackEnableStatusEnum.WAIT.getCode())
                .isNotNull(FeedbackConfigDO::getScheduleEnableTime)
                .le(FeedbackConfigDO::getScheduleEnableTime, LocalDateTime.now())
                .eq(BaseDO::getIsDeleted,false)
                .orderByDesc(FeedbackConfigDO::getId)
                .last("limit "+ pageSize));
        //过滤feedbackConfigDOS
        return feedbackConfigDOS.stream().map(FeedbackConfigDO::getFeedbackCode).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handPassUnEnableFeedback(String feedbackCode) {
        launchFeedback( feedbackCode);
        return  true ;
    }

    @Override
    public List<String> getFeedBackCodeListByDimensionsCode(String dimensionsCode) {
        List<FeedbackConfigDO> list = feedbackConfigDOMapper.selectFeedBackCodeListByDimensionsCode(dimensionsCode);
        if (CollUtil.isNotEmpty(list)) {
            // 创建一个映射，用于快速查找启用状态
            Map<String, Integer> feedbackEnableStatusMap = list.stream()
                    .collect(Collectors.toMap(FeedbackConfigDO::getFeedbackCode, FeedbackConfigDO::getEnableStatus));

            List<FeedbackSnapshotDO> snapshotCodeByFeedbackCode = feedbackSnapshotDOMapper.getSnapshotCodeByFeedbackCode(
                    list.stream().map(FeedbackConfigDO::getFeedbackCode).collect(Collectors.toList()));

            // 根据 feedbackCode 进行倒序排列
            return snapshotCodeByFeedbackCode.stream()
                    .sorted((s1, s2) -> {
                        String code1 = s1.getFeedbackCode();
                        String code2 = s2.getFeedbackCode();

                        // 启用的配置优先
                        boolean isEnable1 = FeedBackEnableStatusEnum.ENABLE.getCode().equals(feedbackEnableStatusMap.get(code1));
                        boolean isEnable2 = FeedBackEnableStatusEnum.ENABLE.getCode().equals(feedbackEnableStatusMap.get(code2));

                        if (isEnable1 && !isEnable2) {
                            return -1; // s1 排在前面
                        } else if (!isEnable1 && isEnable2) {
                            return 1;  // s2 排在前面
                        } else {
                            // 都启用或都不启用，按原逻辑倒序排列
                            return s2.getFeedbackCode().compareTo(s1.getFeedbackCode());
                        }
                    })
                    .map(FeedbackSnapshotDO::getFeedbackCode)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return null;
    }
    /**
     * 删除评价维度
     * @param feedbackCode 评价配置编码
     */
    private void deleteFeedbackDimensions(String feedbackCode) {
        FeedbackDimensionsDO feedbackDimensionsDO = new FeedbackDimensionsDO();
        feedbackDimensionsDO.setIsDeleted(true);
        feedbackDimensionsDO.setUpdatedTime(LocalDateTime.now());
        feedbackDimensionsDOMapper.update(feedbackDimensionsDO, new LambdaUpdateWrapper<FeedbackDimensionsDO>().eq(FeedbackDimensionsDO::getFeedbackCode, feedbackCode));

    }


    /**
     * 设置评价配置列表
     * @param feedbackDimensionsList
     * @param feedbackDimensionsVOList
     */
    private void viewAssembleFeedbackDimensions(List<FeedbackDimensionsDO> feedbackDimensionsList, List<FeedbackDimensionsVO>  feedbackDimensionsVOList) {
        if(CollUtil.isNotEmpty(feedbackDimensionsList)){
            feedbackDimensionsList.forEach(feedbackDimensionsDO -> {
                FeedbackDimensionsVO feedbackDimensionsVO = new FeedbackDimensionsVO();
                BeanUtil.copyProperties(feedbackDimensionsDO,feedbackDimensionsVO);
                feedbackDimensionsVO.setMustInputText(FeedBackMustInputEnum.fromCode(feedbackDimensionsDO.getMustInput()));
                feedbackDimensionsVO.setTypeText(FeedDimensionsTypeEnum.fromCode(feedbackDimensionsDO.getType()));
                //设置JSON
                if(StringUtils.isNotBlank(feedbackDimensionsDO.getOptionJson())){
                    feedbackDimensionsVO.setOptionList(JSONUtil.parseArray(feedbackDimensionsDO.getOptionJson()).toList(FeedbackOptionsDTO.class));
                }
                feedbackDimensionsVOList.add(feedbackDimensionsVO);
            });
        }

    }

    /**
     * 插入评价维度
     * @param feedbackConfigDO 评价设置DO
     * @param feedbackDimensions 评价维度
     */
    private void insertFeedbackDimensions(FeedbackConfigDO feedbackConfigDO, List<FeedbackDimensionsDTO> feedbackDimensions) {
        List<FeedbackDimensionsDO> list = new ArrayList<>();
        feedbackDimensions.forEach(feedbackDimensionsDTO -> {
            FeedbackDimensionsDO feedbackDimensionsDO = new FeedbackDimensionsDO();

            feedbackDimensionsDO.setFeedbackCode(feedbackConfigDO.getFeedbackCode());
            //校验
            if(!FeedDimensionsTypeEnum.STAR_RATING.getCode().equals(feedbackDimensionsDTO.getType())){
                List<FeedbackOptionsDTO> optionList = feedbackDimensionsDTO.getOptionList();
                //校验选项
                if(optionList ==null){
                    throw exception(ErrorCodeConstants.FEEDBACK_OPTIONS_EXIST_ERROR);
                }
                //校验选项个数
                if(optionList.size()>10){
                    throw exception(ErrorCodeConstants.FEEDBACK_OPTIONS_OVER_SIZE_ERROR);
                }
                feedbackDimensionsDO.setOptionJson(JSONUtil.toJsonStr(optionList));
            }

            BeanUtil.copyProperties(feedbackDimensionsDTO,feedbackDimensionsDO);
            feedbackDimensionsDO.setDimensionsCode(ecpIdUtil.nextIdStr());

            list.add(feedbackDimensionsDO);
        });
        feedbackDimensionsDOMapper.insertBatch(list);
    }

    /**
     * 设置评价code
     * @param feedbackConfigDO 评价设置DO
     */
    private void setMaxConfigCodeDO(FeedbackConfigDO feedbackConfigDO) {
        LambdaQueryWrapperX<FeedbackConfigDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.select(FeedbackConfigDO::getFeedbackCode)
                .eq(FeedbackConfigDO::getFeedbackDimensions,feedbackConfigDO.getFeedbackDimensions())
                .orderByDesc(FeedbackConfigDO::getId).last(Constants.LIMIT_ONE);
        FeedbackConfigDO maxDO = feedbackConfigDOMapper.selectOne(wrapper);
        if(maxDO != null && StringUtils.isNotBlank(maxDO.getFeedbackCode())
                && maxDO.getFeedbackCode().startsWith(feedbackConfigDO.getFeedbackDimensions())){
            feedbackConfigDO.setFeedbackCode(CodeGenerator.nextCode(maxDO.getFeedbackCode()));
        }else {
            feedbackConfigDO.setFeedbackCode(CodeGenerator.initCodeWithPrefix(feedbackConfigDO.getFeedbackDimensions()));
        }
    }
}

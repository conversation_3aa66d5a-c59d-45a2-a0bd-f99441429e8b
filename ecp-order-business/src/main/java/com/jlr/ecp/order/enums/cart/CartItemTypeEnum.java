package com.jlr.ecp.order.enums.cart;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 * 商品履约类型
 * 0：虚拟组合商品履约
 * 1：远程车控Remote Service
 * 2：PIVI Subscription Service
 * 3：实物商品
 */
@AllArgsConstructor
@Getter
public enum CartItemTypeEnum {
    /**
     * 虚拟组合商品履约
     */
    BUNDLED_GOODS(0, "虚拟组合商品履约"),
    /**
     * 远程车控
     */
    VCS(1, "远程车控Remote Service"),
    /**
     * PIVI Subscription Service
     */
    PIVI(2, "PIVI Subscription Service"),
    /**
     * 实物商品
     */
    BRAND_GOODS(3, "实物商品"),
    /**
     * 电子兑换券
     */
    E_COUPON(5, "电子兑换券"),
    ;

    /**
     * code
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;

    public static boolean isSingleVcs(Integer cartItemType){
        return VCS.getCode().equals(cartItemType) || PIVI.getCode().equals(cartItemType);
    }
}


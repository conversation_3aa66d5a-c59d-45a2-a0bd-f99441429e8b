package com.jlr.ecp.order.service.order.address;

import com.jlr.ecp.order.api.order.dto.address.OrderGiftAddressDTO;
import com.jlr.ecp.order.api.order.vo.address.AppOrderGiftAddressDetailVO;

import java.util.List;


public interface OrderGiftAddressService {

    /**
     * 根据OrderCode查询收货信息
     * @return AppOrderGiftAddressDetailVO
     */
    AppOrderGiftAddressDetailVO getOrderGiftAddressByOrderCode(String orderCode);

    /**
     * 新增收货信息
     */
    void saveOrderGiftAddress(OrderGiftAddressDTO dto, List<String> orderCodeList);

    /**
     * 代客下单 在等待支付页面 可配置地址
     */
    void saveOrderGiftAddressForValet(OrderGiftAddressDTO dto, List<String> orderCodeList);
}

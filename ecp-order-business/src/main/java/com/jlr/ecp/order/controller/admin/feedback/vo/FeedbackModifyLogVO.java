package com.jlr.ecp.order.controller.admin.feedback.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "管理后台 -  评价操作记录VO ")
@ToString(callSuper = true)
public class FeedbackModifyLogVO {
    @Schema(description = "修改记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "评价配置编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String feedbackCode;

    @Schema(description = "修改模块", requiredMode = Schema.RequiredMode.REQUIRED)
    private String modifyModule;

    @Schema(description = "修改字段数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer modifyFieldCount;

    /**
     * 修改字段
     */
    @Schema(description = "修改字段")
    private String modifyField;

    @Schema(description = "是否显示 修改详情")
    private Boolean modifyDetail;

    @Schema(description = "修改前字段值（JSON格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String modifyFieldOldValue;

    @Schema(description = "修改后字段值（JSON格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String modifyFieldNewValue;

    @Schema(description = "操作时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;

    @Schema(description = "操作人账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operateUser;

    @Schema(description = "字段变更详情")
    private List<FieldChange> changes;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FieldChange {
        @Schema(description = "字段名称")
        private String fieldName;

        @Schema(description = "旧值")
        private String oldValue;

        @Schema(description = "新值")
        private String newValue;
    }

}

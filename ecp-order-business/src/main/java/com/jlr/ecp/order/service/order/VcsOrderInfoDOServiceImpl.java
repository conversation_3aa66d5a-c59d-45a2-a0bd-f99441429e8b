package com.jlr.ecp.order.service.order;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.order.dal.dataobject.order.VCSOrderInfoDO;
import com.jlr.ecp.order.dal.mysql.order.VcsOrderInfoDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_vcs_order_info(t_vcs_order_info)】的数据库操作Service实现
* @createDate 2023-12-20 10:41:04
*/
@Service
@Slf4j
public class VcsOrderInfoDOServiceImpl extends ServiceImpl<VcsOrderInfoDOMapper, VCSOrderInfoDO>
    implements VcsOrderInfoDOService{

}





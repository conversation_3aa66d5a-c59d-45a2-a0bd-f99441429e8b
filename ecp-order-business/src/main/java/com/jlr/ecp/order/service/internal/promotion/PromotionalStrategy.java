package com.jlr.ecp.order.service.internal.promotion;


import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.service.internal.promotion.dto.PromotionRespDto;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;


public interface PromotionalStrategy {
    double HUNDRED = 100d;

    /***
     * <AUTHOR>
     * @description 计算优惠
     * @date 2025/3/6 17:06
     * @param skuInfos: 商品SKU列表
     * @param promotion: 参与计算的优惠券
     * @return: com.jlr.ecp.order.service.internal.productprice.PromotionRespDto： discountTotalAmount 为优惠总金额;
     * cartSkuProductList 为优惠明细，chooseFlag仅针对积分优惠才有用，字段为true表示采用积分进行优惠；
     */

    PromotionRespDto executePromotional(List<CartProductSkuInfo> skuInfos, PromotionDto promotion);


    /***
     * <AUTHOR>
     * @description 检查优惠券或者积分：通用部分
     * @date 2025/3/7 09:34
     * @param skuInfos: SKU商品列表
     * @param promotions: 用户持有的优惠券列表
     * @param userChoosePromotion: 用户选择的优惠
     * @return: PromotionDto 校验通过，返回优惠券信息及符合优惠券的商品总价格
     */

    List<CartProductSkuInfo> checkUserChoose(List<CartProductSkuInfo> skuInfos, List<PromotionDto> promotions, PromotionDto userChoosePromotion) throws ServiceException;

    @NotNull
    default CartProductSkuInfo getCartProductSkuInfo(CartProductSkuInfo skuInfo) {
        CartProductSkuInfo copiedSkuInfo = new CartProductSkuInfo();
        copiedSkuInfo.setProductSkuCode(skuInfo.getProductSkuCode()); // 复制商品SKU编码
        copiedSkuInfo.setProductCode(skuInfo.getProductCode());
        copiedSkuInfo.setSalePrice(skuInfo.getSalePrice()); // 复制售价
        copiedSkuInfo.setSalePointsPrice(skuInfo.getSalePointsPrice()); // 复制现金+积分方式：销售金额
        copiedSkuInfo.setSalePoints(skuInfo.getSalePoints()); // 复制现金+积分方式：积分
        copiedSkuInfo.setSupportCashAndPoints(skuInfo.getSupportCashAndPoints());
        copiedSkuInfo.setCouponModuleCodeList(skuInfo.getCouponModuleCodeList() == null ? new ArrayList<>() : new ArrayList<>(skuInfo.getCouponModuleCodeList())); // 复制优惠券模版列表
        copiedSkuInfo.setChooseCouponCode(skuInfo.getChooseCouponCode()); // 复制用户选择的优惠券
        copiedSkuInfo.setChooseCouponType(skuInfo.getChooseCouponType()); // 复制用户选择的优惠券类型
        copiedSkuInfo.setProductName(skuInfo.getProductName()); // 复制商品名称
        copiedSkuInfo.setChooseFlag(skuInfo.getChooseFlag());
        copiedSkuInfo.setCartItemId(skuInfo.getCartItemId());//这里赋值是为了排序
        copiedSkuInfo.setJoinCalculateFlag(skuInfo.isJoinCalculateFlag());
        copiedSkuInfo.setBusinessCode(skuInfo.getBusinessCode());
        return copiedSkuInfo;
    }
}

package com.jlr.ecp.order.dal.mysql.order;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.order.dal.dataobject.order.OrderDiscountDetailDO;
import com.jlr.ecp.order.enums.order.OrderDiscountTypeEnum;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @createDate 12/03/2025 18:33
 **/
@Mapper
public interface OrderDiscountDetailDOMapper extends BaseMapperX<OrderDiscountDetailDO> {

    default OrderDiscountDetailDO getOrderDiscountDetailDOByOrderItemCode(String orderItemCode,Integer discountType) {
        OrderDiscountDetailDO orderDiscountDetailDO =  this.selectOne(new LambdaQueryWrapper<OrderDiscountDetailDO>()
                .eq(OrderDiscountDetailDO::getOrderItemCode,orderItemCode)
                .eq(OrderDiscountDetailDO::getIsDeleted, Boolean.FALSE)
                .eq(OrderDiscountDetailDO::getDiscountType, discountType)
                .last("LIMIT 1"));
        return orderDiscountDetailDO;
    }
}

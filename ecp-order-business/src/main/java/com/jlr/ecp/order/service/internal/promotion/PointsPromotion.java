package com.jlr.ecp.order.service.internal.promotion;


import cn.hutool.core.collection.CollUtil;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil;
import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.service.internal.promotion.dto.PromotionRespDto;
import com.jlr.ecp.order.util.money.MoneyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

import java.math.BigDecimal;
import java.util.stream.Collectors;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.internal.productprice
 * @className: PointPromotion
 * @author: gaoqig
 * @description: 积分方式计算优惠价
 * @date: 2025/3/6 16:46
 * @version: 1.0
 */
@Component
@Slf4j
public class PointsPromotion extends CommonPromotion{
    @Override
    public PromotionRespDto executePromotional(List<CartProductSkuInfo> skuInfos, PromotionDto promotion) {
        PromotionRespDto result = new PromotionRespDto();
        Integer userPoints = promotion.getPoints();
        if (userPoints < 0){//如果用户积分小于零，则认为不会命中。（等于零可以，因为不排除商品配置的可用积分为零）
            return null;
        }
        //先计算积分模式下，所有能参与该优惠的所有商品汇总信息
        List<CartProductSkuInfo> canUsePromotionSkuList = skuInfos.stream()
                .filter(item-> item.getSupportCashAndPoints() != null
                        && item.getSupportCashAndPoints()
                        && item.isJoinCalculateFlag())
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(canUsePromotionSkuList)){ //如果没有商品能参与优惠，直接返回空，表示没有命中优惠
            return null;
        }

        //根据SKU纬度来统计需要的积分map
        Map<String, Integer> skuNeedPoints = canUsePromotionSkuList.stream()
                .collect(Collectors.toMap(
                        CartProductSkuInfo::getProductSkuCode, // 以 skuCode 为键
                        CartProductSkuInfo::getSalePoints,     // 以 salePoints 为值
                        Integer::sum                           // 对相同 skuCode 的 salePoints 进行求和
                ));

        // 直接在分组时指定使用 LinkedHashMap
        Map<String, List<CartProductSkuInfo>> skuListGroupBySku = skuInfos.stream()
                .collect(Collectors.groupingBy(
                        CartProductSkuInfo::getProductSkuCode,
                        LinkedHashMap::new,    // 使用 LinkedHashMap 作为外层集合类型
                        Collectors.toList()
                ));
        //先计算参与优惠商品的销售总价；分摊折扣金额时要用
        BigDecimal allProductTotalAmount = BigDecimal.ZERO; //总的商品总价（包括不参与优惠的）,这个在下面遍历时计算汇总
        BigDecimal discountAmount = BigDecimal.ZERO;// 总的优惠金额,这个在下面遍历时计算汇总
        Integer totalCostPoint = 0; //总花费的积分
        //遍历所有SKU进行，属性赋值
        List<CartProductSkuInfo> returnSkuInfos = new ArrayList<>();
        //遍历skuListGroupBySku
        for (Iterator<String> iterator = skuListGroupBySku.keySet().iterator(); iterator.hasNext(); ) {
            String key = iterator.next();
            List<CartProductSkuInfo> thisSkuList = skuListGroupBySku.get(key);
            if (thisSkuList.get(0).getSupportCashAndPoints() != null //当前商品要支持积分模式（因为同一个sku商品都是一样的，所以取第一个）
                    && skuNeedPoints.get(key) != null && (userPoints - totalCostPoint) >= skuNeedPoints.get(key)
                    && thisSkuList.get(0).isFinalJoinCalculateFlag()) {//要参与计算的才会进行优惠
                totalCostPoint += skuNeedPoints.get(key);
                for (CartProductSkuInfo skuInfo : thisSkuList) {
                    CartProductSkuInfo returnSkuInfo = getCartProductSkuInfo(skuInfo); //这里要copy一份新的sku信息，因为不能修改原数据，原数据可能还要参与后续的计算
                    allProductTotalAmount = allProductTotalAmount.add(MoneyUtil.convertToCents(returnSkuInfo.getSalePrice()));
                    returnSkuInfo.setChooseFlag(true);
                    returnSkuInfo.setChooseCouponType(CouponTypeEnum.POINTS.getType());

                    /* =========================优惠金额赋值，由当前商品的销售价金额所占比例来=====================**/
                    BigDecimal skuDiscountAmount = MoneyUtil.convertToCents(returnSkuInfo.getSalePrice()).subtract(MoneyUtil.convertToCents(returnSkuInfo.getSalePointsPrice()));//优惠金额会销售价减去积分模式下的价格
                    BigDecimal skuCouponPrice = MoneyUtil.convertToCents(returnSkuInfo.getSalePointsPrice());//当前sku优惠后花费的金额
                    discountAmount = discountAmount.add(skuDiscountAmount);

                    returnSkuInfo.setDiscountAmount(MoneyUtil.convertFromCents(skuDiscountAmount));
                    returnSkuInfo.setCouponPrice(MoneyUtil.convertFromCents(skuCouponPrice));

                    /* =========================优惠金额赋值，由当前商品的销售价金额所占比例来===================== **/
                    returnSkuInfos.add(returnSkuInfo);
                }
            } else {
                for (CartProductSkuInfo skuInfo : thisSkuList) {
                    CartProductSkuInfo returnSkuInfo = getCartProductSkuInfo(skuInfo); //这里要copy一份新的sku信息，因为不能修改原数据，原数据可能还要参与后续的计算
                    allProductTotalAmount = allProductTotalAmount.add(MoneyUtil.convertToCents(returnSkuInfo.getSalePrice()));
                    returnSkuInfo.setChooseFlag(false);
                    //returnSkuInfo.setChooseCouponType(CouponTypeEnum.POINTS.getType());
                    returnSkuInfo.setDiscountAmount("0");
                    returnSkuInfo.setCouponPrice(skuInfo.getSalePrice());
                    returnSkuInfos.add(returnSkuInfo);
                }
            }
        }
        promotion.setDiscountAmount(MoneyUtil.convertFromCents(discountAmount));
        promotion.setCostAmount(MoneyUtil.convertFromCents(allProductTotalAmount.subtract(discountAmount)));

        result.setDiscountTotalAmount(MoneyUtil.convertFromCents(discountAmount));
        result.setCostAmount(MoneyUtil.convertFromCents(allProductTotalAmount.subtract(discountAmount)));
        result.setCartSkuProductList(returnSkuInfos);
        result.setCostPoints(totalCostPoint);
        result.setCouponTypeEnum(CouponTypeEnum.POINTS);
        result.setChooseCoupon(promotion);
        return result;
    }

    @Override
    public List<CartProductSkuInfo> checkUserChoose(List<CartProductSkuInfo> skuInfos, List<PromotionDto> promotions, PromotionDto userChoosePromotion) throws ServiceException {
        int needPoints = 0;
        List<CartProductSkuInfo> canUsePointSkuList = new ArrayList<>();
        for (CartProductSkuInfo item : skuInfos){
            if (Boolean.TRUE.equals(item.getChooseFlag()) && Boolean.TRUE.equals(!item.getSupportCashAndPoints())){
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.PRODUCT_NOT_SUPPORT_POINTS);
            }
            if (Boolean.TRUE.equals(item.getChooseFlag())){
                needPoints = needPoints + item.getSalePoints();
            }
            canUsePointSkuList.add(item);
        }
        PromotionDto pointPromotion = promotions.stream().filter(x-> CouponTypeEnum.POINTS.getType().equals(x.getCouponModelClassify())).findFirst().orElse(null);
        int consumerHasPoint = 0;
        if (pointPromotion != null && pointPromotion.getPoints() != null){
            consumerHasPoint = pointPromotion.getPoints();

        }
        if (consumerHasPoint < needPoints){
           log.info("用户积分不足，所选商品所需积分:{},用户持有积分:{}", needPoints, consumerHasPoint);
           throw ServiceExceptionUtil.exception(ErrorCodeConstants.NOT_ENOUGH_POINTS);
        }

        return canUsePointSkuList;
    }
}

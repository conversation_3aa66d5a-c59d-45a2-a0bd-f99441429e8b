package com.jlr.ecp.order.util.machine.handler.logistics;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.inventory.api.InventoryOrderApi;
import com.jlr.ecp.inventory.dto.qry.InventoryOrderSkuDTO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusLogDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.order.*;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import com.jlr.ecp.order.enums.refund.RefundLogisticsStatusEnum;
import com.jlr.ecp.order.util.machine.LogisticsOrderRefundStatusMachine;
import com.jlr.ecp.order.util.machine.handler.LogisticsEventHandler;
import com.jlr.ecp.order.util.order.OrderAssembleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 退款完成
 */
@Component
@Slf4j
public class LogisticsRefundCompletedHandler implements LogisticsEventHandler {
    @Resource
    private OrderRefundDOMapper orderRefundDOMapper;

    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Resource
    private OrderStatusLogDOMapper statusLogMapper;

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;

    @Resource
    @Lazy
    private LogisticsOrderRefundStatusMachine logisticsOrderRefundStatusMachine;

    @Resource
    private OrderRefundItemDOMapper orderRefundItemDOMapper;

    @Resource
    private InventoryOrderApi inventoryOrderApi;

    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO) {
        //打印各个参数到日志中
        log.info("LogisticsRefundCompletedHandler handleEvent orderInfoDO:{},orderRefundDO:{},orderItemDO:{}",orderInfoDO,orderRefundDO,orderItemDO);
        OrderStatusLogDO logDO = OrderAssembleUtil.assembleOrderStatusLogDO(orderRefundDO.getRefundOrderCode(),orderRefundDO.getCouponRefundStatus(), RefundLogisticsStatusEnum.REFUND_COMPLETED.getCode());
        orderRefundDO.setLogisticsRefundStatus(RefundLogisticsStatusEnum.REFUND_COMPLETED.getCode());
        orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.FULL_REFUND_COMPLETED.getCode());
        orderItemDO.setAftersalesStatus(OrderItemAftersalesStatusEnum.COMPLETED.getCode());
        //根据orderItemCode查询orderRefundItemDOMapper中所有的退单行的订单金额总和
        //Integer totalRefundMoney =orderRefundItemDOMapper.getTotalRefundMoney(orderItemDO.getOrderItemCode());
        if(orderInfoDO.getOrderType().equals(OrderTypeEnum.BRAND_GOOD.getCode())){//实物商品才需要退回库存
            returnSkuInventory(orderItemDO);
        }
        //只要发生退单就设置成已关闭 这样可以防止订单行部分退的情况下 不会一直卡在待发货的状态
        orderItemDO.setItemStatus(OrderItemLogisticsStatusEnum.CLOSED.getCode());
        orderRefundDOMapper.updateById(orderRefundDO);
        orderItemDOMapper.updateById(orderItemDO);
        statusLogMapper.insert(logDO);
        //检查是否需要设置主订单为已发货
        checkIfNeedUpdateFreightTaxInfo(orderInfoDO);
        checkIfFullyShipped(orderItemDO, orderInfoDO);
        if(checkIfAllRefundCompleted(orderItemDO)){//如果子订单行全部都进入终态(已收货 或者已关闭) 则需要完成或者关闭订单
            log.info("LogisticsRefundCompletedHandler handleEvent  checkIfAllRefundCompleted true");
            int event = LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_ORDER_CLOSED.getCode();
            logisticsOrderRefundStatusMachine.changeOrderStatus(event,orderInfoDO,orderRefundDO,orderItemDO);
        }
    }

    /**
     * 如果退款订单行的状态是待发货则需要退回库存
     */
    private void returnSkuInventory(OrderItemDO orderItemDO){
        log.info("LogisticsRefundCompletedHandler handleEvent  checkIfAllRefundCompleted returnSkuInventory orderItemDO:{}",orderItemDO);
        if(orderItemDO.getItemStatus().equals(OrderItemLogisticsStatusEnum.PENDING_SHIPMENT.getCode())){
            InventoryOrderSkuDTO inventoryOrderSkuDTO = getInventoryOrderSkuDTO(orderItemDO);
            log.info("LogisticsRefundCompletedHandler handleEvent  checkIfAllRefundCompleted returnSkuInventory inventoryOrderSkuDTO:{}",inventoryOrderSkuDTO);
            CommonResult<String> result = inventoryOrderApi.orderRefund(inventoryOrderSkuDTO);
            log.info("LogisticsRefundCompletedHandler handleEvent  checkIfAllRefundCompleted returnSkuInventory result:{}",result);
            if (result == null ||!result.isSuccess()){
                //todo 这里未来需要加入重试的机制
                log.error("LogisticsRefundCompletedHandler handleEvent  checkIfAllRefundCompleted returnSkuInventory error:{}",result.getMsg());
            }

        }
    }


    private static InventoryOrderSkuDTO getInventoryOrderSkuDTO(OrderItemDO orderItemDO) {
        InventoryOrderSkuDTO inventoryOrderSkuDTO = new InventoryOrderSkuDTO();
        inventoryOrderSkuDTO.setOrderCode(orderItemDO.getOrderCode());
        InventoryOrderSkuDTO.ProductItem productItem = new InventoryOrderSkuDTO.ProductItem();
        productItem.setProductSkuCode(orderItemDO.getKingdeeSkuCode());
        productItem.setChangeNum(orderItemDO.getProductQuantity());
        List<InventoryOrderSkuDTO.ProductItem> productItems = List.of(productItem);
        inventoryOrderSkuDTO.setProductItems(productItems);
        return inventoryOrderSkuDTO;
    }

    /**
     * 检查所有的订单行是否都是已经发货,已妥投或已关闭,如果是则需要设置主订单为已发货
     */
    private void checkIfFullyShipped(OrderItemDO orderItemDO,OrderInfoDO orderInfoDO){
        log.info("LogisticsRefundCompletedHandler handleEvent  checkIfAllRefundCompleted orderItemDO:{}",orderItemDO);
        List<OrderItemDO> orderItemListByStatus = getOrderItemListByStatus(orderItemDO,List.of(OrderItemLogisticsStatusEnum.CLOSED.getCode(),OrderItemLogisticsStatusEnum.SHIPPED.getCode(),OrderItemLogisticsStatusEnum.DELIVERED.getCode()));
        List<OrderItemDO> orderItemDOS = getOrderItemDOList(orderItemDO);
        //判断两个集合的size是否相等
        log.info("LogisticsRefundCompletedHandler handleEvent  checkIfAllRefundCompleted orderItemDOS:{},orderItemListByStatus:{}",orderItemDOS,orderItemListByStatus);
        if(CollectionUtils.isNotEmpty(orderItemDOS) && CollectionUtils.isNotEmpty(orderItemListByStatus)
                && orderItemDOS.size() == orderItemListByStatus.size()){
            orderInfoDO.setLogisticsStatus(OrderLogisticsStatusEnum.FULLY_SHIPPED.getCode());
            orderInfoDO.setUpdatedTime(LocalDateTime.now());
            orderInfoDO.setUpdatedBy("system");
            orderInfoDOMapper.updateById(orderInfoDO);
        }

    }


    /**
     * 检查所有订单行是否都已经进入终态(已收货 或者已关闭)
     * @param orderItemDO
     * @return
     */
    private boolean checkIfAllRefundCompleted(OrderItemDO orderItemDO){
        log.info("LogisticsRefundCompletedHandler handleEvent  checkIfAllRefundCompleted orderItemDO:{}",orderItemDO);
        List<OrderItemDO> orderItemAftersalesDOS = getOrderItemListByStatus(orderItemDO,List.of(OrderItemLogisticsStatusEnum.CLOSED.getCode(),OrderItemLogisticsStatusEnum.RECEIVED.getCode()));
        log.info("LogisticsRefundCompletedHandler handleEvent  checkIfAllRefundCompleted orderItemAftersalesDOS:{}",orderItemAftersalesDOS);
        List<OrderItemDO> orderItemDOS = getOrderItemDOList(orderItemDO);
        log.info("LogisticsRefundCompletedHandler handleEvent  checkIfAllRefundCompleted orderItemDOS:{}",orderItemDOS);
        if(CollectionUtils.isNotEmpty(orderItemDOS) && CollectionUtils.isNotEmpty(orderItemAftersalesDOS)
                && orderItemDOS.size() == orderItemAftersalesDOS.size()){
            return true;
        }
        return false;
    }

    private List<OrderItemDO> getOrderItemDOList(OrderItemDO orderItemDO) {
        List<OrderItemDO> orderItemDOS =orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(BaseDO::getIsDeleted, Boolean.FALSE)
                .eq(OrderItemDO::getOrderCode, orderItemDO.getOrderCode())
        );
        return orderItemDOS;
    }

    private List<OrderItemDO> getOrderItemListByStatus(OrderItemDO orderItemDO,List<Integer> statusList) {
        List<OrderItemDO> orderItemAftersalesDOS =orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(BaseDO::getIsDeleted, Boolean.FALSE)
                .eq(OrderItemDO::getOrderCode, orderItemDO.getOrderCode())
                .in(OrderItemDO::getItemStatus, statusList)
        );
        return orderItemAftersalesDOS;
    }

    /**
     * 检查是否需要更新运费税率
     * 运费（不含税）计算逻辑与开票的运费逻辑保持一致，即：
     * 若未发生退款：运费用订单下第一个订单行的商品对应的税率计算
     * 若订单内所有订单行都退过款：运费用订单下第一个订单行的商品对应的税率计算
     * 若订单内部分订单行退过款：运费用未发生过退款的订单行中，第一个订单行的商品对应的税率计算
     * @param orderInfoDO
     */
    private void checkIfNeedUpdateFreightTaxInfo(OrderInfoDO orderInfoDO){
        //根据orderCode查询非售后状态的第一个子订单
        OrderItemDO orderItemDO= orderItemDOMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, orderInfoDO.getOrderCode())
                .eq(OrderItemDO::getIsDeleted, Boolean.FALSE)
                .notIn(OrderItemDO::getAftersalesStatus,List.of(OrderItemAftersalesStatusEnum.PROCESSING.getCode(), OrderItemAftersalesStatusEnum.COMPLETED.getCode()))
                .orderByAsc(OrderItemDO::getId)
                .last(Constants.LIMIT_ONE)
        );
        if(ObjectUtils.isEmpty(orderItemDO)){//如果为空则说明所有单据都已经退款
            //根据orderCode查找id最小的订单行
            orderItemDO = orderItemDOMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                    .eq(OrderItemDO::getOrderCode, orderInfoDO.getOrderCode())
                    .eq(OrderItemDO::getIsDeleted, Boolean.FALSE)
                    .orderByAsc(OrderItemDO::getId)
                    .last(Constants.LIMIT_ONE)
            );
        }
        BigDecimal taxRate = orderItemDO.getTaxRate();
        String taxCode = orderItemDO.getTaxCode();
        //更新运费税率详细信息
        orderInfoDO.setFreightTax(taxRate);
        orderInfoDO.setFreightCode(taxCode);
        orderInfoDO.setFreightTaxUpdateTime(LocalDateTime.now());
        orderInfoDOMapper.updateById(orderInfoDO);
    }

}

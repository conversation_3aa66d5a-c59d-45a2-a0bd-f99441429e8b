package com.jlr.ecp.order.controller.admin.dashboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Schema(description = "dashboard-评价分数VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScoreVO {
    @Schema(description = "名字")
    private String title;

    @Schema(description = "分数")
    private String score;
}

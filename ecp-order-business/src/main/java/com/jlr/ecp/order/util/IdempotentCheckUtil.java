package com.jlr.ecp.order.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
public class IdempotentCheckUtil {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final String OK = "OK";

    /**
     * 检查操作是否幂等。(默认5秒)
     * 通过在Redis中设置一个键值对来实现，如果键已存在，则表示该操作已经被执行过，返回false；
     * 如果键不存在，则设置键值对并返回true，表示该操作可以执行。
     * 幂等性是指一个操作多次执行的效果和一次执行的效果相同。
     *
     * @param idempotentKey 幂等键，用于唯一标识一个操作。
     * @return 如果操作可以执行，返回true；如果操作已经执行过，返回false。
     */
    public Boolean checkIdempotent(String idempotentKey) {
        if (StringUtils.isBlank(idempotentKey)) {
            return false;
        }
        return checkIdempotent(idempotentKey, OK, 5, TimeUnit.SECONDS);
    }

    /**
     * 检查操作是否幂等。
     * 通过传入的幂等键来判断当前操作是否已经执行过，以防止重复操作。如果幂等键为空，则直接认为操作不幂等。
     * 此方法主要用于简化调用，通过默认操作结果为“成功”（OK），来快速判断是否可以执行操作。
     *
     * @param idempotentKey 幂等键，用于标识操作的唯一性。如果为空，则认为操作不幂等。
     * @param expireTime 幂等键的过期时间。
     * @param timeUnit 幂等键的过期时间单位。
     * @return 如果幂等键存在且未过期，则返回false，表示操作已执行过，不应重复执行；否则返回true，表示可以执行操作。
     */
    public Boolean checkIdempotent(String idempotentKey, Integer expireTime, TimeUnit timeUnit) {
        if (StringUtils.isBlank(idempotentKey)) {
            return false;
        }
        return checkIdempotent(idempotentKey, OK, expireTime, timeUnit);
    }

    /**
     * 检查操作是否幂等。
     * 通过将一个唯一的标识符（idempotentKey）和一个值（val）绑定到Redis中来实现。如果这个绑定不存在（即操作尚未执行过），
     * 则将它们绑定并返回true，表示此操作是幂等的，可以执行。如果绑定已存在，则返回false，表示此操作已执行过，应避免重复执行。
     * 幂等性是指一个操作多次执行的效果与执行一次相同。在分布式系统中，幂等性用于确保在面对网络延迟、重复请求等情况下，
     * 服务能够正确处理请求，避免重复操作带来的问题。
     *
     * @param idempotentKey 幂等性键，用于唯一标识一个操作。
     * @param val 与幂等性键相关联的值，通常可以是操作的唯一标识或其他相关信息。
     * @param expireTime 键的过期时间。
     * @param timeUnit 过期时间的单位。
     * @return 如果操作是幂等的（即键值对在Redis中不存在），则返回true；否则返回false。
     */
    public Boolean checkIdempotent(String idempotentKey, String val, Integer expireTime, TimeUnit timeUnit) {
        if (StringUtils.isBlank(idempotentKey)) {
            return false;
        }
        Boolean checkRes = redisTemplate.opsForValue().setIfAbsent(idempotentKey, val, expireTime, timeUnit);
        return Objects.nonNull(checkRes) && checkRes;
    }

}

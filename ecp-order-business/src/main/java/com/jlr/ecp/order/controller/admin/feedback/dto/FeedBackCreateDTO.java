package com.jlr.ecp.order.controller.admin.feedback.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "管理后台 -  评价配置创建DTO ")
@ToString(callSuper = true)
public class FeedBackCreateDTO {


    @Schema(description = "业务线编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String businessCode;

    /**
     * 评价维度：PM开头、已支付OR、订单完成、CL、订单整单取消
     */
    @Schema(description = "评价适用环节：PM开头、已支付OR、订单完成、CL、订单整单取消", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "评价适用环节不能为空")
    private String feedbackDimensions;


    /**
     * 是否允许输入框：0=否；1=是
     */
    @Schema(description = "是否设置输入框：0=否；1=是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否设置输入框不能为空")
    private Integer enableInput;

    /**
     * 输入框是否必填：0=否；1=是
     */
    @Schema(description = "输入框是否必填：0=否；1=是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "输入框是否必填选项不能为空")
    private Integer mustInput;

    /**
     * 输入框提示信息
     */
    @Schema(description = "输入框提示信息是", requiredMode = Schema.RequiredMode.REQUIRED)
    @Length(max = 50, message = "输入框提示信息不能超过50个字符")
    private String inputText;

    /**
     * 备注说明
     */
    @Schema(description = "说明", requiredMode = Schema.RequiredMode.REQUIRED)
    @Length(max = 50, message = "说明信息不能超过50个字符")
    private String remark;


    /**
     * 评价维度配置信息
     */
    @Schema(description = "说明", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "评价维度配置信息不能为空")
    List<FeedbackDimensionsDTO> dimensionsList;
}

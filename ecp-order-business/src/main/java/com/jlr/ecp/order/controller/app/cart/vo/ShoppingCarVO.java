package com.jlr.ecp.order.controller.app.cart.vo;

import com.jlr.ecp.product.api.product.vo.ProductDetailReqVO;
import com.jlr.ecp.product.api.product.vo.ProductRelationInfoReqVO;
import com.jlr.ecp.product.api.product.vo.ProductSkuRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * t_shopping_car(ShoppingCar)表实体类
 *
 * <AUTHOR>
 */
@Data
@Schema(description = " 购物车- VO")
@ToString(callSuper = true)
public class ShoppingCarVO {

    @Schema(description = "业务线编码")
    private String businessCode;

    @Schema(description = "item列表")
    private List<ShoppingCarItemVO> shoppingCarItemList;


}


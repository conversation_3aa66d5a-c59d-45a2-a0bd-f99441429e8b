package com.jlr.ecp.order.service.feedback;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedBackCreateDTO;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedBackEnableStatusDTO;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedBackUpdateDTO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedBackConfigVO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedBackTypeVO;
import com.jlr.ecp.order.controller.app.feedback.dto.FeedbackQueryDTO;
import com.jlr.ecp.order.controller.app.feedback.vo.FeedbackSettingVO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackConfigDO;

import java.util.List;

/**
 * 评价设置表Service
 */
public interface FeedbackConfigDOService extends IService<FeedbackConfigDO> {

    /**
     * 创建评价设置
     * @param feedBackCreateDTO 评价设置
     * @return 评价设置
     */
    Boolean createFeedback(FeedBackCreateDTO feedBackCreateDTO);


    /**
     * 评价配置详情接口
     * @param feedbackCode 评价code
     * @return FeedBackConfigVO
     */
    FeedBackConfigVO getOneByFeedBackCode(String feedbackCode);


    /**
     * 编辑评价设置
     * @param updateDTO 评价设置
     * @return 评价设置
     */
    Boolean edit(FeedBackUpdateDTO updateDTO);

    /**
     * 删除评价设置
     * @param feedbackCode 评价code
     * @return 评价设置
     */
    Boolean delete(String feedbackCode);

    /**
     * 分页查询评价设置
     * @param dto 分页参数
     * @return 评价设置
     */
    PageResult<FeedBackConfigVO> selectFeedBackPage(PageParam dto);

    /**
     * 启用评价设置
     * @param dto 评价设置
     * @return 评价设置
     */
    Boolean updateEnableStatus(FeedBackEnableStatusDTO dto);

    /**
     * 停用评价设置
     * @param dto 评价设置
     * @return 评价设置
     */
    Boolean updateDisableStatus(FeedBackEnableStatusDTO dto);

    /**
     * 根据评价维度查询评价设置
     * @param dto 评价设置
     * @return 评价设置
     */
    FeedbackSettingVO getFeedBackByDimensions(FeedbackQueryDTO dto);


    /**
     * 评价设置启动
     * @param feedbackCode 评价code
     * @return 评价设置
     */
    void launchFeedback(String feedbackCode);

    /**
     * 获取超时未启用的评价设置
     * @param pageSize 获取数量
     * @return 评价设置
     */
    List<String> getPassTimeUnEnableFeedback(Integer pageSize);

    /**
     * 处理超时未启用的评价设置
     * @param feedbackCode 评价code
     * @return 评价设置
     */
    Boolean handPassUnEnableFeedback(String feedbackCode);

    /**
     * 根据评价维度查询评价code
     * @param dimensionsCode 评价维度code
     * @return 评价code
     */
    List<String> getFeedBackCodeListByDimensionsCode(String dimensionsCode);
}

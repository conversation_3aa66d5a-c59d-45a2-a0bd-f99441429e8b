package com.jlr.ecp.order.enums.order;


/**
 * 订单物流状态枚举
 */
public enum OrderLogisticsStatusEnum {

    /**
     * 待支付
     */
    PENDING_PAYMENT(90101, "待支付"),

    /**
     * 待发货
     */
    PENDING_SHIPMENT(90201, "待发货"),

    /**
     * 部分发货
     */
    PARTIALLY_SHIPPED(90202, "部分发货"),

    /**
     * 全部发货;小程序和portal都需要展示为已发货
     */
    FULLY_SHIPPED(90203, "已发货"),

    /**
     * 订单完成
     */
    ORDER_COMPLETED(90301, "订单完成"),

    /**
     * 订单关闭
     */
    ORDER_CLOSED(90401, "订单关闭"),

    /**
     * 售后处理中
     */
    AFTER_SALE_PROCESSING(90501, "售后处理中");

    private final int code;
    private final String name;

    OrderLogisticsStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据状态码获取枚举实例
     * @param code 状态编码
     * @return 对应枚举实例，未找到返回null
     */
    public static OrderLogisticsStatusEnum getByCode(int code) {
        for (OrderLogisticsStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

    // 增强版方法（可选）
    public static OrderLogisticsStatusEnum getByCodeStrict(int code) {
        OrderLogisticsStatusEnum status = getByCode(code);
        if (status == null) {
            throw new IllegalArgumentException("无效的物流订单状态码: " + code);
        }
        return status;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
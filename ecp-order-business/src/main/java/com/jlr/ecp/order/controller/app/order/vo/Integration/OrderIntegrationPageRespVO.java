package com.jlr.ecp.order.controller.app.order.vo.Integration;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleListRespVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "小程序端 - 订单信息的Page Resp VO")
@Data
public class OrderIntegrationPageRespVO {
    /**
     * 订单编码
     */
    @Schema(description = "订单编码")
    private String orderCode;

    /**
     * 车架号
     */
    @Schema(description = "车架号")
    private String carVin;

    /**
     * 下单时间
     */
    @Schema(description = "下单时间 created_time")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime createdTime;

    /**
     * 应付金额;订单金额，单位分
     */
    @Schema(description = "应付金额;订单金额，单位分")
    private String feeTotalAmount;

    @Schema(description = "订单消费金额")
    private String costAmount;

    /**
     * 折扣金额;折扣金额，单位分
     */
    @Schema(description = "折扣金额;折扣金额，单位分")
    private String discountTotalAmount;

    @Schema(description = "订单包含的商品总数")
    private Integer totalProductNum;

    /**
     * 付款时间
     */
    @Schema(description = "下单时间 paymentTime")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm", timezone = "GMT+8")
    private LocalDateTime paymentTime;

    /**
     * 积分
     */
    private String pointAmount;
    /**
     * 各个订单下购买的商品信息 包括
     * 商品名称
     * 商品缩略图
     * 商品属性
     * 价格
     * 购买数量
     */
    @Schema(description = "订单下购买的商品信息")
    private List<ProductItemInfoData> productItemInfo;

    @Data
    public static class ProductItemInfoData {

        /**
         * 商品编码
         */
        @Schema(description = "商品编码")
        private String productCode;


        /**
         * 商品名称
         */
        @Schema(description = "商品名称")
        private String productName;

        /**
         * 商品主图URL
         */
        @Schema(description = "商品主图URL")
        private String productImageUrl;

        /**
         * 购买数量
         */
        @Schema(description = "购买数量")
        private Integer productQuantity;

        /**
         * stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI
         */
        @Schema(description = "stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI")
        private String productAttribute;

        /**
         * 标价
         */
        @Schema(description = "标价")
        private String productMarketPrice;

        /**
         * 售价
         */
        @Schema(description = "售价")
        private String productSalePrice;


        /**
         * 应付总金额;应付总金额，单位分
         */
        @Schema(description = "应付总金额")
        private String totalAmount;



        /**
         * 实付金额;实付金额，单位分
         */
        @Schema(description = "实付总金额")

        private String totalCostAmount;

        /**
         * 实付积分;实付积分，单位分
         */
        @Schema(description = "应付总积分")

        private String totalPointAmount;

        /**
         * 实付金额;实付金额，单位分
         */
        @Schema(description = "实付单价金额")

        private String costAmount;

        /**
         * 实付积分;实付积分，单位分
         */
        @Schema(description = "实付单价积分")
        private String pointAmount;

    }

    @Schema(description = "车辆信息")
    private IcrVehicleListRespVO vehicleRespVO;

}

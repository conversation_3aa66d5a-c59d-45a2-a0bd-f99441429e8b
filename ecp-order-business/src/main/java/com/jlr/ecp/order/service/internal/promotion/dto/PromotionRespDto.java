package com.jlr.ecp.order.service.internal.promotion.dto;


import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.internal.productprice
 * @className: PromotionRespDto
 * @author: gaoqig
 * @description: 优惠后的相关信息
 * @date: 2025/3/6 16:58
 * @version: 1.0
 */
@Getter
@Setter
public class PromotionRespDto {
    /**
     * 优惠总金额
     */
    private String discountTotalAmount;

    /**
     * 优惠后需要花费的金额
     */
    private String costAmount;

    /**
     * 商品SKU优惠信息
     */
    private List<CartProductSkuInfo> cartSkuProductList;

    /**
     * 优惠券类型
     * 如果改字段为空，则说明没有优惠未生效
     */
    private CouponTypeEnum couponTypeEnum;

    /**
     * 优惠券编码（积分不会返回）
     */
    private PromotionDto chooseCoupon;

    /**
     * 优惠券模版名称
     */
    private String couponModelName;

    /**
     * 消耗的积分
     */
    private Integer costPoints = 0;
}

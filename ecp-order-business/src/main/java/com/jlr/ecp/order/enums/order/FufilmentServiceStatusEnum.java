package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum FufilmentServiceStatusEnum {
    UNACTIVATED(1, "未激活"),
    ACTIVATED(2, "已激活"),
    ACTIVATE_OFF(3, "激活关闭"),
    ACTIVATE_FAILURE(4, "激活失败");

    /**
     * 状态码
     * */
    public final Integer status;

    /**
     * 状态描述
     * */
    public final String desc;
}

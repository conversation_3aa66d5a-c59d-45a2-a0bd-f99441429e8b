package com.jlr.ecp.order.controller.admin.dashboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 查is_deleted 未被删除的 ； 有数字的是在数据库里查的，无数字是在java应用层里计算处理的
 */
@Schema(description = "仪表盘-销售总览 统计响应")
@Data
public class SalesSummaryRespVo {
    // 订单数量统计
    /**
     * 1.代客下单订单数 （order_channel为5代客下单）
     * 2.订单总数量
     * 代客下单订单数 占全部订单的 百分比
     */
    @Schema(description = "订单总数量")
    private BigDecimal totalOrderCount = BigDecimal.ZERO;

    // 成交数统计
    /**
     * 3.代客下单成交数（支付状态为“已支付” payment_status为1：已支付）
     * 4.订单成交数=已支付状态下的订单总数量
     * 代客下单成交数 占 订单成交数的 百分比
     */
    @Schema(description = "订单成交数=已支付状态下的订单总数量")
    private BigDecimal totalPaidCount = BigDecimal.ZERO;

    // 交易额统计
    /**
     * 5.代客下单交易额（支付状态为“已支付”状态的所有”代客下单实付cost_amount 总金额（含税）“之和）
     * 6.已支付状态下 的 所有订单交易额
     * 代客下单交易额 占 订单交易额的百分比
     */
    @Schema(description = "已支付状态下 的 所有订单交易额")
    private BigDecimal totalGmv = BigDecimal.ZERO;

    // 收入统计
    /**
     * 代客下单总收入 = 5- 7.代客下单总退款额
     * 所有订单总收入 = 6- 8.已支付状态下 订单 的总退款额
     * 代客下单总收入 占 所有订单总收入 的百分比
     */
    @Schema(description = "所有订单总收入")
    private BigDecimal totalIncome = BigDecimal.ZERO;

    // 客单价统计
    /**
     * 代客下单成交客单价 = 5 / 3
     * 订单成交客单价=6 / 4
     * 展示“比小程序下单”多的百分比，计算方式：代客下单成交客单价/所有订单成交客单价-1
     */
    @Schema(description = "订单成交客单价")
    private BigDecimal totalCustomerPrice = BigDecimal.ZERO;

    // 商品总数统计
    /**
     * 9.已支付状态下 代客下单成交商品总数
     * 10.已支付状态下 所有订单成交商品总数
     * 代客下单成交商品总数 占 所有订单成交商品总数 的百分比
     */
    @Schema(description = "已支付状态下 所有订单成交商品总数")
    private BigDecimal totalProductCount = BigDecimal.ZERO;


    @Schema(description = "总退款金额")
    private BigDecimal totalRefundAmount = BigDecimal.ZERO;

    /**
     * 入参
     */
    @Schema(description = "开始时间，格式：yyyy-MM-dd")
    private String startTime;

    @Schema(description = "结束时间，格式：yyyy-MM-dd")
    private String endTime;

    // Getters and Setters
}

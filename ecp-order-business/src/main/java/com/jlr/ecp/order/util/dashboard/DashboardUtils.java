package com.jlr.ecp.order.util.dashboard;

import com.jlr.ecp.order.enums.dashboard.TimeIntervalEnum;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

/**
 * Dashboard工具类
 *
 */
@Slf4j
public class DashboardUtils {
    public static String calculateTimeInterval(LocalDate startDate, LocalDate endDate,
                                               List<String> xAxis) {
        // 计算时间间隔
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        log.info("开始日期:{}, 结束日期:{}, daysBetween:{}", startDate, endDate, daysBetween);
        String dateFormat;

        if (daysBetween < 1) {
            // 小于一天，按小时计算
            dateFormat = TimeIntervalEnum.BY_HOUR.getExpression();
            resetChartQueryHourAxis(startDate, xAxis);
        } else if (daysBetween < 31) {
            // 小于31天，按天计算
            dateFormat = TimeIntervalEnum.BY_DAY.getExpression();
            resetChartQueryDayAxis(startDate, endDate, xAxis);
        } else if (daysBetween < 366) {
            // 小于366天，按月计算
            dateFormat = TimeIntervalEnum.BY_MONTH.getExpression();
            resetChartQueryMonthAxis(startDate, endDate, xAxis);
        } else {
            // 大于366天，按年计算
            dateFormat = TimeIntervalEnum.BY_YEAR.getExpression();
            resetChartQueryYearAxis(startDate, endDate, xAxis);
        }
        return dateFormat;
    }

    /**
     * 以小时为维度，设置要查询的时间列表
     *
     * @param startDate 开始时间
     */
    public static void resetChartQueryHourAxis(LocalDate startDate, List<String> xAxis) {
        // 从该日期的开始时间（00:00:00）开始
        LocalDateTime startDateTime = startDate.atStartOfDay();
        // 遍历24小时
        for (int hour = 0; hour < 24; hour++) {
            String tempDate = formatHour(startDateTime.plusHours(hour));
            xAxis.add(tempDate);
        }

    }

    /**
     * 以天为维度，设置要查询的日期列表
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     */
    public static void resetChartQueryDayAxis(LocalDate startDate, LocalDate endDate,
                                              List<String> xAxis) {
        while (!startDate.isAfter(endDate)) {
            String tempDate = formatDate(startDate);
            xAxis.add(tempDate);
            startDate = startDate.plusDays(1);
        }
    }

    /**
     * 以月为维度，设置要查询的日期列表
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     */
    public static void resetChartQueryMonthAxis(LocalDate startDate, LocalDate endDate,
                                                List<String> xAxis) {

        while (!startDate.withDayOfMonth(1).isAfter(endDate.withDayOfMonth(1))) {
            String tempDate = formatMonth(startDate);
            xAxis.add(tempDate);
            startDate = startDate.plusMonths(1);
        }
    }

    /**
     * 以年为维度，设置要查询的日期列表
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     */
    public static void resetChartQueryYearAxis(LocalDate startDate, LocalDate endDate,
                                               List<String> xAxis) {
        while (startDate.getYear() <= endDate.getYear()) {
            String tempDate = formatYear(startDate);
            xAxis.add(tempDate);
            startDate = startDate.plusYears(1);
        }
    }

    public static String formatYear(LocalDate date) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy");
        // 格式化日期时间
        return date.format(formatter);
    }

    public static String formatMonth(LocalDate date) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM");
        // 格式化日期时间
        return date.format(formatter);
    }

    public static String formatDate(LocalDate date) {
        // 定义日期格式
        DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                .appendPattern("MMM dd")
                .toFormatter(Locale.ENGLISH);
        // 格式化日期
        return date.format(formatter);
    }

    public static String formatHour(LocalDateTime dateTime) {
        // 定义日期时间格式
        DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                .appendPattern("h a")
                .toFormatter(Locale.ENGLISH);
        // 格式化日期时间
        return dateTime.format(formatter);
    }

    /**
     * 格式化百分比数据
     *
     * @param value 值
     * @return 展示值
     */
    public static String formatPercentNumber(BigDecimal value) {
        if (Objects.isNull(value)) {
            return null;
        }
        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMaximumFractionDigits(2);
        numberFormat.setMinimumFractionDigits(2);
        return numberFormat.format(value);
    }
}

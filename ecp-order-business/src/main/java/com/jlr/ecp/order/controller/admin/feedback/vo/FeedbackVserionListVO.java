package com.jlr.ecp.order.controller.admin.feedback.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

@Data
@Schema(description = "管理后台 -  评价维度配置创建DTO ")
@ToString(callSuper = true)
public class FeedbackVserionListVO {



    @Schema(description = "评价配置编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String feedbackCode;

    /**
     * 快照编码：版本号，启用时自动生成：feedback_code_PT+时间戳（YYYYMMDDHHMMSS）
     */
    @Schema(description = "快照编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String snapshotCode;


    /**
     * 停用时间
     */
    @Schema(description = "停用时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime downtime;


    /**
     * 启用时间
     */
    @Schema(description = "启用时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime enableTime;

    /**
     * 评价提交数量
     */
    @Schema(description = "评价提交数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer submitNum;


}

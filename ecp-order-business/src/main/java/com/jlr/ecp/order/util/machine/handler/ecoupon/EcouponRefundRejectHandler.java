package com.jlr.ecp.order.util.machine.handler.ecoupon;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusLogDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.order.OrderItemAftersalesStatusEnum;
import com.jlr.ecp.order.enums.order.RefundCouponStatusEnum;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import com.jlr.ecp.order.util.machine.handler.EcouponEventHandler;
import com.jlr.ecp.order.util.machine.handler.EventHandler;
import com.jlr.ecp.order.util.order.OrderAssembleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 退款申请审核被拒绝
 */
@Component
@Slf4j
public class EcouponRefundRejectHandler implements EcouponEventHandler {

    @Resource
    private OrderRefundDOMapper orderRefundDOMapper;


    @Resource
    private OrderStatusLogDOMapper statusLogMapper;

    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO) {
        //打印各个参数到日志中
            log.info("EcouponRefundRejectHandler handleEvent orderInfoDO:{},orderRefundDO:{},orderItemDO:{}",orderInfoDO,orderRefundDO,orderItemDO);
            OrderStatusLogDO logDO = OrderAssembleUtil.assembleOrderStatusLogDO(orderRefundDO.getRefundOrderCode(),orderRefundDO.getCouponRefundStatus(),RefundCouponStatusEnum.REJECT_REFUND_REQUEST.getCode());
            orderRefundDO.setCouponRefundStatus(RefundCouponStatusEnum.REJECT_REFUND_REQUEST.getCode());
            orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.FULL_REFUND_REFUSE.getCode());
            orderItemDO.setAftersalesStatus(OrderItemAftersalesStatusEnum.CLOSED.getCode());
            orderRefundDOMapper.updateById(orderRefundDO);
            statusLogMapper.insert(logDO);
            orderItemDOMapper.updateById(orderItemDO);
    }
}

package com.jlr.ecp.order.controller.admin.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackStatisticQueryDTO {

    @Schema(description = "评价适用环节：PM开头、已支付OR、订单完成、CL、订单整单取消")
    @NotBlank(message = "评价适用环节不能为空")
    private String feedbackDimensions;


    @Schema(description = "评价编码：生成规则：feedback_dimensions+递增三位数编号（例如：PM001）")
    @NotBlank(message = "评价编码不能为空")
    private String feedbackCode;


    @Schema(description = "快照编码：版本号，启用时自动生成：feedback_code_PT+时间戳（YYYYMMDDHHMMSS）")
    @NotBlank(message = "版本号编码不能为空")
    private String snapshotCode;

    @Schema(description = "下单渠道, OrderChannelCodeEnum")
    private String orderChannel;
}

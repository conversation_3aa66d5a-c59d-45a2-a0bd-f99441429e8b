package com.jlr.ecp.order.service.dashboard;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.controller.admin.dashboard.dto.KpiQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.ValetDashboardReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.ValetSalesTrendReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ValetDashboardRespVO;
import com.jlr.ecp.order.enums.dashboard.KpiEnumForValet;

import java.util.List;

// Service接口
public interface ValetDashboardService {
    ValetDashboardRespVO getValetOrderStatistics(ValetDashboardReqDTO reqDTO);

    CommonResult<ProductSalesTrendRespVo> getSalesTrend(ValetSalesTrendReqDTO reqDTO);

    CommonResult<List<KpiQueryDTO>> getValetKpiList();
}
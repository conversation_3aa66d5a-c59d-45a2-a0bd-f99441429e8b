package com.jlr.ecp.order.service.coupon.status;


import com.jlr.ecp.order.api.refund.dto.BaseOrderRefundApplyDTO;
import com.jlr.ecp.order.dal.dataobject.order.OrderCouponDetailDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderCouponDetailDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.service.refund.ecoupon.EcouponOrderRefundDOService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.coupon.status
 * @className: TransactionOperator
 * @author: gaoqig
 * @description: 事务操作服务，该服务尽量做到最小操作单位进行事务管理，避免其他业务逻辑造成回滚或长时间锁
 * @date: 2025/3/14 09:56
 * @version: 1.0
 */
@Component
public class TransactionOperator {
    @Resource
    OrderCouponDetailDOMapper orderCouponDetailDOMapper;

    @Resource
    OrderInfoDOMapper orderInfoDOMapper;

    @Resource
    OrderItemDOMapper orderItemDOMapper;

    @Resource
    EcouponOrderRefundDOService ecouponOrderRefundDOService;

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderCouponDetail(List<OrderCouponDetailDO> orderCouponDetailDO, BaseOrderRefundApplyDTO refundApplyDTO, Integer operationType){
        orderCouponDetailDOMapper.updateBatch(orderCouponDetailDO);
        if(StringUtils.isBlank(ecouponOrderRefundDOService.ecouponOrderRefundApply(List.of(refundApplyDTO), operationType))){
            throw new RuntimeException("优惠券退款失败");
        }
    }

    /***
     * <AUTHOR>
     * @description 事务更新 优惠券订单明细表，订单行表以及主订单表
     * 请传完整对象更新（从数据库取出对象，修改要更新的字段），不支持空值赋值
     * @date 2025/3/14 14:26
     * @param orderCouponDetailDO:
     * @param orderInfoDO:
     * @return: void
    */

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderCouponAndOrderInfo(OrderCouponDetailDO orderCouponDetailDO, OrderInfoDO orderInfoDO, OrderItemDO orderItemDO){
        orderCouponDetailDOMapper.updateById(orderCouponDetailDO);
        orderInfoDOMapper.updateById(orderInfoDO);
        orderItemDOMapper.updateById(orderItemDO);

    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderCouponAndOrderItemInfo(OrderCouponDetailDO orderCouponDetailDO, OrderItemDO orderItemDO){
        orderCouponDetailDOMapper.updateById(orderCouponDetailDO);
        orderItemDOMapper.updateById(orderItemDO);

    }
}

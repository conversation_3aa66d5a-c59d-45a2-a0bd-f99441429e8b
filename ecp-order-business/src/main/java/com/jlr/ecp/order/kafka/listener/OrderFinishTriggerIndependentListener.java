package com.jlr.ecp.order.kafka.listener;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.api.independent.dto.TriggerIndependentReqDTO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import com.jlr.ecp.order.service.coupon.status.dto.OrderStatusChangedKafkaDto;
import com.jlr.ecp.order.service.independent.OrderIndependentDOService;
import com.jlr.ecp.order.util.RedisReentrantLockUtil;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.errors.TimeoutException;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.kafka.KafkaException;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 订单完成后触发分账
 * <AUTHOR> Hongyi
 * */
@Component
@Slf4j
public class OrderFinishTriggerIndependentListener {

    @Resource
    private OrderIndependentDOService orderIndependentDOService;
    @Resource
    private Redisson redisson;
    @Resource
    private RedisReentrantLockUtil redisReentrantLockUtil;

    @KafkaListener(topics = KafkaConstants.ORDER_STATUS_CHANGE_TOPIC, groupId = "order-status-group", properties = "max.poll.records:1")
    @Retryable(value = {KafkaException.class, TimeoutException.class}, exclude = Exception.class, backoff = @Backoff(delay = 5000, multiplier = 2, maxDelay = 30000), maxAttempts = 2)
    public void onMessage(String messageStr) {
        OrderStatusChangedKafkaDto message = JSON.parseObject(messageStr, OrderStatusChangedKafkaDto.class);
        if (!OrderStatusEnum.COMPLETED.getCode().equals(message.getOrderStatus())) {
            log.info("监听到订单状态变化，状态变化不为订单完成，放弃分账，orderCode={}，message={}", message.getOrderCode(), messageStr);
            return;
        } else if (!BusinessIdEnum.LRE.getCode().equals(message.getBusinessCode())) {
            log.info("监听到订单状态变化，非LRE订单，放弃分账，orderCode={}，message={}", message.getOrderCode(), messageStr);
            return;
        }

        // LRE体验券的【订单完成分账】需要暂停3秒，然后去获取锁，不然可能会进行错误的订单完成分账
        // 因为LRE体验券在最后一张券核销时会同时触发【券核销分账】与【订单完成分账】
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            log.error("监听到订单状态变化，订单完成分账sleep异常，orderCode={}，message={}", message.getOrderCode(), messageStr);
            throw new KafkaException("监听到订单状态变化，订单完成分账sleep异常");
        }

        log.info("监听到订单状态变化，订单完成分账尝试获取锁，orderCode={}，message={}", message.getOrderCode(), messageStr);
        RLock rLock = redisson.getLock(Constants.REDIS_ORDER_INDEPENDENT_LOCK_KEY + message.getOrderCode());
        if (!redisReentrantLockUtil.tryLock(rLock, 15,  30, TimeUnit.SECONDS)) {
            throw new KafkaException("监听到订单状态变化，订单完成分账尝试获取锁失败，orderCode=" + message.getOrderCode());
        }
        try {
            log.info("监听到订单状态变化，订单完成分账开始，orderCode={}，message={}", message.getOrderCode(), messageStr);
            LocalDateTime completedTimeLimit = message.getUpdateTime();
            if (completedTimeLimit == null) {
                completedTimeLimit = LocalDateTime.now();
            }
            completedTimeLimit = completedTimeLimit.plusSeconds(10); //加10秒

            TenantContextHolder.setTenantId(message.getTenantId());
            TriggerIndependentReqDTO req = new TriggerIndependentReqDTO();
            req.setBusinessCode(message.getBusinessCode());
            req.setOrderCodes(List.of(message.getOrderCode()));
            req.setLreCompletedTimeLimit(completedTimeLimit);
            orderIndependentDOService.triggerIndependentForOrderSucc(req);
        } catch (Exception e) {
            log.error("监听到订单状态变化，订单完成分账异常，orderCode={}，message={}", message.getOrderCode(), messageStr);
            throw new KafkaException("监听到订单状态变化，订单完成分账异常");
        } finally {
            TenantContextHolder.clear();
            redisReentrantLockUtil.unlock(rLock, 3);
        }
    }

}
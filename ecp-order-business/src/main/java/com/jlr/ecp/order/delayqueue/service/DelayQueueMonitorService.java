package com.jlr.ecp.order.delayqueue.service;

import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 延迟队列监控服务
 * 提供队列状态查询功能，主要用于日志记录和问题排查
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DelayQueueMonitorService {

    @Resource
    private RedissonOrderCancelDelayService delayService;

    @Resource
    private RedissonRetryQueueService retryQueueService;

    /**
     * 记录队列状态日志（用于监控和排查问题）
     */
    public void logQueueStatus() {
        try {
            int bgLreMainSize = getQueueSize(delayService.getBgLreQueue());
            int vcsMainSize = getQueueSize(delayService.getVcsQueue());
            int retrySize = retryQueueService.getRetryQueueSize();
            int totalSize = bgLreMainSize + vcsMainSize + retrySize;

            log.info("延迟队列状态监控 - 主队列: {}({}), {}({}) | 重试队列: {} | 总计: {}",
                    BusinessIdEnum.BRAND_GOODS.getName(), bgLreMainSize,
                    BusinessIdEnum.VCS.getName(), vcsMainSize,
                    retrySize, totalSize);

            // 检查是否有异常情况需要告警
            if (retrySize > 300) {
                log.warn("重试队列积压较多: {}, 建议检查处理", retrySize);
            }

        } catch (Exception e) {
            log.error("获取队列状态失败", e);
        }
    }


    /**
     * 获取队列大小（安全方式）
     *
     * @param queue 队列
     * @return 队列大小，异常时返回-1
     */
    private int getQueueSize(Object queue) {
        try {
            if (queue instanceof org.redisson.api.RBlockingQueue) {
                return ((org.redisson.api.RBlockingQueue<?>) queue).size();
            }
            return 0;
        } catch (Exception e) {
            log.warn("获取队列大小失败", e);
            return -1;
        }
    }
}

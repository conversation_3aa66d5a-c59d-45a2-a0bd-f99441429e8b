package com.jlr.ecp.order.controller.app.cart.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jlr.ecp.product.api.product.vo.ProductDetailReqVO;
import com.jlr.ecp.product.api.product.vo.ProductRelationInfoReqVO;
import com.jlr.ecp.product.api.product.vo.ProductSkuRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * t_shopping_car(ShoppingCar)表实体类
 *
 * <AUTHOR>
 */
@Data
@Schema(description = " 购物车- VO")
@ToString(callSuper = true)
public class ShoppingCarItemVO {

    /**
     * 数据Id
     */
    @Schema(description = "数据Id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    /**
     * 用户编码
     */
    @Schema(description = "用户编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户编码不能为空")
    private String consumerCode;

    /**
     * 购物车code
     */
    @Schema(description = "购物车code", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "购物车code不能为空")
    private String cartCode;

    /**
     * 购物车商品编码
     */
    @Schema(description = "购物车商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "购物车商品编码不能为空")
    private String cartItemCode;

    /**
     * 购物车商品类型
     */
    @Schema(description = "购物车商品类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "购物车商品类型不能为空")
    private Integer cartItemType;


    /**
     * 商品编码
     */
    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "商品编码不能为空")
    private String productCode;

    /**
     * 商品编码
     */
    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productName;

    /**
     * 商品SKU编码
     */
    @Schema(description = "商品SKU编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "商品SKU编码不能为空")
    private String productSkuCode;

    /**
     * 商品数量
     */
    @Schema(description = "商品数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品数量不能为空")
    private Integer quantity;

    /**
     * 车型编码
     */
    @Schema(description = "车型编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车型编码不能为空")
    private String seriesCode;

    /**
     * 车型编码
     */
    @Schema(description = "车型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String seriesName;

    /**
     * 车辆VIN码
     */
    @Schema(description = "车辆VIN码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "车辆VIN码不能为空")
    private String carVin;

    /**
     * SKU 列表
     */
    @Schema(description = "商品sku信息")
    @NotNull(message = "商品sku信息不能为空")
    private List<ProductSkuRespVO> skuList;


    /**
     * SKU 属性值
     */
    @Schema(description = "sku属性值")
    private String attributeValues;


    /**
     * 标价; 标价, 单位分
     */
    @Schema(description = "标价")
    private Long salePrice;

    /**
     * 标价;标价，单位分
     */
    @Schema(description = "标价——String类型的元单位")
    private String salePriceYuanStr;

    /**
     * 市场价（划线价）;销售价格，单位分
     */
    @Schema(description = "销售价格")
    private Long marketPrice;

    /**
     * 市场价（划线价）;销售价格，单位分
     */
    @Schema(description = "标价——String类型的元单位")
    private String marketPriceYuanStr;

    /**
     * 是否被逻辑删除
     */
    @Schema(description = "是否被删除")
    private Boolean isDeleted;

    /**
     * 主图
     */
    @Schema(description = "主图")
    private ProductDetailReqVO productDetailReqVO;

    /**
     * 商品上下架状态
     */
    @Schema(description = "商品上下架状态")
    private Integer shelfStatus;

    /**
     * 商品条款信息
     */
    @Schema(description = "商品条款信息")
    private List<ProductRelationInfoReqVO> policyInfo;

    @Schema(description = "履约类型")
    private Integer fulfilmentType;

    /**
     * 商品包含的履约类型(組合商品)
     */
    @Schema(description = "商品包含的履约类型")
    private Set<Integer> childFulfilmentType;

    /**
     * 折扣金额
     */
    @TableField(value = "折扣金额")
    private String discountTotalAmount;

    /**
     * 应付金额，单位元，两位小数
     */
    @TableField(value = "应付金额，单位元，两位小数")
    private String costAmount;

    /**
     * 实际花费积分
     */
    @Schema(description = "应付积分,可能为空")
    private Integer costPoints;

    /**
     * 品牌 Code
     */
    @Schema(description = "品牌 Code")
    private String brandCode;

    /**
     * 业务线 Code
     */
    @Schema(description = "业务线 Code")
    private String businessCode;

    /**
     * 用户 ICR 账户 ID
     */
    @Schema(description = "用户 ICR 账户 ID")
    private String incontrolId;

    /**
     * 进入购物车时间
     */
    @Schema(description = "进入购物车时间")
    private LocalDateTime createdTime;


    @Schema(description = "1:现金 ，2：积分，3:优惠券")
    private Integer paymentType;

    /**
     * 商品是否有效
     */
    @Schema(description = "商品是否有效")
    private Boolean valid;

    //优惠后单价
    @Schema(description = "单件优惠券价格（优惠价格/数量）")
    private String unitCouponPrice;

    @Schema(description = "是否支持积分模式优惠")
    private boolean supportPointDiscount = false;

    @Schema(description = "是否选择积分优惠")
    private boolean choosePointFlag = false;

    @Schema(description = "积分优惠时的现金价格")
    private String salePointsPrice;

    @Schema(description = "积分优惠时的积分")
    private Integer salePoints;

    @Schema(description = "库存")
    private Integer stockQuantity;

    /**
     * 前段通过该字段可以置灰商品:true 不可购买， false 可以购买
     * 不可购买时，不会参与优惠计算
     */
    @Schema(description = "不可购买")
    private boolean unPurchasableFlag;

    /**
     * 商品是否已售罄
     */
    @Schema(description = "商品是否已售罄")
    private Boolean soldOut;

    /**
     * 商品sku单个重量
     */
    @Schema(description = "商品sku单个重量")
    private String weight;

    /**
     * 运费模版code
     */
    @Schema(description = "运费模版code")
    private String freightTemplateCode;

    @Schema(description = "所属一级分类，多个由逗号隔开")
    private String categoryCodeLevel1Name;

    @Schema(description = "所属二级分类，多个由逗号隔开")
    private String categoryCodeLevel2Name;

    @Schema(description = "所属三级分类，多个由逗号隔开")
    private String categoryCodeLevel3Name;
}


package com.jlr.ecp.order.dal.mysql.order;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemLogisticsDO;
import com.jlr.ecp.order.enums.order.OrderItemLogisticsStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_order_item_logistics(履约方式为实物时，记录物流信息)】的数据库操作Mapper
* @createDate 2025-03-12 10:41:04
* @Entity com.jlr.ecp.order.dal.dataobject.order.OrderItemLogisticsDO
*/
@Mapper
public interface OrderItemLogisticsDOMapper extends BaseMapperX<OrderItemLogisticsDO> {

    /**
     * 获取未签收数据
     * 已发货||（已收货&&签收日期为空）
     * 发货时前180天内
     * 未删除
     */
    default List<OrderItemLogisticsDO> getNotSignList(LocalDateTime sendTimeMin) {
        LambdaQueryWrapper<OrderItemLogisticsDO> wrapper = Wrappers.lambdaQuery(OrderItemLogisticsDO.class)
                .ge(OrderItemLogisticsDO::getSendTime, sendTimeMin)
                .eq(OrderItemLogisticsDO::getIsDeleted, Boolean.FALSE)
                .and(wq -> wq.eq(OrderItemLogisticsDO::getLogisticsStatus, OrderItemLogisticsStatusEnum.SHIPPED.getCode())
                        .or(iwq -> iwq.eq(OrderItemLogisticsDO::getLogisticsStatus, OrderItemLogisticsStatusEnum.RECEIVED.getCode())
                        .isNull(OrderItemLogisticsDO::getSignTime)));
        return selectList(wrapper);
    }

    default Map<String, OrderItemLogisticsDO> mapByOrderCode(List<String> orderCodes) {
        if (CollUtil.isEmpty(orderCodes)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<OrderItemLogisticsDO> wrapper = Wrappers.lambdaQuery(OrderItemLogisticsDO.class)
                .in(OrderItemLogisticsDO::getOrderCode, orderCodes)
                .eq(OrderItemLogisticsDO::getIsDeleted, Boolean.FALSE);
        List<OrderItemLogisticsDO> logisticsList = selectList(wrapper);

        Map<String, OrderItemLogisticsDO> map = new HashMap<>();
        for (OrderItemLogisticsDO logistics:logisticsList) {
            map.put(logistics.getOrderCode(), logistics);
        }
        return map;
    }

} 
package com.jlr.ecp.order.kafka.listener;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderPaymentRecordsDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderPaymentRecordsMapper;
import com.jlr.ecp.order.enums.order.BusinessCodeEnum;
import com.jlr.ecp.order.enums.order.OrderTypeEnum;
import com.jlr.ecp.order.kafka.message.CancelOrderMessage;
import com.jlr.ecp.order.kafka.message.PayTimeoutMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PayTimeoutCancelOrderListener {

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;
    @Resource
    private ProducerTool producerTool;

    @Resource
    private OrderPaymentRecordsMapper orderPaymentRecordsMapper;

    @KafkaListener(topics = KafkaConstants.PAY_TIMEOUT_CANCEL_TOPIC, groupId = "pay-timeout-cancel-group-1", batch = "true",
            properties = {"max.poll.records:100"})
    public void timeoutCancelOrder(List<ConsumerRecord<String,String>> records) {
        log.info("支付超时自动取消订单，records:{}", records);
        List<PayTimeoutMessage> payTimeoutMessageList = buildPayTimeoutMessageList(records);
        if (CollectionUtils.isEmpty(payTimeoutMessageList)) {
            return;
        }
        TenantContextHolder.setTenantId(payTimeoutMessageList.get(0).getTenantId());
        try {
            for (PayTimeoutMessage payTimeoutMessage : payTimeoutMessageList) {
                OrderInfoDO orderInfo = orderInfoDOMapper.queryOrderDoByOrderCode(payTimeoutMessage.getOrderCode());
                if (orderInfo == null){
                    continue;
                }

                // 处理支付超时订单，判断是否可以关闭
                boolean canCancel = handlePayTimeoutOrder(orderInfo, payTimeoutMessage.getPayApplyNo());

                // 只有当可以取消时，才发送取消订单消息
                if (canCancel) {
                    CancelOrderMessage cancelOrderMessage = new CancelOrderMessage();
                    cancelOrderMessage.setMessageId(UUID.randomUUID().toString().replace("-", ""));
                    cancelOrderMessage.setParentOrderCode(orderInfo.getParentOrderCode());
                    cancelOrderMessage.setOrderCode(orderInfo.getOrderCode());
                    cancelOrderMessage.setSendTime(orderInfo.getOrderTime());
                    cancelOrderMessage.setTenantId(payTimeoutMessage.getTenantId());
                    cancelOrderMessage.setBusinessCode(orderInfo.getBusinessCode());
                    producerTool.sendMsg(KafkaConstants.REAL_ORDER_TIMEOUT_CANCEL_TOPIC, "", JSON.toJSONString(cancelOrderMessage));
                    log.info("发送订单取消消息，orderCode:{}", orderInfo.getOrderCode());
                } else {
                    log.info("订单存在未超时的其他支付记录，不发送取消消息，orderCode:{}", orderInfo.getOrderCode());
                }
            }
        } catch (Exception e) {
            log.error("支付超时自动取消订单, 异常:", e);
        } finally {
            TenantContextHolder.clear();
        }
    }

    private boolean handlePayTimeoutOrder(OrderInfoDO orderInfo, String payApplyNo) {
        log.info("处理支付超时订单，orderCode:{}, payApplyNo:{}, orderType:{}",
                orderInfo.getOrderCode(), payApplyNo, orderInfo.getOrderType());

        // 首先判断是否为父单
        if (OrderTypeEnum.PARENT.getCode().equals(orderInfo.getOrderType())) {
            return handleParentOrderTimeout(orderInfo, payApplyNo);
        } else {
            return handleChildOrderTimeout(orderInfo, payApplyNo);
        }
    }

    /**
     * 处理父单超时逻辑
     * @return true表示可以关闭父单，false表示不能关闭
     */
    private boolean handleParentOrderTimeout(OrderInfoDO parentOrder, String timeoutPayApplyNo) {
        log.info("处理父单超时，parentOrderCode:{}, timeoutPayApplyNo:{}",
                parentOrder.getOrderCode(), timeoutPayApplyNo);

        // 查询当前父单的所有子单
        List<OrderInfoDO> childOrders = orderInfoDOMapper.selectList(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderInfoDO::getParentOrderCode, parentOrder.getOrderCode())
        );

        if (CollectionUtils.isEmpty(childOrders)) {
            log.warn("父单没有找到子单，parentOrderCode:{}", parentOrder.getOrderCode());
            return false;
        }

        // 收集所有子单的订单编号
        List<String> childOrderCodes = childOrders.stream()
                .map(OrderInfoDO::getOrderCode)
                .collect(Collectors.toList());

        // 查询所有子单的支付记录
        List<OrderPaymentRecordsDO> allPaymentRecords = orderPaymentRecordsMapper
                .queryAllPaymentRecordsByOrderCodes(childOrderCodes);

        // 排除当前超时的支付申请单号
        List<OrderPaymentRecordsDO> otherPaymentRecords = allPaymentRecords.stream()
                .filter(record -> !timeoutPayApplyNo.equals(record.getPayApplyNo()))
                .collect(Collectors.toList());

        // 检查其他支付记录是否还在有效期内
        boolean hasValidPayment = otherPaymentRecords.stream()
                .anyMatch(record -> isPaymentRecordValid(record, parentOrder.getBusinessCode()));

        if (hasValidPayment) {
            log.info("父单存在未超时的其他支付记录，不能关闭父单，parentOrderCode:{}",
                    parentOrder.getOrderCode());
            return false;
        } else {
            log.info("父单所有其他支付记录都已超时，可以关闭父单，parentOrderCode:{}",
                    parentOrder.getOrderCode());
            return true;
        }
    }

    /**
     * 处理子单超时逻辑
     * @return true表示可以关闭子单，false表示不能关闭
     */
    private boolean handleChildOrderTimeout(OrderInfoDO childOrder, String timeoutPayApplyNo) {
        log.info("处理子单超时，childOrderCode:{}, timeoutPayApplyNo:{}",
                childOrder.getOrderCode(), timeoutPayApplyNo);

        // 查询当前子单的所有支付记录
        List<OrderPaymentRecordsDO> allPaymentRecords = orderPaymentRecordsMapper
                .queryAllPaymentRecordsByOrderCode(childOrder.getOrderCode());

        // 排除当前超时的支付申请单号
        List<OrderPaymentRecordsDO> otherPaymentRecords = allPaymentRecords.stream()
                .filter(record -> !timeoutPayApplyNo.equals(record.getPayApplyNo()))
                .collect(Collectors.toList());

        // 检查该子单的其他支付记录是否还在有效期内
        boolean hasValidPayment = otherPaymentRecords.stream()
                .anyMatch(record -> isPaymentRecordValid(record, childOrder.getBusinessCode()));

        if (hasValidPayment) {
            log.info("子单存在未超时的其他支付记录，不能关闭子单，childOrderCode:{}",
                    childOrder.getOrderCode());
            return false;
        } else {
            log.info("子单所有其他支付记录都已超时，可以关闭子单，childOrderCode:{}",
                    childOrder.getOrderCode());
            return true;
        }
    }

    /**
     * 判断支付记录是否还在有效期内
     * VCS订单：2小时内有效
     * BG和LRE订单：15分钟内有效
     */
    private boolean isPaymentRecordValid(OrderPaymentRecordsDO paymentRecord, String businessCode) {
        if (paymentRecord.getSubmitTime() == null) {
            return false;
        }

        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(paymentRecord.getSubmitTime(), now);
        long elapsedMillis = duration.toMillis();

        // 根据业务类型确定超时时间
        long timeoutMillis = getPaymentTimeoutMillis(businessCode);

        // 如果支付记录提交时间在超时时间内，则认为还在有效期内
        boolean isValid = elapsedMillis < timeoutMillis;

        log.debug("支付记录有效性检查，payApplyNo:{}, businessCode:{}, submitTime:{}, elapsedMillis:{}, timeoutMillis:{}, isValid:{}",
                paymentRecord.getPayApplyNo(), businessCode, paymentRecord.getSubmitTime(),
                elapsedMillis, timeoutMillis, isValid);

        return isValid;
    }

    /**
     * 根据业务类型获取支付超时时间
     */
    private long getPaymentTimeoutMillis(String businessCode) {
        if (BusinessCodeEnum.VCS.getCode().equals(businessCode)) {
            // VCS订单：2小时超时
            return Constants.VCS_PAY_TIMEOUT_MILLS;
        } else if (BusinessCodeEnum.BRAND_GOODS.getCode().equals(businessCode) ||
                   BusinessCodeEnum.LRE.getCode().equals(businessCode)) {
            // BG和LRE订单：15分钟超时
            return Constants.BG_LRE_PAY_TIMEOUT_MILLS;
        } else {
            // 默认使用15分钟超时
            log.warn("未知的业务类型，使用默认超时时间，businessCode:{}", businessCode);
            return Constants.BG_LRE_PAY_TIMEOUT_MILLS;
        }
    }

    /**
     * 获取PayTimeoutMessageList
     * @param records 消息记录
     * @return List<PayTimeoutMessage>
     * */
    public List<PayTimeoutMessage> buildPayTimeoutMessageList(List<ConsumerRecord<String, String>> records) {
        List<PayTimeoutMessage> payTimeoutMessageList = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return payTimeoutMessageList;
        }
        for (ConsumerRecord<String, String> record : records) {
            try {
                PayTimeoutMessage payTimeoutMessage = JSON.parseObject(record.value(), PayTimeoutMessage.class);
                payTimeoutMessageList.add(payTimeoutMessage);
            } catch (Exception e) {
                log.error("转化为PayTimeoutMessage异常, record:{}, 原因:", record, e);
            }
        }
        return payTimeoutMessageList;
    }

}
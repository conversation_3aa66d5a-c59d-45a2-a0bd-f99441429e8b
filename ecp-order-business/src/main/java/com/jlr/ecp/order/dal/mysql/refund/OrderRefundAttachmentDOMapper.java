package com.jlr.ecp.order.dal.mysql.refund;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundAttachmentDO;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【t_order_refund_attachment(t_order_refund_attachment)】的数据库操作Mapper
* @createDate 2024-01-15 11:28:44
* @Entity com.jlr.ecp.order.dal.dataobject.refund.OrderRefundAttachmentDO
*/
@Mapper
public interface OrderRefundAttachmentDOMapper extends BaseMapperX<OrderRefundAttachmentDO> {

}





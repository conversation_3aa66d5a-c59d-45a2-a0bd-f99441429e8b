package com.jlr.ecp.order.convert;

import com.jlr.ecp.order.api.refund.dto.OrderRefundItemDto;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * OrderRefundDOConvert
 *
 * <AUTHOR> <PERSON>
 * @since 2025-03-23 23:26:51
 */
@Mapper(componentModel = "spring")
public interface OrderRefundItemDOConvert {

    List<OrderRefundItemDto> toDtoList(List<OrderRefundItemDO> orderRefundItemDOS);

    OrderRefundItemDto toDTO(OrderRefundItemDO orderRefundItemDO);
}

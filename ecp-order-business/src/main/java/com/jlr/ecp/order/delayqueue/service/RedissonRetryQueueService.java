package com.jlr.ecp.order.delayqueue.service;

import com.jlr.ecp.order.delayqueue.config.OrderCancelDelayConfig;
import com.jlr.ecp.order.delayqueue.config.RetryConfig;
import com.jlr.ecp.order.delayqueue.dto.CancelOrderTask;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * Redisson重试队列服务
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class RedissonRetryQueueService {
    
    @Resource
    private RedissonClient redissonClient;
    
    @Resource
    private RetryConfig retryConfig;

    @Resource
    private OrderCancelDelayConfig delayConfig;
    
    // 统一重试队列
    @Getter
    private RBlockingQueue<CancelOrderTask> retryQueue;
    private RDelayedQueue<CancelOrderTask> retryDelayedQueue;
    
    @PostConstruct
    public void init() {
        // 初始化统一重试队列
        retryQueue = redissonClient.getBlockingQueue("cancel-order-retry-queue");
        if (!retryQueue.isExists()) {
            retryDelayedQueue = redissonClient.getDelayedQueue(retryQueue);
            log.info("重试队列初始化完成");
        } else {
            retryDelayedQueue = redissonClient.getDelayedQueue(retryQueue);
            log.info("重试队列已存在，跳过初始化");
        }
    }
    
    /**
     * 处理任务失败，决定重试或进入死信队列
     * 
     * @param task 失败的任务
     * @param exception 异常信息
     * @param businessLine 业务线
     */
    public void handleTaskFailure(CancelOrderTask task, Exception exception, String businessLine) {
        // 更新任务的错误信息
        task.setLastErrorMessage(exception.getMessage());
        task.setErrorType(retryConfig.getErrorTypeEnum(exception));
        
        // 判断是否可以重试
        boolean canRetry = retryConfig.canRetry(task.getRetryCount(), task.getMaxRetryCount()) 
                          && retryConfig.isRetryableException(exception);
        
        if (canRetry) {
            // 增加重试次数
            task.setRetryCount(task.getRetryCount() + 1);

            // 计算重试延迟时间
            long retryDelay = retryConfig.getRetryDelay(task.getRetryCount());

            // 添加到重试队列
            addToRetryQueue(task, retryDelay, businessLine);

            log.warn("任务处理失败，已加入重试队列，taskId={}, orderCode={}, businessLine={}, retryCount={}/{}, retryDelay={}ms, errorType={}",
                    task.getTaskId(), task.getOrderCode(), businessLine,
                    task.getRetryCount(), task.getMaxRetryCount(), retryDelay, task.getErrorType().getCode());
        } else {
            // 超过最大重试次数，记录日志
            log.error("任务处理失败且已达到最大重试次数，taskId={}, orderCode={}, businessLine={}, retryCount={}/{}, errorType={}, errorMessage={}",
                    task.getTaskId(), task.getOrderCode(), businessLine,
                    task.getRetryCount(), task.getMaxRetryCount(), task.getErrorType().getCode(), task.getLastErrorMessage());
        }
    }
    
    /**
     * 添加任务到重试队列
     *
     * @param task 任务
     * @param retryDelay 重试延迟时间（毫秒）
     * @param businessLine 业务线
     */
    private void addToRetryQueue(CancelOrderTask task, long retryDelay, String businessLine) {
        try {
            retryDelayedQueue.offer(task, retryDelay, TimeUnit.MILLISECONDS);

            log.info("任务已添加到重试队列，taskId={}, orderCode={}, businessLine={}, retryDelay={}ms",
                    task.getTaskId(), task.getOrderCode(), businessLine, retryDelay);

        } catch (Exception e) {
            log.error("添加任务到重试队列失败，taskId={}, orderCode={}, businessLine={}",
                    task.getTaskId(), task.getOrderCode(), businessLine, e);
        }
    }

    
    /**
     * 获取重试队列长度
     *
     * @return 队列长度
     */
    public int getRetryQueueSize() {
        try {
            return retryQueue.size();
        } catch (Exception e) {
            log.error("获取重试队列长度失败", e);
            return -1;
        }
    }


}

package com.jlr.ecp.order.dal.mysql.independent;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentDO;
import com.jlr.ecp.order.enums.independent.OrderIndependentStatusEnum;
import com.jlr.ecp.order.enums.independent.OrderIndependentTypeEnum;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
* <AUTHOR> Hongyi
* @description 针对表【t_order_independent】的数据库操作Mapper
* @createDate 2025-03-13 01:08:44
* @Entity com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentDO
*/
@Mapper
public interface OrderIndependentDOMapper extends BaseMapper<OrderIndependentDO> {

    /**
     * 查询订单下分账类型为“订单完成”的分账记录
     */
    default OrderIndependentDO getOrderFinishByOrderCode(String orderCode) {
        LambdaQueryWrapper<OrderIndependentDO> wrapper = Wrappers.lambdaQuery(OrderIndependentDO.class)
                .eq(OrderIndependentDO::getOrderCode, orderCode)
                .eq(OrderIndependentDO::getIndependentType, OrderIndependentTypeEnum.ORDER_SUCCESS.getType())
                .eq(OrderIndependentDO::getIsDeleted, Boolean.FALSE)
                .orderByDesc(OrderIndependentDO::getId)
                .last(Constants.LIMIT_ONE);
        return selectOne(wrapper);
    }

    /**
     * 查询指定创建时间之前且为待分账状态的分账任务，只有调用PC创建分账申请出现异常才会让分账记录一直停留在待分账状态
     */
    default List<OrderIndependentDO> listTodoIndependent(LocalDateTime createdTime, Integer limit, List<String> orderCodes) {
        LambdaQueryWrapper<OrderIndependentDO> wrapper = Wrappers.lambdaQuery(OrderIndependentDO.class)
                .eq(OrderIndependentDO::getIndependentStatus, OrderIndependentStatusEnum.TODO.getStatus())
                .eq(OrderIndependentDO::getIsDeleted, Boolean.FALSE)
                .le(OrderIndependentDO::getCreatedTime, createdTime)
                .in(CollUtil.isNotEmpty(orderCodes), OrderIndependentDO::getOrderCode, orderCodes) // 可选
                .orderByDesc(OrderIndependentDO::getId)
                .last("limit " + limit);
        return selectList(wrapper);
    }

}
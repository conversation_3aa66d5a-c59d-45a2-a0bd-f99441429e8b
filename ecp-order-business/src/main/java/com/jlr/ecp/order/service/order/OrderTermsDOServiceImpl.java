package com.jlr.ecp.order.service.order;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.order.dal.dataobject.order.OrderTermsDO;
import com.jlr.ecp.order.service.order.OrderTermsDOService;
import com.jlr.ecp.order.dal.mysql.order.OrderTermsDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_order_terms(t_order_terms)】的数据库操作Service实现
* @createDate 2023-12-20 10:41:04
*/
@Service
@Slf4j
public class OrderTermsDOServiceImpl extends ServiceImpl<OrderTermsDOMapper, OrderTermsDO>
    implements OrderTermsDOService{

}





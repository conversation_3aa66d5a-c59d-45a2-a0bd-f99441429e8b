package com.jlr.ecp.order.controller.admin;

import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.order.kafka.producer.BrandedGoodsNotificationProducer;
import com.jlr.ecp.order.util.RedisReentrantLockUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.redisson.Redisson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.Date;

@RestController
public class TestController {
    private static final Logger log = LogManager.getLogger(TestController.class);

    @Autowired
    ProducerTool producerTool;

    @Resource
    private Redisson redisson;

    @Resource
    private RedisReentrantLockUtil redisReentrantLockUtil;

    @Resource
    private BrandedGoodsNotificationProducer notificationProducer;

    @PermitAll
    @GetMapping(value = "/testmsg")
    public String testmsg()
    {
        String str = String.format("data is %s",new Date().toString());
//        producerTool.sendMsg("test-topic","",
//                str);
        String orderCode = "TEST-ORDER-001";
        String phone = "18581533766";
        String url = "https://test.wx.url";

        notificationProducer.sendPartialDeliveryNotification(orderCode, "logisticsNumber", phone, url);
        notificationProducer.sendReturnRefundNotification(orderCode, phone, url);
        notificationProducer.sendRefundOnlyNotification(orderCode, phone, url);
        notificationProducer.sendPaymentSuccessNotification(orderCode, phone, url);
        return str;
    }

//    @PermitAll
//    @PostMapping("/test/redis/lock")
//    public void testRedisLock(@RequestParam String redisLockKey) {
//        threadPoolTaskExecutor.execute(() -> {
//            redisLock(redisLockKey);
//        });
//    }
//
//    private void redisLock(String redisLockKey) {
//        RLock reentrantLock = redisson.getLock(Constants.REDIS_LOCK_KEY + redisLockKey);
//        try {
//            boolean res = redisReentrantLockUtil.tryLock(reentrantLock, 30, 60, TimeUnit.SECONDS);
//            log.info("获取分布式锁结果：{}", res);
//        } catch (Exception e) {
//            log.error("获取redis分布式锁异常：", e);
//        } finally {
//            redisReentrantLockUtil.unlock(reentrantLock, 3);
//        }
//    }
}

package com.jlr.ecp.order.dal.mysql.refund;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundPaymentRecordsDO;
import com.jlr.ecp.order.enums.payment.RefundTradeStatus;
import org.apache.ibatis.annotations.Mapper;

import static com.jlr.ecp.order.constant.Constants.LIMIT_ONE;

@Mapper
public interface OrderRefundPaymentRecordsMapper extends BaseMapperX<OrderRefundPaymentRecordsDO> {
    default OrderRefundPaymentRecordsDO selectByRefundOrderCode(String orderRefundCode){
        return selectOne(new LambdaQueryWrapperX<OrderRefundPaymentRecordsDO>()
                .eq(OrderRefundPaymentRecordsDO::getRefundOrderCode,orderRefundCode)
                .eq(BaseDO::getIsDeleted,false)
                .eq(OrderRefundPaymentRecordsDO::getTradeStatus, RefundTradeStatus.PENDING.getCode())
                .orderByDesc(OrderRefundPaymentRecordsDO::getId)
                .last(LIMIT_ONE));
    }
    // 可以添加自定义查询方法
} 
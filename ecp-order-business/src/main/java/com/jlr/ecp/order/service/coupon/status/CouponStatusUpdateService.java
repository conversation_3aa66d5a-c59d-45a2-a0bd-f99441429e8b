package com.jlr.ecp.order.service.coupon.status;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.refund.dto.BaseOrderRefundApplyDTO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.dal.dataobject.order.OrderCouponDetailDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderCouponDetailDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.order.*;
import com.jlr.ecp.order.enums.refund.RefundOrderTypeEnum;
import com.jlr.ecp.order.service.coupon.status.Enum.CouponStatusFromSqsEnum;
import com.jlr.ecp.order.service.coupon.status.dto.CouponStatusChangedKafkaDto;
import com.jlr.ecp.order.service.coupon.status.dto.CouponStatusNotifyDto;
import com.jlr.ecp.order.service.coupon.status.dto.OrderStatusChangedKafkaDto;
import com.jlr.ecp.order.util.RedisReentrantLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.sqs.model.Message;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.jlr.ecp.order.constant.Constants.COUPON_STATUS_CANCEL_REDIS_LOCK_KEY;
import static com.jlr.ecp.order.constant.Constants.COUPON_STATUS_USED_REDIS_LOCK_KEY;
import static com.jlr.ecp.order.constant.KafkaConstants.COUPON_STATUS_CHANGE_TOPIC;
import static com.jlr.ecp.order.constant.KafkaConstants.ORDER_STATUS_CHANGE_TOPIC;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.coupon.status
 * @className: CouponStatusUpdateService
 * @author: gaoqig
 * @description: 优惠券状态发生变化时，需要处理的逻辑服务
 * @date: 2025/3/13 10:58
 * @version: 1.0
 */
@Service
@Slf4j
public class CouponStatusUpdateService {
    final String SYSTEM = "System";
    final List<Integer> ORDER_COUPON_FINAL_STATUS = List.of(EcouponStatusEnum.INVALIDATED.getCode(), EcouponStatusEnum.VERIFIED.getCode(), EcouponStatusEnum.EXPIRED.getCode());
    final List<Integer> ORDER_ITEM_FINAL_STATUS = List.of(OrderItemCouponStatusEnum.VERIFIED.getCode(), OrderItemCouponStatusEnum.RECLAIMED.getCode());

    final List<Integer> NO_NEED_DEAL_COUPON_STATUS_FROM_SQS = List.of(CouponStatusFromSqsEnum.IN_STOCK.getCouponStatus(), CouponStatusFromSqsEnum.NOT_EFFECTIVE.getCouponStatus());
    final Integer OPERATION_TYPE = 2; //"操作类型 1-用户发起 2-系统自动 3-运营发起"

    @Resource
    TransactionOperator transactionOperator;

    @Resource
    OrderCouponDetailDOMapper orderCouponDetailDOMapper;

    @Resource
    OrderInfoDOMapper orderInfoDOMapper;

    @Resource
    OrderItemDOMapper orderItemDOMapper;

    @Resource
    private ProducerTool producerTool;

    @Resource
    private RedisReentrantLockUtil redisReentrantLockUtil;

    @Resource
    private Redisson redisson;


    /***
     * <AUTHOR>
     * @description 处理SQS消息
     * @date 2025/3/25 11:14
     * @param message:
     * @param couponStatusNotifyDto:
     * @return: java.lang.Boolean true 需要删除SQS消息， false：不删除SQS消息
    */

    public Boolean dealWithMessage(Message message, CouponStatusNotifyDto couponStatusNotifyDto) {
        if (NO_NEED_DEAL_COUPON_STATUS_FROM_SQS.contains(couponStatusNotifyDto.getCouponStatus())){
            log.info("优惠券:{}状态为:{},无需处理，直接删除SQS消息", couponStatusNotifyDto.getCouponCode(), CouponStatusFromSqsEnum.getByCouponStatus(couponStatusNotifyDto.getCouponStatus()));
            return true;
        }
        OrderCouponDetailDO orderCouponDetailDO = orderCouponDetailDOMapper.selectOne(new LambdaQueryWrapperX<OrderCouponDetailDO>()
                .eq(OrderCouponDetailDO::getCouponCode, couponStatusNotifyDto.getCouponCode())
                .eq(OrderCouponDetailDO::getIsDeleted, false)
                .last(Constants.LIMIT_ONE));

        if (orderCouponDetailDO == null) {
            log.info("优惠券:{}在系统coupon detail 中不存在，忽略状态变化，直接删除SQS消息", couponStatusNotifyDto.getCouponCode());
            return true;
        }

        log.info("处理优惠券消息状态变更逻辑，券code:{},新状态：{}", couponStatusNotifyDto.getCouponCode(), couponStatusNotifyDto.getCouponStatus());

        try {
            dealWithCouponStatus(couponStatusNotifyDto, orderCouponDetailDO);//如果这边业务逻辑处理失败，则不执行发送kafka逻辑
        } catch (Exception e) {
            log.info("优惠券:{}业务逻辑处理失败，将依赖SQS重试机制进行重试,SQS消息ID:{}", couponStatusNotifyDto.getCouponCode(), message.messageId());
            return false;//如果处理时抛出了异常，则直接返回失败，不删除消息，也不发送kafka消息
        }


        //所有消息状态都要向kafka发送
        String kafkaMessage = null;
        try {
            kafkaMessage = CouponStatusChangedKafkaDto.getInstanceJsonStr(orderCouponDetailDO, couponStatusNotifyDto);
            producerTool.sendMsg(COUPON_STATUS_CHANGE_TOPIC, "", kafkaMessage);
            log.info("优惠券状态变更消息发送成功，topic:{}, 优惠券code:{},消息内容{}", COUPON_STATUS_CHANGE_TOPIC, couponStatusNotifyDto.getCouponCode(), kafkaMessage);
        } catch (Exception e) {
            log.info("优惠券:{}业务逻辑处理完成，但发送kafka失败，将依赖SQS重试机制进行重试,SQS消息ID:{},kafka消息内容:{}", couponStatusNotifyDto.getCouponCode(), message.messageId(), kafkaMessage);
            return true;//即使异常，也返回成功，因为业务逻辑已经处理完整，不能重复处理。因为目前的SQS采用的FIFO（先进先出队列），如果不删除，单纯的重试发消息，可能会影响消费能力
        }
        log.info("优惠券状态消息发送成功，优惠券code:{},消息内容{}", couponStatusNotifyDto.getCouponCode(), kafkaMessage);
        return true;
    }

    /***
     * <AUTHOR>
     * @description 处理消费券状态变化的消息
     * @date 2025/3/11 22:13
     * @param couponStatusNotifyDto: 消息状态
     * @return: boolean true:需要发送kafka消息
     * @throws Exception 如果处理业务过程中有捕预期的事情发生，需要直接抛出异常
    */
    public void dealWithCouponStatus(CouponStatusNotifyDto couponStatusNotifyDto, OrderCouponDetailDO orderCouponDetailDO) throws Exception {
        CouponStatusFromSqsEnum statusEnum = CouponStatusFromSqsEnum.getByCouponStatus(couponStatusNotifyDto.getCouponStatus());
        if (statusEnum == null) {
            log.warn("暂不支持该状态优惠券的处理:{},直接忽略", couponStatusNotifyDto.getCouponStatus());
            return;
        }
        switch (statusEnum) {
            case EXPIRED: //如果是过期的话需要添加一个锁，涉及到退款
                handleExpiredCoupon(couponStatusNotifyDto, orderCouponDetailDO);
                break;
            case USED:
                handleUsedCoupon(couponStatusNotifyDto, orderCouponDetailDO);
                break;
            case CANCELLED:
                handleCancelledCoupon(couponStatusNotifyDto, orderCouponDetailDO);
                break;
//                case TO_BE_USED:    //暂时不处理
//                    handleResult = handleToBeUsedCoupon(couponStatusNotifyDto, orderCouponDetailDO);
//                    break;
            default:
                log.warn("暂不支持该状态优惠券的处理");
                break;
        }
    }

    /**
     * 处理过期优惠券状态
     * 一个order item下的优惠券的过期时间一定是一样的，所以如果有多张出现，就需要将剩下的全部置为已过期，后续进来的消息直接忽略即可
     * @param couponStatusNotifyDto 优惠券状态通知DTO
     * @param orderCouponDetailDO 优惠券订单信息DO
     * @return 处理结果
     */

    public Boolean handleExpiredCoupon(CouponStatusNotifyDto couponStatusNotifyDto, OrderCouponDetailDO orderCouponDetailDO) {
        log.info("开始处理过期优惠券，券码：{}", couponStatusNotifyDto.getCouponCode());
        if (EcouponStatusEnum.EXPIRED.getCode() == (orderCouponDetailDO.getStatus())) {
            log.info("券码：{}已过期，无需处理", couponStatusNotifyDto.getCouponCode());
            return true;//处理过
        }

        //涉及到退款，所以加锁单条单条处理保险一点
        RLock rLock = redisson.getLock(COUPON_STATUS_CANCEL_REDIS_LOCK_KEY + orderCouponDetailDO.getOrderItemCode());
        try {
            if (redisReentrantLockUtil.tryLock(rLock, 10, 5, TimeUnit.SECONDS)) {//等待时间不能太长，因为SQS漂浮时间不是很长(20s)
                log.info("成功获取到锁，券码：{}", couponStatusNotifyDto.getCouponCode());
                //检查状态是不是已过期状态，如果是，不再处理。否则可能造成多退款的
                //因为可能多个线程同时在阻塞拿锁，先拿到的可能会修改coupon detail的状态，但是状态检测已经过了，所以需要在检测一次
                orderCouponDetailDO = orderCouponDetailDOMapper.selectOne(new LambdaQueryWrapperX<OrderCouponDetailDO>()
                        .eq(OrderCouponDetailDO::getCouponCode, couponStatusNotifyDto.getCouponCode())
                        .eq(OrderCouponDetailDO::getIsDeleted, false)
                        .last("limit 1"));

                if (EcouponStatusEnum.EXPIRED.getCode() == (orderCouponDetailDO.getStatus())) {
                    log.info("券码：{}已并发处理过，无需处理", couponStatusNotifyDto.getCouponCode());
                    return true;//处理过
                }

                BaseOrderRefundApplyDTO refundApplyDTO = new BaseOrderRefundApplyDTO();
                refundApplyDTO.setOriginOrderCode(orderCouponDetailDO.getOrderCode());
                refundApplyDTO.setOrderItemCode(orderCouponDetailDO.getOrderItemCode());
                refundApplyDTO.setRefundReason(CouponRefundReasonEnum.AUTO_REFUND_OVERDUE.getCode());
                refundApplyDTO.setRefundOrderType(RefundOrderTypeEnum.REFUND_ONLY.getCode());
                refundApplyDTO.setRefundFulfilmentType(OrderTypeEnum.ELECTRONIC_COUPON.getCode());//追加履约方式


                List<OrderCouponDetailDO> notFinalOrderCouPonDetailDos = orderCouponDetailDOMapper.selectList(new LambdaQueryWrapperX<OrderCouponDetailDO>()
                        .eq(OrderCouponDetailDO::getOrderItemCode, orderCouponDetailDO.getOrderItemCode())
                        .notIn(OrderCouponDetailDO::getStatus, ORDER_COUPON_FINAL_STATUS)//这里注意不要拿到那些正常的终态
                        .eq(OrderCouponDetailDO::getIsDeleted, 0));

                notFinalOrderCouPonDetailDos.forEach(item -> {
                    item.setStatus(EcouponStatusEnum.EXPIRED.getCode());
                    item.setUpdatedTime(couponStatusNotifyDto.getUpdateTime());
                });

                try {
                    transactionOperator.updateOrderCouponDetail(notFinalOrderCouPonDetailDos, refundApplyDTO, OPERATION_TYPE);
                } catch (RuntimeException e) {
                    log.warn("更新优惠券状态失败，券码：{}", couponStatusNotifyDto.getCouponCode(), e);
                    throw e;
                }
                return true;

            }else{
                log.info("获取锁失败，券码：{}", couponStatusNotifyDto.getCouponCode());
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.INTERNAL_EXCEPTION);
            }
        } finally {
            redisReentrantLockUtil.unlock(rLock, 3);
        }

    }

    /**
     * 处理已核销优惠券状态
     * @param couponStatusNotifyDto 优惠券状态通知DTO
     * @return 处理结果
     */
    private Boolean handleUsedCoupon(CouponStatusNotifyDto couponStatusNotifyDto, OrderCouponDetailDO orderCouponDetailDO) {
        log.info("开始处理已核销优惠券，券码：{}", couponStatusNotifyDto.getCouponCode());
        if (EcouponStatusEnum.VERIFIED.getCode() == (orderCouponDetailDO.getStatus())) {
            log.info("券码：{}已核销，无需处理", couponStatusNotifyDto.getCouponCode());
            return true;
        }

        //涉及到退款，所以加锁单条单条处理保险一点
        RLock rLock = redisson.getLock(COUPON_STATUS_USED_REDIS_LOCK_KEY + orderCouponDetailDO.getOrderItemCode());
        try {
            if (redisReentrantLockUtil.tryLock(rLock, 10, 15, TimeUnit.SECONDS)) {//等待时间不能太长，因为SQS漂浮时间不是很长
                //因为可能多个线程同时在阻塞拿锁，先拿到的会修改coupon detail的状态，但是状态检测已经过了，所以需要在检测一次
                orderCouponDetailDO = orderCouponDetailDOMapper.selectOne(new LambdaQueryWrapperX<OrderCouponDetailDO>()
                        .eq(OrderCouponDetailDO::getCouponCode, couponStatusNotifyDto.getCouponCode())
                        .eq(OrderCouponDetailDO::getIsDeleted, false)
                        .last("limit 1"));

                if (EcouponStatusEnum.VERIFIED.getCode() == (orderCouponDetailDO.getStatus())) {
                    log.info("券码：{}已并发核销过，无需处理", couponStatusNotifyDto.getCouponCode());
                    return true;//处理过
                }

                LocalDateTime now = LocalDateTime.now();

                orderCouponDetailDO.setStatus(EcouponStatusEnum.VERIFIED.getCode());
                orderCouponDetailDO.setUpdatedBy(SYSTEM);
                orderCouponDetailDO.setUpdatedTime(now);
                orderCouponDetailDO.setUsedTime(couponStatusNotifyDto.getUpdateTime());

                List<OrderCouponDetailDO> orderCouponDetailDOS = orderCouponDetailDOMapper.selectList(new LambdaQueryWrapperX<OrderCouponDetailDO>()
                        .eq(OrderCouponDetailDO::getOrderItemCode, orderCouponDetailDO.getOrderItemCode())//查看该订单行的其他订单明细状态
                        .eq(OrderCouponDetailDO::getIsDeleted, false));

                //如果订单券明细表中的状态都是终态，则需要更新订单行的信息
                boolean needUpdateOrderItemInfo = true;
                boolean needUpdateOrderInfo = true;
                for (OrderCouponDetailDO item : orderCouponDetailDOS) {
                    if (existNotFinalStatusOrderCouponDetail(orderCouponDetailDO, item)) {//要把自己排除出去
                        log.info("存在非终态的order coupon detail, id={},order_item_code={}, coupon_code={}", item.getId(), item.getOrderItemCode(), item.getCouponCode());
                        needUpdateOrderItemInfo = false;
                        break;
                    }
                }

                needUpdateOrderInfo = isNeedUpdateOrderInfo(orderCouponDetailDO, needUpdateOrderItemInfo, needUpdateOrderInfo);

                if (updateDb(couponStatusNotifyDto, orderCouponDetailDO, needUpdateOrderItemInfo, needUpdateOrderInfo, now))
                    return false;

                couponStatusNotifyDto.setLastUsedCoupon(needUpdateOrderItemInfo);
            } else {
                log.info("获取券取消分布锁失败，不处理，券code:{}", couponStatusNotifyDto.getCouponCode());
                throw new RuntimeException("获取券取消分布锁失败，不处理");
            }
        } finally {
            redisReentrantLockUtil.unlock(rLock, 3);
        }
        return true;
    }

    private boolean isNeedUpdateOrderInfo(OrderCouponDetailDO orderCouponDetailDO, boolean needUpdateOrderItemInfo, boolean needUpdateOrderInfo) {
        if (needUpdateOrderItemInfo) {//如果需要更新订单行，则判断是否需要更新主订单，判断逻辑也是类似（所有订单行都已经是终态）
                                        /*'item状态;VCS: 1:待激活  2:激活中  3：已激活 4：激活失败
                                          物流履约订单状态：1：待发货，2：已发货，3：已妥投，4: 已收货
                                        优惠券履约订单状态：1：待发放，2：待核销  3：已核销  4：已回收'*/
            List<OrderItemDO> orderItemDOS = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                    .eq(OrderItemDO::getOrderCode, orderCouponDetailDO.getOrderCode())//查看该大订单下的其他订单明细状态
                    .eq(OrderItemDO::getIsDeleted, false));
            for (OrderItemDO item : orderItemDOS) {
                if (existNotFinalOrderItemStatus(orderCouponDetailDO, item)) {//要把自己排除出去
                    log.info("存在非终态的order item , id={},order_item_code={}", item.getId(), item.getOrderItemCode());
                    needUpdateOrderInfo = false;
                    break;
                }
            }
        } else {//如果订单行都不需要更新，则大订单也必然不需要更新
            needUpdateOrderInfo = false;
        }
        return needUpdateOrderInfo;
    }

    private boolean updateDb(CouponStatusNotifyDto couponStatusNotifyDto, OrderCouponDetailDO orderCouponDetailDO, boolean needUpdateOrderItemInfo, boolean needUpdateOrderInfo, LocalDateTime now) {
        if (needUpdateOrderItemInfo && needUpdateOrderInfo) {
            return updateThreeOrderInfo(couponStatusNotifyDto, orderCouponDetailDO, now);

        } else if (needUpdateOrderItemInfo) {
            log.info("进行两表更新: order_item_code={}, order_coupon_detail={}", orderCouponDetailDO.getOrderItemCode(), orderCouponDetailDO.getCouponCode());
            OrderItemDO orderItemDO = orderItemDOMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                    .eq(OrderItemDO::getOrderItemCode, orderCouponDetailDO.getOrderItemCode())//只更新当前订单行的数据即可，不用更新其他行）
                    .eq(OrderItemDO::getIsDeleted, false)
            );
            orderItemDO.setItemStatus(OrderStatusEnum.COMPLETED.getCode());
            orderItemDO.setUpdatedBy(SYSTEM);
            orderItemDO.setUpdatedTime(now);

            transactionOperator.updateOrderCouponAndOrderItemInfo(orderCouponDetailDO, orderItemDO);
        } else {
            orderCouponDetailDOMapper.updateById(orderCouponDetailDO);
        }
        return false;
    }

    private boolean existNotFinalOrderItemStatus(OrderCouponDetailDO orderCouponDetailDO, OrderItemDO item) {
        return !ORDER_ITEM_FINAL_STATUS.contains(item.getItemStatus()) && !item.getOrderItemCode().equals(orderCouponDetailDO.getOrderItemCode());
    }

    private boolean existNotFinalStatusOrderCouponDetail(OrderCouponDetailDO orderCouponDetailDO, OrderCouponDetailDO item) {
        return !ORDER_COUPON_FINAL_STATUS.contains(item.getStatus()) && !item.getCouponCode().equals(orderCouponDetailDO.getCouponCode());
    }

    private boolean updateThreeOrderInfo(CouponStatusNotifyDto couponStatusNotifyDto, OrderCouponDetailDO orderCouponDetailDO, LocalDateTime now) {
        log.info("进行三表更新:order_code={}, order_item_code={}, order_coupon_detail={}", orderCouponDetailDO.getOrderCode(), orderCouponDetailDO.getOrderItemCode(), orderCouponDetailDO.getCouponCode());
        OrderInfoDO orderInfoDO = orderInfoDOMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCouponDetailDO.getOrderCode())
                .eq(OrderInfoDO::getIsDeleted, false)
        );
        orderInfoDO.setCouponStatus(OrderCouponStatusEnum.VERIFIED.getCode());
        orderInfoDO.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
        orderInfoDO.setUpdatedBy(SYSTEM);
        orderInfoDO.setUpdatedTime(now);
        orderInfoDO.setCompletedTime(couponStatusNotifyDto.getUpdateTime());

        OrderItemDO orderItemDO = orderItemDOMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderItemCode, orderCouponDetailDO.getOrderItemCode())//只更新当前订单行的数据即可，不用更新其他行）
                .eq(OrderItemDO::getIsDeleted, false)
        );
        orderItemDO.setItemStatus(OrderStatusEnum.COMPLETED.getCode());
        orderItemDO.setUpdatedBy(SYSTEM);
        orderItemDO.setUpdatedTime(now);

        transactionOperator.updateOrderCouponAndOrderInfo(orderCouponDetailDO, orderInfoDO, orderItemDO);

        //如果大订单状态完成了，需要发送kafka通知
        String kafkaMessage;
        try {
            OrderStatusChangedKafkaDto orderStatusChangedKafkaDto = new OrderStatusChangedKafkaDto();
            orderStatusChangedKafkaDto.setBusinessCode(orderInfoDO.getBusinessCode());
            orderStatusChangedKafkaDto.setOrderCode(orderInfoDO.getOrderCode());
            orderStatusChangedKafkaDto.setUpdateTime(orderInfoDO.getCompletedTime());
            orderStatusChangedKafkaDto.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
            orderStatusChangedKafkaDto.setTenantId((long) orderInfoDO.getTenantId());
            kafkaMessage = JSONObject.toJSONString(orderStatusChangedKafkaDto);
            producerTool.sendMsg(ORDER_STATUS_CHANGE_TOPIC, "", kafkaMessage);
            log.info("主订单完成:{}，发送kafka通知成功，topic:{}, 通知内容{},", orderInfoDO.getOrderCode(), ORDER_STATUS_CHANGE_TOPIC, kafkaMessage);
        } catch (Exception e) {
            log.info("主订单完成:{}，券code:{}，但发送kafka失败", orderInfoDO.getOrderCode(), couponStatusNotifyDto.getCouponCode(), e);
            return true;
        }
        return false;
    }


    public Boolean handleCancelledCoupon(CouponStatusNotifyDto couponStatusNotifyDto, OrderCouponDetailDO orderCouponDetailDO) {
        log.info("开始处理已作废优惠券，券码：{}", couponStatusNotifyDto.getCouponCode());
        if (EcouponStatusEnum.INVALIDATED.getCode() == (orderCouponDetailDO.getStatus())) {
            log.info("券码：{}已作废，无需处理", couponStatusNotifyDto.getCouponCode());
            return true;
        }

        orderCouponDetailDO.setStatus(EcouponStatusEnum.INVALIDATED.getCode());
        orderCouponDetailDO.setUpdatedTime(LocalDateTime.now());

        //作废只需要更新当前订单行的状态，大订单会在发起作废的时候，发起方处理，所以这里不用处理
        orderCouponDetailDOMapper.updateById(orderCouponDetailDO);
        return true;
    }

//    public Boolean handleToBeUsedCoupon(CouponStatusNotifyDto couponStatusNotifyDto, OrderCouponDetailDO orderCouponDetailDO) {
//        log.info("开始处理待使用优惠券，券码：{}", couponStatusNotifyDto.getCouponCode());
//
//        orderCouponDetailDO.setStatus(EcouponStatusEnum.PENDING_USE.getCode());
//        orderCouponDetailDO.setUpdatedBy(SYSTEM);
//        orderCouponDetailDO.setUpdatedTime(LocalDateTime.now());
//        orderCouponDetailDO.setUsedTime(null);//如果商品重新发过来，需要把核销时间清空
//
//        //
//        orderCouponDetailDOMapper.update(orderCouponDetailDO, new LambdaUpdateWrapper<OrderCouponDetailDO>()
//                .eq(OrderCouponDetailDO::getId, orderCouponDetailDO.getId()));
//        return true;
//    }

}

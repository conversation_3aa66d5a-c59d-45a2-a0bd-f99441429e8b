package com.jlr.ecp.order.controller.admin.feedback;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.datapermission.core.annotation.DataPermission;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedBackCreateDTO;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedBackEnableStatusDTO;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedBackUpdateDTO;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedbackPageReqDTO;
import com.jlr.ecp.order.controller.admin.feedback.vo.*;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.feedback.FeedBackEnableStatusEnum;
import com.jlr.ecp.order.enums.feedback.FeedBackTypeEnum;
import com.jlr.ecp.order.service.feedback.FeedbackConfigDOService;
import com.jlr.ecp.order.service.feedback.FeedbackModifyLogDOService;
import com.jlr.ecp.order.service.feedback.FeedbackSnapshotDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.ArrayList;
import java.util.List;

/**
 * 评价配置配置-评价维度配置
 *
 * <AUTHOR>
 */
@Tag(name = "商品管理 - 评价配置")
@RestController
@RequestMapping("v1/feedback")
@Validated
@Slf4j
public class FeedBackController {


    @Resource
    private FeedbackConfigDOService feedbackConfigDOService;

    @Resource
    private FeedbackModifyLogDOService feedbackModifyLogDOService;

    @Resource
    private FeedbackSnapshotDOService feedbackSnapshotDOService;

    /**
     * 评价环节选下拉列表
     */
    @GetMapping("/getFeedBackTypeList")
    @Operation(summary = "评价环节选下拉列表接口")
    @PermitAll
    CommonResult<List<FeedBackTypeVO>> getFeedBackTypeList() {
        List<FeedBackTypeVO> typeList = new ArrayList<>();
        for (FeedBackTypeEnum value : FeedBackTypeEnum.values()) {
            FeedBackTypeVO vo = new FeedBackTypeVO();
            vo.setCode(value.getCode());
            vo.setText(value.getDesc());
            typeList.add(vo);
        }
        return CommonResult.success(typeList);
    }



    /**
     * 创建评价API接口
     * @param feedBackCreateDTO 入参
     * @return CommonResult<String>
     */
    @PostMapping( "/create")
    @Operation(summary = "创建评价")
    @PreAuthorize("@ss.hasPermission('product:config-evaluate:create')")
    @DataPermission(enable = false)
    CommonResult<String> createFeedback(@Validated @RequestBody FeedBackCreateDTO feedBackCreateDTO){
        Boolean success = feedbackConfigDOService.createFeedback(feedBackCreateDTO);
        if(success){
            return CommonResult.success(Constants.FEEDBACK_CREATE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.FEEDBACK_CREATE_FAIL);
    }


    /**
     * 评价配置评价详情API
     * @param feedbackCode
     * @return CommonResult<List<FeedBackConfigVO>>
     */
    @GetMapping( "/view")
    @Operation(summary = "评价配置评价查询接口")
    @Parameter(name = "feedbackCode", description = "评价CODE")
    @PreAuthorize("@ss.hasPermission('product:config-evaluate:detail')")
    @DataPermission(enable = false)
    CommonResult<FeedBackConfigVO> view(@RequestParam(value = "feedbackCode") String feedbackCode) {
        FeedBackConfigVO feedBackConfigVO = feedbackConfigDOService.getOneByFeedBackCode(feedbackCode);
        return CommonResult.success(feedBackConfigVO);
    }

    /**
     * 评价配置评价复制API
     * @param feedbackCode
     * @return CommonResult<List<FeedBackConfigVO>>
     */
    @GetMapping( "/copy")
    @Operation(summary = "评价配置评价复制接口")
    @Parameter(name = "feedbackCode", description = "评价CODE")
    @PreAuthorize("@ss.hasPermission('product:config-evaluate:copy')")
    @DataPermission(enable = false)
    CommonResult<FeedBackConfigVO> copy(@RequestParam(value = "feedbackCode") String feedbackCode) {
        FeedBackConfigVO feedBackConfigVO = feedbackConfigDOService.getOneByFeedBackCode(feedbackCode);
        return CommonResult.success(feedBackConfigVO);
    }

    /**
     * 编辑评价API接口
     * @param updateDTO 修改入参
     * @return String
     */
    @PutMapping( "/edit")
    @Operation(summary = "编辑评价")
    @PreAuthorize("@ss.hasPermission('product:config-evaluate:edit')")
    @DataPermission(enable = false)
    CommonResult<String> edit(@Validated @RequestBody FeedBackUpdateDTO updateDTO){
        Boolean success = feedbackConfigDOService.edit(updateDTO);
        if(success){
            return CommonResult.success(Constants.FEEDBACK_UPDATE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.FEEDBACK_UPDATE_FAIL);
    }



    /**
     * 删除评价
     * @param feedbackCode 评价编码
     * @return CommonResult<String>
     */
    @DeleteMapping( "/delete")
    @Operation(summary = "删除评价")
    @Parameter(name = "feedbackCode", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('product:config-evaluate:delete')")
    @DataPermission(enable = false)
    CommonResult<String> deleteByFeedbackCode(@RequestParam("feedbackCode") String feedbackCode){
        Boolean success = feedbackConfigDOService.delete(feedbackCode);
        if(success){
            return CommonResult.success(Constants.FEEDBACK_DELETE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.FEEDBACK_DELETE_FAIL);

    }




    @PostMapping( "/page")
    @Operation(summary = "评价配置分页列表查询接口")
    @PreAuthorize("@ss.hasPermission('product:config-evaluate:list')")
    @DataPermission(enable = false)
    CommonResult<PageResult<FeedBackConfigVO>> page(@Validated @RequestBody PageParam dto){
        PageResult<FeedBackConfigVO> pageResult = feedbackConfigDOService.selectFeedBackPage(dto);
        return CommonResult.success(pageResult);
    }



    /**
     * 编辑记录信息分页查询
     * @param dto 入参
     * @return
     */
    @PostMapping( "/modifyPage")
    @Operation(summary = "编辑记录 分页查询")
    @PreAuthorize("@ss.hasPermission('product:config-evaluate:detail')")
    @DataPermission(enable = false)
    CommonResult<PageResult<FeedbackModifyLogVO>> selectModifyPage(@Validated @RequestBody FeedbackPageReqDTO dto){
        PageResult<FeedbackModifyLogVO> pageResult = feedbackModifyLogDOService.selectModifyPage(dto);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/modifyLog/detail")
    @Operation(summary = "编辑记录详情查询")
    @PreAuthorize("@ss.hasPermission('product:config-evaluate:detail')")
    public CommonResult<FeedbackModifyDetailVO> getModifyLogDetail(@RequestParam("id") @Schema(description = "修改记录 日志ID") Long id) {
        FeedbackModifyDetailVO detail = feedbackModifyLogDOService.getLogDetail(id);
        return CommonResult.success(detail);
    }


    @PostMapping( "/historyPage")
    @Operation(summary = "历史版本分页列表")
    @PreAuthorize("@ss.hasPermission('product:config-evaluate:detail')")
    @DataPermission(enable = false)
    CommonResult<PageResult<FeedbackVserionListVO>> historyPage(@Validated @RequestBody FeedbackPageReqDTO dto){
        PageResult<FeedbackVserionListVO> pageResult = feedbackSnapshotDOService.selectHistoryPage(dto);
        return CommonResult.success(pageResult);
    }


    @PostMapping("/enable")
    @Operation(summary = "评价配置 - 启用")
    @PreAuthorize("@ss.hasPermission('product:config-evaluate:start-using')")
    @DataPermission(enable = false)
    CommonResult<String> updateProductEnableStatus(@Validated @RequestBody FeedBackEnableStatusDTO dto) {
        dto.setEnableStatus(FeedBackEnableStatusEnum.ENABLE.getCode());

        if (!feedbackConfigDOService.updateEnableStatus(dto)) {
            return CommonResult.error(ErrorCodeConstants.FEEDBACK_ENABLE_ERROR);
        }
        return CommonResult.success(Constants.FEEDBACK_ENABLE_SUCCESS_MESSAGE);
    }

    @PostMapping("/disable")
    @Operation(summary = "评价配置 - 停用")
    @PreAuthorize("@ss.hasPermission('product:config-evaluate:stop-using')")
    @DataPermission(enable = false)
    CommonResult<String> updateProductDisableStatus(@Validated @RequestBody FeedBackEnableStatusDTO dto) {
        dto.setEnableStatus(FeedBackEnableStatusEnum.DISABLE.getCode());

        if (!feedbackConfigDOService.updateDisableStatus(dto)) {
            return CommonResult.error(ErrorCodeConstants.FEEDBACK_DISABLE_ERROR);
        }
        return CommonResult.success(Constants.FEEDBACK_DISABLE_SUCCESS_MESSAGE);
    }


    @GetMapping("/getFeedBackCodeListByDimensionsCode")
    @Operation(summary = "根据评价环节查询评价CODE")
    @PermitAll
    CommonResult<List<String>> getFeedBackCodeListByDimensionsCode(String dimensionsCode) {

        return CommonResult.success(feedbackConfigDOService.getFeedBackCodeListByDimensionsCode(dimensionsCode));
    }


    @GetMapping("/getSnapshotCodeByFeedbackCode")
    @Operation(summary = "根据评价CODE查询评价版本快照")
    @PermitAll
    CommonResult<List<FeedbackVserionListVO>> getSnapshotCodeByFeedbackCode(String feedbackCode) {

        return CommonResult.success(feedbackSnapshotDOService.getSnapshotCodeByFeedbackCode(feedbackCode));
    }
}
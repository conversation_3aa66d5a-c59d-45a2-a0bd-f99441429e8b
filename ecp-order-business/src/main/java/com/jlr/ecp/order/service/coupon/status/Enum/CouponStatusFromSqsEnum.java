package com.jlr.ecp.order.service.coupon.status.Enum;


import lombok.AllArgsConstructor;
import lombok.Getter;


@AllArgsConstructor
@Getter
public enum CouponStatusFromSqsEnum {
    //卡券状态1-入库（先有券码，未发放） 2-未生效；3-待使用；4-已作废；5-已核销；6-已过期
    IN_STOCK(1,"入库"),
    NOT_EFFECTIVE(2,"未生效"),
    TO_BE_USED(3,"待使用"),
    CANCELLED(4,"已作废"),
    USED(5,"已核销"),
    EXPIRED(6,"已过期");

    private final Integer couponStatus;
    private final String couponStatusName;

    //通过couponStatus，获取枚举
    public static CouponStatusFromSqsEnum getByCouponStatus(Integer couponStatus) {
        for (CouponStatusFromSqsEnum couponStatusFromSqsEnum : CouponStatusFromSqsEnum.values()) {
            if (couponStatusFromSqsEnum.getCouponStatus().equals(couponStatus)) {
                return couponStatusFromSqsEnum;
            }
        }
        return null;
    }
}

package com.jlr.ecp.order.controller.app.order.vo.Integration.crcQueryOrderInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * BG 商品列表item信息
 */
@Schema(description = "BG 商品列表item信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BgGood {

    @Schema(description = "商品属性")
    private String productAttribute;

    /**
     * 原本逻辑
     *       attr_two: `￥${i.productSalePoints ? i.unitSalePointsPrice : i.salePrice}`,
     * 订单行是否有用积分productSalePoints
     *
     * a.setProductSalePoints(orderItemDO.getPointAmount())
     * b.setUnitSalePointsPrice(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderItemDO.getCostAmount()/ orderItemDO.getProductQuantity()))
     * c.setSalePrice(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderItemDO.getProductSalePrice()))
     * 可能有可用积分的售价
     */
    @Schema(description = "商品售价")
    private String salePrice;


    @Schema(description = "商品主图URL")
    private String productImageUrl;

    @Schema(description = "商品数量")
    private String productQuantity;

    @Schema(description = "商品名称")
    private String productName;

    /**
     * 原有逻辑
     *       point: i.productSalePoints ? i.unitSalePoints : 0,
     */
    @Schema(description = "积分")
    private Integer point;

    @Schema(description = "所属一级分类，多个由逗号隔开")
    private String categoryCodeLevel1Name;

    @Schema(description = "所属二级分类，多个由逗号隔开")
    private String categoryCodeLevel2Name;
}
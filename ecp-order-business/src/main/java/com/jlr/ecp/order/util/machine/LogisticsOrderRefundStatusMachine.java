package com.jlr.ecp.order.util.machine;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.enums.order.LogisticsOrderRefundEventEnum;
import com.jlr.ecp.order.util.machine.handler.LogisticsEventHandler;
import com.jlr.ecp.order.util.machine.handler.logistics.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 单例电子券退单状态变更
 * <AUTHOR>
 */
@Component
public class LogisticsOrderRefundStatusMachine {

    private Map<Integer, LogisticsEventHandler> eventHandlerMap;

    @Autowired
    public LogisticsOrderRefundStatusMachine(LogisticsRefundApplyHandler eCouponRefundApplyHandler,
                                             LogisticsRefundProcessingHandler logisticsRefundProcessingHandler,
                                             LogisticsRefundApproveHandler logisticsRefundApproveHandler,
                                             LogisticsRefundIndependentHandler logisticsRefundIndependentHandler,
                                             LogisticsRefundCompletedHandler logisticsRefundCompletedHandler,
                                             LogisticsRefundRejectHandler logisticsRefundRejectHandler,
                                             LogisticsRefundBuyerCancelHandler logisticsRefundBuyerCancelHandler,
                                             LogisticsRefundOrderClosedHandler logisticsRefundOrderClosedHandler,
                                             LogisticsReturnApproveHandler logisticsReturnApproveHandler,
                                             LogisticsReturnRejectHandler logisticsReturnRejectHandler
                                           ) {
        // 初始化事件处理器映射
        eventHandlerMap = new HashMap<>();

        // 将handler绑定对应的事件 添加到map中
        eventHandlerMap.put(LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_APPLY.getCode(), eCouponRefundApplyHandler);
        eventHandlerMap.put(LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_PROCESSING.getCode(), logisticsRefundProcessingHandler);
        eventHandlerMap.put(LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_AUDIT.getCode(), logisticsRefundApproveHandler);
        eventHandlerMap.put(LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_INDEPENDENT.getCode(), logisticsRefundIndependentHandler);
        eventHandlerMap.put(LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_COMPLETED.getCode(), logisticsRefundCompletedHandler);
        eventHandlerMap.put(LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_REJECT.getCode(), logisticsRefundRejectHandler);
        eventHandlerMap.put(LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_BUYER_CANCEL.getCode(), logisticsRefundBuyerCancelHandler);
        eventHandlerMap.put(LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_ORDER_CLOSED.getCode(), logisticsRefundOrderClosedHandler);
        eventHandlerMap.put(LogisticsOrderRefundEventEnum.LOGISTICS_RETURN_AUDIT.getCode(), logisticsReturnApproveHandler);
        eventHandlerMap.put(LogisticsOrderRefundEventEnum.LOGISTICS_RETURN_REJECT.getCode(), logisticsReturnRejectHandler);

    }

    public void changeOrderStatus(Integer event,OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO) {
        LogisticsEventHandler eventHandler = eventHandlerMap.get(event);
        if (eventHandler != null) {
            orderRefundDO.setUpdatedTime(LocalDateTime.now());
            eventHandler.handleEvent(orderInfoDO, orderRefundDO, orderItemDO);
        } else {
            // 处理默认情况
        }
    }

}

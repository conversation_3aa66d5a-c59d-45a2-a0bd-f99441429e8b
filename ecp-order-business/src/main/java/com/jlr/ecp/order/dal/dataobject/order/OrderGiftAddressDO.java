package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.tenant.core.db.TenantBaseDO;
import lombok.Data;

/**
 * t_order_gift_address
 */
@TableName(value = "t_order_gift_address")
@Data
public class OrderGiftAddressDO extends TenantBaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 订单编号;订单编号
     */
    @TableField("order_code")
    private String orderCode;

    /**
     * 省编码;省编码
     */
    @TableField("province_code")
    private String provinceCode;

    /**
     * 市编码;市编码
     */
    @TableField("city_code")
    private String cityCode;

    /**
     * 区编码;区编码
     */
    @TableField("area_code")
    private String areaCode;

    /**
     * 详细地址;详细地址
     */
    @TableField("detail_address")
    private String detailAddress;

    /**
     * 收件人;收件人
     */
    @TableField("recipient")
    private String recipient;

    /**
     * 收件人电话;收件人电话
     */
    @TableField("recipient_phone")
    private String recipientPhone;
}
package com.jlr.ecp.order.enums.payment;

import lombok.Getter;

/**
 * DiscountTypeEnum
 *
 * <AUTHOR> <PERSON>
 * @since 2025-03-13 16:06:47
 */
@Getter
public enum DiscountTypeEnum {

    POINT_DISCOUNT(1, "积分"),

    COUPON_DISCOUNT(2, "优惠券");

    DiscountTypeEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    private final Integer type;

    private final String description;
}

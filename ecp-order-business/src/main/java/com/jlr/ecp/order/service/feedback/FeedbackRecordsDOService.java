package com.jlr.ecp.order.service.feedback;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.order.controller.admin.dashboard.dto.FeedbackStatisticQueryDTO;
import com.jlr.ecp.order.controller.app.feedback.dto.FeedbackSubmitDTO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackRecordsDO;

import java.util.Map;

/**
 * 评价记录表Service
 */
public interface FeedbackRecordsDOService extends IService<FeedbackRecordsDO> {
    /**
     * 评价记录表
     */
    Boolean submitFeedback(FeedbackSubmitDTO dto);

    Map<String, Object> scoreCount(FeedbackStatisticQueryDTO dto);
}

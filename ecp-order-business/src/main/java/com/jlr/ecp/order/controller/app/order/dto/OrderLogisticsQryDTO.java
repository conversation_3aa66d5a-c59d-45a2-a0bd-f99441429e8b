package com.jlr.ecp.order.controller.app.order.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * OrderLogisticsQryDTO
 *
 * <AUTHOR> <PERSON>
 * @since 2025-04-03 14:47
 */
@Data
public class OrderLogisticsQryDTO {

    @NotBlank(message = "Parameter order code can not be blank")
    private String orderCode;

    @NotBlank(message = "Parameter company can not be blank")
    private String company;

    @NotBlank(message = "Parameter number can not be blank")
    private String number;

}

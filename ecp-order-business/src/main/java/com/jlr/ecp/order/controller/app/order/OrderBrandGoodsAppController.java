package com.jlr.ecp.order.controller.app.order;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.order.dto.BrandGoodsOrderConfirmReceiptDTO;
import com.jlr.ecp.order.api.order.dto.BrandGoodsOrderCreateDTO;
import com.jlr.ecp.order.api.order.dto.BrandGoodsOrderCreatePdpDTO;
import com.jlr.ecp.order.api.order.dto.BrandGoodsOrderShopCarItemDTO;
import com.jlr.ecp.order.api.order.vo.OrderCreateRespVO;
import com.jlr.ecp.order.service.order.BrandGoodsOrderInfoDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单
 *
 * <AUTHOR>
 */
@Tag(name = "app端 - BG订单")
@RestController
@RequestMapping("v1/order/brandGoods")
@Validated
@Slf4j
public class OrderBrandGoodsAppController {
    @Resource
    private BrandGoodsOrderInfoDOService brandGoodsOrderInfoDOService;


    /**
     * 创建订单API接口
     *
     * @param orderCreateDTO
     * @return
     */
    @PostMapping("/create")
    @Operation(summary = "购物车创建订单")
    CommonResult<OrderCreateRespVO> createOrderInfo(@Valid @RequestBody BrandGoodsOrderCreateDTO orderCreateDTO,
                                                    @RequestHeader(value = "client-id", required = false) String clientId,
                                                    @RequestHeader(value = "jlrId") String jlrId) throws JsonProcessingException {
        orderCreateDTO.getGlobalInfoDTO().setConsumerCode(jlrId);
        // 假设存在一个通用的validate方法
        orderCreateDTO.getShopCarItemList().forEach(BrandGoodsOrderShopCarItemDTO::validateCartItemDetails);
        log.info("请求头clientId:{}, shopCarItemList={}", clientId, orderCreateDTO.getShopCarItemList());

        // 校验联系人电话号码
        orderCreateDTO.validateContactPhone();

        OrderCreateRespVO result = brandGoodsOrderInfoDOService.createOrderInfo(orderCreateDTO);
        return CommonResult.success(result);
    }

    /**
     * 创建订单API接口,通过pdp跳进提交订单页面
     *
     * @param orderCreatePdpDTO
     * @return
     */
    @PostMapping("/pdp/create")
    @Operation(summary = "从pdp页面直接跳转创建订单")
    CommonResult<OrderCreateRespVO> createOrderInfoByPdp(@Valid @RequestBody BrandGoodsOrderCreatePdpDTO orderCreatePdpDTO,
                                                         @RequestHeader(value = "client-id", required = false) String clientId,
                                                         @RequestHeader(value = "jlrId") String jlrId) throws JsonProcessingException {
        orderCreatePdpDTO.getGlobalInfoDTO().setConsumerCode(jlrId);

        // 校验联系人电话号码
        orderCreatePdpDTO.validateContactPhone();

        BrandGoodsOrderCreateDTO orderCreateDTO = new BrandGoodsOrderCreateDTO();
        BeanUtils.copyProperties(orderCreatePdpDTO, orderCreateDTO);
        // 复制 shopCarItemList
        List<BrandGoodsOrderShopCarItemDTO> shopCarItemDTOList = orderCreatePdpDTO.getShopCarItemList().stream()
                .map(pdpItem -> {
                    BrandGoodsOrderShopCarItemDTO dtoItem = new BrandGoodsOrderShopCarItemDTO();
                    BeanUtils.copyProperties(pdpItem, dtoItem);
                    return dtoItem;
                })
                .collect(Collectors.toList());

        orderCreateDTO.setShopCarItemList(shopCarItemDTOList);

        OrderCreateRespVO result = brandGoodsOrderInfoDOService.createOrderInfo(orderCreateDTO);
        return CommonResult.success(result);
    }

    /**
     * 确认收货
     */
    @PostMapping("/confirmReceipt")
    @Operation(summary = "确认收货")
    CommonResult<Void> confirmReceipt(@Valid @RequestBody BrandGoodsOrderConfirmReceiptDTO reqDto) {
        return CommonResult.success(brandGoodsOrderInfoDOService.confirmReceipt(reqDto));
    }
}

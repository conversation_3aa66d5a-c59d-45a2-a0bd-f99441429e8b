package com.jlr.ecp.order.dal.mysql.order;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.dal.dataobject.order.OrderPaymentRecordsDO;
import com.jlr.ecp.order.enums.payment.PaymentStatus;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.jlr.ecp.order.constant.Constants.LIMIT_ONE;

@Mapper
public interface OrderPaymentRecordsMapper extends BaseMapperX<OrderPaymentRecordsDO> {
    default OrderPaymentRecordsDO queryByOrderCode(String orderCode,String parentCode){
        OrderPaymentRecordsDO orderPaymentRecordsDO = selectOne(new LambdaQueryWrapperX<OrderPaymentRecordsDO>()
                .eq(OrderPaymentRecordsDO::getOrderCode, orderCode)
                .eq(OrderPaymentRecordsDO::getPayStatus, PaymentStatus.PAID.getCode())
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(OrderPaymentRecordsDO::getId)
                .last(LIMIT_ONE));
        if(orderPaymentRecordsDO == null){
            orderPaymentRecordsDO = selectOne(new LambdaQueryWrapperX<OrderPaymentRecordsDO>()
                    .eq(OrderPaymentRecordsDO::getOrderCode, parentCode)
                    .eq(OrderPaymentRecordsDO::getPayStatus, PaymentStatus.PAID.getCode())
                    .eq(BaseDO::getIsDeleted, false)
                    .orderByDesc(OrderPaymentRecordsDO::getId)
                    .last(LIMIT_ONE));
            return orderPaymentRecordsDO;
        }

        return orderPaymentRecordsDO;
    }

    default OrderPaymentRecordsDO queryByLastOrderCode(String orderCode,String parentCode){
        OrderPaymentRecordsDO orderPaymentRecordsDO = selectOne(new LambdaQueryWrapperX<OrderPaymentRecordsDO>()
                .eq(OrderPaymentRecordsDO::getOrderCode, orderCode)
                .eq(OrderPaymentRecordsDO::getPayStatus,PaymentStatus.PAID.getCode())
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(OrderPaymentRecordsDO::getId)
                .last(LIMIT_ONE));
        if(orderPaymentRecordsDO == null){
            orderPaymentRecordsDO = selectOne(new LambdaQueryWrapperX<OrderPaymentRecordsDO>()
                    .eq(OrderPaymentRecordsDO::getOrderCode, parentCode)
                    .eq(BaseDO::getIsDeleted, false)
                    .eq(OrderPaymentRecordsDO::getPayStatus,PaymentStatus.PAID.getCode())
                    .orderByDesc(OrderPaymentRecordsDO::getId)
                    .last(LIMIT_ONE));
            return orderPaymentRecordsDO;
        }

        return orderPaymentRecordsDO;
    }

    default OrderPaymentRecordsDO queryLastByOrderCode(String orderCode) {
        return selectOne(new LambdaQueryWrapperX<OrderPaymentRecordsDO>()
                .eq(OrderPaymentRecordsDO::getOrderCode, orderCode)
                .eq(BaseDO::getIsDeleted, Boolean.FALSE)
                .orderByDesc(OrderPaymentRecordsDO::getId)
                .last(LIMIT_ONE));
    }

    /**
     * 查询订单支付单号
     */
    default Map<String, String> mapPayApplyNo(List<String> orderCodes) {
        LambdaQueryWrapper<OrderPaymentRecordsDO> wrapper = Wrappers.lambdaQuery(OrderPaymentRecordsDO.class)
                .eq(OrderPaymentRecordsDO::getPayStatus, PaymentStatus.PAID.getCode())
                .eq(OrderPaymentRecordsDO::getIsDeleted, Boolean.FALSE)
                .in(OrderPaymentRecordsDO::getOrderCode, orderCodes);
        List<OrderPaymentRecordsDO> paymentRecords = this.selectList(wrapper);
        return paymentRecords.stream().collect(Collectors.toMap(OrderPaymentRecordsDO::getOrderCode, OrderPaymentRecordsDO::getPayApplyNo));
    }

    /**
     * 查询订单的所有支付记录（不限制支付状态）
     */
    default List<OrderPaymentRecordsDO> queryAllPaymentRecordsByOrderCodes(List<String> orderCodes) {
        LambdaQueryWrapper<OrderPaymentRecordsDO> wrapper = Wrappers.lambdaQuery(OrderPaymentRecordsDO.class)
                .eq(OrderPaymentRecordsDO::getIsDeleted, Boolean.FALSE)
                .in(OrderPaymentRecordsDO::getOrderCode, orderCodes)
                .orderByDesc(OrderPaymentRecordsDO::getSubmitTime);
        return this.selectList(wrapper);
    }

    /**
     * 查询单个订单的所有支付记录（不限制支付状态）
     */
    default List<OrderPaymentRecordsDO> queryAllPaymentRecordsByOrderCode(String orderCode) {
        LambdaQueryWrapper<OrderPaymentRecordsDO> wrapper = Wrappers.lambdaQuery(OrderPaymentRecordsDO.class)
                .eq(OrderPaymentRecordsDO::getIsDeleted, Boolean.FALSE)
                .eq(OrderPaymentRecordsDO::getOrderCode, orderCode)
                .orderByDesc(OrderPaymentRecordsDO::getSubmitTime);
        return this.selectList(wrapper);
    }

} 
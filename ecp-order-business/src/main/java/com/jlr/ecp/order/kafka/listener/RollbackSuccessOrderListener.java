package com.jlr.ecp.order.kafka.listener;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.kafka.CancelSuccessMessage;
import com.jlr.ecp.order.service.refund.OrderRefundDOService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.errors.TimeoutException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.KafkaException;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;


/**
 * 回退状态同步消费
 *
 * <AUTHOR>
 * */
@Component
@Slf4j
public class RollbackSuccessOrderListener {

    @Resource
    private OrderRefundDOService orderRefundService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @KafkaListener(topics= KafkaConstants.ORDER_ROLLBACK_STATUS_UPDATES_TOPIC, groupId = "order-rollback-status-updates-group-1", properties = "max.poll.records:1")
    @Retryable(value = {KafkaException.class, TimeoutException.class}, exclude = Exception.class, backoff = @Backoff(delay = 5000, multiplier = 2, maxDelay = 30000), maxAttempts = 2)
    public void onMessage(String message) {
        log.info("order rollback success 消费消息, message:{}", message);

        try {
            CancelSuccessMessage cancelMessage = JSON.parseObject(message, CancelSuccessMessage.class);

            boolean checkRes = redisTemplate.opsForValue().setIfAbsent(cancelMessage.getRefundOrderCode()+":"+cancelMessage.getFufilmentId(), "1", 1, TimeUnit.MINUTES);
            if (!checkRes) {
                log.info("order rollback success 消费消息，重复消费，cancelMessage:{}", cancelMessage);
                return;
            }
            TenantContextHolder.setTenantId(cancelMessage.getTenantId());
            Integer integer = orderRefundService.tsdpRefundCallBack(cancelMessage.getRefundOrderCode(),cancelMessage.getUpdateStatus());
            if (integer>0){
                log.info("order rollback success 状态同步成功");
            }else {
                log.info("order rollback success 状态同步失败");
            }
        }  finally {
            TenantContextHolder.clear();
        }
    }

}

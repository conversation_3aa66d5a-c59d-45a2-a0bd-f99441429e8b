package com.jlr.ecp.order.controller.admin.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(description = "仪表盘 - 查询参数")
@Data
@ToString(callSuper = true)
public class SummaryQueryReqDTO {

    @Schema(description = "开始日期")
    @NotBlank(message="开始日期不能为空")
    private String startTime;

    @Schema(description = "结束日期")
    @NotBlank(message="结束日期不能为空")
    private String endTime;

    @Schema(description = "类型筛选, TypeFilterEnum 分组Group By " +
            "eg.渠道1 车型3 品牌3")
    @NotNull(message="类型不能为空")
    private Integer type;

    @Schema(description = "下单渠道, OrderChannelEnum")
    private String orderChannel;

    @Schema(description = "指标, KpiEnum")
    @NotNull(message="指标KPI不能为空")
    private Integer kpi;

    @Schema(description = "业务线")
    @NotNull(message="业务线")
    private String businessCode;


}

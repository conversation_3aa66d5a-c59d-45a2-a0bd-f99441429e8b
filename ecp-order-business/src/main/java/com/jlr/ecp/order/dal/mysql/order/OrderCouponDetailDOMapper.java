package com.jlr.ecp.order.dal.mysql.order;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.dal.dataobject.order.OrderCouponDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【t_order_coupon_detail(t_order_coupon_detail)】的数据库操作Mapper
* @createDate 2025-03-11 10:33:22
* @Entity com.jlr.ecp.order.dal.dataobject.order.OrderCouponDetailDO
*/
@Mapper
public interface OrderCouponDetailDOMapper extends BaseMapperX<OrderCouponDetailDO> {

    default List<OrderCouponDetailDO> listByOrderItemCode(String orderItemCode) {
        LambdaQueryWrapper<OrderCouponDetailDO> wrapper = Wrappers.lambdaQuery(OrderCouponDetailDO.class)
                .eq(OrderCouponDetailDO::getOrderItemCode, orderItemCode)
                .eq(OrderCouponDetailDO::getIsDeleted, Boolean.FALSE);
        return selectList(wrapper);
    }

    default OrderCouponDetailDO getByCouponCode(String couponCode) {
        LambdaQueryWrapper<OrderCouponDetailDO> wrapper = Wrappers.lambdaQuery(OrderCouponDetailDO.class)
                .eq(OrderCouponDetailDO::getCouponCode, couponCode)
                .eq(OrderCouponDetailDO::getIsDeleted, Boolean.FALSE);
        return selectOne(wrapper);
    }

    default  Long getCouponCountByStatus(String orderItemCode,List<Integer> status){
        Long verifiedCouponCount = this.selectCount(new LambdaQueryWrapperX<OrderCouponDetailDO>()
                .eq(BaseDO::getIsDeleted, Boolean.FALSE)
                .eq(OrderCouponDetailDO::getOrderItemCode, orderItemCode)
                .in(OrderCouponDetailDO::getStatus, status)
        );
        if(verifiedCouponCount == null){
             verifiedCouponCount=0L;
        }

        return verifiedCouponCount;
    }

    default  Long getCouponCountByStatus(List<String> orderItemCodes,List<Integer> status){
        Long verifiedCouponCount = this.selectCount(new LambdaQueryWrapperX<OrderCouponDetailDO>()
                .eq(BaseDO::getIsDeleted, Boolean.FALSE)
                .in(OrderCouponDetailDO::getOrderItemCode, orderItemCodes)
                .in(OrderCouponDetailDO::getStatus, status)
        );
        if(verifiedCouponCount == null){
            verifiedCouponCount=0L;
        }

        return verifiedCouponCount;
    }

    default Long getAllCouponCountByItemCode(String itemCode){
        Long count =  this.selectCount(new LambdaQueryWrapperX<OrderCouponDetailDO>()
                .eq(OrderCouponDetailDO::getOrderItemCode, itemCode)
                .eq(OrderCouponDetailDO::getIsDeleted, Boolean.FALSE));
        if(count == null){
            return  0L;
        }
        return count;
    }

    default List<OrderCouponDetailDO> listByOrderCode(String orderCode) {
        return this.selectList(new LambdaQueryWrapperX<OrderCouponDetailDO>()
                .eq(OrderCouponDetailDO::getIsDeleted, Boolean.FALSE)
                .eq(OrderCouponDetailDO::getOrderCode, orderCode)
        );
    }

    default Map<String, List<OrderCouponDetailDO>> mapByOrderCode(List<String> orderCodes) {
        List<OrderCouponDetailDO> couponDetails = this.selectList(new LambdaQueryWrapperX<OrderCouponDetailDO>()
                .eq(OrderCouponDetailDO::getIsDeleted, Boolean.FALSE)
                .in(OrderCouponDetailDO::getOrderCode, orderCodes)
        );
        return couponDetails.stream().collect(Collectors.groupingBy(OrderCouponDetailDO::getOrderCode));
    }

}
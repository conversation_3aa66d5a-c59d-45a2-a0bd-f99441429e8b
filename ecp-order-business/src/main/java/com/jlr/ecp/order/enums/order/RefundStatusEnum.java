package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum RefundStatusEnum {
    /**
     * 未退款
     * 退款状态；0：未退款 未发生退款
     */
    NO_REFUND(0, "未发生退款"),

    /**
     * 部分退款
     * 退款状态；1：部分退款
     */
    PARTIAL_REFUND(1, "部分退款"),

    /**
     * 全退款
     * 退款状态；2：全退款
     */
    FULL_REFUND(2, "全部退款");

    private final Integer code;
    private final String description;

    /**
     * 根据退款状态的整数值获取对应的描述。
     *
     * @param code 退款状态的整数值
     * @return 对应的退款状态描述
     */
    public static String getDescriptionByCode(Integer code) {
        for (RefundStatusEnum status : RefundStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        throw new IllegalArgumentException("Invalid refund status code: " + code);
    }
}
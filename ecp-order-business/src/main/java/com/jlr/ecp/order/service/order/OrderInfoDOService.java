package com.jlr.ecp.order.service.order;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.order.dto.*;
import com.jlr.ecp.order.api.order.dto.customer.ResendMessageDTO;
import com.jlr.ecp.order.api.order.vo.*;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppDetailPage;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppDetailPolicyRespVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.OrderNewBrandRespVO;
import com.jlr.ecp.order.api.order.vo.detail.OrderDetailRespVO;
import com.jlr.ecp.order.api.payment.dto.PayRequestDTO;
import com.jlr.ecp.order.controller.app.order.dto.OrderLatestListReqDTO;
import com.jlr.ecp.order.controller.app.order.vo.Integration.OrderIntegrationLatestOrderRespVO;
import com.jlr.ecp.order.controller.app.order.vo.Integration.crcQueryOrderInfo.OrderIntegrationRespVO;
import com.jlr.ecp.order.controller.app.order.vo.PayOrderRespVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【t_order_info(t_order_info)】的数据库操作Service
 * @createDate 2023-12-20 10:41:04
 */
public interface OrderInfoDOService extends IService<OrderInfoDO> {

    /**
     * 创建订单
     *
     * @param orderCreateDTO
     * @param clientId
     * @return
     */
    OrderCreateRespVO createOrderInfo(OrderCreateDTO orderCreateDTO, String clientId) throws JsonProcessingException;

    /**
     * 订单分页列表
     *
     * @param dto 分页入参
     * @return PageResult<OrderInfoPageVO>
     */
    PageResult<OrderInfoPageVO> getPage(OrderPageReqDTO dto);

    /**
     * 订单详情
     *
     * @param orderCode 订单编号
     * @return OrderDetailRespVO
     */
    OrderDetailRespVO getOrderDetail(String orderCode);

    /**
     * 编辑订单
     *
     * @param orderEditDTO
     * @return
     */
    Boolean editOrderDetail(OrderEditDTO orderEditDTO);

    /**
     * 操作日志分页
     *
     * @param dto
     * @return PageResult<OrderModifyPageVO>
     */
    PageResult<OrderModifyPageVO> selectModifyPage(OrderModifyPageReqDTO dto);

    /**
     * 操作日志详情
     *
     * @param id 日志id
     * @return OrderModifyDetailVO
     */
    OrderModifyDetailVO getLogDetail(Long id);

    /**
     * 小程序端 订单详情
     *
     * @param orderCode
     * @return
     */
    OrderAppDetailPage getAppOrderDetail(String orderCode, String jlrId);

    /***
     * <AUTHOR>
     * @description 获取小程序端订单详情
     * @date 2025/3/9 17:45
     * @param orderCode: 订单编号
     * @param jlrId: 用户ID
     * @return: com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppDetailPage
    */

    OrderAppDetailPage getAppOrderDetail2(String orderCode, String jlrId);
//    /**
//     * 我的订阅tab item下的 订单列表
//     *
//     * @param orderBrandListDTO
//     * @return
//     */
//    List<OrderSubscriptionRespVO> getSubscriptionList(OrderBrandListDTO orderBrandListDTO);

    /**
     * 订单详情下的 条款列表
     *
     * @param orderCode
     * @return
     */
    List<OrderAppDetailPolicyRespVO> getAppOrderDetailPolicyList(String orderCode);

    /**
     * 小程序端 订单列表
     * new version
     *
     * @param dto
     * @return
     */
    List<OrderNewBrandRespVO> getNewOrderList(OrderBrandListNewDTO dto,Integer orderChannel);

    /**
     * 获取订单信息
     *
     * @param orderCode
     * @param jlrId
     * @return
     */
    OrderIntegrationRespVO getOrder(String orderCode, String jlrId);

    /**
     * integration getlatestorders
     *
     * @param dto
     * @return
     */
    OrderIntegrationLatestOrderRespVO getLatestOrders(OrderLatestListReqDTO dto);

    /**
     * 订单支付后 更新订单状态
     *
     * @param orderCode
     * @return
     */
    Integer updateOrderStatus(String orderCode);

    /**
     * 支付成功完成后 更新订单状态
     *
     * @param orderCode
     * @return
     */
    Integer updateOrderStatusOnSuccess(String orderCode, String payFinishTime);

    /**
     * 使用orderCode获取订单下单时间
     *
     * @param orderCode 订单编号
     * @return CommonResult<OrderInvoiceDTO>
     */
    CommonResult<OrderInvoiceDTO> queryOrderInfoByOrderCode(String orderCode);

    /**
     * 根据List<orderCode>查询订单状态为售后处理中的orderCode
     *
     * @param orderCodeList 订单编号
     * @return Set<String>
     */
    Set<String> getAfterSalesOrderCode(List<String> orderCodeList);

    /**
     * 构建支付请求参数
     *
     * @param dto
     * @return
     */
    PayOrderRespVO buildPaymentRequest(PayRequestDTO dto);

    /**
     *  关闭订单超时没有取消的订单
     * @param days 天数
     * @return Integer
     * */
    Integer orderTimeoutCancel(Integer days);

    /**
     *  唤起支付
     * @param orderCode 订单编号
     * @return Boolean
     * */
    Boolean callPayment(String orderCode, String type);

    /**
     *  支付失败
     * @param orderCode 订单编号
     * @return Boolean
     * */
    Boolean paymentFail(String orderCode);

    /**
     * 代客下单-重发短信
     * @param resendMessageDTO
     * @return
     */
    ResendMessageRespVO resendMessage(ResendMessageDTO resendMessageDTO);

    /**
     * 代客下单-回绑订单
     *
     * @param orderNumber
     * @param wxPhone
     * @param clientId
     * @param jlrId
     * @return
     */
    OrderBindRespVO bindOrder(String orderNumber, String wxPhone, String jlrId, String clientId);

    /**
     * 代客下单-选购商品时 前置校验接口 sprint47请勿重复下单的情况提前
     * @param guestOrderPreValidationDTO
     * @return
     */
    OrderCreateRespVO preValidateOrderInfo(OrderCreateDTO guestOrderPreValidationDTO);

    /**
     * 根据Vin查询在途订单
     * @param  carVin carVin
     * @param  serviceType serviceType
     * @return boolean
     */
    boolean checkOrderInTransit(String carVin, Integer serviceType);

    /**
     * 根据VinList查询在途订单
     * @param  carVinList carVinList
     * @param  serviceType serviceType
     * @return List<String>
     */
    List<String> checkOrderInTransitByVinList(List<String> carVinList, Integer serviceType);

    /***
     * <AUTHOR>
     * @description 获取指定用户的订单列表（分页）
     * @date 2025/3/8 14:41
     * @param consumerCode: 用户code
     * @param dto: 查询参数
     * @return: com.jlr.ecp.framework.common.pojo.PageResult<com.jlr.ecp.order.api.order.vo.brandcategory.OrderNewBrandRespVO>
    */

    PageResult<OrderNewBrandRespVO> getOrderPage(String consumerCode, OrderPageReqDTO dto);

    /**
     * 完成订单
     *
     */
    Boolean finishOrder(String orderCode);

    /**
     * 订单异常提示
     *
     */
    Boolean getFailAlert();
}

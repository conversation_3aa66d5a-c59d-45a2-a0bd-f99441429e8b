package com.jlr.ecp.order.util.machine.handler;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import org.springframework.stereotype.Component;

/**
 * 退单事件处理
 * <AUTHOR>
 */
@Component
public interface EventHandler {
    /**
     * 处理退单状态
     * @param orderInfoDO 订单
     * @param orderRefundDO 退单
     */
    void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO);
}

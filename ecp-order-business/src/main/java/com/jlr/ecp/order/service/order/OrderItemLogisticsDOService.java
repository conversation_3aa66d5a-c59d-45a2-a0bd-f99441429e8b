package com.jlr.ecp.order.service.order;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.logistics.api.packageinfo.dto.PackageInfoQueryRespDTO;
import com.jlr.ecp.order.controller.app.order.dto.OrderLogisticsQryDTO;
import com.jlr.ecp.order.controller.app.order.vo.OrderLogisticsQryRespVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemLogisticsDO;

/**
 * OrderItemLogisticsDOService
 *
 * <AUTHOR> <PERSON>
 * @since 2025-04-03 15:20
 */
public interface OrderItemLogisticsDOService extends IService<OrderItemLogisticsDO> {

    OrderLogisticsQryRespVO queryOrderLogisticsInfo(OrderLogisticsQryDTO orderLogisticsQryDTO);

    PackageInfoQueryRespDTO getLogisticsInfo(String companyName, String number, String phone);

}

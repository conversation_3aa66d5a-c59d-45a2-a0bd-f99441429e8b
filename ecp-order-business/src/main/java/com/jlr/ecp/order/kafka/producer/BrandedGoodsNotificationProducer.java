package com.jlr.ecp.order.kafka.producer;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.kafka.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import cn.hutool.core.lang.Snowflake;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * 精选好物通知消息生产者
 */
@Component
@Slf4j
public class BrandedGoodsNotificationProducer {
    
    @Resource
    private ProducerTool producerTool;
    
    @Resource
    private Snowflake ecpIdUtil;
    
    @Value("${branded-goods.kafka.topics.partial-delivery}")
    private String partialDeliveryTopic;
    
    @Value("${branded-goods.kafka.topics.return-refund}")
    private String returnRefundTopic;
    
    @Value("${branded-goods.kafka.topics.refund-only}")
    private String refundOnlyTopic;
    
    @Value("${branded-goods.kafka.topics.payment-success}")
    private String paymentSuccessTopic;

    private String serviceName = "ecp-order-service";

    /**
     * 发送部分发货通知消息
     * 
     * @param orderNumber 订单号
     * @param logisticsNumber 包裹编号
     * @param phoneNumber 手机号
     * @param wxUrl 微信链接
     */
    public void sendPartialDeliveryNotification(String orderNumber, String logisticsNumber, String phoneNumber, String wxUrl) {
        try {
            BrandedGoodsPartialDeliveryMessage message = new BrandedGoodsPartialDeliveryMessage();
            message.setMessageId(ecpIdUtil.nextIdStr());
            message.setBgorderNumber(orderNumber);
            message.setLogisticsNumber(logisticsNumber);
            message.setPhoneNumber(phoneNumber);
            message.setWxUrl(wxUrl);
            message.setTaskCode("brandedgoods-partial-delivery-notification");
            message.setServiceName(serviceName);
            message.setTenantId(1L);

            String messageContent = JSON.toJSONString(message);
            producerTool.sendMsg(partialDeliveryTopic, "", messageContent);
            log.info("发送商品部分发货通知消息成功：{}", messageContent);
        } catch (Exception e) {
            log.error(Constants.SEND_KAFKA_ERROR, e.getMessage());
        }
    }

    /**
     * 发送退货退款通知消息
     * 
     * @param orderNumber 订单号
     * @param phoneNumber 手机号
     * @param wxUrl 微信链接
     */
    public void sendReturnRefundNotification(String orderNumber, String phoneNumber, String wxUrl) {
        try {
            BrandedGoodsReturnRefundMessage message = new BrandedGoodsReturnRefundMessage();
            message.setMessageId(ecpIdUtil.nextIdStr());
            message.setBgorderNumber(orderNumber);
            message.setPhoneNumber(phoneNumber);
            message.setWxUrl(wxUrl);
            message.setTaskCode("brandedgoods-return-refund-notification");
            message.setServiceName(serviceName);
            message.setTenantId(1L);

            String messageContent = JSON.toJSONString(message);
            producerTool.sendMsg(returnRefundTopic, "", messageContent);
            log.info("发送退货退款通知消息成功：{}", messageContent);
        } catch (Exception e) {
            log.error(Constants.SEND_KAFKA_ERROR, e.getMessage());
        }
    }

    /**
     * 发送仅退款通知消息
     * todo 名称待确认
     * @param orderNumber 订单号
     * @param phoneNumber 手机号
     * @param wxUrl 微信链接
     */
    public void sendRefundOnlyNotification(String orderNumber, String phoneNumber, String wxUrl) {
        try {
            BrandedGoodsRefundOnlyMessage message = new BrandedGoodsRefundOnlyMessage();
            message.setMessageId(ecpIdUtil.nextIdStr());
            message.setBgorderNumber(orderNumber);
            message.setPhoneNumber(phoneNumber);
            message.setWxUrl(wxUrl);
            message.setTaskCode("brandedgoods-refund-only-notification");
            message.setServiceName(serviceName);
            message.setTenantId(1L);

            String messageContent = JSON.toJSONString(message);
            producerTool.sendMsg(refundOnlyTopic, "", messageContent);
            log.info("发送仅退款通知消息成功：{}", messageContent);
        } catch (Exception e) {
            log.error(Constants.SEND_KAFKA_ERROR, e.getMessage());
        }
    }

    /**
     * 发送支付成功通知消息
     * 
     * @param orderNumber 订单号
     * @param phoneNumber 手机号
     * @param wxUrl 微信链接
     */
    public void sendPaymentSuccessNotification(String orderNumber, String phoneNumber, String wxUrl) {
        try {
            BrandedGoodsPaymentSuccessMessage message = new BrandedGoodsPaymentSuccessMessage();
            message.setMessageId(ecpIdUtil.nextIdStr());
            message.setBgorderNumber(orderNumber);
            message.setPhoneNumber(phoneNumber);
            message.setWxUrl(wxUrl);
            message.setTaskCode("brandedgoods-payment-success-notification");
            message.setServiceName(serviceName);
            message.setTenantId(1L);

            String messageContent = JSON.toJSONString(message);
            producerTool.sendMsg(paymentSuccessTopic, "", messageContent);
            log.info("发送支付成功通知消息成功：{}", messageContent);
        } catch (Exception e) {
            log.error(Constants.SEND_KAFKA_ERROR, e.getMessage());
        }
    }
} 
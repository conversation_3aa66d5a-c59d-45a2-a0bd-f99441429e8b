package com.jlr.ecp.order.util.order;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.text.CharSequenceUtil;
import com.jlr.ecp.order.api.order.dto.ESpecialInvoiceDTO;
import com.jlr.ecp.order.api.order.dto.PaperInvoiceDTO;
import com.jlr.ecp.order.enums.ErrorCodeConstants;

import java.util.Objects;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
/**
 * 参数校验工具类
 *
 */
public class RequestValidationUtil {
    private RequestValidationUtil() {
        throw new IllegalStateException("Utility class");
    }
    /**
     * 校验纸质发票参数
     *
     * @param paperInvoiceDTO 发票信息
     * @param viewInvoice 是否有查看发票权限
     */
    public static void checkPaperInvoiceDTO(PaperInvoiceDTO paperInvoiceDTO, boolean viewInvoice) {
        if (Objects.isNull(paperInvoiceDTO)) {
            return;
        }
        // 如果开票状态=0，不做参数校验
        if (paperInvoiceDTO.getInvoiceStatus() == 0) {
            return;
        }
        if (CharSequenceUtil.isBlank(paperInvoiceDTO.getCompanyAddress())) {
            throw exception(ErrorCodeConstants.COMPANY_ADDRESS_NULL);
        }
        if (CharSequenceUtil.isBlank(paperInvoiceDTO.getCompanyMobile())) {
            throw exception(ErrorCodeConstants.COMPANY_MOBILE_NULL);
        }
        if (CharSequenceUtil.isBlank(paperInvoiceDTO.getInvoiceTitleName())) {
            throw exception(com.jlr.ecp.payment.enums.ErrorCodeConstants.INVOICE_TITLE_NAME_NULL);
        }
        if (CharSequenceUtil.isBlank(paperInvoiceDTO.getCompanyBankName())) {
            throw exception(ErrorCodeConstants.COMPANY_BANK_NAME);
        }
        if (CharSequenceUtil.isBlank(paperInvoiceDTO.getCompanyBankAccount())) {
            throw exception(ErrorCodeConstants.COMPANY_BANK_ACCOUNT);
        }
        if (CharSequenceUtil.isBlank(paperInvoiceDTO.getTitleTaxNo())) {
            throw exception(ErrorCodeConstants.INVOICE_COMPANY_TAX_NUMBER_NULL);
        }
        // 税号必须15-20位
        if (paperInvoiceDTO.getTitleTaxNo().length() < 15 || paperInvoiceDTO.getTitleTaxNo().length() > 20) {
            throw exception(com.jlr.ecp.payment.enums.ErrorCodeConstants.INVOICE_TITLE_TAX_NUMBER_LENGTH_ERROR);
        }
        validRecipientInfo(paperInvoiceDTO, viewInvoice);
    }

    /**
     * 校验纸质发票接收人信息
     *
     * @param paperInvoiceDTO 发票信息
     * @param viewInvoice 是否有查看发票权限
     */
    private static void validRecipientInfo(PaperInvoiceDTO paperInvoiceDTO, boolean viewInvoice) {
        // 没有查看发票权限, 不做接收人信息参数校验
        if(!viewInvoice){
            return;
        }
        if (CharSequenceUtil.isBlank(paperInvoiceDTO.getRecipientName())) {
            throw exception(ErrorCodeConstants.RECIPIENT_NAME_NULL);
        }
        if (CharSequenceUtil.isBlank(paperInvoiceDTO.getRecipientPhone())) {
            throw exception(ErrorCodeConstants.RECIPIENT_PHONE_NULL);
        }
        if (!Validator.isMobile(paperInvoiceDTO.getRecipientPhone())) {
            throw exception(ErrorCodeConstants.RECIPIENT_PHONE_INVALID);
        }
        if (CharSequenceUtil.isBlank(paperInvoiceDTO.getRecipientAddress())) {
            throw exception(ErrorCodeConstants.RECIPIENT_ADDRESS_NULL);
        }
    }

    public static void checkESpecialInvoiceDTO(ESpecialInvoiceDTO eSpecialInvoiceDTO, boolean viewInvoice) {
        if (Objects.isNull(eSpecialInvoiceDTO)) {
            return;
        }
        // 如果开票状态=0，不做参数校验
        if (eSpecialInvoiceDTO.getInvoiceStatus() == 0) {
            return;
        }
        if (CharSequenceUtil.isBlank(eSpecialInvoiceDTO.getCompanyAddress())) {
            throw exception(ErrorCodeConstants.COMPANY_ADDRESS_NULL);
        }
        if (CharSequenceUtil.isBlank(eSpecialInvoiceDTO.getCompanyMobile())) {
            throw exception(ErrorCodeConstants.COMPANY_MOBILE_NULL);
        }
        if (CharSequenceUtil.isBlank(eSpecialInvoiceDTO.getInvoiceTitleName())) {
            throw exception(com.jlr.ecp.payment.enums.ErrorCodeConstants.INVOICE_TITLE_NAME_NULL);
        }
        if (CharSequenceUtil.isBlank(eSpecialInvoiceDTO.getCompanyBankName())) {
            throw exception(ErrorCodeConstants.COMPANY_BANK_NAME);
        }
        if (CharSequenceUtil.isBlank(eSpecialInvoiceDTO.getCompanyBankAccount())) {
            throw exception(ErrorCodeConstants.COMPANY_BANK_ACCOUNT);
        }
        if (CharSequenceUtil.isBlank(eSpecialInvoiceDTO.getTitleTaxNo())) {
            throw exception(ErrorCodeConstants.INVOICE_COMPANY_TAX_NUMBER_NULL);
        }
        // 税号必须15-20位
        if (eSpecialInvoiceDTO.getTitleTaxNo().length() < 15 || eSpecialInvoiceDTO.getTitleTaxNo().length() > 20) {
            throw exception(com.jlr.ecp.payment.enums.ErrorCodeConstants.INVOICE_TITLE_TAX_NUMBER_LENGTH_ERROR);
        }
        validESpecialRecipientInfo(eSpecialInvoiceDTO, viewInvoice);
    }

    /**
     * 校验电子专票接收人信息
     *
     * @param eSpecialInvoiceDTO 发票信息
     * @param viewInvoice 是否有查看发票权限
     */
    private static void validESpecialRecipientInfo(ESpecialInvoiceDTO eSpecialInvoiceDTO, boolean viewInvoice) {
        // 没有查看发票权限, 不做接收人信息参数校验
        if(!viewInvoice){
            return;
        }
        if (CharSequenceUtil.isBlank(eSpecialInvoiceDTO.getRecipientName())) {
            throw exception(ErrorCodeConstants.RECIPIENT_NAME_NULL);
        }
        if (CharSequenceUtil.isBlank(eSpecialInvoiceDTO.getRecipientPhone())) {
            throw exception(ErrorCodeConstants.RECIPIENT_PHONE_NULL);
        }
        if (!Validator.isMobile(eSpecialInvoiceDTO.getRecipientPhone())) {
            throw exception(ErrorCodeConstants.RECIPIENT_PHONE_INVALID);
        }
        if (CharSequenceUtil.isBlank(eSpecialInvoiceDTO.getRecipientAddress())) {
            throw exception(ErrorCodeConstants.RECIPIENT_ADDRESS_NULL);
        }
    }

}

package com.jlr.ecp.order.enums.order;

/**
 * 退款发起操作类型
 * author: maojie
 */
public enum RefundOrderOperationTypeEnum {

    /**
     * 用户发起
     */
    USER_INITIATED(1, "用户发起", "用户"),

    /**
     * 系统自动
     */
    SYSTEM_AUTO(2, "系统自动", "系统"),

    /**
     * 运营发起
     */
    OPERATION_INITIATED(3, "运营发起", "运营");

    private final Integer code;
    private final String name;
    private final String frontName;

    RefundOrderOperationTypeEnum(Integer code, String name, String frontName) {
        this.code = code;
        this.name = name;
        this.frontName = frontName;
    }

    /**
     * 根据编码获取枚举实例
     * @param code 状态编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static RefundOrderOperationTypeEnum getByCode(Integer code) {
        for (RefundOrderOperationTypeEnum type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    //根据code获取frontName
    public static String getFrontNameByCode(Integer code) {
        for (RefundOrderOperationTypeEnum type : values()) {
            if (type.code == code) {
                return type.frontName;
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getFrontName() {
        return frontName;
    }
}

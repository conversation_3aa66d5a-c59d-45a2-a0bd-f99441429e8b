package com.jlr.ecp.order.controller.admin.feedback.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "管理后台 -  评价启用状态DTO ")
@ToString(callSuper = true)
public class FeedBackEnableStatusDTO {



    @Schema(description = "评价配置编码")
    @NotBlank(message = "评价配置编码不能为空")
    private String feedbackCode;


    /**
     * 商品使用状态(启用状态，0=待启用 1=已启用 2=已停用)
     */
    @Schema(description = "商品使用状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer enableStatus;


    /**
     * 启用时间
     */
    @Schema(description = "启用时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String enableTime;

    /**
     * 启用时间
     */
    @Schema(description = "是否是定时启用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean enableAuto;

}

package com.jlr.ecp.order.util.machine.handler;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import org.springframework.stereotype.Component;

/**
 * 2：发起部分退款申请
 * <AUTHOR>
 */
@Component
public class PartialApplyHandler implements EventHandler {
    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO) {
        //部分退单申请事件处理...变更条件：1.退单状态为null 或者 退单状态为部分退单申请
        if ((orderRefundDO.getRefundOrderStatus()==null
                || orderRefundDO.getRefundOrderStatus().equals(OrderRefundStatusEnum.PARTIAL_REFUND_APPLY.getCode()))
        ){
            orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.PARTIAL_REFUND_APPLY.getCode());
        }
        //变更条件：订单状态：1.完成 或者 售后状态
        if(orderInfoDO.getOrderStatus().equals(OrderStatusEnum.COMPLETED.getCode())
                ||orderInfoDO.getOrderStatus().equals(OrderStatusEnum.AFTER_SALES.getCode())
                ||orderInfoDO.getOrderStatus().equals(OrderStatusEnum.PAID.getCode())
                ||orderInfoDO.getOrderStatus().equals(OrderStatusEnum.PARTIAL_CANCELLED.getCode())
        ){
            orderInfoDO.setOrderStatus(OrderStatusEnum.AFTER_SALES.getCode());
        }
    }

}

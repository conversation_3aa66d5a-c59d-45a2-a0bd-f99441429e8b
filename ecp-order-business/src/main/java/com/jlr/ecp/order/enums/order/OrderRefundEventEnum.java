package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 * 触发事件：
 * 1：发起整单退款申请
 * 2：发起部分退款申请
 * 3：同意整单退款申请
 * 4：同意部分退款申请
 * 5：订单整单退款完成
 * 6：订单部分退款完成
 * 7：拒绝整单退单申请
 * 8：拒绝部分退款申请
 */
@AllArgsConstructor
@Getter
public enum OrderRefundEventEnum {


    /****
     * 触发事件：
     * 1：发起整单退款申请
     * 2：发起部分退款申请
     * 3：同意整单退款申请
     * 4：同意部分退款申请
     * 5：订单整单退款完成(Payment broker完成退款)
     * 6：订单部分退款完成(Payment broker完成退款)
     * 7：拒绝整单退单申请
     * 8：拒绝部分退款申请
     * 9：订单整单退款完成(TSDP停止服务)
     * 10：订单部分退款完成(TSDP停止服务)
     */
    EVENT_FULL_REFUND_APPLY(1, "发起整单退款申请"),
    EVENT_PARTIAL_REFUND_APPLY(2, "发起部分退款申请"),
    EVENT_FULL_REFUND_APPROVE(3, "同意整单退款申请"),
    EVENT_PARTIAL_REFUND_APPROVE(4, "同意部分退款申请"),
    EVENT_FULL_REFUND_COMPLETED_PAYMENT(5, "订单整单退款完成(Payment broker完成退款)"),
    EVENT_PARTIAL_REFUND_COMPLETED_PAYMENT(6, "订单部分退款完成(Payment broker完成退款)"),
    EVENT_FULL_REFUND_REFUSE(7, "拒绝整单退单申请"),
    EVENT_PARTIAL_REFUND_REFUSE(8, "拒绝部分退款申请"),
    EVENT_FULL_REFUND_COMPLETED_TSDP(9, "订单整单退款完成(TSDP停止服务)"),
    EVENT_PARTIAL_REFUND_COMPLETED_TSDP(10, "订单部分退款完成(TSDP停止服务)");

    /**
     * code
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;


}

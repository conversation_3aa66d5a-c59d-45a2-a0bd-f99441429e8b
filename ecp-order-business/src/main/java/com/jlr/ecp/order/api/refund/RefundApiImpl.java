package com.jlr.ecp.order.api.refund;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.refund.dto.OrderRefundDto;
import com.jlr.ecp.order.api.refund.dto.OrderRefundItemDto;
import com.jlr.ecp.order.service.refund.OrderRefundDOService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Validated
public class RefundApiImpl implements RefundApi {

    @Resource
    private OrderRefundDOService orderRefundService;

    @Override
    public CommonResult<Integer> refundPaymentCallback(String orderRefundCode) {
        Integer success = orderRefundService.paymentCallbackProcess(orderRefundCode);
        return CommonResult.success(success);
    }

    @Override
    public CommonResult<Integer> refundTransPaymentCallback(String orderRefundCode) {
        Integer success = orderRefundService.transPaymentCallbackProcess(orderRefundCode);
        return CommonResult.success(success);
    }

    @Override
    public CommonResult<List<OrderRefundDto>> getOrderRefundByOrderCode(String orderCode) {
        List<OrderRefundDto> orderRefundDtoList =
                orderRefundService.getOrderRefundInfoByOrderCode(orderCode);
        return CommonResult.success(orderRefundDtoList);
    }

    @Override
    public CommonResult<List<OrderRefundItemDto>> getOrderRefundItemByOrderRefundCode(
            List<String> orderRefundCodeList) {
        List<OrderRefundItemDto> orderRefundItemDtoList =
                orderRefundService.getOrderRefundItemByOrderRefundCode(orderRefundCodeList);
        return CommonResult.success(orderRefundItemDtoList);
    }
}

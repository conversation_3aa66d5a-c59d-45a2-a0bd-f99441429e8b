package com.jlr.ecp.order.util.machine.handler;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import org.springframework.stereotype.Component;

/**
 * TSDP回调 事件处理
 * <AUTHOR>
 */
@Component
public class TsdpCallbackHandler implements EventHandler {
    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO) {
        //判断改订单是否是处于已支付的时候才进行变更到订单完成
        if(OrderStatusEnum.PAID.getCode().equals(orderInfoDO.getOrderStatus())){
            orderInfoDO.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
        }

    }

}

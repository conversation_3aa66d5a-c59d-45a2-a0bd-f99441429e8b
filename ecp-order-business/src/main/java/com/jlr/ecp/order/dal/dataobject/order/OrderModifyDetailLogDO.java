package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_order_modify_detail_log
 *
 * <AUTHOR>
 * @TableName t_order_modify_detail_log
 */
@TableName(value = "t_order_modify_detail_log")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderModifyDetailLogDO extends BaseDO {
    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 订单编码;订单编码
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 修改模块
     */
    @TableField(value = "modify_module")
    private String modifyModule;

    /**
     * 修改字段数量
     */
    @TableField(value = "modify_field_count")
    private Integer modifyFieldCount;

    /**
     * 修改前字段值;涉及修改字段旧值，用于存储规则修改字段的json
     */
    @TableField(value = "modify_field_old_value")
    private String modifyFieldOldValue;

    /**
     * 修改后字段值;涉及修改字段新值，用于存储规则修改字段的json
     */
    @TableField(value = "modify_field_new_value")
    private String modifyFieldNewValue;

    /**
     * 操作时间
     */
    @TableField(value = "operate_time")
    private LocalDateTime operateTime;

    /**
     * 操作人
     */
    @TableField(value = "operate_user")
    private String operateUser;
}
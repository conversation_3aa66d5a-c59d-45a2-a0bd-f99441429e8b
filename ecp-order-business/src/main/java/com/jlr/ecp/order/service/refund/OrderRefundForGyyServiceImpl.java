package com.jlr.ecp.order.service.refund;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.logistics.enums.company.LogisticsCompanyEnum;
import com.jlr.ecp.order.api.refund.dto.OrderRefundForGyyPageReqDTO;
import com.jlr.ecp.order.api.refund.dto.OrderRefundForGyyPageRespDTO;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.enums.order.CouponRefundReasonEnum;
import com.jlr.ecp.order.enums.order.LogisticsRefundReasonEnum;
import com.jlr.ecp.order.enums.order.OrderTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
* <AUTHOR> Hongyi
* @description 提供给管易云调用的接口
* @createDate 2025-04-09 02:00:00
*/
@Service
@Slf4j
public class OrderRefundForGyyServiceImpl implements OrderRefundForGyyService {

    @Resource
    private OrderRefundDOMapper orderRefundDOMapper;

    @Override
    public PageResult<OrderRefundForGyyPageRespDTO> pageOrderRefund(OrderRefundForGyyPageReqDTO req) {
        req.setFilfullments(List.of(OrderTypeEnum.BRAND_GOOD.getCode()));

        Page<OrderRefundForGyyPageRespDTO> refundOrderItemPage = orderRefundDOMapper.pageForGyy(new Page<>(req.getPageNo(), req.getPageSize()), req);

        List<OrderRefundForGyyPageRespDTO> refundOrderItemList = refundOrderItemPage.getRecords();
        if (CollUtil.isEmpty(refundOrderItemList)) {
            return new PageResult<>(Collections.emptyList(), refundOrderItemPage.getTotal());
        }

        for (OrderRefundForGyyPageRespDTO refundOrderItem:refundOrderItemList) {
            refundOrderItem.setLogisticsCompanyName(LogisticsCompanyEnum.getName(refundOrderItem.getLogisticsCompanyCode()));
            if (OrderTypeEnum.ELECTRONIC_COUPON.getCode().equals(refundOrderItem.getRefundOrderFufilmentType())) {
                refundOrderItem.setRefundReasonName(CouponRefundReasonEnum.getName(refundOrderItem.getRefundReason()));
            } else if (OrderTypeEnum.BRAND_GOOD.getCode().equals(refundOrderItem.getRefundOrderFufilmentType())) {
                refundOrderItem.setRefundReasonName(LogisticsRefundReasonEnum.getName(refundOrderItem.getRefundReason()));
            }
        }
        return new PageResult<>(refundOrderItemList, refundOrderItemPage.getTotal());
    }

}

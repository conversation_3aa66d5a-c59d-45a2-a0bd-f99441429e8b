package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 * TODO 不同场景下 存储在数据库中的枚举值 ，待确认
 */

@AllArgsConstructor
@Getter
public enum PaymentStatusEnum {
    /**
     * 待支付
     * 支付状态；0:待支付
     */
    TO_BE_PAID(0, "待支付"),

    /**
     * 已支付
     * 支付状态；1：已支付
     */
    PAID(1, "已支付"),

    /**
     * 未支付
     * 支付状态；2:未支付
     */
    UNPAID(2, "未支付");


    private final Integer code;
    private final String description;

    /**
     * 根据支付状态的整数值获取对应的描述。
     *
     * @param code 支付状态的整数值
     * @return 对应的支付状态描述
     */
    public static String getDescriptionByCode(Integer code) {
        for (PaymentStatusEnum status : PaymentStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        throw new IllegalArgumentException("Invalid payment status code: " + code);
    }
}

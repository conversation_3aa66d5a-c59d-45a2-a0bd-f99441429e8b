package com.jlr.ecp.order.service.internal.promotion;


import cn.hutool.core.collection.CollUtil;
import com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil;
import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.service.internal.promotion.dto.PromotionRespDto;
import com.jlr.ecp.order.util.money.MoneyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.jlr.ecp.order.enums.ErrorCodeConstants.NOT_ENOUGH_MONEY;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.internal.promotion
 * @className: DiscountCouponPromotion
 * @author: gaoqig
 * @description: 满减优惠; 用券原则： 1 - 折减金额， 2 - 优惠券的过期时间
 * @date: 2025/3/6 17:11
 * @version: 1.0
 */
@Component
@Slf4j
public class CashBackCouponPromotion extends CommonPromotion {

    @Override
    public PromotionRespDto executePromotional(List<CartProductSkuInfo> skuInfos, PromotionDto promotion) {
        PromotionRespDto result = new PromotionRespDto();
        //先计算本优惠券下，所有能参与该优惠券的所有商品汇总信息
        List<CartProductSkuInfo> canUsePromotionSkuList = skuInfos.stream()
                .filter(item->CollUtil.isNotEmpty(item.getCouponModuleCodeList())
                        && item.getCouponModuleCodeList().contains(promotion.getCouponModelCode())
                        && item.isJoinCalculateFlag())
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(canUsePromotionSkuList)){ //如果没有商品能参与优惠，直接返回空，表示没有命中优惠
            return null;
        }
        //只参与优惠的商品总价
        BigDecimal usePromotionProductTotalAmount = canUsePromotionSkuList.stream()
                .map(item -> MoneyUtil.convertToCents(item.getSalePrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //如果小于了优惠券的出发门槛，直接返回空，表示没有命中优惠
        if (usePromotionProductTotalAmount.compareTo(MoneyUtil.convertToCents(promotion.getTriggerAmount())) < 0){
            return null;
        }

        int canUsePromotionSkuCount = canUsePromotionSkuList.size();
        //先计算参与优惠商品的销售总价；分摊折扣金额时要用
        BigDecimal allProductTotalAmount = BigDecimal.ZERO; //总的商品总价（包括不参与优惠的）

        BigDecimal discountAmount = usePromotionProductTotalAmount.min(MoneyUtil.convertToCents(promotion.getTriggerAmount()));
        BigDecimal usedDiscountAmount = BigDecimal.ZERO;  //已经分摊(使用)的优惠金额，用来计算分摊金额使用
        //遍历所有SKU进行，属性赋值
        List<CartProductSkuInfo> returnSkuInfos = new ArrayList<>();
        for (CartProductSkuInfo skuInfo : skuInfos) {
            CartProductSkuInfo returnSkuInfo = getCartProductSkuInfo(skuInfo); //这里要copy一份新的sku信息，因为不能修改原数据，原数据可能还要参与后续的计算
            allProductTotalAmount = allProductTotalAmount.add(MoneyUtil.convertToCents(returnSkuInfo.getSalePrice()));
            if (CollUtil.isNotEmpty(returnSkuInfo.getCouponModuleCodeList())
                    && returnSkuInfo.getCouponModuleCodeList().contains(promotion.getCouponModelCode())//只对命中优惠的SKU进行赋值，没有命中的保持不变
                    && returnSkuInfo.isJoinCalculateFlag()) {//要参与计算的才能进行优惠
                returnSkuInfo.setChooseFlag(false);
                returnSkuInfo.setChooseCouponCode(promotion.getCouponCode());
                returnSkuInfo.setChooseCouponType(CouponTypeEnum.CASH_BACK.getType());

                /* =========================优惠金额赋值，由当前商品的销售价金额所占比例来===================== **/
                if (canUsePromotionSkuCount == 1) { //如果是优惠商品的最后一个，则采用这个逻辑
                    BigDecimal skuDiscountAmount = discountAmount.subtract(usedDiscountAmount);
                    BigDecimal skuCouponPrice = MoneyUtil.convertToCents(returnSkuInfo.getSalePrice()).subtract(skuDiscountAmount);//当前sku优惠后花费的金额
                    returnSkuInfo.setDiscountAmount(MoneyUtil.convertFromCents(skuDiscountAmount));
                    returnSkuInfo.setCouponPrice(MoneyUtil.convertFromCents(skuCouponPrice));
                } else {
                    canUsePromotionSkuCount--;
                    BigDecimal amountMultiPrice = MoneyUtil.convertToCents(returnSkuInfo.getSalePrice()).multiply(discountAmount);//先乘在除。
                    BigDecimal skuDiscountAmount = amountMultiPrice.divide(usePromotionProductTotalAmount, 0, RoundingMode.DOWN);
//                    BigDecimal skuSalePricePercent = MoneyUtil.convertToCents(returnSkuInfo.getSalePrice()).divide(usePromotionProductTotalAmount, 5, RoundingMode.DOWN); //当前商品的销售价金额所占参与优惠商品总金额的比例，保留五位小数，四舍五入
//                    BigDecimal skuDiscountAmount = skuSalePricePercent.multiply(discountAmount).setScale(0, RoundingMode.DOWN);;//因为单位已经是分，保留整数部分
                    BigDecimal skuCouponPrice = MoneyUtil.convertToCents(returnSkuInfo.getSalePrice()).subtract(skuDiscountAmount);//当前sku优惠后花费的金额
                    usedDiscountAmount = usedDiscountAmount.add(skuDiscountAmount);
                    returnSkuInfo.setDiscountAmount(MoneyUtil.convertFromCents(skuDiscountAmount));
                    returnSkuInfo.setCouponPrice(MoneyUtil.convertFromCents(skuCouponPrice));
                }
                /* =========================优惠金额赋值，由当前商品的销售价金额所占比例来===================== **/
            }
            returnSkuInfos.add(returnSkuInfo);
        }

        promotion.setDiscountAmount(MoneyUtil.convertFromCents(discountAmount));
        promotion.setCostAmount(MoneyUtil.convertFromCents(allProductTotalAmount.subtract(discountAmount)));

        result.setDiscountTotalAmount(MoneyUtil.convertFromCents(discountAmount));
        result.setCostAmount(MoneyUtil.convertFromCents(allProductTotalAmount.subtract(discountAmount)));
        result.setCartSkuProductList(returnSkuInfos);
        result.setCouponModelName(promotion.getCouponModelName());
        result.setCouponTypeEnum(CouponTypeEnum.CASH_BACK);
        result.setChooseCoupon(promotion);
        return result;
    }

    @Override
    public List<CartProductSkuInfo> checkUserChoose(List<CartProductSkuInfo> skuInfos, List<PromotionDto> promotions, PromotionDto userChoosePromotion){
        List<CartProductSkuInfo> canUsePromotionProductSkuInfos = super.checkUserChoose(skuInfos, promotions, userChoosePromotion);

        //检查商品是否符合满减要求
        //先计算本优惠券下，所有能参与该优惠券的所有商品汇总信息
        //只参与优惠的商品总价
        BigDecimal usePromotionProductTotalAmount = canUsePromotionProductSkuInfos.stream()
                .map(item -> MoneyUtil.convertToCents(item.getSalePrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //如果小于了优惠券的出发门槛，直接返回空，表示没有命中优惠
        if (usePromotionProductTotalAmount.compareTo(MoneyUtil.convertToCents(userChoosePromotion.getTriggerAmount())) < 0){
            throw ServiceExceptionUtil.exception(NOT_ENOUGH_MONEY);
        }
        return canUsePromotionProductSkuInfos;
    }
}

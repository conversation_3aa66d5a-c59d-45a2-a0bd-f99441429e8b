package com.jlr.ecp.order.controller.app.refund;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.logistics.enums.company.LogisticsCompanyEnum;
import com.jlr.ecp.order.api.order.vo.RefundReasonStatusVO;
import com.jlr.ecp.order.api.order.vo.logistics.LogisticsCompanyVO;
import com.jlr.ecp.order.api.refund.dto.BaseOrderRefundApplyDTO;
import com.jlr.ecp.order.api.refund.dto.LogisticsOrderRefundApproveDTO;
import com.jlr.ecp.order.api.refund.dto.OrderRefundLogisticsDTO;
import com.jlr.ecp.order.controller.app.refund.vo.LogisticsRefundItemDetailVO;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.order.LogisticsRefundReasonEnum;
import com.jlr.ecp.order.enums.order.RefundOrderOperationTypeEnum;
import com.jlr.ecp.order.enums.refund.RefundOrderTypeEnum;
import com.jlr.ecp.order.service.refund.bg.BrandGoodsOrderRefundDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 */
@Tag(name = "app端 - 退单")
@RestController
@RequestMapping("v1/refund/bg")
@Validated
@Slf4j
public class BrandGoodsRefundController {

    @Resource
    private BrandGoodsOrderRefundDOService brandGoodsOrderRefundDOService;


    @GetMapping("/getRefundReasonList")
    @Operation(summary = "获取所有BG退款原因选项 type 1-退货退款 2-仅退款")
    @PermitAll
    CommonResult<List<RefundReasonStatusVO>> getBgRefundReasonList(int type) {
        List<RefundReasonStatusVO>  refundReasonList= LogisticsRefundReasonEnum.getByType(type);
        return CommonResult.success(refundReasonList);
    }


    @PostMapping("/userCancelRefund")
    @Operation(summary = "用户取消退款申请")
    @PermitAll
    CommonResult<String> userCancelRefund(@RequestBody @Validated LogisticsOrderRefundApproveDTO userCancelRefundDTO) {
       String refundOrderCode = brandGoodsOrderRefundDOService.logisticsOrderRefundUserCancel(userCancelRefundDTO);
        return CommonResult.success(refundOrderCode);
    }

    @PostMapping("/submitLogisticsInfo")
    @Operation(summary = "用户提交物流信息")
    @PermitAll
    CommonResult<String> submitLogisticsInfo(@RequestBody @Validated OrderRefundLogisticsDTO orderRefundLogisticsDTO) {
        String refundOrderCode = brandGoodsOrderRefundDOService.submitLogisticsInfo(orderRefundLogisticsDTO);
        return CommonResult.success(refundOrderCode);
    }

    @GetMapping("/getRefundDetails")
    @Operation(summary = "实物商品退单详情")
    @Parameter(name = "orderItemCode", description = "订单行编码", required = true)
    @Parameter(name = "orderRefundCode", description = "订单退款编码", required = true)
    @PermitAll
    CommonResult<LogisticsRefundItemDetailVO> getBgRefundDetails(@RequestParam("orderItemCode") @NotBlank(message = "订单行编码不能为空") String orderItemCode
            , @RequestParam("orderRefundCode") @NotBlank(message = "订单退款编码") String orderRefundCode) {
        LogisticsRefundItemDetailVO logisticsRefundItemDetailVO = brandGoodsOrderRefundDOService.getLogisticsRefundItemDetail(orderItemCode,orderRefundCode);
        return CommonResult.success(logisticsRefundItemDetailVO);
    }

    @GetMapping("/getLogisticsCompanyList")
    @Operation(summary = "获取所有快递公司名称和code")
    @PermitAll
    CommonResult<List<LogisticsCompanyVO>> getLogisticsCompanyList() {
        List<LogisticsCompanyVO> refundReasonList= new ArrayList<>();
        for (LogisticsCompanyEnum reason : LogisticsCompanyEnum.values()) {
            refundReasonList.add(new LogisticsCompanyVO(reason.getCode(), reason.getName()));

        }
        return CommonResult.success(refundReasonList);
    }


    @PostMapping("/apply")
    @Operation(summary = "用户发起退货退款/仅退款申请")
    @PermitAll
    CommonResult<String> refund(@RequestBody @NotEmpty(message = "退款申请对象不能为空") List<BaseOrderRefundApplyDTO> baseOrderRefundApplyDTOList) {
        for(BaseOrderRefundApplyDTO baseOrderRefundApplyDTO:baseOrderRefundApplyDTOList){
            if(baseOrderRefundApplyDTO.getRefundOrderType() == null || RefundOrderTypeEnum.getByCode(baseOrderRefundApplyDTO.getRefundOrderType()) == null){
                throw exception(ErrorCodeConstants.ORDER_REFUND_TYPE_EMPTY);
            }
        }
        String orderRefundCode = brandGoodsOrderRefundDOService.logisticsOrderRefundApply(baseOrderRefundApplyDTOList, RefundOrderOperationTypeEnum.USER_INITIATED.getCode());
        return CommonResult.success(orderRefundCode);
    }
}

package com.jlr.ecp.order.dal.mysql.order.po;


import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.controller.app.order.po
 * @className: OrderItemRefundVcsPo
 * @author: gaoqig
 * @description: 根据OrderItem表查其他相关信息(Refund,VCS)的接收类
 * @date: 2025/3/8 14:31
 * @version: 1.0
 */
@Data
public class OrderItemRefundVcsPo {
    /**
     * 主订单表code
     */
    private String orderCode;
    /**
     * 订单项编码
     */
    private String orderItemCode;

    /**
     * 车辆VIN码
     */
    private String carVin;

    /**
     * 车系编码
     */
    private String seriesCode;

    /**
     * 车系名称
     */
    private String seriesName;

    /**
     * 订单item商品的类型，1普通商品 2组合商品
     */
    private Integer orderItemSpuType;
    /**
     * 服务开始日期
     */
    private LocalDateTime serviceBeginDate;

    /**
     * 服务结束日期
     */
    private LocalDateTime serviceEndDate;

    /**
     * 退款订单状态
     * VSC适用
     */
    private String vcsRefundStatus;

    /**
     * 退款状态
     * 非VCS 适用
     * todo 待添加
     */
    private Integer refundStatus;

    /**
     * 产品版本编码
     */
    private String productVersionCode;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品SKU编码
     */
    private String productSkuCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品图片URL
     */
    private String productImageUrl;

    /**
     * 产品属性
     */
    private String productAttribute;

    /**
     * 产品市场价
     */
    private Integer productMarketPrice;

    /**
     * 产品销售价
     */
    private Integer productSalePrice;

    /**
     * 产品数量
     */
    private Integer productQuantity;

    /***
     * @description 实付金额
     * @date 2025/3/8 18:02
    */

    private Integer costAmount;

    /**
     * 订单行的履约类型类型
     */
    private Integer itemFufilementType;

    /**
     * 售后状态
     */
    private Integer aftersalesStatus;
}

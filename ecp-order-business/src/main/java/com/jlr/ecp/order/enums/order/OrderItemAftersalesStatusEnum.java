package com.jlr.ecp.order.enums.order;


/**
 * 子订单售后状态
 */
/**
 * 订单项退款状态枚举
 */
public enum OrderItemAftersalesStatusEnum {


    NO_AFTERSALES(0, "未发生售后"),
    /**
     * 售后处理中
     */
    PROCESSING(1, "售后处理中"),

    /**
     * 售后已完成
     */
    COMPLETED(2, "售后关闭，已退款"),

    /**
     * 售后关闭
     */
    CLOSED(3, "售后关闭");

    private final Integer code;
    private final String name;

    OrderItemAftersalesStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据状态码获取枚举实例
     * @param code 状态编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static OrderItemAftersalesStatusEnum getByCode(Integer code) {
        for (OrderItemAftersalesStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 严格模式获取枚举（可选）
     * @throws IllegalArgumentException 当code无效时抛出异常
     */
    public static OrderItemAftersalesStatusEnum getByCodeStrict(Integer code) {
        OrderItemAftersalesStatusEnum status = getByCode(code);
        if (status == null) {
            throw new IllegalArgumentException("无效的退款状态码: " + code);
        }
        return status;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

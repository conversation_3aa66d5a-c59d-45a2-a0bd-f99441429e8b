package com.jlr.ecp.order.service.order;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusLogDOMapper;
import com.jlr.ecp.order.service.order.OrderStatusLogDOService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_order_status_log(t_order_status_log)】的数据库操作Service实现
* @createDate 2023-12-28 20:48:17
*/
@Service
@Slf4j
public class OrderStatusLogDOServiceImpl extends ServiceImpl<OrderStatusLogDOMapper, OrderStatusLogDO>
    implements OrderStatusLogDOService{

}





package com.jlr.ecp.order.controller.app.refund.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/09 11:07
 */
@Data
@Schema(description = "App端 - 物流单退款详情VO")
public class LogisticsRefundItemDetailVO extends BaseRefundItemDetailVO {
    /**
     * 物流单号
     */
    @Schema(description = "logistics_code")
    private String logisticsCode;

    /**
     * 物流公司code
     */
    @Schema(description = "logistics_company_code")
    private String logisticsCompanyCode;

    /**
     * 物流公司名称
     */
    @Schema(description = "logistics_company_name")
    private String logisticsCompanyName;

    /**
     * 退货审核备注
     */
    @Schema(description = "return_audit_remark")
    private String returnAuditRemark;

    /**
     * 退款审核备注
     */
    @Schema(description = "refund_audit_remark")
    private String refundAuditRemark;

    /**
     * 联系人
     */
    @Schema(description = "contact_name")
    private String contactName;

    /**
     * 电话
     */
    @Schema(description = "contact_phone")
    private String contactPhone;

    /**
     * 联系地址
     */
    @Schema(description = "contact_address")
    private String contactAddress;

    @Schema(description = "凭证图片地址")
    private List<String> attachmentUrls;

    @Schema(description = "收件人姓名")
    private String receiverName;

    @Schema(description = "收件人电话")
    private String receiverPhone;

    @Schema(description = "收件人地址")
    private String receiverAddress;

    @Schema(description = "发货时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime sendTime;
}

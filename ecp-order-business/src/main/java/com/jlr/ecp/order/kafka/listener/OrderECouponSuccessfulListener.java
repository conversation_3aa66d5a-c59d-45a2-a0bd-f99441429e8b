package com.jlr.ecp.order.kafka.listener;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.dal.dataobject.order.OrderCouponDetailDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderCouponDetailDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.enums.order.OrderCouponStatusEnum;
import com.jlr.ecp.order.enums.order.OrderItemCouponStatusEnum;
import com.jlr.ecp.order.enums.order.OrderTypeEnum;
import com.jlr.ecp.order.kafka.OrderECouponSuccessfulMessage;
import com.jlr.ecp.virtual.api.coupon.CouponApi;
import com.jlr.ecp.virtual.dto.coupon.CouponInfoDto;
import com.jlr.ecp.virtual.dto.coupon.command.CouponCreateCmd;
import com.jlr.ecp.virtual.dto.coupon.response.CouponCreateResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * 支付成功后券发放消息监听器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderECouponSuccessfulListener {

    @Resource
    private OrderCouponDetailDOMapper orderCouponDetailDOMapper;
    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;
    @Resource
    private OrderItemDOMapper orderItemDOMapper;
    @Resource
    private CouponApi couponApi;
    @Resource
    private TransactionTemplate transactionTemplate;
    
    @KafkaListener(topics = KafkaConstants.ORDER_ECOUPON_SUCCESSFUL_TOPIC, 
                  groupId = "order-ecoupon-successful-group", 
                  batch = "true",
                  properties = "max.poll.records:1")
    public void onMessage(String messageStr) {
        log.info("券发放消息消费开始, message:{}", messageStr);
        OrderECouponSuccessfulMessage message = JSON.parseObject(messageStr, OrderECouponSuccessfulMessage.class);

        try {
            // 设置租户上下文
            TenantContextHolder.setTenantId(message.getTenantId());

            LambdaQueryWrapper wrapper = Wrappers.lambdaQuery(OrderInfoDO.class)
                    .eq(OrderInfoDO::getOrderCode, message.getOrderCode());
            OrderInfoDO orderInfo = orderInfoDOMapper.selectOne(wrapper);
            if (!OrderTypeEnum.ELECTRONIC_COUPON.getCode().equals(orderInfo.getOrderType())) {
                return;
            }
            if (!OrderCouponStatusEnum.PENDING_ISSUANCE.getCode().equals(orderInfo.getCouponStatus())) {
                log.warn("订单不为“待发放”状态，orderCode={}", orderInfo.getOrderCode());
                return;
            }

            // 调用虚拟服务发放券
            CouponCreateCmd couponCreateCmd = buildCouponCreateCmd(message);
            CommonResult<List<CouponCreateResp>> result = couponApi.couponCreate(couponCreateCmd);
            if (!result.isSuccess()) {
                log.error("发放券失败, orderCode={}, result={}", message.getOrderCode(), JSON.toJSONString(result));
                throw new RuntimeException("发放券失败");
            }

            // 使用groupingBy替代原来的toMap，这样每个couponModelCode可以对应多个OrderItem
            Map<String, List<CouponCreateResp>> couponRespMap = result.getData().stream()
                    .collect(Collectors.groupingBy(CouponCreateResp::getCouponModelCode));

            Map<String, List<OrderECouponSuccessfulMessage.OrderItem>> orderItemsMap = message.getOrderItems().stream()
                    .collect(Collectors.groupingBy(OrderECouponSuccessfulMessage.OrderItem::getCouponModelCode));

            LocalDateTime current = LocalDateTime.now();

            List<OrderCouponDetailDO> orderCouponRecordDOList = new ArrayList<>();

            // 处理每种券类型
            for (Map.Entry<String, List<CouponCreateResp>> entry : couponRespMap.entrySet()) {
                String couponModelCode = entry.getKey();
                List<CouponCreateResp> coupons = entry.getValue();
                List<OrderECouponSuccessfulMessage.OrderItem> items = orderItemsMap.get(couponModelCode);
                
                if (items == null || items.isEmpty() || coupons == null || coupons.isEmpty()) {
                    continue;
                }
                
                // 用于追踪当前处理到哪一张券
                int couponIndex = 0;
                
                // 处理每个订单项
                for (OrderECouponSuccessfulMessage.OrderItem item : items) {
                    // 根据当前订单项的productQuantity创建相应数量的记录
                    for (int i = 0; i < item.getProductQuantity() && couponIndex < coupons.size(); i++, couponIndex++) {
                        CouponCreateResp coupon = coupons.get(couponIndex);
                        
                        OrderCouponDetailDO record = new OrderCouponDetailDO();
                        record.setTenantId(message.getTenantId().intValue());
                        record.setOrderCode(message.getOrderCode());
                        record.setOrderItemCode(item.getOrderItemCode());
                        record.setCouponModelCode(coupon.getCouponModelCode());
                        record.setCouponCode(coupon.getCouponCode());
                        record.setStatus(coupon.getCouponStatus());
                        record.setValidStartTime(coupon.getValidStartTime());
                        record.setValidEndTime(coupon.getValidEndTime());
                        record.setCreatedTime(current);
                        record.setUpdatedTime(current);
                        orderCouponRecordDOList.add(record);
                    }
                }
            }

            // 批量插入券发放结果
            transactionTemplate.executeWithoutResult((status) -> {
                OrderInfoDO updateOrder = new OrderInfoDO();
                updateOrder.setId(orderInfo.getId());
                updateOrder.setCouponStatus(OrderCouponStatusEnum.PENDING_VERIFICATION.getCode());
                updateOrder.setUpdatedTime(current);
                orderInfoDOMapper.updateById(updateOrder);
                orderCouponDetailDOMapper.insertBatch(orderCouponRecordDOList);

                OrderItemDO orderItem = new OrderItemDO();
                orderItem.setItemStatus(OrderItemCouponStatusEnum.PENDING_VERIFICATION.getCode());
                orderItem.setUpdatedTime(current);
                LambdaQueryWrapper<OrderItemDO> orderItemWrapper = Wrappers.lambdaQuery(OrderItemDO.class)
                        .eq(OrderItemDO::getOrderCode, orderInfo.getOrderCode());
                orderItemDOMapper.update(orderItem, orderItemWrapper);
            });
            log.info("券发放成功, orderCode:{}", message.getOrderCode());
        } catch (Exception e) {
            log.error("券发放消息处理异常, message:{}, error:{}", messageStr, e.getMessage(), e);
            throw new RuntimeException("发放券失败");
        } finally {
            TenantContextHolder.clear();
        }
    }
    
    /**
     * 构建券发放请求DTO
     */
    private CouponCreateCmd buildCouponCreateCmd(OrderECouponSuccessfulMessage message) {
        List<CouponInfoDto> couponInfoList = message.getOrderItems().stream().map(orderItem -> {
            CouponInfoDto couponInfoDto = new CouponInfoDto();
            couponInfoDto.setCouponModelCode(orderItem.getCouponModelCode());
            couponInfoDto.setDeliverNum(orderItem.getProductQuantity());
            couponInfoDto.setDeliverDesc(message.getOrderCode());
            couponInfoDto.setJlrId(message.getConsumerCode());
            return couponInfoDto;
        }).collect(Collectors.toList());

        CouponCreateCmd reqDTO = new CouponCreateCmd();
        reqDTO.setRequestId(message.getOrderCode());
        reqDTO.setCouponList(couponInfoList);
        return reqDTO;
    }

}
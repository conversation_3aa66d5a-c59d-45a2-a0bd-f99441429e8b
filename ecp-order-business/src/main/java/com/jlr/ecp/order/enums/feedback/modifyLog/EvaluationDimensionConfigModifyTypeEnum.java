package com.jlr.ecp.order.enums.feedback.modifyLog;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 评价维度配置-修改日志-修改模块 枚举
 *
 * 可废弃
 */
@AllArgsConstructor
@Getter
public enum EvaluationDimensionConfigModifyTypeEnum {

    ADD_DIMENSION(0, "添加评价维度"),
    EDIT_DIMENSION(1, "编辑评价维度"),
    EDIT_INPUT_CONFIG(2, "编辑评价输入框配置"),
    EDIT_QUESTIONNAIRE_DESCRIPTION(3, "编辑评价问卷说明"),
    EDIT_ENABLE_STATUS(4, "编辑启用状态");

    private final Integer code;
    private final String name;

    // 根据code获取对应的枚举实例
    public static EvaluationDimensionConfigModifyTypeEnum fromCode(Integer code) {
        for (EvaluationDimensionConfigModifyTypeEnum type : EvaluationDimensionConfigModifyTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null; // 或者可以抛出异常，如果认为找不到对应code是不可接受的情况
    }
}
package com.jlr.ecp.order.dal.dataobject.order;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * <AUTHOR>
 * @createDate 12/03/2025 17:59
 **/
@TableName(value = "t_order_discount_detail")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderDiscountDetailDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

    /**
     * 订单编号
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 订单明细编号
     */
    @TableField(value = "order_item_code")
    private String orderItemCode;

    /**
     * 折扣类型
     */
    @TableField(value = "discount_type")
    private Integer discountType;

    /**
     * 优惠券编码
     */
    @TableField(value = "coupon_code")
    private String couponCode;

    /**
     * 卡券模版code
     */
    @TableField(value = "coupon_model_code")
    private String couponModelCode;

    /**
     * 卡券模版名称
     */
    @TableField(value = "coupon_model_name")
    private String couponModelName;

    /**
     * 卡券模板类型：1-兑换券 2-代金券 3-折扣券 4-满减券
     */
    @TableField(value = "coupon_model_classify")
    private Integer couponModelClassify;

    /**
     * 消耗积分
     */
    @TableField(value = "cost_points")
    private Integer costPoints;

    /**
     * 折扣金额
     */
    @TableField(value = "discount_amount")
    private Integer discountAmount;
}

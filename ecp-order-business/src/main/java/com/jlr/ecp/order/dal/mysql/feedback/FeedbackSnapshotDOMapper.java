package com.jlr.ecp.order.dal.mysql.feedback;


import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackSnapshotDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 评价设置快照表Mapper
 */
@Mapper
public interface FeedbackSnapshotDOMapper extends BaseMapperX<FeedbackSnapshotDO> {

    default FeedbackSnapshotDO selectOneBySnapshotCode(String snapshotCode) {

        return selectOne(new LambdaQueryWrapperX<FeedbackSnapshotDO>()
                .eq(FeedbackSnapshotDO::getSnapshotCode,snapshotCode)
                .eq(BaseDO::getIsDeleted,false)
                .orderByDesc(FeedbackSnapshotDO::getId)
                .last(Constants.LIMIT_ONE));
    }

    default  List<FeedbackSnapshotDO> getSnapshotCodeByFeedbackCode(String feedbackCode){
        return selectList((new LambdaQueryWrapperX<FeedbackSnapshotDO>()
                .eq(FeedbackSnapshotDO::getFeedbackCode, feedbackCode)
                .eq(FeedbackSnapshotDO::getIsDeleted, false)
                .orderByDesc(FeedbackSnapshotDO::getId)));
    }


    default  List<FeedbackSnapshotDO> getSnapshotCodeByFeedbackCode(List<String> feedbackCodes){
        return selectList((new LambdaQueryWrapperX<FeedbackSnapshotDO>()
                .inIfPresent(FeedbackSnapshotDO::getFeedbackCode, feedbackCodes)
                .eq(FeedbackSnapshotDO::getIsDeleted, false)
                .orderByDesc(FeedbackSnapshotDO::getId)));
    }
}

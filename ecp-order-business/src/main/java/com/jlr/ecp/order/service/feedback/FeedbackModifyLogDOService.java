package com.jlr.ecp.order.service.feedback;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedbackPageReqDTO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedbackModifyDetailVO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedbackModifyLogVO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackModifyLogDO;

/**
 * 评价配置操作记录表Service
 */
public interface FeedbackModifyLogDOService extends IService<FeedbackModifyLogDO> {
    PageResult<FeedbackModifyLogVO> selectModifyPage(FeedbackPageReqDTO dto);

    FeedbackModifyDetailVO getLogDetail(Long id);
}

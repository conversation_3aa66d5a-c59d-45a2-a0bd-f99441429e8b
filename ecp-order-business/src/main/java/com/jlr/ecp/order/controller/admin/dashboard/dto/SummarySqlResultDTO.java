package com.jlr.ecp.order.controller.admin.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "仪表盘 - SQL查询结果")
@Data
@ToString(callSuper = true)
public class SummarySqlResultDTO {

    @Schema(description = "订单渠道")
    private String code;

    @Schema(description = "X轴时间")
    private String label;

    @Schema(description = "数量")
    private Long quantity;

    @Schema(description = "金额")
    private Long amount;
}

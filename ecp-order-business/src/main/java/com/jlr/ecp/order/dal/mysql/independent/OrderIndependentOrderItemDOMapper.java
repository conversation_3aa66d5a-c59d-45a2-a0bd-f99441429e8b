package com.jlr.ecp.order.dal.mysql.independent;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentOrderItemDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR> Hongyi
* @description 针对表【t_order_independent_order_item】的数据库操作Mapper
* @createDate 2025-03-13 01:08:44
* @Entity com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentOrderItemDO
*/
@Mapper
public interface OrderIndependentOrderItemDOMapper extends BaseMapper<OrderIndependentOrderItemDO> {

    /**
     * 查询订单下的所有订单行的分账明细
     */
    default Map<String, List<OrderIndependentOrderItemDO>> mapByOrderItemCode(List<String> orderCodes) {
        LambdaQueryWrapper<OrderIndependentOrderItemDO> wrapper = Wrappers.lambdaQuery(OrderIndependentOrderItemDO.class)
                .in(OrderIndependentOrderItemDO::getOrderCode, orderCodes)
                .eq(OrderIndependentOrderItemDO::getIsDeleted, Boolean.FALSE);
        List<OrderIndependentOrderItemDO> orderItems = selectList(wrapper);
        Map<String, List<OrderIndependentOrderItemDO>> orderItemMap = orderItems.stream()
                .collect(Collectors.groupingBy(OrderIndependentOrderItemDO::getOrderItemCode));
        return orderItemMap;
    }

    default List<OrderIndependentOrderItemDO> listByIndependentCode(String independentCode) {
        LambdaQueryWrapper<OrderIndependentOrderItemDO> wrapper = Wrappers.lambdaQuery(OrderIndependentOrderItemDO.class)
                .in(OrderIndependentOrderItemDO::getIndependentCode, independentCode)
                .eq(OrderIndependentOrderItemDO::getIsDeleted, Boolean.FALSE);
        return selectList(wrapper);
    }

}
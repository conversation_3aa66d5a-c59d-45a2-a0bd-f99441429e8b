package com.jlr.ecp.order.service.refund;

import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.refund.dto.OrderRefundForGyyPageReqDTO;
import com.jlr.ecp.order.api.refund.dto.OrderRefundForGyyPageRespDTO;

/**
* <AUTHOR> Hongyi
* @description 提供给管易云调用的接口
* @createDate 2025-04-09 02:00:00
*/
public interface OrderRefundForGyyService {

    /**
     * 查询指定时间段的售后单或查询指定单号的售后单（管易云侧叫售后单下载）
     */
    PageResult<OrderRefundForGyyPageRespDTO> pageOrderRefund(OrderRefundForGyyPageReqDTO req);

}
package com.jlr.ecp.order.controller.admin.dashboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Schema(description = "dashboard-表格响应参数VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TableRespVo {

  @Schema(description = "表头")
  private List<HeaderItem> headers;

  @Schema(description = "表数据")
  private List<Map<String, String>> tableData;

  @Schema(description = "列响应参数VO")
  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class HeaderItem {
    @Schema(description = "prop")
    private String prop;

    @Schema(description = "label")
    private String label;

    @Schema(description = "子表头")
    private List<HeaderItem> subHeaders;
  }
}

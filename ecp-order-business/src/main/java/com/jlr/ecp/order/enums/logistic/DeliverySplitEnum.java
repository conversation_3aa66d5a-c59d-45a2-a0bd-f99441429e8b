package com.jlr.ecp.order.enums.logistic;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.enums.logsitc
 * @className: DeliverySplitEnum
 * @author: gaoqig
 * @description: 发货拆单类型枚举
 * @date: 2025/4/3 15:41
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum DeliverySplitEnum {

    WHOLE(1, "整单发货"),
    SPLIT(2, "拆单发货");

    /**
     * 类型值
     */
    private final Integer code;

    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code 类型值
     * @return 发货类型枚举
     */
    public static DeliverySplitEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DeliverySplitEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
package com.jlr.ecp.order.service.dashboard.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.EnumUtil;
import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.controller.admin.dashboard.dto.*;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.SalesSummaryRespVo;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.dashboard.KpiEnumForSales;
import com.jlr.ecp.order.enums.dashboard.TypeFilterEnum;
import com.jlr.ecp.order.service.dashboard.SalesOverviewService;
import com.jlr.ecp.order.util.dashboard.DashboardUtils;
import com.jlr.ecp.order.util.money.MoneyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.jlr.ecp.order.util.dashboard.DashboardBuilder.*;
import static com.jlr.ecp.order.util.money.MoneyUtil.convertFromCentsToYuanBigDecimal;

@Service
@Slf4j
public class SalesOverviewServiceImpl implements SalesOverviewService {

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;

    @Resource
    private OrderRefundItemDOMapper orderRefundItemDOMapper;

    @Resource
    private RedisService redisService;

    @Override
    public SalesSummaryRespVo getSalesSummary(SalesSummaryReqDTO reqDTO) {
        log.info("==================== 销售总览统计开始 ====================");
        log.info("销售总览统计请求参数: {}", JSON.toJSONString(reqDTO));
        String startDate = LocalDate.parse(reqDTO.getStartTime()).toString();
        String endDate = LocalDate.parse(reqDTO.getEndTime()).plusDays(1).toString();
        SalesSummaryRespVo vo = new SalesSummaryRespVo();
        StopWatch stopWatch = new StopWatch();

        // 订单数量统计
        //2.订单总数量；
        stopWatch.start();
        ValetDashboardTempDTO temp = orderInfoDOMapper.countTotalOrderCount(startDate, endDate,reqDTO.getBusinessCode());
        stopWatch.stop();
        log.info("orderInfoDOMapper.countTotalOrderCount 查询耗时，{}s", stopWatch.getTotalTimeSeconds());
        if (Objects.isNull(temp)) {
            log.info("case1-查询订单统计信息为空");
            return emptyStatics(vo, reqDTO);
        }
        vo.setTotalOrderCount(temp.getTotalOrderCount());

        // 支付订单统计
        //4.已支付状态下的订单总数量；
        stopWatch.start();
        ValetDashboardTempDTO temp2 = orderInfoDOMapper.countTotalOrderByPaymentStatus(startDate, endDate,reqDTO.getBusinessCode());
        stopWatch.stop();
        log.info("orderInfoDOMapper.countTotalOrderByPaymentStatus 查询耗时，{}s", stopWatch.getTotalTimeSeconds());
        if (Objects.isNull(temp2)) {
            log.info("case2-查询订单统计信息为空");
            return emptyStatics(vo, reqDTO);
        }
        vo.setTotalPaidCount(temp2.getTotalPaidCount());

        // 交易额统计
        //6.已支付状态下 的 所有订单交易额；
        BigDecimal totalGmv = convertFromCentsToYuanBigDecimal(temp2.getTotalGmv());
        vo.setTotalGmv(totalGmv);

        // 退款统计 & 总收入统计
        // 所有订单总收入 = 6- 8.已支付状态下 订单 的总退款额;
        stopWatch.start();
        ValetDashboardTempDTO temp3 = orderRefundItemDOMapper.getTotalRefundAmounts( startDate, endDate,reqDTO.getBusinessCode()); // 查询 所有订单的总退款额
        stopWatch.stop();
        log.info("orderRefundItemDOMapper.getTotalRefundAmounts 查询耗时，{}s", stopWatch.getTotalTimeSeconds());
        BigDecimal totalRefundAmount = convertFromCentsToYuanBigDecimal(temp3.getTotalRefundAmount()); // 8.已支付状态下 订单 的总退款额
        vo.setTotalIncome(totalGmv.subtract(totalRefundAmount));

        // 客单价统计
        //订单成交客单价=6 / 4;
        vo.setTotalCustomerPrice(calculateCustomerPrice(totalGmv, temp2.getTotalPaidCount()));

        // 退款统计
        //10.已支付状态下 所有订单的退款统计；
        vo.setTotalRefundAmount(MoneyUtil.convertFromCentsToYuanBigDecimal(temp3.getTotalRefundAmount()));
//        stopWatch.start();
//        ValetDashboardTempDTO temp4 = orderInfoDOMapper.countTotalOrderItemQuantity(startDate, endDate,reqDTO.getBusinessCode());
//        stopWatch.stop();
//        log.info("orderInfoDOMapper.countTotalOrderItemQuantity 查询耗时，{}s", stopWatch.getTotalTimeSeconds());
//        vo.setTotalProductCount(temp4.getTotalProductCount());

        log.info("==================== 销售总览统计完成 ====================");
        return vo;
    }

    @Override
    public CommonResult<ProductSalesTrendRespVo> querySalesSummaryCharts(SummaryQueryReqDTO dto) {
        log.info("querySalesSummaryCharts start, SummaryQueryReqDTO:{}",JSON.toJSONString(dto));

        // 1.提取、校验 开始日期、结束日期
        LocalDate startDate = LocalDate.parse(dto.getStartTime());
        LocalDate endDate = LocalDate.parse(dto.getEndTime());
        CommonResult<Void> checkedResult = checkRequestParam(startDate, endDate);
        if (checkedResult != null) {
            return CommonResult.error(checkedResult);
        }

        // 2.设置SqlQueryDTO查询参数
        SqlQueryDTO sqlQueryDTO = new SqlQueryDTO();
        BeanUtil.copyProperties(dto, sqlQueryDTO);
        sqlQueryDTO.setStartDate(startDate);
        sqlQueryDTO.setEndDate(endDate.plusDays(1)); // 查询时结束日期需+1

        // 图表X轴
        // 表格标题
        List<String> tableHeaders = new ArrayList<>();
        String dateFormat = DashboardUtils.calculateTimeInterval(startDate, endDate, tableHeaders);  //根据起始日期算X轴；返回dateFormat（下单时间按dateFormat分组）
        sqlQueryDTO.setDateFormat(dateFormat);
        List<String> xAxis = new ArrayList<>(tableHeaders);


        List<SummarySqlResultDTO> resultDTOS;
        List<SummarySqlResultDTO> refundResultDTOS;
        if (TypeFilterEnum.CHANNEL.getCode().equals(dto.getType())) {
            switch (EnumUtil.getBy(KpiEnumForSales::getCode, dto.getKpi())) {
                case TOTAL_ORDER_NUMBER:
                case DEAL_ORDER_NUMBER:
                    resultDTOS = orderInfoDOMapper.getSalesSummaryGroupChannel(sqlQueryDTO);
                    return buildLineChartAndTableDataByCountForChannel(tableHeaders, resultDTOS, xAxis);
                case TOTAL_ORDER_AMOUNT:
                    resultDTOS = orderInfoDOMapper.getSalesSummaryGroupChannel(sqlQueryDTO);
                    return buildLineChartAndTableDataByAmountForChannel(tableHeaders, resultDTOS, xAxis);
//                case UNIT_PRICE_PER_CUSTOMER:
//                    resultDTOS = orderInfoDOMapper.getSalesSummaryGroupChannel(sqlQueryDTO);
//                    return buildLineChartAndTableDataByPerForChannel(tableHeaders, resultDTOS, xAxis);
                case INCOME:
                    resultDTOS = orderInfoDOMapper.getSalesSummaryGroupChannel(sqlQueryDTO);
                    refundResultDTOS = orderInfoDOMapper.getSalesSummaryRefund(sqlQueryDTO);
                    return buildLineChartAndTableDataByIncomeForChannel(tableHeaders, resultDTOS, xAxis, refundResultDTOS);
                case REFUND:
                    resultDTOS = orderInfoDOMapper.getSalesSummaryRefund(sqlQueryDTO);
                    return buildLineChartAndTableDataByAmountForChannel(tableHeaders, resultDTOS, xAxis);
                default:
                    return CommonResult.error(ErrorCodeConstants.TYPE_FILTER_ERROR);
            }
        } else if (TypeFilterEnum.VEHICLE_MODEL.getCode().equals(dto.getType())) {
            switch (EnumUtil.getBy(KpiEnumForSales::getCode, dto.getKpi())) {
                case TOTAL_ORDER_NUMBER:
                case DEAL_ORDER_NUMBER:
                    resultDTOS = orderInfoDOMapper.getSalesSummaryGroupSeriesCode(sqlQueryDTO);
                    return buildLineChartAndTableDataByCountForSeriesCode(tableHeaders, resultDTOS, xAxis);
                case TOTAL_ORDER_AMOUNT:
                    resultDTOS = orderInfoDOMapper.getSalesSummaryGroupSeriesCode(sqlQueryDTO);
                    return buildLineChartAndTableDataByAmountForSeriesCode(tableHeaders, resultDTOS, xAxis);
//                case UNIT_PRICE_PER_CUSTOMER:
//                    resultDTOS = orderInfoDOMapper.getSalesSummaryGroupSeriesCode(sqlQueryDTO);
//                    return buildLineChartAndTableDataByPerForSeriesCode(tableHeaders, resultDTOS, xAxis);
                case INCOME:
                    resultDTOS = orderInfoDOMapper.getSalesSummaryGroupSeriesCode(sqlQueryDTO);
                    refundResultDTOS = orderInfoDOMapper.getSalesSummaryRefundGroupSeriesCode(sqlQueryDTO);
                    return buildLineChartAndTableDataByIncomeForSeriesCode(tableHeaders, resultDTOS, xAxis, refundResultDTOS);
                case REFUND:
                    resultDTOS = orderInfoDOMapper.getSalesSummaryRefundGroupSeriesCode(sqlQueryDTO);
                    return buildLineChartAndTableDataByAmountForSeriesCode(tableHeaders, resultDTOS, xAxis);
                default:
                    return CommonResult.error(ErrorCodeConstants.TYPE_FILTER_ERROR);
            }
        } else if (TypeFilterEnum.PRODUCT.getCode().equals(dto.getType())) {
            switch (EnumUtil.getBy(KpiEnumForSales::getCode, dto.getKpi())) {
                case TOTAL_ORDER_NUMBER:
                case DEAL_ORDER_NUMBER:
                    resultDTOS = orderInfoDOMapper.getSalesSummaryGroupProductName(sqlQueryDTO);
                    return buildLineChartAndTableDataByCountForProductName(tableHeaders, resultDTOS, xAxis);
                case TOTAL_ORDER_AMOUNT:
                    resultDTOS = orderInfoDOMapper.getSalesSummaryGroupProductName(sqlQueryDTO);
                    return buildLineChartAndTableDataByAmountForProductName(tableHeaders, resultDTOS, xAxis);
                case INCOME:
                    resultDTOS = orderInfoDOMapper.getSalesSummaryGroupProductName(sqlQueryDTO);
                    refundResultDTOS = orderInfoDOMapper.getSalesSummaryRefundGroupProductName(sqlQueryDTO);
                    return buildLineChartAndTableDataByIncomeForProductName(tableHeaders, resultDTOS, xAxis, refundResultDTOS);
                case REFUND:
                    resultDTOS = orderInfoDOMapper.getSalesSummaryRefundGroupProductName(sqlQueryDTO);
                    return buildLineChartAndTableDataByAmountForProductName(tableHeaders, resultDTOS, xAxis);
                default:
                    return CommonResult.error(ErrorCodeConstants.TYPE_FILTER_ERROR);
            }
        }
        return CommonResult.error(ErrorCodeConstants.TYPE_FILTER_ERROR);
    }

    @Override
    public CommonResult<List<KpiQueryDTO>> salesKpiList() {
        List<KpiQueryDTO> collect = Arrays.stream(KpiEnumForSales.values())
                .map(KpiQueryDTO::new)
                .collect(Collectors.toList());
        return CommonResult.success(collect);
    }

    /**
     * 检查请求参数
     *

     * @param startDate 查询的开始日期
     * @param endDate 查询的结束日期
     * @return 如果参数校验不通过，则返回错误的CommonResult对象，否则返回null
     */
    private CommonResult<Void> checkRequestParam(LocalDate startDate, LocalDate endDate) {
        // 开始日期不能大于结束日期
        if (startDate.isAfter(endDate)) {
            return CommonResult.error(ErrorCodeConstants.START_TIME_GREATER_THAN_END_TIME);
        }
        return null;
    }

    /**
     * 统计为空时，返回空数据
     */
    private SalesSummaryRespVo emptyStatics(SalesSummaryRespVo vo, SalesSummaryReqDTO reqDTO) {
        vo.setStartTime(reqDTO.getStartTime());
        vo.setEndTime(reqDTO.getEndTime());
        return vo;
    }

    /**
     * 计算客单价
     */
    private BigDecimal calculateCustomerPrice(BigDecimal gmv, BigDecimal count) {
        if (count == null || count.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return gmv.divide(count, 2, RoundingMode.HALF_UP);
    }



}
package com.jlr.ecp.order.service.order;

import com.jlr.ecp.order.api.order.vo.OrderCancelVO;
import com.jlr.ecp.order.enums.order.OrderCloseReasonEnum;
import com.jlr.ecp.order.kafka.message.CancelOrderMessage;

import java.util.List;

public interface CancelOrderService {

    /**
     * 小程序订单取消
     * VCS传入的是子单号
     * BG、LRE在拆单场景下传入的是父单号
     * @param orderCode 要关单的订单号
     * @param closeReason 关单原因
     */
    OrderCancelVO orderCancel(String orderCode, OrderCloseReasonEnum closeReason);

    void processMessage(List<CancelOrderMessage> sortCancelMessages);

}

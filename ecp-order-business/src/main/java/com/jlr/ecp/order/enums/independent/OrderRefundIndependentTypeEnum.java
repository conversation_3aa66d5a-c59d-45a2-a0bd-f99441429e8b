package com.jlr.ecp.order.enums.independent;

import lombok.Getter;

/**
 * 退款订单分账标识
 */
@Getter
public enum OrderRefundIndependentTypeEnum {

    NO_INDEPENDENT_REFUND(0, "未分账退款"),
    INDEPENDENT_REFUND(1, "分账退款");

    /**
     * 类型值
     */
    private final int code;

    /**
     * 类型名称
     */
    private final String name;

    OrderRefundIndependentTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

}

package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

/**
 *  订单类型
 *  0：聚合父订单，1：VCS，2：Brand_goods，3：Charging
 * */
@AllArgsConstructor
@Getter
public enum OrderTypeEnum {
    PARENT(0, "聚合父订单", ""),
    VCS(1, "PIVI-Remote", "InControl Remote Service"),
    PIVI(2, "PIVI-Online Pack", "InControl Online Service"),
    BRAND_GOOD(3, "实物商品子订单", ""),
    BUNDLED_GOODS(4, "组合商品", "InControl Remote + Online Service"),
    /**
     * 电子商品
     */
    ELECTRONIC_COUPON(5, "电子兑换券","Electronic exchange coupons");
    private final Integer code;

    private final String desc;

    private final String dashboardDisplay;

    public static List<OrderTypeEnum> getDashboardDisplayList() {
        return List.of(VCS, PIVI, BUNDLED_GOODS);
    }
}
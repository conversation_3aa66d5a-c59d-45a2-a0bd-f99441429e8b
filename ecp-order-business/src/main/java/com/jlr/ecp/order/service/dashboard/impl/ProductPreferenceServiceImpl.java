package com.jlr.ecp.order.service.dashboard.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.EnumUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.admin.dashboard.dto.KpiQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.QueryReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO;
import com.jlr.ecp.order.controller.admin.dashboard.vo.PieChartRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesProportionByAttributesRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.TableRespVo;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.dashboard.KpiEnum;
import com.jlr.ecp.order.enums.dashboard.OrderChannelEnum;
import com.jlr.ecp.order.enums.dashboard.ServiceTimeEnum;
import com.jlr.ecp.order.enums.dashboard.TypeFilterEnum;
import com.jlr.ecp.order.enums.order.OrderTypeEnum;
import com.jlr.ecp.order.service.dashboard.ProductPreferenceService;
import com.jlr.ecp.order.util.dashboard.DashboardUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.jlr.ecp.order.util.dashboard.DashboardBuilder.*;

/**
 * 商品偏好服务
 *
 */
@Service
@Slf4j
public class ProductPreferenceServiceImpl implements ProductPreferenceService {

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;

    @Resource
    private RedisService redisService;

    private static final String FIRST_COLUMN = "firstColumn";
    private static final String FIRST_SUB_COLUMN = "firstSubColumn";

    @Override
    public CommonResult<List<String>> vehicleModelList() {
        Map<String, String> map = redisService.getCacheMap(Constants.REDIS_KEY.SERIES_CACHE_KEY);
        if (map != null && !map.isEmpty()) {
            return CommonResult.success(new ArrayList<>(map.keySet()));
        }
        return CommonResult.success(Collections.emptyList());
    }

    @Override
    public CommonResult<List<KpiQueryDTO>> salesTrendKpiList() {
        List<KpiQueryDTO> collect = Arrays.stream(KpiEnum.values())
                .map(KpiQueryDTO::new)
                .collect(Collectors.toList());
        return CommonResult.success(collect);
    }

    @Override
    public CommonResult<List<KpiQueryDTO>> salesProportionByAttributesKpiList() {
        List<KpiQueryDTO> collect = Arrays.stream(KpiEnum.values())
                .filter(kpiEnum -> !kpiEnum.getCode().equals(KpiEnum.UNIT_PRICE_PER_CUSTOMER.getCode()))
                .map(KpiQueryDTO::new)
                .collect(Collectors.toList());
        return CommonResult.success(collect);
    }

    @Override
    public CommonResult<ProductSalesTrendRespVo> querySalesTrend(QueryReqDTO dto) {
        if (Objects.isNull(dto.getKpi())) {
            return CommonResult.success(null);
        }
        LocalDate startDate = LocalDate.parse(dto.getStartTime());
        LocalDate endDate = LocalDate.parse(dto.getEndTime());
        CommonResult<Void> checkedResult = checkRequestParam(startDate, endDate);
        if (checkedResult != null) {
            return CommonResult.error(checkedResult);
        }

        // 设置查询参数
        SqlQueryDTO sqlQueryDTO = new SqlQueryDTO();
        BeanUtil.copyProperties(dto, sqlQueryDTO);
        sqlQueryDTO.setStartDate(startDate);
        // 查询时结束日期需+1
        sqlQueryDTO.setEndDate(endDate.plusDays(1));

        // 表格标题
        List<String> tableHeaders = new ArrayList<>();
        String dateFormat = DashboardUtils.calculateTimeInterval(startDate, endDate, tableHeaders);
        sqlQueryDTO.setDateFormat(dateFormat);
        // 图表X轴
        List<String> xAxis = new ArrayList<>(tableHeaders);
        List<SqlResultDTO> resultDTOS;
        List<SqlResultDTO> refundResultDTOS;
        if (TypeFilterEnum.CHANNEL.getCode().equals(dto.getType())) {
            switch (EnumUtil.getBy(KpiEnum::getCode, dto.getKpi())) {
                case TOTAL_ORDER_NUMBER:
                case DEAL_ORDER_NUMBER:
                    resultDTOS = orderInfoDOMapper.getSalesTrendByChannel(sqlQueryDTO);
                    return buildLineChartAndTableDataByCount(tableHeaders, resultDTOS, xAxis,false);
                case TOTAL_ORDER_AMOUNT:
                    resultDTOS = orderInfoDOMapper.getSalesTrendByChannel(sqlQueryDTO);
                    return buildLineChartAndTableDataByAmount(tableHeaders, resultDTOS, xAxis,false);
                case UNIT_PRICE_PER_CUSTOMER:
                    resultDTOS = orderInfoDOMapper.getSalesTrendByChannel(sqlQueryDTO);
                    return buildLineChartAndTableDataByPer(tableHeaders, resultDTOS, xAxis,false);
                case INCOME:
                    resultDTOS = orderInfoDOMapper.getSalesTrendByChannel(sqlQueryDTO);
                    refundResultDTOS = orderInfoDOMapper.getSalesTrendRefundByChannel(sqlQueryDTO);
                    return buildLineChartAndTableDataByIncome(tableHeaders, resultDTOS, xAxis, refundResultDTOS,false);
                case TOTAL_GOODS_NUMBER:
                    resultDTOS = orderInfoDOMapper.getSalesTrendProductCountByChannel(sqlQueryDTO);
                    return buildLineChartAndTableDataByCount(tableHeaders, resultDTOS, xAxis,false);
                default:
                    return CommonResult.error(ErrorCodeConstants.TYPE_FILTER_ERROR);
            }
        } else if (TypeFilterEnum.VEHICLE_MODEL.getCode().equals(dto.getType())) {
            switch (EnumUtil.getBy(KpiEnum::getCode, dto.getKpi())) {
                case TOTAL_ORDER_NUMBER:
                case DEAL_ORDER_NUMBER:
                    resultDTOS = orderInfoDOMapper.getSalesTrendBySeriesCode(sqlQueryDTO);
                    return buildLineChartAndTableDataByCount(tableHeaders, resultDTOS, xAxis,false);
                case TOTAL_ORDER_AMOUNT:
                    resultDTOS = orderInfoDOMapper.getSalesTrendBySeriesCode(sqlQueryDTO);
                    return buildLineChartAndTableDataByAmount(tableHeaders, resultDTOS, xAxis,false);
                case UNIT_PRICE_PER_CUSTOMER:
                    resultDTOS = orderInfoDOMapper.getSalesTrendBySeriesCode(sqlQueryDTO);
                    return buildLineChartAndTableDataByPer(tableHeaders, resultDTOS, xAxis,false);
                case INCOME:
                    resultDTOS = orderInfoDOMapper.getSalesTrendBySeriesCode(sqlQueryDTO);
                    refundResultDTOS = orderInfoDOMapper.getSalesTrendRefundBySeriesCode(sqlQueryDTO);
                    return buildLineChartAndTableDataByIncome(tableHeaders, resultDTOS, xAxis, refundResultDTOS,false);
                case TOTAL_GOODS_NUMBER:
                    resultDTOS = orderInfoDOMapper.getSalesTrendProductCountBySeriesCode(sqlQueryDTO);
                    return buildLineChartAndTableDataByCount(tableHeaders, resultDTOS, xAxis,false);
                default:
                    return CommonResult.error(ErrorCodeConstants.TYPE_FILTER_ERROR);
            }
        }
        return CommonResult.error(ErrorCodeConstants.TYPE_FILTER_ERROR);
    }

    /**
     * 检查请求参数
     *
     * @param startDate 查询的开始日期
     * @param endDate 查询的结束日期
     * @return 如果参数校验不通过，则返回错误的CommonResult对象，否则返回null
     */
    private static CommonResult<Void> checkRequestParam(LocalDate startDate, LocalDate endDate) {
        // 开始日期不能大于结束日期
        if (startDate.isAfter(endDate)) {
            return CommonResult.error(ErrorCodeConstants.START_TIME_GREATER_THAN_END_TIME);
        }
        return null;
    }

    @Override
    public CommonResult<ProductSalesProportionByAttributesRespVo> querySalesProportionByAttributes(QueryReqDTO dto) {
        if (Objects.isNull(dto.getKpi())) {
            return CommonResult.success(null);
        }
        LocalDate startDate = LocalDate.parse(dto.getStartTime());
        LocalDate endDate = LocalDate.parse(dto.getEndTime());
        CommonResult<Void> checkedResult = checkRequestParam(startDate, endDate);
        if (checkedResult != null) {
            return CommonResult.error(checkedResult);
        }

        // 设置查询参数
        SqlQueryDTO sqlQueryDTO = new SqlQueryDTO();
        BeanUtil.copyProperties(dto, sqlQueryDTO);
        sqlQueryDTO.setStartDate(startDate);
        // 查询时结束日期需+1
        sqlQueryDTO.setEndDate(endDate.plusDays(1));


        // 表格标题
        List<String> tableHeaders = new ArrayList<>();
        String dateFormat = DashboardUtils.calculateTimeInterval(startDate, endDate,  tableHeaders);
        sqlQueryDTO.setDateFormat(dateFormat);
        CommonResult<ProductSalesProportionByAttributesRespVo> result= CommonResult.error(ErrorCodeConstants.TYPE_FILTER_ERROR);
        List<SqlResultDTO> resultDTOS;
        List<SqlResultDTO> refundResultDTOS;
        if (TypeFilterEnum.CHANNEL.getCode().equals(dto.getType())) {
            switch (EnumUtil.getBy(KpiEnum::getCode, dto.getKpi())) {
                case TOTAL_ORDER_NUMBER:
                case DEAL_ORDER_NUMBER:
                    resultDTOS = orderInfoDOMapper.getSalesProportionByChannel(sqlQueryDTO);
                    result = buildPieChartAndTableDataByCount(resultDTOS, OrderChannelEnum.getDescriptionByCode(dto.getOrderChannel()));
                    break;
                case TOTAL_ORDER_AMOUNT:
                    resultDTOS = orderInfoDOMapper.getSalesProportionByChannel(sqlQueryDTO);
                    result = buildPieChartAndTableDataByAmount(resultDTOS, OrderChannelEnum.getDescriptionByCode(dto.getOrderChannel()));
                    break;
                case INCOME:
                    resultDTOS = orderInfoDOMapper.getSalesProportionByChannel(sqlQueryDTO);
                    refundResultDTOS = orderInfoDOMapper.getSalesProportionRefundByChannel(sqlQueryDTO);
                    result =  buildPieChartAndTableDataByIncome(resultDTOS, OrderChannelEnum.getDescriptionByCode(dto.getOrderChannel()), refundResultDTOS);
                    break;
                case TOTAL_GOODS_NUMBER:
                    resultDTOS = orderInfoDOMapper.getSalesProportionProductCountByChannel(sqlQueryDTO);
                    result =  buildPieChartAndTableDataByCount(resultDTOS, OrderChannelEnum.getDescriptionByCode(dto.getOrderChannel()));
                    break;
                default:
                    result =  CommonResult.error(ErrorCodeConstants.TYPE_FILTER_ERROR);
            }
        } else if (TypeFilterEnum.VEHICLE_MODEL.getCode().equals(dto.getType())) {
            switch (EnumUtil.getBy(KpiEnum::getCode, dto.getKpi())) {
                case TOTAL_ORDER_NUMBER:
                case DEAL_ORDER_NUMBER:
                    resultDTOS = orderInfoDOMapper.getSalesProportionBySeriesCode(sqlQueryDTO);
                    result =  buildPieChartAndTableDataByCount(resultDTOS, sqlQueryDTO.getVehicleModel());
                    break;
                case TOTAL_ORDER_AMOUNT:
                    resultDTOS = orderInfoDOMapper.getSalesProportionBySeriesCode(sqlQueryDTO);
                    result =  buildPieChartAndTableDataByAmount(resultDTOS, sqlQueryDTO.getVehicleModel());
                    break;
                case INCOME:
                    resultDTOS = orderInfoDOMapper.getSalesProportionBySeriesCode(sqlQueryDTO);
                    refundResultDTOS = orderInfoDOMapper.getSalesProportionRefundBySeriesCode(sqlQueryDTO);
                    result =  buildPieChartAndTableDataByIncome(resultDTOS, sqlQueryDTO.getVehicleModel(), refundResultDTOS);
                    break;
                case TOTAL_GOODS_NUMBER:
                    resultDTOS = orderInfoDOMapper.getSalesProportionProductCountBySeriesCode(sqlQueryDTO);
                    result =  buildPieChartAndTableDataByCount(resultDTOS, sqlQueryDTO.getVehicleModel());
                    break;
                default:
                    result =  CommonResult.error(ErrorCodeConstants.TYPE_FILTER_ERROR);
            }
        }
        //处理result结果pie>10组合数据
        handleResultOthersDisplay(result);

        return result;
    }
    /**
     * 处理result结果pie>10组合数据
     * @param result CommonResult<ProductSalesProportionByAttributesRespVo>
     */
    private void handleResultOthersDisplay(CommonResult<ProductSalesProportionByAttributesRespVo> result) {
        //处理result结果pie>10组合数据
        if (result.isSuccess()) {
            List<PieChartRespVo> pieChart = result.getData().getPieChart();
            if (CollectionUtils.isNotEmpty(pieChart) && pieChart.size() > 10) {
                // 创建一个新的排序列表
                List<PieChartRespVo> sortedList = new ArrayList<>(pieChart);
                // 先对List<PieChartRespVo>根据displayValue百分比数值降序, name升序排序
                sortedList.sort(
                        Comparator.comparing((PieChartRespVo vo) -> {
                            // 将百分比字符串转换为数值进行比较
                            String percentStr = vo.getDisplayValue().replace("%", "");
                            return Double.parseDouble(percentStr);
                        }).reversed()
                                .thenComparing(PieChartRespVo::getName)
                );

                // 截取前10条
                List<PieChartRespVo> top10 = new ArrayList<>(sortedList.subList(0, 10));
                // 剩余部分作为 Others
                List<PieChartRespVo> others = sortedList.subList(10, sortedList.size());

                // 计算others的displayValue百分比值的总和
                double othersTotal = others.stream()
                        .mapToDouble(vo -> Double.parseDouble(vo.getDisplayValue().replace("%", "")))
                        .sum();
                String othersDisplayValue = String.format("%.2f%%", othersTotal);

                // 将others数据组装为一个"Others"的PieChartRespVo对象添加到top10
                top10.add(new PieChartRespVo("Others", othersDisplayValue, others));

                // 最后赋值设置回result
                result.getData().setPieChart(top10);
            }
        }
    }

    /**
     * 根据订单类型和标签构建饼图和表格数据
     * 该方法用于处理查询结果，计算订单总数，按订单类型分组，并计算每种类型订单的百分比
     * 同时，它还构建了饼图和表格数据，用于在前端展示
     *
     * @param results 查询结果列表，包含订单类型、标签和数量信息
     * @param columnValue 用于表格第一列的标题
     * @return 返回一个包含饼图和表格数据的CommonResult对象
     */
    private static CommonResult<ProductSalesProportionByAttributesRespVo> buildPieChartAndTableDataByCount(List<SqlResultDTO> results, String columnValue) {
        ProductSalesProportionByAttributesRespVo attributesRespVo = new ProductSalesProportionByAttributesRespVo();
        // 饼图数据
        List<PieChartRespVo> respVoList = new ArrayList<>();
        TableRespVo table = new TableRespVo();
        // 表格数据
        List<Map<String, String>> tableData = new ArrayList<>();
        // 表格标题
        List<TableRespVo.HeaderItem> headerItems = new ArrayList<>();

        // 计算出订单总数
        long totalOrderCount = results.stream()
                .map(SqlResultDTO::getQuantity)
                .reduce(0L, Long::sum);
        if (totalOrderCount == 0) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }

        // 按 orderType 和 label 分组
        Map<Integer, Map<String, Long>> groupedData = results.stream()
                .collect(Collectors.groupingBy(
                        SqlResultDTO::getOrderType,
                        Collectors.toMap(SqlResultDTO::getLabel, SqlResultDTO::getQuantity)
                ));

        Map<String, String> tableItem = new HashMap<>();
        tableItem.put(FIRST_SUB_COLUMN, CharSequenceUtil.isBlank(columnValue) ? "全部" : columnValue);
        // 计算百分比
        calculatePercentage(groupedData, totalOrderCount, tableItem, respVoList, headerItems);
        // 表格第一列标题为空
        TableRespVo.HeaderItem subHeaderItem = new TableRespVo.HeaderItem(FIRST_SUB_COLUMN, "", null);
        headerItems.add(0, new TableRespVo.HeaderItem(FIRST_COLUMN, "", List.of(subHeaderItem)));

        // 构建表格数据
        tableData.add(tableItem);
        table.setTableData(tableData);
        table.setHeaders(headerItems);

        attributesRespVo.setTable(table);
        attributesRespVo.setPieChart(respVoList);
        return CommonResult.success(attributesRespVo);
    }

    /**
     * 计算各订单类型在不同服务时间内的百分比，用于仪表盘展示
     *
     * @param groupedData 按订单类型和服务时间分组的数据
     * @param totalOrderCount 总订单数量
     * @param tableItem 用于存储表格数据的容器
     * @param respVoList 存储饼图响应数据的列表
     * @param headerItems 存储表格响应数据的列表
     */
    private static void calculatePercentage(Map<Integer, Map<String, Long>> groupedData, long totalOrderCount, Map<String, String> tableItem, List<PieChartRespVo> respVoList, List<TableRespVo.HeaderItem> headerItems) {
        for (OrderTypeEnum orderTypeEnum : OrderTypeEnum.getDashboardDisplayList()) {
            PieChartRespVo chartRespVo = new PieChartRespVo();
            TableRespVo.HeaderItem headerItem = new TableRespVo.HeaderItem();
            headerItem.setProp(orderTypeEnum.getDashboardDisplay());
            headerItem.setLabel(orderTypeEnum.getDashboardDisplay());
            List<TableRespVo.HeaderItem> subHeaders = new ArrayList<>();

            Map<String, Long> typeDataMap = groupedData.getOrDefault(orderTypeEnum.getCode(), Collections.emptyMap());
            // 计算总数
            long totalCountByType = typeDataMap.values().stream().reduce(0L, Long::sum);
            // 计算每个履约类型的百分比
            BigDecimal divideByType = BigDecimal.valueOf(totalCountByType).divide(BigDecimal.valueOf(totalOrderCount), 4, RoundingMode.HALF_UP);
            // 计算每个服务时长的百分比
            List<PieChartRespVo> hoverDisplay = new ArrayList<>();
            // 遍历每个服务时长，计算百分比并添加到hoverDisplay中
            for (ServiceTimeEnum serviceTimeEnum : ServiceTimeEnum.values()) {
                String label = serviceTimeEnum.getServiceTime();
                Long count = typeDataMap.getOrDefault(label, 0L);
                PieChartRespVo hoverDisplayItem = new PieChartRespVo();
                // 饼图显示
                BigDecimal divide = totalCountByType != 0 ?
                        BigDecimal.valueOf(count).divide(BigDecimal.valueOf(totalCountByType), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;
                hoverDisplayItem.setName(label);
                hoverDisplayItem.setDisplayValue(DashboardUtils.formatPercentNumber(divide));
                hoverDisplay.add(hoverDisplayItem);
                // headerItem.getProp() + label 保证唯一
                TableRespVo.HeaderItem subHeaderItem = new TableRespVo.HeaderItem(headerItem.getProp() + label, label, null);
                subHeaders.add(subHeaderItem);

                // 表格显示
                BigDecimal tableDivide = BigDecimal.valueOf(count).divide(BigDecimal.valueOf(totalOrderCount), 4, RoundingMode.HALF_UP);
                tableItem.put(headerItem.getProp() + label, DashboardUtils.formatPercentNumber(tableDivide));
            }
            chartRespVo.setName(orderTypeEnum.getDashboardDisplay());
            chartRespVo.setDisplayValue(DashboardUtils.formatPercentNumber(divideByType));
            chartRespVo.setHoverDisplay(hoverDisplay);
            respVoList.add(chartRespVo);
            headerItem.setSubHeaders(subHeaders);
            headerItems.add(headerItem);
        }
    }

    /**
     * 根据金额构建饼图和表格数据
     * 此方法处理和转换从数据库查询得到的结果，生成用于展示产品销售比例的饼图和表格数据
     *
     * @param results 数据库查询结果，包含订单金额等信息
     * @param columnValue 用于表格第一列的标题
     * @return 包含产品销售比例的响应对象
     */
    private static CommonResult<ProductSalesProportionByAttributesRespVo> buildPieChartAndTableDataByAmount(List<SqlResultDTO> results, String columnValue) {
        ProductSalesProportionByAttributesRespVo attributesRespVo = new ProductSalesProportionByAttributesRespVo();
        // 饼图数据
        List<PieChartRespVo> respVoList = new ArrayList<>();
        TableRespVo table = new TableRespVo();
        // 表格数据
        List<Map<String, String>> tableData = new ArrayList<>();
        // 表格标题
        List<TableRespVo.HeaderItem> headerItems = new ArrayList<>();

        // 计算出订单总金额
        long totalOrderAmount = results.stream()
                .map(SqlResultDTO::getAmount)
                .reduce(0L, Long::sum);
        if (totalOrderAmount == 0) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }

        // 按 orderType 和 label 分组
        Map<Integer, Map<String, Long>> groupedData = results.stream()
                .collect(Collectors.groupingBy(
                        SqlResultDTO::getOrderType,
                        Collectors.toMap(SqlResultDTO::getLabel, SqlResultDTO::getAmount)
                ));

        Map<String, String> tableItem = new HashMap<>();
        tableItem.put(FIRST_SUB_COLUMN, CharSequenceUtil.isBlank(columnValue) ? "全部" : columnValue);
        // 计算百分比
        calculatePercentage(groupedData, totalOrderAmount, tableItem, respVoList, headerItems);
        // 表格第一列标题为空
        TableRespVo.HeaderItem subHeaderItem = new TableRespVo.HeaderItem(FIRST_SUB_COLUMN, "", null);
        headerItems.add(0, new TableRespVo.HeaderItem(FIRST_COLUMN, "", List.of(subHeaderItem)));

        // 构建表格数据
        tableData.add(tableItem);
        table.setTableData(tableData);
        table.setHeaders(headerItems);

        attributesRespVo.setTable(table);
        attributesRespVo.setPieChart(respVoList);
        return CommonResult.success(attributesRespVo);
    }

    /**
     * 根据收入构建饼图和表格数据
     * 此方法处理和分析从数据库查询得到的订单和退款数据，以生成产品销售比例的可视化展示
     * 它主要关注于计算不同订单类型和标签下的收入比例，用于dashboard展示
     *
     * @param results 订单数据列表，包含每个订单的金额和标签等信息
     * @param columnValue 用于标识数据来源
     * @param refundResults 退款数据列表，包含每个退款订单的金额和标签等信息
     * @return 返回一个CommonResult对象，包含处理后的数据用于展示
     */
    private static CommonResult<ProductSalesProportionByAttributesRespVo> buildPieChartAndTableDataByIncome(List<SqlResultDTO> results, String columnValue, List<SqlResultDTO> refundResults) {
        ProductSalesProportionByAttributesRespVo attributesRespVo = new ProductSalesProportionByAttributesRespVo();
        // 饼图数据
        List<PieChartRespVo> respVoList = new ArrayList<>();
        TableRespVo table = new TableRespVo();
        // 表格数据
        List<Map<String, String>> tableData = new ArrayList<>();
        // 表格标题
        List<TableRespVo.HeaderItem> headerItems = new ArrayList<>();

        // 计算出订单总金额
        long totalOrderAmount = results.stream()
                .map(SqlResultDTO::getAmount)
                .reduce(0L, Long::sum);
        if (totalOrderAmount == 0) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }

        // 计算出订单总退款金额
        long totalRefundOrderAmount = refundResults.stream()
                .map(SqlResultDTO::getAmount)
                .reduce(0L, Long::sum);
        // 计算出总收入
        long totalIncome = totalOrderAmount - totalRefundOrderAmount;

        // 按 orderType 和 label 分组
        Map<Integer, Map<String, Long>> groupedData = results.stream()
                .collect(Collectors.groupingBy(
                        SqlResultDTO::getOrderType,
                        Collectors.toMap(SqlResultDTO::getLabel, SqlResultDTO::getAmount)
                ));

        // 按 orderType 和 label 分组
        Map<Integer, Map<String, Long>> refundGroupedData = refundResults.stream()
                .collect(Collectors.groupingBy(
                        SqlResultDTO::getOrderType,
                        Collectors.toMap(SqlResultDTO::getLabel, SqlResultDTO::getAmount)
                ));

        List<OrderTypeEnum> orderTypeEnums = OrderTypeEnum.getDashboardDisplayList();
        Map<String, String> tableItem = new HashMap<>();
        tableItem.put(FIRST_SUB_COLUMN, CharSequenceUtil.isBlank(columnValue) ? "全部" : columnValue);
        // 计算百分比
        for (OrderTypeEnum orderTypeEnum : orderTypeEnums) {
            PieChartRespVo chartRespVo = new PieChartRespVo();
            TableRespVo.HeaderItem headerItem = new TableRespVo.HeaderItem();
            headerItem.setProp(orderTypeEnum.getDashboardDisplay());
            headerItem.setLabel(orderTypeEnum.getDashboardDisplay());
            List<TableRespVo.HeaderItem> subHeaders = new ArrayList<>();

            Map<String, Long> typeDataMap = groupedData.getOrDefault(orderTypeEnum.getCode(), Collections.emptyMap());
            Map<String, Long> refundTypeDataMap = refundGroupedData.getOrDefault(orderTypeEnum.getCode(), Collections.emptyMap());
            // 计算总收入
            long totalAmountByType = typeDataMap.values().stream().reduce(0L, Long::sum);
            long totalRefundByType = refundTypeDataMap.values().stream().reduce(0L, Long::sum);
            long incomeByType = totalAmountByType - totalRefundByType;
            // 计算每个履约类型的百分比
            BigDecimal divideByType = totalIncome != 0 ?
                    BigDecimal.valueOf(incomeByType).divide(BigDecimal.valueOf(totalIncome), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            // 计算每个服务时长的百分比
            List<PieChartRespVo> hoverDisplay = new ArrayList<>();
            for (ServiceTimeEnum serviceTimeEnum : ServiceTimeEnum.values()) {
                String label = serviceTimeEnum.getServiceTime();
                Long amount = typeDataMap.getOrDefault(label, 0L);
                PieChartRespVo hoverDisplayItem = new PieChartRespVo();
                Long refund = refundTypeDataMap.getOrDefault(label, 0L);
                long income = amount - refund;
                // 饼图显示
                BigDecimal divide = incomeByType != 0 ?
                        BigDecimal.valueOf(income).divide(BigDecimal.valueOf(incomeByType), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;
                hoverDisplayItem.setName(label);
                hoverDisplayItem.setDisplayValue(DashboardUtils.formatPercentNumber(divide));
                hoverDisplay.add(hoverDisplayItem);
                // headerItem.getProp() + label 保证唯一
                TableRespVo.HeaderItem subHeaderItem = new TableRespVo.HeaderItem(headerItem.getProp() + label, label, null);
                subHeaders.add(subHeaderItem);

                // 表格显示
                BigDecimal tableDivide = totalIncome != 0 ?
                        BigDecimal.valueOf(income).divide(BigDecimal.valueOf(totalIncome), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;

                tableItem.put(headerItem.getProp() + label, DashboardUtils.formatPercentNumber(tableDivide));
            }
            chartRespVo.setName(orderTypeEnum.getDashboardDisplay());
            chartRespVo.setDisplayValue(DashboardUtils.formatPercentNumber(divideByType));
            chartRespVo.setHoverDisplay(hoverDisplay);
            respVoList.add(chartRespVo);
            headerItem.setSubHeaders(subHeaders);
            headerItems.add(headerItem);
        }
        // 表格第一列标题为空
        TableRespVo.HeaderItem subHeaderItem = new TableRespVo.HeaderItem(FIRST_SUB_COLUMN, "", null);
        headerItems.add(0, new TableRespVo.HeaderItem(FIRST_COLUMN, "", List.of(subHeaderItem)));

        // 构建表格数据
        tableData.add(tableItem);
        table.setTableData(tableData);
        table.setHeaders(headerItems);

        attributesRespVo.setTable(table);
        attributesRespVo.setPieChart(respVoList);
        return CommonResult.success(attributesRespVo);
    }
}


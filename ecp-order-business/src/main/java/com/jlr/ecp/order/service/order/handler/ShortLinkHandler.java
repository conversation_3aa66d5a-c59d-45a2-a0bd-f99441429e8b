package com.jlr.ecp.order.service.order.handler;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.api.dto.ShortLinkReqDto;
import com.jlr.ecp.notification.api.jaguarlandover.ShorLinkAPI;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.enums.payment.PayCenterBizLineCodeEnum;
import com.jlr.ecp.order.enums.sms.ShortLinkPathEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */


/**
 * 获取短链接
 */
@Component
@Slf4j
public class ShortLinkHandler {


    @Resource
    private ShorLinkAPI shorLinkAPI;

    /**
     * 获取短链
     * @param orderItemCode
     * @param orderRefundCode
     * @param path path 从ShortLinkPathEnum中取值
     * @return
     */
    public  String getShortLink(String orderItemCode,String orderRefundCode,String path,String businessCode){
        String query = "?orderItemCode=" + orderItemCode+"&orderRefundCode="+orderRefundCode+"&type="+ PayCenterBizLineCodeEnum.getByBusinessCode(businessCode).getBusinessCode() + Constants.SHORT_LINK_QUERY;
        log.info("generate getShortLink shortLink:{}", path+query);
        ShortLinkReqDto shortLinkReqDto = new ShortLinkReqDto();
        shortLinkReqDto.setPath(path+query);
        //shortLinkReqDto.setQuery(query);
        // 生成短链
        // 品牌编码为LR 的短链接生成逻辑
        return callServiceGetLink(shortLinkReqDto);
    }

    /**
     * 获取BG订单详情的短链
     * @param orderCode 订单号
     * @return String 短链url
     */
    public  String getBGOrderDetailShortLink(String orderCode){
        String query = "?orderCode=" + orderCode + "&source=orderList" + Constants.SHORT_LINK_QUERY;
        log.info("generate getShortLink shortLink: path:{}, query: {}", ShortLinkPathEnum.BG_ORDER_DETAIL.getPath(), query);
        ShortLinkReqDto shortLinkReqDto = new ShortLinkReqDto();
        shortLinkReqDto.setPath(ShortLinkPathEnum.BG_ORDER_DETAIL.getPath()+query);
        //shortLinkReqDto.setQuery(query);
        return callServiceGetLink(shortLinkReqDto);
    }

    private String callServiceGetLink(ShortLinkReqDto shortLinkReqDto) {
        // 生成短链
        // 品牌编码为LR 的短链接生成逻辑
        CommonResult<String> stringCommonResult = null;
        try {
            stringCommonResult = shorLinkAPI.genShortLink(shortLinkReqDto);
            log.info("generate getShortLink result:{}", stringCommonResult);
        } catch (Exception e) {
            log.error("generate getShortLink error:{}", e.getMessage());
        }
        if (stringCommonResult != null && stringCommonResult.getData() != null) {
            return stringCommonResult.getData();
        }
        return "";
    }
}

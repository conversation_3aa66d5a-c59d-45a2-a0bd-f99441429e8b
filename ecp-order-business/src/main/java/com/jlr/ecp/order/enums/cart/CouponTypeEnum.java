package com.jlr.ecp.order.enums.cart;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.enums.cart
 * @enumName: CouponTypeEnum
 * @author: gaoqig
 * @description: 优惠券类型枚举
 * @date: 2025/3/6 19:05
 * @version: 1.0
 */
@AllArgsConstructor
@Getter
public enum CouponTypeEnum {
    //0-积分 1-兑换券 2-代金券 3-折扣券 4-满减券

    POINTS(0, "积分", 4),
    EXCHANGE(1, "兑换券", 0),
    VOUCHER(2, "代金券", 1),
    DISCOUNT(3, "折扣券", 2),
    CASH_BACK(4, "满减券",3)
    ;

    private final Integer type;
    private final String desc;
    //优先级 积分>满减>折扣>代金>兑换; 越左越优先
    private final Integer priority;

    public static CouponTypeEnum getByType(Integer type) {
        for (CouponTypeEnum couponTypeEnum : values()) {
            if (couponTypeEnum.getType().equals(type)) {
                return couponTypeEnum;
            }
        }
        return null;
    }

    public CarPaymentTypeEnum coverToCarpaymentTypeEnum() {
        if (this == POINTS){//只有积分需要返回积分，其他均为优惠券
            return CarPaymentTypeEnum.POINT;
        }
        return CarPaymentTypeEnum.COUPON;
     }

    public static CarPaymentTypeEnum coverToCarpaymentTypeEnum(Integer couponTypeCode) {
        if (couponTypeCode == null){
            return null;
        }
        if (POINTS.getType().equals(couponTypeCode)){//只有积分需要返回积分，其他均为优惠券
            return CarPaymentTypeEnum.POINT;
        }
        return CarPaymentTypeEnum.COUPON;
    }
}

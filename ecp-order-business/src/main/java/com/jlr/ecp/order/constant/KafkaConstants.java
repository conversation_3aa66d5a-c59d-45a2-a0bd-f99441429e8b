package com.jlr.ecp.order.constant;

/**
 * kafka topic
 * <AUTHOR>
 *
 */
public class KafkaConstants {
    //代客下单kafka-topic:    valet-order-topic
    public static final String ORDER_BEHALFOF_CREATE_TOPIC = "valet-order-topic";

    public static final String ORDER_CANCEL_TOPIC = "order-cancel-topic";
    public static final String ORDER_SUCCESSFUL_TOPIC = "order-successful-topic";
    public static final String ORDER_FUFILMENT_TOPIC = "order-fufilment-topic";
    public static final String SERVICE_ACTIVATE_TOPIC = "service-activate-topic";

    public static final String ORDER_TIMEOUT_CANCEL_TOPIC = "order-timeout-cancel-topic";

    public static final String REAL_ORDER_TIMEOUT_CANCEL_TOPIC = "real-order-timeout-cancel-topic";

    public static final String ORDER_REFUND_SUCCESS_TOPIC = "order-refund-success-topic";

    public static final String PAY_TIMEOUT_CANCEL_TOPIC = "pay-timeout-cancel-topic";
    public static final String ORDER_COMPLETE_STATUS_UPDATES_TOPIC = "order-complete-status-updates-topic";
    public static final String ORDER_ROLLBACK_STATUS_UPDATES_TOPIC = "order-rollback-status-updates-topic";

    // LRE
    public static final String ORDER_ECOUPON_SUCCESSFUL_TOPIC = "order-ecoupon-successful-topic";
    // LRE
    public static final String ORDER_ECOUPON_REFUND_TOPIC = "order-ecoupon-refund-topic";

    // 优惠券核销
    public static final String COUPON_STATUS_CHANGE_TOPIC = "coupon-status-change";
    // 订单状态变化MQ
    public static final String ORDER_STATUS_CHANGE_TOPIC = "order-status-change";

}
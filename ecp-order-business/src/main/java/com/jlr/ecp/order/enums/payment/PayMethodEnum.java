package com.jlr.ecp.order.enums.payment;

import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 支付方式枚举类
 *
 * <AUTHOR>
 * @date 2024/12/02
 */
@Getter
@Schema(description = "支付方式枚举类")
public enum PayMethodEnum {

    @Schema(description = "支付宝支付")
    ALIPAY("ALIPAY", "支付宝支付"),

    @Schema(description = "微信支付")
    WXPAY("WXPAY", "微信支付"),

    @Schema(description = "银联支付")
    CHINAPAY("CHINAPAY", "银联支付");

    @EnumValue
    private final String code;

    private final String description;

    PayMethodEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举实例
     *
     * @param code 代码
     * @return 枚举实例
     * @throws IllegalArgumentException 如果代码不存在
     */
    public static PayMethodEnum fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (PayMethodEnum method : PayMethodEnum.values()) {
            if (method.getCode().equals(code)) {
                return method;
            }
        }
        throw new IllegalArgumentException("Invalid PaymentMethod code: " + code);
    }
}

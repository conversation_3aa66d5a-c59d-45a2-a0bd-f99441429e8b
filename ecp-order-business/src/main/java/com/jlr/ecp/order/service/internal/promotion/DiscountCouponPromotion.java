package com.jlr.ecp.order.service.internal.promotion;


import cn.hutool.core.collection.CollUtil;
import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.service.internal.promotion.dto.PromotionRespDto;
import com.jlr.ecp.order.util.money.MoneyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.internal.promotion
 * @className: DiscountCouponPromotion
 * @author: gaoqig
 * @description: 折扣券，打折券
 * @date: 2025/3/6 18:01
 * @version: 1.0
 */
@Component
@Slf4j
public class DiscountCouponPromotion extends CommonPromotion{
    @Override
    public PromotionRespDto executePromotional(List<CartProductSkuInfo> skuInfos, PromotionDto promotion) {
        PromotionRespDto result = new PromotionRespDto();
        //先计算本优惠券下，所有能参与该优惠券的所有商品汇总信息
        List<CartProductSkuInfo> canUsePromotionSkuList = skuInfos.stream()
                .filter(item->CollUtil.isNotEmpty(item.getCouponModuleCodeList())
                        && item.getCouponModuleCodeList().contains(promotion.getCouponModelCode())
                        && item.isJoinCalculateFlag())
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(canUsePromotionSkuList)){ //如果没有商品能参与优惠，直接返回空，表示没有命中优惠
            return null;
        }

        //折扣券的折扣（转换为百分比，原来是大于0小于100的表示法）
        //这里要先转成string,不然转成BigDecimal会丢失精度
        String discountPercentStr = String.valueOf((HUNDRED - promotion.getDiscountPercent())/HUNDRED);
        BigDecimal discountPercent = new BigDecimal(discountPercentStr);
        //先计算参与优惠商品的销售总价；分摊折扣金额时要用
        BigDecimal allProductTotalAmount = BigDecimal.ZERO; //总的商品总价（包括不参与优惠的）
        BigDecimal discountAmount = BigDecimal.ZERO;//总的优惠金额,要先计算出来是为了总和对的上
        //遍历所有SKU进行，属性赋值
        List<CartProductSkuInfo> returnSkuInfos = new ArrayList<>();
        for (CartProductSkuInfo skuInfo : skuInfos) {
            CartProductSkuInfo returnSkuInfo = getCartProductSkuInfo(skuInfo); //这里要copy一份新的sku信息，因为不能修改原数据，原数据可能还要参与后续的计算
            allProductTotalAmount = allProductTotalAmount.add(MoneyUtil.convertToCents(returnSkuInfo.getSalePrice()));
            if (CollUtil.isNotEmpty(returnSkuInfo.getCouponModuleCodeList())
                    && returnSkuInfo.getCouponModuleCodeList().contains(promotion.getCouponModelCode())
                    && returnSkuInfo.isJoinCalculateFlag()) {//只对命中优惠的SKU进行赋值，没有命中的保持不变
                returnSkuInfo.setChooseFlag(false);
                returnSkuInfo.setChooseCouponCode(promotion.getCouponCode());
                returnSkuInfo.setChooseCouponType(CouponTypeEnum.DISCOUNT.getType());

                /* =========================优惠金额赋值，由当前商品的销售价金额所占比例来。优惠后的实际价格如果小于1分钱=====================**/
                //先计算优惠金额，向下取整。实际金额等于销售金额-优惠金额

                BigDecimal skuDiscountAmount = MoneyUtil.convertToCents(returnSkuInfo.getSalePrice()).multiply(discountPercent).setScale(0, RoundingMode.DOWN);
                BigDecimal skuCouponPrice = MoneyUtil.convertToCents(returnSkuInfo.getSalePrice()).subtract(skuDiscountAmount);//当前sku优惠后花费的金额,如果小于一分，则向上整

                discountAmount = discountAmount.add(skuDiscountAmount);

                returnSkuInfo.setDiscountAmount(MoneyUtil.convertFromCents(skuDiscountAmount));
                returnSkuInfo.setCouponPrice(MoneyUtil.convertFromCents(skuCouponPrice));

                /* =========================优惠金额赋值，由当前商品的销售价金额所占比例来===================== **/
            }
            returnSkuInfos.add(returnSkuInfo);
        }

        promotion.setDiscountAmount(MoneyUtil.convertFromCents(discountAmount));
        promotion.setCostAmount(MoneyUtil.convertFromCents(allProductTotalAmount.subtract(discountAmount)));

        result.setDiscountTotalAmount(MoneyUtil.convertFromCents(discountAmount));
        result.setCostAmount(MoneyUtil.convertFromCents(allProductTotalAmount.subtract(discountAmount)));
        result.setCartSkuProductList(returnSkuInfos);
        result.setCouponModelName(promotion.getCouponModelName());
        result.setCouponTypeEnum(CouponTypeEnum.DISCOUNT);
        result.setChooseCoupon(promotion);
        return result;
    }
}

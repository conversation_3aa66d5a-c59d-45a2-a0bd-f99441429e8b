package com.jlr.ecp.order.enums.dashboard;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TimeIntervalEnum {

    /**
     * BY_HOUR
     */
    BY_HOUR(1, "%l %p"),

    /**
     * BY_DAY
     */
    BY_DAY(2, "%b %d"),

    /**
     * BY_MONTH
     */
    BY_MONTH(3, "%Y/%m"),

    /**
     * BY_YEAR
     */
    BY_YEAR(4, "%Y");

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 表达式
     */
    private final String expression;
}
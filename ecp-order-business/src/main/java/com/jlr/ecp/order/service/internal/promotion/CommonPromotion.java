package com.jlr.ecp.order.service.internal.promotion;


import cn.hutool.core.collection.CollUtil;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil;
import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static com.jlr.ecp.order.enums.ErrorCodeConstants.NOT_EXIST_PRODUCT_SUPPORT;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.internal.promotion.dto
 * @className: CommonPromotion
 * @author: gaoqig
 * @description: 公共抽象优惠券策略
 * @createDate: 2025/3/7 09:34
 * @date: 2025/3/22 10:10
 * @version: 1.0
 */
abstract class CommonPromotion implements PromotionalStrategy {
    /***
     * <AUTHOR>
     * @description 检查优惠券或者积分：通用部分
     * @date 2025/3/7 09:34
     * @param skuInfos: SKU商品列表
     * @param promotions: 用户持有的优惠券列表
     * @param userChoosePromotion: 用户选择的优惠
     * @return: PromotionDto 校验通过，返回优惠券信息及符合优惠券的商品总价格
     */

    @Override
    public List<CartProductSkuInfo> checkUserChoose(List<CartProductSkuInfo> skuInfos, List<PromotionDto> promotions, PromotionDto userChoosePromotion) throws ServiceException {
        Logger log = LoggerFactory.getLogger(PromotionalStrategy.class);
        // 检查 skuInfos 中的 chooseCouponCode 是否唯一
        // 是否包含该优惠券
        boolean userHasPromotion = false;
        for (PromotionDto promotionDto : promotions){
            userHasPromotion = isUserHasPromotion(userChoosePromotion, promotionDto, userHasPromotion, log);
        }

        if (!userHasPromotion){
            log.info("该优惠券不存在");
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.NOT_FOUND_COUPON);
        }

        List<CartProductSkuInfo> canUsePromotionSkuList = skuInfos.stream()
                .filter(item-> CollUtil.isNotEmpty(item.getCouponModuleCodeList()) && item.getCouponModuleCodeList().contains(userChoosePromotion.getCouponModelCode()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(canUsePromotionSkuList)){ //如果没有商品能参与优惠，直接返回空，表示没有命中优惠
            throw ServiceExceptionUtil.exception(NOT_EXIST_PRODUCT_SUPPORT);
        }
        return canUsePromotionSkuList;
    }

    private static boolean isUserHasPromotion(PromotionDto userChoosePromotion, PromotionDto promotionDto, boolean userHasPromotion, Logger log) {
        if (CouponTypeEnum.POINTS.getType().equals(promotionDto.getCouponModelClassify())){//因为当前是满减，所以不考虑积分
            return userHasPromotion;
        }
        if (promotionDto.getCouponCode().equals(userChoosePromotion.getCouponCode())){
            userHasPromotion = true;
            if (promotionDto.getValidStartTime() != null && promotionDto.getValidStartTime().isAfter(LocalDateTime.now())){
                log.info("该优惠券未生效:{},生效时间为:{}", promotionDto.getCouponCode(), promotionDto.getValidStartTime());
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.EXPIRED_COUPON);
            }
            if (promotionDto.getValidEndTime() != null && promotionDto.getValidEndTime().isBefore(LocalDateTime.now())) {
                // 优惠券不在有效时间范围内
                log.info("对不起，该优惠券已失效:{},失效时间为:{}", promotionDto.getCouponCode(), promotionDto.getValidEndTime());
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.EXPIRED_COUPON);
            }
        }
        return userHasPromotion;
    }

}

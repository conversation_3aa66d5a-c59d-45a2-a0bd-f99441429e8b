package com.jlr.ecp.order.dal.dataobject.feedback;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@TableName("t_feedback_modify_log")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackModifyLogDO extends BaseDO {
    /**
     * 修改记录ID
     */
    @TableId
    private Long id;

    /**
     * 评价编码：生成规则：feedback_dimensions+递增三位数编号（例如：PM001）
     */
    @TableField("feedback_code")
    private String feedbackCode;

    /**
     * 修改模块
     */
    @TableField("modify_module")
    private String modifyModule;

    /**
     * 修改字段数量
     */
    @TableField("modify_field_count")
    private Integer modifyFieldCount;

    /**
     * 修改前字段值（JSON格式）
     */
    @TableField("modify_field_old_value")
    private String modifyFieldOldValue;

    /**
     * 修改后字段值（JSON格式）
     */
    @TableField("modify_field_new_value")
    private String modifyFieldNewValue;

    /**
     * 修改时间
     */
    @TableField("operate_time")
    private LocalDateTime operateTime;

    /**
     * 修改人账号
     */
    @TableField("operate_user")
    private String operateUser;

    /**
     * 租户号
     */
    @TableField("tenant_id")
    private Integer tenantId;

    // 移除旧字段：operateType, modifyUserId, modifyUserName
}
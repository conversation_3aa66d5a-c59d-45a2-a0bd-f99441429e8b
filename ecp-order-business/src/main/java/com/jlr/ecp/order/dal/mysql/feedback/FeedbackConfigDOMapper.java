package com.jlr.ecp.order.dal.mysql.feedback;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackConfigDO;
import com.jlr.ecp.order.enums.feedback.FeedBackEnableStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评价设置表Mapper
 */
@Mapper
public interface FeedbackConfigDOMapper extends BaseMapperX<FeedbackConfigDO> {
    default FeedbackConfigDO selectOneByFeedBackCode(String feedbackCode){
        return selectOne(new LambdaQueryWrapperX<FeedbackConfigDO>()
                .eq(FeedbackConfigDO::getFeedbackCode,feedbackCode)
                .eq(BaseDO::getIsDeleted,false));
    }

    default FeedbackConfigDO selectOneByDimensions(String feedbackDimensions){
        return selectOne(new LambdaQueryWrapperX<FeedbackConfigDO>()
                .eq(FeedbackConfigDO::getFeedbackDimensions,feedbackDimensions)
                .eq(FeedbackConfigDO::getEnableStatus, FeedBackEnableStatusEnum.ENABLE.getCode())
                .eq(BaseDO::getIsDeleted,false)
                .orderByDesc(FeedbackConfigDO::getId)
                .last(Constants.LIMIT_ONE));
    }

    default FeedbackConfigDO selectOneByDimensionsAndStatus(String feedbackDimensions, Integer code, LocalDateTime time){
        return selectOne( new LambdaQueryWrapperX<FeedbackConfigDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(FeedbackConfigDO::getEnableStatus, code)
                .eq(FeedbackConfigDO::getFeedbackDimensions, feedbackDimensions)
                .eqIfPresent(FeedbackConfigDO::getScheduleEnableTime, time)
                .orderByDesc(FeedbackConfigDO::getId)
                .last(Constants.LIMIT_ONE));
    }


    default List<FeedbackConfigDO> selectFeedBackCodeListByDimensionsCode (String dimensionsCode){
        return selectList(new LambdaQueryWrapperX<FeedbackConfigDO>()
                .eq(FeedbackConfigDO::getFeedbackDimensions, dimensionsCode)
//                .ne(FeedbackConfigDO::getEnableStatus,FeedBackEnableStatusEnum.WAIT.getCode())
                .eq(BaseDO::getIsDeleted, false)
                .orderByAsc(FeedbackConfigDO::getId));
    }
}

package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 是否领取礼物类型
 */

@AllArgsConstructor
@Getter
public enum GiftAddressEnum {

    /**
     * 否
     */
    NO(0, "否"),

    /**
     * 是
     */
    YES(1, "是"),

    /**
     * 无需设置
     */
    NOT_REQUIRED(2, "-");

    private final Integer code;

    private final String description;

    // 根据code获取描述
    public static String getDescription(Integer code) {
        if(Objects.isNull(code)){
            return "-";
        }
        for (GiftAddressEnum status : GiftAddressEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        return "-";
    }
}
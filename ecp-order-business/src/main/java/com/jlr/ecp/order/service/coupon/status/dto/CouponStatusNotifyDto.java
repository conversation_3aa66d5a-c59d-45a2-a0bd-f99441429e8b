package com.jlr.ecp.order.service.coupon.status.dto;


import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.coupon.statuupdate
 * @className: CouponStatusNotifyDto
 * @author: gaoqig
 * @description: 卡券变更通知dto
 * @date: 2025/3/11 22:08
 * @version: 1.0
 */
@Getter
@Setter
public class CouponStatusNotifyDto {
    /**
     * 卡券模板code
     */
    private String couponModelCode;
    /**
     * 卡券券码
     */
    private String couponCode;
    /**
     * 卡券状态
     * 卡券状态1-入库（先有券码，未发放） 2-未生效；3-待使用；4-已作废；5-已核销；6-已过期
     */
    private Integer couponStatus;
    /**
     * 变更时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否为最后一张核销的卡券（order item 纬度）
     */
    private Boolean lastUsedCoupon;
}

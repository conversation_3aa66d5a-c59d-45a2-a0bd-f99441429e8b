package com.jlr.ecp.order.controller.app.order.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * LogisticsDetailItemVO
 *
 * <AUTHOR> <PERSON>
 * @since 2025-04-03 14:52
 */
@Data
public class LogisticsDetailItemVO {

    /**
     * 物流状态: 揽收、在途等
     */
    private String status;

    /**
     * 物流状态最新时间 yyyy-MM-dd  HH:mm
     */
    private String statusDate;

    /**
     * 该物流状态下的物流详细
     */
    private List<LogisticsDetailVO> detailList;

}

package com.jlr.ecp.order.api.independent;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.independent.dto.TriggerIndependentReqDTO;
import com.jlr.ecp.order.api.independent.dto.UpdateStatusToSuccReqDTO;
import com.jlr.ecp.order.service.independent.OrderIndependentDOService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> Hongyi
 */
@RestController
@Validated
public class IndependentAppApiImpl implements IndependentAppApi {

    @Resource
    private OrderIndependentDOService orderIndependentDOService;

    @Override
    public CommonResult<Boolean> triggerIndependent(TriggerIndependentReqDTO req) {
        if (req.getCompensate()) {
            return CommonResult.success(orderIndependentDOService.triggerIndependentForRetry(req));
        } else {
            return CommonResult.success(orderIndependentDOService.triggerIndependentForOrderSucc(req));
        }
    }

    @Override
    public CommonResult<Boolean> updateStatusToSucc(UpdateStatusToSuccReqDTO req) {
        return CommonResult.success(orderIndependentDOService.updateStatusToSucc(req));
    }

}

package com.jlr.ecp.order.enums.independent;

import lombok.Getter;

/**
 * 订单分账状态
 */
@Getter
public enum OrderIndependentStatusEnum {

    NO_NEED(0, "无需分账"),
    TODO(1, "待分账"),
    ING(2, "分账中"),
    DONE(3, "已分账");

    /**
     * 状态
     */
    private final Integer status;

    /**
     * 状态名称
     */
    private final String name;

    OrderIndependentStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }

    public static boolean isIndependentRefund(Integer status){
        if(ING.status.equals(status) || DONE.status.equals(status)){
            return true;
        }else {
            return false;
        }
    }

}

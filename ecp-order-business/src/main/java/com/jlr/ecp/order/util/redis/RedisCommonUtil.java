package com.jlr.ecp.order.util.redis;

import com.jlr.ecp.order.config.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 公共代码处理工具
 * <AUTHOR>
 */
@Component
@Slf4j
public class RedisCommonUtil {

    private final static String BUSINESS_DEFAULT_KEY = "BUSINESS:001";
    /**
    处理传入参数list参数
     */
   public static  List<String> handleListParam(String params){
       List<String> paramList = new ArrayList<>();
        if(params !=null && !params.isEmpty()){
             paramList = Arrays.stream(params.split(","))
                    .map(String::trim)
                    .collect(Collectors.toList());
        }
        return paramList;
   }

    /**
     * 根据code获取name
     * @param redisService
     * @param cacheKey
     * @param businessCode
     * @return
     */
    public static String getBusinessNameFromRedis(RedisService redisService, String cacheKey, String businessCode) {
       try {
           String cacheMapValue = redisService.getCacheMapValue(cacheKey, businessCode);
           if(cacheMapValue !=null && !cacheMapValue.isEmpty()){
               return cacheMapValue;
           }
       }catch (Exception e){
           log.info("redis 缓存error");
       }

        return businessCode;
    }

    /**
     * 从缓存判断业务线是否存在
     * @param redisService
     * @param cacheKey
     * @param businessCode
     * @return
     */
    public static Boolean jungleBusinessExist(RedisService redisService, String cacheKey, String businessCode) {
        try {
            String cacheMapValue = redisService.getCacheMapValue(cacheKey, businessCode);
            if(cacheMapValue !=null && !cacheMapValue.isEmpty()){
                return false;
            }
        }catch (Exception e){
            log.info("redis 取业务线缓存error");
            return true;
        }
        return true;
    }


    /**
     * 根据businessName找key值
     * @param redisService redisService
     * @param cacheKey cacheKey
     * @param businessName businessName
     * @return businessKey
     */
    public static String findBusinessKeysWithValue(RedisService redisService,String cacheKey,String businessName) {
        // 获取哈希表中的所有字段和值
        Map<String, String> hashMap = redisService.getCacheMap(cacheKey);

        // 遍历哈希表
        for (Map.Entry<String, String> entry : hashMap.entrySet()) {
            String field = entry.getKey(); // 字段（key）
            String value = entry.getValue(); // 值

            // 如果值是 "VCS"，输出对应的字段（key）
            if (businessName.equals(value)) {
                return field;
            }
        }
        log.info("没有对应的VCS的BUSINESS_CODE值，返回默认值BUSINESS:001");
        return BUSINESS_DEFAULT_KEY;
    }
}

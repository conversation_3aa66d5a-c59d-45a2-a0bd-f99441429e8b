package com.jlr.ecp.order.service.independent;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentOrderItemDO;
import com.jlr.ecp.order.dal.mysql.independent.OrderIndependentOrderItemDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> <PERSON>yi
 */
@Service
@Slf4j
public class OrderIndependentOrderItemDOServiceImpl extends ServiceImpl<OrderIndependentOrderItemDOMapper, OrderIndependentOrderItemDO>
        implements OrderIndependentOrderItemDOService {

}
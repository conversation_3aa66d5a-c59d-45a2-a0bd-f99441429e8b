package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款券状态枚举
 */

@AllArgsConstructor
@Getter
public enum RefundCouponStatusEnum {

    /**
     * 发起退款申请
     */
    INITIATE_REFUND_REQUEST(90101, "发起退款申请","退款申请已提交，请耐心等待","发起退款申请"),

    /**
     * 拒绝退款申请
     */
    REJECT_REFUND_REQUEST(90701, "拒绝退款申请","","拒绝退款申请"),

    /**
     * 分账退款处理中
     */
    SPLIT_REFUND_PROCESSING(90301, "分账退款处理中","","分账退款处理中"),

    /**
     * 退款处理中
     */
    REFUND_PROCESSING(90302, "退款处理中","处理中,请耐心等待","退款处理中"),

    /**
     * 退款完成售后关闭
     */
    REFUND_COMPLETED(90501, "售后关闭，已退款","钱款已退回","退款成功"),

    /**
     * 买家已撤销退款申请
     */
    BUYER_CANCELED(90702, "买家已撤销退款申请","很抱歉，商家无法支持您本次退货退款申请，有问题请联系客服处理，祝您购物愉快","买家已撤销退款申请");

    private final int code;
    private final String name;

    /**
     * 在前端的详细描述信息
     */
    private final String detailDescriptionOnFrontend;

    /**
     * 在app端显示的
     */
    private final String nameOnApp;



    /**
     * 根据状态码获取枚举实例
     * @param code 状态编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static RefundCouponStatusEnum getByCode(int code) {
        for (RefundCouponStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return RefundCouponStatusEnum.REFUND_COMPLETED;
    }

}

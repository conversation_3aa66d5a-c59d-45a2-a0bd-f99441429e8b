package com.jlr.ecp.order.dal.mysql.feedback;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackDimensionsDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 评价维度配置表Mapper
 */
@Mapper
public interface FeedbackDimensionsDOMapper extends BaseMapperX<FeedbackDimensionsDO> {
    default List<FeedbackDimensionsDO> selectListByFeedBackCode(String feedbackCode){
        return selectList(new LambdaQueryWrapperX<FeedbackDimensionsDO>()
                .eq(FeedbackDimensionsDO::getFeedbackCode,feedbackCode)
                .eq(BaseDO::getIsDeleted,false)
                .orderByAsc(FeedbackDimensionsDO::getSort));
    }


    default void deleteFeedbackDimensions(String feedbackCode){
        //逻辑删除，把IsDeleted设置为true

    }
}

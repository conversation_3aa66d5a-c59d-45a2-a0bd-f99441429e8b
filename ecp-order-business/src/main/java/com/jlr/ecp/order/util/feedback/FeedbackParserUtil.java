package com.jlr.ecp.order.util.feedback;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.order.api.order.vo.feedback.Option;
import com.jlr.ecp.order.api.order.vo.feedback.OrderFeedbackDimension;
import com.jlr.ecp.order.api.order.vo.feedback.OrderFeedbackInfo;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackRecordsDO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class FeedbackParserUtil {
    /**
     * 解析反馈记录为 OrderFeedbackInfo 对象
     * [
     *     {
     *         "name": "订单完成速度",
     *         "type": 0,
     *         "mustInput": 0,
     *         "sort": 1,
     *         "result": [
     *             "5"
     *         ]
     *     },
     *     {
     *         "name": "小程序界面便捷度",
     *         "type": 0,
     *         "mustInput": 0,
     *         "sort": 2,
     *         "result": [
     *             "4"
     *         ]
     *     },
     *     {
     *         "name": "渠道",
     *         "type": 2,
     *         "mustInput": 0,
     *         "optionJson": [
     *             {
     *                 "option": "ICR APP",
     *                 "sort": 1
     *             },
     *             {
     *                 "option": "广告",
     *                 "sort": 2
     *             }
     *         ],
     *         "sort": 2,
     *         "result": [
     *             "广告",
     *             "ICR APP"
     *         ]
     *     }
     * ]
     *
     * @param record 反馈记录
     * @return OrderFeedbackInfo 对象
     */
    public static OrderFeedbackInfo parseFeedbackRecord(FeedbackRecordsDO record) {
        OrderFeedbackInfo feedbackInfo = new OrderFeedbackInfo();
        feedbackInfo.setFeedbackDimensions(record.getFeedbackDimensions());
        feedbackInfo.setTotal(record.getTotal());
        feedbackInfo.setInputExtra(record.getInputExtra());

        // 解析 feedback_json 字段
        String feedbackJson = record.getFeedbackJson();
        try {
            JSONArray jsonArray = JSON.parseArray(feedbackJson);
            List<OrderFeedbackDimension> dimensions = new ArrayList<>();
            for (Object obj : jsonArray) {
                JSONObject dimensionJson = (JSONObject) obj;

                OrderFeedbackDimension dimension = new OrderFeedbackDimension();
                dimension.setName(dimensionJson.getString("name"));
                dimension.setType(dimensionJson.getIntValue("type"));
                dimension.setMustInput(dimensionJson.getIntValue("mustInput"));
                dimension.setSort(dimensionJson.getIntValue("sort"));

                // 处理选项列表
                if (dimensionJson.containsKey("optionJson")) {
                    JSONArray optionJsonArray = dimensionJson.getJSONArray("optionJson");
                    List<Option> optionList = new ArrayList<>();
                    for (Object optionObj : optionJsonArray) {
                        JSONObject optionJson = (JSONObject) optionObj;
                        Option option = new Option();
                        option.setOption(optionJson.getString("option"));
                        option.setSort(optionJson.getIntValue("sort"));
                        optionList.add(option);
                    }
                    dimension.setOptionJson(optionList);
                }

                // 处理结果列表
                if (dimensionJson.containsKey("result")) {
                    JSONArray resultJsonArray = dimensionJson.getJSONArray("result");
                    List<String> resultList = new ArrayList<>();
                    for (Object resultObj : resultJsonArray) {
                        resultList.add(resultObj.toString());
                    }
                    dimension.setResult(resultList);
                }

                dimensions.add(dimension);
            }
            feedbackInfo.setDimensionsContent(dimensions);
        } catch (Exception e) {
            log.error("解析 feedback_json 字段异常, feedback_json={}, 原因:", feedbackJson, e);
        }

        return feedbackInfo;
    }

//    public static void main(String[] args) {
//        // 创建模拟的 FeedbackRecordsDO 对象
//        FeedbackRecordsDO feedbackRecordsDO = createMockFeedbackRecordsDO();
//        //feedbackJson String转成List<OrderFeedbackDimension> list
//
//
//        List<OrderFeedbackDimension> dimensions = JSONArray.parseArray(feedbackRecordsDO.getFeedbackJson(), OrderFeedbackDimension.class);
//        System.out.println(JSON.toJSONString(dimensions));
//        // 调用 parseFeedbackRecord 方法进行测试
//        OrderFeedbackInfo orderFeedbackInfo = parseFeedbackRecord(feedbackRecordsDO);
//
//        // 输出结果以验证解析是否正确
//        System.out.println(JSON.toJSONString(orderFeedbackInfo));
//    }

    private static FeedbackRecordsDO createMockFeedbackRecordsDO() {
        FeedbackRecordsDO feedbackRecordsDO = new FeedbackRecordsDO();

        // 设置反馈维度、总分、额外输入等字段
        feedbackRecordsDO.setFeedbackDimensions("订单完成");
        feedbackRecordsDO.setTotal(42);
        feedbackRecordsDO.setInputExtra("这是一个额外输入");

        // 模拟 feedback_json 字段内容
        String feedbackJson = "[{\"name\":\"订单完成速度\",\"type\":0,\"mustInput\":0,\"sort\":1,\"result\":[\"5\"]},{\"name\":\"小程序界面便捷度\",\"type\":0,\"mustInput\":0,\"sort\":2,\"result\":[\"4\"]},{\"name\":\"渠道\",\"type\":2,\"mustInput\":0,\"optionJson\":[{\"option\":\"ICR APP\",\"sort\":1},{\"option\":\"广告\",\"sort\":2}],\"sort\":2,\"result\":[\"广告\",\"ICR APP\"]}]\n";

        feedbackRecordsDO.setFeedbackJson(feedbackJson);

        return feedbackRecordsDO;
    }
}

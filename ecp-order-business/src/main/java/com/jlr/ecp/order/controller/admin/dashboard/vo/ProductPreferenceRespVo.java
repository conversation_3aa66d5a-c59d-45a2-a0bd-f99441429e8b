package com.jlr.ecp.order.controller.admin.dashboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "dashboard-虚拟产品偏好")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductPreferenceRespVo {
  @Schema(description = "产品销售趋势图")
  private ProductSalesTrendRespVo left;

  @Schema(description = "产品及不同属性销售占比")
  private ProductSalesProportionByAttributesRespVo right;
}

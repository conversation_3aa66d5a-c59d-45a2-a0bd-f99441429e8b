package com.jlr.ecp.order.service.order.customer.order.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.order.dal.dataobject.customer.order.CustomerServiceOrderDO;
import com.jlr.ecp.order.dal.mysql.customer.order.CustomerServiceOrderDOMapper;
import com.jlr.ecp.order.service.order.customer.order.CustomerServiceOrderDOService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【t_customer_service_order(t_customer_service_order)】的数据库操作Service实现
 * @createDate 2023-12-20 10:41:04
 */
@Service
@Slf4j
public class CustomerServiceOrderDOServiceImpl extends ServiceImpl<CustomerServiceOrderDOMapper, CustomerServiceOrderDO>
        implements CustomerServiceOrderDOService {

    // 在这里实现额外的业务逻辑方法
}
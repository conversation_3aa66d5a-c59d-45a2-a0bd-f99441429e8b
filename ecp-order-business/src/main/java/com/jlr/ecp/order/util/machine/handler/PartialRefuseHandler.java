package com.jlr.ecp.order.util.machine.handler;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusLogDOMapper;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;


/**
 * 部分退单拒绝事件处理
 * <AUTHOR>
 */
@Component
public class PartialRefuseHandler implements EventHandler {

    @Resource
    private OrderStatusLogDOMapper statusLogMapper;

    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO) {
        //拒绝部分退单事件处理...
        if(orderRefundDO.getRefundOrderStatus().equals(OrderRefundStatusEnum.PARTIAL_REFUND_APPLY.getCode())){
            orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.PARTIAL_REFUND_REFUSE.getCode());
        }
        //订单状态:售后中
        if(orderInfoDO.getOrderStatus().equals(OrderStatusEnum.AFTER_SALES.getCode())){
            List<OrderStatusLogDO> lastCompleteStep = statusLogMapper.findLastCompleteStep(orderInfoDO.getOrderCode());
            //没有日志记录则订单完成
            if(CollectionUtils.isEmpty(lastCompleteStep)){
                orderInfoDO.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
            }else {
                // 返回上一次 orderStatus 的完成节节点
                OrderStatusLogDO orderStatusLogDO = lastCompleteStep.get(0);
                orderInfoDO.setOrderStatus(orderStatusLogDO.getAfterStatus());
            }

        }
    }

}

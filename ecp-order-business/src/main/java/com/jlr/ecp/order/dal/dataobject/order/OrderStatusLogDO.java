package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_order_status_log
 *
 * @TableName t_order_status_log
 */
@TableName(value = "t_order_status_log")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderStatusLogDO extends BaseDO {
    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 订单号;订单号
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 当前状态;变更前状态
     */
    @TableField(value = "before_status")
    private Integer beforeStatus;

    /**
     * 变更后状态;变更后状态
     */
    @TableField(value = "after_status")
    private Integer afterStatus;

    /**
     * 变更时间;变更时间
     */
    @TableField(value = "change_time")
    private LocalDateTime changeTime;
}
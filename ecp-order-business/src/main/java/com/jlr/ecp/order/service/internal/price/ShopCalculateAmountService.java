package com.jlr.ecp.order.service.internal.price;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil;
import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.dal.dataobject.cart.ShoppingCarItemDO;
import com.jlr.ecp.order.dal.mysql.cart.ShoppingCarItemMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.service.internal.promotion.PromotionalFactory;
import com.jlr.ecp.order.service.internal.promotion.PromotionalStrategy;
import com.jlr.ecp.order.service.internal.promotion.dto.PromotionRespDto;
import com.jlr.ecp.order.util.money.MoneyUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.internal.productprice
 * @className: PriceToolService
 * @author: gaoqig
 * @description: 计算方法
 * @date: 2025/3/6 10:22
 * @version: 1.0
 */
@Component
@Slf4j
public class ShopCalculateAmountService {
    @Resource
    private ShoppingCarItemMapper carItemMapper;


    /***
     * <AUTHOR>
     * @description 检查购物车商品是否正常(数量一致，均属于当前用户)，如果正常返回正常商品列表
     * @date 2025/3/6 10:22
     * @param cartItemCodeList 购物车商品编码列表
     * @param customerCode 客户编码
     * @return: java.util.List<com.jlr.ecp.order.dal.dataobject.cart.ShoppingCarItemDO> 购物车商品列表
     */
    public List<ShoppingCarItemDO> checkCartItem(List<String> cartItemCodeList, String customerCode){
        if (cartItemCodeList.isEmpty()){
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.CART_ITEM_EMPTY);
        }
        //检查购物车商品是否存在
        LambdaQueryWrapper<ShoppingCarItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ShoppingCarItemDO::getCartItemCode, cartItemCodeList)
                .eq(ShoppingCarItemDO::getConsumerCode, customerCode);
        List<ShoppingCarItemDO> shoppingCarItemDOS = carItemMapper.selectList(queryWrapper);

        if (shoppingCarItemDOS.size() != cartItemCodeList.size()){
            log.warn("商品数量与系统不符，传输购物车商品数量为:{},系统查询到的商品数量为:{}", cartItemCodeList.size(), shoppingCarItemDOS.size());
            throw exception(ErrorCodeConstants.CART_ITEM_COUNT_ERROR);
        }
        return shoppingCarItemDOS;
    }

    /***
     * <AUTHOR>
     * @description 计算购物车商品的总价（原价）
     * @date 2025/3/6 14:54
     * @param shoppingCarItems:
     * @return: java.math.BigDecimal
    */

    public BigDecimal calculateCartItemAmount(List<CartProductSkuInfo> shoppingCarItems, boolean isFromShopCartPage) {
        if (shoppingCarItems.isEmpty()){
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.CART_ITEM_EMPTY);
        }

        return shoppingCarItems.stream()
                .filter(x-> isFromShopCartPage || x.isJoinCalculateFlag()) //如果是来自购物车页面，则全部计算，否则只计算参与的
                .map(item -> MoneyUtil.convertToCents(item.getSalePrice()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /***
     * <AUTHOR>
     * @description 综合计算优惠
     * @date 2025/3/6 18:24
     * @param skuInfoList: skuList，注意：改列表可能在执行该方法后被修改
     * @param couponList: 优惠券列表，包括积分也算作一种优惠券
     * @param userChoosePromotion: 用户选择的优惠券，如果为空，则说明用户没有选择优惠券，采用最优计算
     * @return: java.math.BigDecimal 命中优惠后的信息，如果为空，则说明没有命中，需要调用方自行判断
    */

    public PromotionRespDto compareCouponAmount(List<CartProductSkuInfo> skuInfoList, List<PromotionDto> couponList, PromotionDto userChoosePromotion, Integer paymentType){

        if (CollUtil.isNotEmpty(couponList)) {
            if (userChoosePromotion != null ) { //如果用户券类型已经被指定，则使用指定的方法进行计算
                CouponTypeEnum userChooseCouponType = getCouponTypeEnum(skuInfoList, couponList, userChoosePromotion);

                PromotionRespDto finalPromotionRespDto = null;
                finalPromotionRespDto = getPromotionRespDto(skuInfoList, couponList, userChoosePromotion, userChooseCouponType, finalPromotionRespDto);
                return finalPromotionRespDto;
            } else {
                PromotionRespDto maxPromotionRespDto = null;
                couponList = couponList.stream().sorted(Comparator.comparing(PromotionDto::getValidEndTime, Comparator.nullsLast(Comparator.naturalOrder()))
                                .thenComparing(x->CouponTypeEnum.getByType(x.getCouponModelClassify()).getPriority(),Comparator.reverseOrder())) // 再按 类型优先级 排序
                        .collect(Collectors.toList());

                for (PromotionDto promotionDto : couponList) {
                    CouponTypeEnum thisCouponTypeEnum = getCouponTypeEnum(promotionDto);
                    PromotionRespDto promotionRespDto = getPromotionRespDto(skuInfoList, promotionDto, thisCouponTypeEnum);
                    maxPromotionRespDto = getFinaMaxPromotionRespDto(paymentType, promotionDto, promotionRespDto, maxPromotionRespDto);
                }
                return maxPromotionRespDto;
            }

        }
        return null;
    }

    private static PromotionRespDto getFinaMaxPromotionRespDto(Integer paymentType, PromotionDto promotionDto, PromotionRespDto promotionRespDto, PromotionRespDto maxPromotionRespDto) {
        if (promotionRespDto == null) return maxPromotionRespDto;
        //与当前最大优惠比较，更优惠则采纳
        if (paymentType == null) {//如果用户没有选择支付方式，则按以前的逻辑来计算最优
            maxPromotionRespDto = getMaxPromotionRespDto(promotionDto, maxPromotionRespDto, promotionRespDto);
        } else {
            if (promotionRespDto.getCouponTypeEnum().coverToCarpaymentTypeEnum().getCode().equals(paymentType)){//如果用户选择了支付方式，则返回的优惠券类型必须是用户选择的才行，否则pass
                maxPromotionRespDto = getMaxPromotionRespDto(promotionDto, maxPromotionRespDto, promotionRespDto);
            }
        }
        return maxPromotionRespDto;
    }

    @Nullable
    private static PromotionRespDto getPromotionRespDto(List<CartProductSkuInfo> skuInfoList, PromotionDto promotionDto, CouponTypeEnum thisCouponTypeEnum) {
        if (thisCouponTypeEnum == null) return null;
        PromotionalStrategy promotional = PromotionalFactory.getPromotional(thisCouponTypeEnum);
        PromotionRespDto promotionRespDto = promotional.executePromotional(skuInfoList, promotionDto);

        //如果计算出了的结果为空，或者优惠金额为空，则认为没有命中
        if (promotionRespDto == null || new BigDecimal(promotionRespDto.getDiscountTotalAmount()).compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        return promotionRespDto;
    }

    @Nullable
    private static CouponTypeEnum getCouponTypeEnum(PromotionDto promotionDto) {
        if (promotionDto == null || promotionDto.getCouponModelClassify() == null) {
            return null;
        }
        CouponTypeEnum thisCouponTypeEnum = CouponTypeEnum.getByType(promotionDto.getCouponModelClassify());
        if (thisCouponTypeEnum == null) {//实际上不会命中这个条件，如果命中，说明传进来的优惠券列表过滤有问题
            return null;
        }
        return thisCouponTypeEnum;
    }

    private static PromotionRespDto getMaxPromotionRespDto(PromotionDto promotionDto, PromotionRespDto maxPromotionRespDto, PromotionRespDto promotionRespDto) {
        if (maxPromotionRespDto == null) {
            maxPromotionRespDto = promotionRespDto;
        } else if (new BigDecimal(maxPromotionRespDto.getCostAmount()).compareTo(new BigDecimal(promotionRespDto.getCostAmount())) > 0) { //如果比当前最优方案花费的金额少，则直接采纳
            maxPromotionRespDto = promotionRespDto;
        } else if (new BigDecimal(maxPromotionRespDto.getCostAmount()).compareTo(new BigDecimal(promotionRespDto.getCostAmount())) == 0) {//如果金额相等了，则积分最优先，然后按排序来（排序前面已经做了所以先忽略）
            if (CouponTypeEnum.POINTS.getType().equals(promotionDto.getCouponModelClassify())) {
                maxPromotionRespDto = promotionRespDto;
            }
        }
        return maxPromotionRespDto;
    }

    private static PromotionRespDto getPromotionRespDto(List<CartProductSkuInfo> skuInfoList, List<PromotionDto> couponList, PromotionDto userChoosePromotion, CouponTypeEnum userChooseCouponType, PromotionRespDto finalPromotionRespDto) {
        for (PromotionDto promotionDto : couponList) {
            CouponTypeEnum thisCouponTypeEnum = CouponTypeEnum.getByType(promotionDto.getCouponModelClassify());
            if (thisCouponTypeEnum == null) {//实际上不会命中这个条件，如果命中，说明传进来的优惠券列表过滤有问题
                continue;
            }
            PromotionalStrategy promotional = PromotionalFactory.getPromotional(thisCouponTypeEnum);
//            if (CouponTypeEnum.POINTS.equals(thisCouponTypeEnum)){
//                promotionDto.setRuleType(0);
//            }
            PromotionRespDto promotionRespDto = promotional.executePromotional(skuInfoList, promotionDto);
            //如果计算出了的结果为空，或者优惠金额为空，则认为没有命中
            if (promotionRespDto == null || new BigDecimal(promotionRespDto.getDiscountTotalAmount()).compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            if (CouponTypeEnum.POINTS.equals(thisCouponTypeEnum)){//如果当前是积分计算
                if (userChooseCouponType.equals(CouponTypeEnum.POINTS)){//如果用户选择的也是积分，则直接赋值。否则啥事不用做
                    finalPromotionRespDto = promotionRespDto;
                }
            } else if(promotionRespDto.getChooseCoupon().getCouponCode().equals(userChoosePromotion.getCouponCode())){//如果当前计算的优惠券是用户选择的，则最终返回的就是这个，但是遍历依然要进行，因为需要计算所有优惠券的优惠金额
                finalPromotionRespDto = promotionRespDto;
            }
        }
        return finalPromotionRespDto;
    }

    @NotNull
    private static CouponTypeEnum getCouponTypeEnum(List<CartProductSkuInfo> skuInfoList, List<PromotionDto> couponList, PromotionDto userChoosePromotion) {
        CouponTypeEnum userChooseCouponType = CouponTypeEnum.getByType(userChoosePromotion.getCouponModelClassify());
        if (userChooseCouponType == null){
            log.info("用户选择的优惠券类型编码为:{},暂不支持", userChoosePromotion.getCouponModelClassify());
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.NOT_SUPPORT_COUPON);
        }
        //检查用户的选择是否合理
        PromotionalStrategy userChoosePromotionalStrategy = PromotionalFactory.getPromotional(userChooseCouponType);
        userChoosePromotionalStrategy.checkUserChoose(skuInfoList, couponList, userChoosePromotion);//如果当前用户
        return userChooseCouponType;
    }
}

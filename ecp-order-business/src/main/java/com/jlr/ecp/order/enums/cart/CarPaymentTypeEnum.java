package com.jlr.ecp.order.enums.cart;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.enums.cart
 * @enumName: CarItemPaymentTypeEnum
 * @author: gaoqig
 * @description: 购物车支付方式枚举
 * @date: 2025/3/6 15:08
 * @version: 1.0
 */
@AllArgsConstructor
@Getter
public enum CarPaymentTypeEnum {
    /**
     * 现金
     */
    CASH(1, "现金"),
    /**
     * 积分
     */
    POINT(2, "积分"),
    /**
     * 优惠券
     */
    COUPON(3, "优惠券")
    ;

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String description;

}

package com.jlr.ecp.order.constant;

/**
 * Constants
 *
 * <AUTHOR> <PERSON>
 * @since 2025-03-18 14:54:57
 */
public class Constants {

    /**
     * JLR ID Header
     */
    public static final String JLR_ID_HEADER = "jlrId";

    /**
     * 代客下单 初始化 consumerCode 为"CUSTOMER_SERVICE_ORDER"
     */
    public static final String CUSTOMER_SERVICE_ORDER = "CUSTOMER_SERVICE_ORDER";


    /**
     * 业务线缓存key
     */
    public static final String BUSINESS_CACHE_KEY = "global:business:info";

    /**
     * redis分布式锁的key
     * */
    public static final String REDIS_LOCK_KEY = "redis:lock:key:";

    public static final String REDIS_CANCEL_ORDER_LOCK_KEY = "redis:lock:key:cancel:order";

    public static final String REDIS_ORDER_INDEPENDENT_LOCK_KEY = "redis:lock:key:independent:order:";

    /**
     * redis分布式锁的key: 优惠券已核销，发送topic
     * */
    public static final String COUPON_STATUS_USED_REDIS_LOCK_KEY = "redis:lock:key:coupon:used";

    /**
     * redis分布式锁的key: 优惠券取消/作废，发送topic
     * */
    public static final String COUPON_STATUS_CANCEL_REDIS_LOCK_KEY = "redis:lock:key:coupon:cancel";

    /**
     * 超时取消订单的幂等校验key
     * */
    public static final String TIMEOUT_CANCEL_ORDER_KEY = "timeout:cancel:order:key:";

    /**
     * 退单幂等校验key
     * */
    public static final String REFUND_IDEMPOTENT_KEY = "refund:idempotent:key:";

    /**
     * LRE退单幂等校验key
     * */
    public static final String LRE_REFUND_IDEMPOTENT_KEY = "refund:lre:idempotent:key:";

    /**
     * BG退单幂等校验key
     * */
    public static final String BG_REFUND_IDEMPOTENT_KEY = "refund:bg:idempotent:key:";

    /**
     * 正在支付中的订单号key
     */
    public static final String PAYING_ORDER_KEY = "global:paying:order:key:";

    public static final String SUCCESS_PAYING_ORDER_KEY = "success:payStatus:";
    public static final String LAND_ROVER_MINI_PROGRAM = "路虎小程序";
    public static final String JAGUAR_MINI_PROGRAM = "捷豹小程序";
    public static final String LIMIT_ONE = "limit 1";
    public static final String CONCAT_SYMBOL = "::";

    public static final String SHORT_LINK_QUERY = "&shortLink=1";

    // 小程序跳转标识
    public static final Integer SHORT_LINK_IDENTIFIER = 1;

    /**
     * ========== order相关 ==========
     */
    public static final String ORDER_CREATE_SUCCESS_MESSAGE = "订单创建成功";
    public static final String ORDER_DELETE_SUCCESS_MESSAGE = "订单刪除成功";
    public static final String ORDER_EDIT_SUCCESS_MESSAGE = "订单编辑成功";
    public static final int PAY_DUE_TIME = 15 * 60 * 1000;
    public static final int PAYING_ORDER_REDIS_DUE_TIME = 16 * 60 * 1000;

    /**
     * VCS支付记录超时时间：2.5小时
     */
    public static final long VCS_PAY_TIMEOUT_MILLS = 5 * 30 * 60 * 1000L;

    /**
     * BG和LRE支付记录超时时间：15分钟
     */
    public static final long BG_LRE_PAY_TIMEOUT_MILLS = 15 * 60 * 1000L;
    public static final String ORDER_CREATE_RESPONSE = "创建订单返回结果orderCreateRespVO:{}";
    public static final String SEND_KAFKA_ERROR = "send kafka msg error:{}";

    public static final String PERMANENT_VALIDITY = "永久有效";


    /**
     * ========购物车相关=======
     */
    public static final String CART_CREATE_SUCCESS_MESSAGE = "购物车添加成功";
    public static final String CART_UPDATE_SUCCESS_MESSAGE = "购物车添加成功";
    public static final String CART_DELETE_SUCCESS_MESSAGE = "购物车商品删除成功";



    /**
     * ========退单相关=======
     */
    public static final String REFUND_APPLY_SUCCESS_MESSAGE = "取消订单提交成功";
    public static final String REFUND_APPROVE_SUCCESS_MESSAGE = "取消订单审核完成";
//    public static final String CART_DELETE_SUCCESS_MESSAGE = "购物车商品删除成功";

    public static final String SYSTEM="system";



    /**
     *  redis key
     */
    public static class REDIS_KEY {
        /**
         * 车型编码映射缓存
         */
        public static final String SERIES_CACHE_KEY = "global:series:mapping";

        /**
         * icr校验标记 icr-check:jlrId:carVin
         */
        public static final String ICR_CHECK_CACHE_KEY = "icr-check:";

        /**
         * icr有效结果 icr-valid:jlrId:carVin
         */
        public static final String ICR_VALID_CACHE_KEY = "icr-valid:";
    }

    public static final String AUTH_CODE_VIEW_PHONE = "trade:order:view-phone";
    public static final String AUTH_CODE_VIEW_ICR = "trade:order:view-icr";
    public static final String AUTH_CODE_VIEW_VIN = "trade:order:view-vin";




    /***
     *  ========评价配置相关=======
     */
    public static final String FEEDBACK_CREATE_SUCCESS_MESSAGE = "评价配置创建成功";
    public static final String FEEDBACK_UPDATE_SUCCESS_MESSAGE = "评价配置编辑成功";
    public static final String FEEDBACK_DELETE_SUCCESS_MESSAGE = "评价配置刪除成功";
    public static final String FEEDBACK_ENABLE_SUCCESS_MESSAGE = "评价配置启用成功";
    public static final String FEEDBACK_DISABLE_SUCCESS_MESSAGE = "评价配置停用成功";

    // 快递100 签收
    public static final String KUAIDI100_STATE_SIGN = "3";

    /**
     * 小程序-购物车列表, LRE + BG 合并Code, 前端使用该Key取值
     */
    public static final String MIXED = "MIXED";

}

package com.jlr.ecp.order.controller.admin.sqs;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.service.coupon.status.listen.LREStatusListener;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.controller.admin.sqs
 * @className: SqsController
 * @author: gaoqig
 * @description: 控制LRE商品状态监听启停
 * @date: 2025/3/17 18:39
 * @version: 1.0
 */
@Tag(name = "预留开关 - 控制LRE商品状态监听启停")
@RestController
@RequestMapping("v1/sqsManage")
@Validated
@ConditionalOnProperty(name = "spring.cloud.aws.sqs.enabled", havingValue = "true", matchIfMissing = true)
public class SqsController {
    @Resource
    LREStatusListener lreStatusListener;

    /**
     * 关闭监听LRE优惠券状态变化
     *
     * @return ProductSalesTrendRespVo
     */
    @GetMapping("/stop")
    @Operation(summary = "关闭监听LRE优惠券状态变化")
    CommonResult<Boolean> stopListen() {
        lreStatusListener.stopListener();
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 关闭监听LRE优惠券状态变化
     *
     * @return ProductSalesTrendRespVo
     */
    @GetMapping("/start")
    @Operation(summary = "开启监听LRE优惠券状态变化")
    CommonResult<Boolean> startListen() {
        lreStatusListener.startListener();
        return CommonResult.success(Boolean.TRUE);
    }
}

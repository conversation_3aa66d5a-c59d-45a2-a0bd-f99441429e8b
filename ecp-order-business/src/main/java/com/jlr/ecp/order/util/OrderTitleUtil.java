package com.jlr.ecp.order.util;

import cn.hutool.core.collection.CollUtil;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

@Slf4j
public class OrderTitleUtil {

    public static String getOrderTitle(Set<String> businessCodes) {
        if (CollUtil.isEmpty(businessCodes)) {
            log.error("业务线转换失败，businessCodes={}", businessCodes);
            throw new RuntimeException("业务线转换失败");
        }

        if (businessCodes.contains(BusinessIdEnum.BRAND_GOODS.getCode()) && businessCodes.contains(BusinessIdEnum.LRE.getCode()) && businessCodes.contains(BusinessIdEnum.VCS.getCode())) {
            return "Branded Goods+Land Rover Experience+VCS";
        } else if (businessCodes.contains(BusinessIdEnum.BRAND_GOODS.getCode()) && businessCodes.contains(BusinessIdEnum.LRE.getCode())) {
            return "Branded Goods+Land Rover Experience";
        } else if (businessCodes.contains(BusinessIdEnum.BRAND_GOODS.getCode()) && businessCodes.contains(BusinessIdEnum.VCS.getCode())) {
            return "Branded Goods+VCS";
        } else if (businessCodes.contains(BusinessIdEnum.LRE.getCode()) && businessCodes.contains(BusinessIdEnum.VCS.getCode())) {
            return "Land Rover Experience+VCS";
        } else if (businessCodes.contains(BusinessIdEnum.LRE.getCode())) {
            return "Land Rover Experience";
        } else if (businessCodes.contains(BusinessIdEnum.BRAND_GOODS.getCode())) {
            return "Branded Goods";
        } else if (businessCodes.contains(BusinessIdEnum.VCS.getCode())) {
            return "VCS";
        } else {
            log.error("业务线转换失败，businessCodes={}", businessCodes);
            throw new RuntimeException("业务线转换失败");
        }
    }

}
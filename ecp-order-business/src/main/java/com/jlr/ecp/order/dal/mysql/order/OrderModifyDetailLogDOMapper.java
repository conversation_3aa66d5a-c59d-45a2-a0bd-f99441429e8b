package com.jlr.ecp.order.dal.mysql.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.order.dal.dataobject.order.OrderModifyDetailLogDO;
import com.jlr.ecp.order.enums.order.OrderModifyLogEnum;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @description 针对表【t_order_modify_detail_log(t_order_modify_detail_log)】的数据库操作Mapper
* @createDate 2023-12-20 10:41:04
* @Entity com.jlr.ecp.order.dal.dataobject.order.OrderModifyDetailLogDO
*/
@Mapper
public interface OrderModifyDetailLogDOMapper extends BaseMapperX<OrderModifyDetailLogDO> {

    /**
     * 保存订单操作日志
     * @param orderCode 订单号
     * @param content 内容
     * @return
     */
    default void createModifyLog(String orderCode, String content, String oldVal, String newVal){
        OrderModifyDetailLogDO modifyDetailLogDO = new OrderModifyDetailLogDO();
        modifyDetailLogDO.setOrderCode(orderCode);
        modifyDetailLogDO.setModifyModule(content);
        int modifyFieldCount = 1;
        if(OrderModifyLogEnum.contains(content)){
            modifyFieldCount = oldVal != null ? JSON.parseObject(oldVal).size() : 0;
        }
        modifyDetailLogDO.setModifyFieldCount(modifyFieldCount);
        modifyDetailLogDO.setModifyFieldOldValue(oldVal);
        modifyDetailLogDO.setModifyFieldNewValue(newVal);
        modifyDetailLogDO.setOperateUser(WebFrameworkUtils.getLoginUserName());
        modifyDetailLogDO.setOperateTime(LocalDateTime.now());
        insert(modifyDetailLogDO);
    }
}





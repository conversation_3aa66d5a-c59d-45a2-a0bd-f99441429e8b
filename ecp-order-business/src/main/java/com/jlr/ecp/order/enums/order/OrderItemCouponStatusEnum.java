package com.jlr.ecp.order.enums.order;

/**
 * 优惠券履约订单状态枚举
 */
public enum OrderItemCouponStatusEnum {

    /**
     * 待发放
     */
    PENDING_ISSUANCE(1, "待发放"),

    /**
     * 待核销
     */
    PENDING_VERIFICATION(2, "待核销"),

    /**
     * 已核销
     */
    VERIFIED(3, "已核销"),

    /**
     * 已回收
     */
    RECLAIMED(4, "已回收");

    private final int code;
    private final String name;

    OrderItemCouponStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据状态码获取枚举实例
     * @param code 状态编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static OrderItemCouponStatusEnum getByCode(int code) {
        for (OrderItemCouponStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 严格校验模式（推荐生产环境使用）
     * @throws IllegalArgumentException 当code无效时抛出异常
     */
    public static OrderItemCouponStatusEnum getByCodeStrict(int code) {
        OrderItemCouponStatusEnum status = getByCode(code);
        if (status == null) {
            throw new IllegalArgumentException("无效的优惠券状态码: " + code);
        }
        return status;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

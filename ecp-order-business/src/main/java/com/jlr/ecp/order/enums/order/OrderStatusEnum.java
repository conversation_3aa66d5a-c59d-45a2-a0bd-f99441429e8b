package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum OrderStatusEnum {

    /**
     * 已下单
     * 订单状态；1：已下单
     */
    ORDERED(1, "已下单"),

    /**
     * 已支付
     * 订单状态；2：已支付
     */
    PAID(2, "已支付"),

    /**
     * 订单完成
     * 订单状态；3：订单完成
     */
    COMPLETED(3, "订单完成"),

    /**
     * 订单关闭
     * 订单状态；4：订单关闭
     */
    CLOSED(4, "订单关闭"),


    /**
     * 售后处理中
     */
    AFTER_SALES(5, "售后处理中"),

    /**
     * 订单部分取消
     * 订单状态；6：订单部分取消
     */
    PARTIAL_CANCELLED(6, "订单部分取消"),

    /**
     * 订单整单取消
     * 订单状态；7：订单整单取消
     */
    FULLY_CANCELLED(7, "订单整单取消");

    private final Integer code;
    private final String description;

    /**
     * 根据订单状态的整数值获取对应的描述。
     *
     * @param code 订单状态的整数值
     * @return 对应的订单状态描述
     */
    public static String getDescriptionByCode(Integer code) {
        for (OrderStatusEnum status : OrderStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        throw new IllegalArgumentException("Invalid order status code: " + code);
    }
    public static OrderStatusEnum getEnumByCode(Integer code) {
        for (OrderStatusEnum status : OrderStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
       return null;
    }

    /**
     * 判断支付单完态
     */
    public static boolean isCancelled(Integer code){
        List<Integer> enumList = Arrays.asList(
                OrderStatusEnum.PARTIAL_CANCELLED.getCode(),
                OrderStatusEnum.FULLY_CANCELLED.getCode()
        );
        return enumList.contains(code);
    }

    /**
     * 非在途订单状态
     */
    public static List<Integer> getNotInTransitList(){
        return Arrays.asList(
                OrderStatusEnum.COMPLETED.getCode(),
                OrderStatusEnum.CLOSED.getCode(),
                OrderStatusEnum.PARTIAL_CANCELLED.getCode(),
                OrderStatusEnum.FULLY_CANCELLED.getCode()
        );
    }
}
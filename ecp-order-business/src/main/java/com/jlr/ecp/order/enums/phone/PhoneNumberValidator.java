package com.jlr.ecp.order.enums.phone;

import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 电话号码格式校验
 * */
public class PhoneNumberValidator {

    /**
     *  中国手机号码格式校验
     * @param phoneNumber  手机号码
     * @return boolean
     * */
    public static boolean isValidChinaMobileNumber(String phoneNumber) {
        // 正则表达式匹配中国大陆手机号码：以1开头，第二位为3至9之间的数字，后跟8个数字
        String regex = "^1[3-9]\\d{9}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(phoneNumber);
        return matcher.matches();
    }

    /**
     *  国际手机号码格式校验
     * @param phoneNumber 电话号码
     * @return boolean
     * */
    public static boolean isValidInternationalMobileNumber(String phoneNumber) {
        // 简化版正则表达式匹配部分国际手机号码格式：+号开头，随后是1到3位的国家代码，然后跟随一系列数字（长度因国家而异）
        // 注意：此正则不包括所有的国际手机号码格式，具体需参考E.164标准或其他详细规范
        String regex = "^\\+\\d{1,3}[\\s.-]?\\d{4,14}(?:[\\s.-]?\\d+)?$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(phoneNumber);
        return matcher.matches();
    }
}

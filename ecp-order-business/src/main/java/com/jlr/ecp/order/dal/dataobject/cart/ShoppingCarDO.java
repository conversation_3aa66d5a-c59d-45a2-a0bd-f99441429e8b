package com.jlr.ecp.order.dal.dataobject.cart;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_shopping_car(ShoppingCar)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_shopping_car")
public class ShoppingCarDO extends BaseDO {

     /**
     * 租户号
     */    
    private Integer tenantId;

     /**
     * 主键
     */
     @TableId
    private Long id;

     /**
     * 用户编码
     */    
    private String consumerCode;

     /**
     * 购物车code
     */    
    private String cartCode;

    /**
     * 购物车原价
     */
    private Long originalFeeTotal;

    /**
     * 购物车折扣价
     */
    private Long discountFeeTotal;



}


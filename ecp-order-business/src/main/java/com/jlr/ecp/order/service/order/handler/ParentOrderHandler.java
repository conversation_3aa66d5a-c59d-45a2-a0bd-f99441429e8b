package com.jlr.ecp.order.service.order.handler;

import com.jlr.ecp.order.api.order.dto.OrderCreateDTO;
import com.jlr.ecp.order.api.order.dto.OrderShopCarItemDTO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

//@Slf4j
//public class ParentOrderHandler implements OrderSplitHandler {
//    private OrderCreateDTO orderCreateDTOData = null;
//    private OrderSplitHandler nextSplitHandler = null;
//    public OrderSplitHandler setNext(OrderSplitHandler handler) {
//        nextSplitHandler = handler;
//        return nextSplitHandler;
//    }
//
//    public OrderSplitHandler getNext() {
//        return this.nextSplitHandler;
//    }
//
//    public boolean canItemMatch() {
//        return false;
//    }
//
//    public List<OrderShopCarItemDTO> matchItems() {
//        return null;
//    }
//
//
//    //依据需要生成父订单
//    public List<OrderInfoDO> handleRequest(OrderCreateDTO orderCreateDTO, List<OrderInfoDO> lastOrderInfoList) {
//
//        log.info("开始处理父订单逻辑");
//        orderCreateDTOData = orderCreateDTO;
//
//
//        log.info("结束处理父订单逻辑");
//        if(nextSplitHandler != null)
//            lastOrderInfoList = this.nextSplitHandler.handleRequest(orderCreateDTO,lastOrderInfoList);
//        return lastOrderInfoList;
//    }
//}

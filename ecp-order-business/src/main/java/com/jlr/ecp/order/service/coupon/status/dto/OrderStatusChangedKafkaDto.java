package com.jlr.ecp.order.service.coupon.status.dto;


import com.alibaba.fastjson.JSON;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.coupon.status.dto
 * @className: CouponStatusChangedKafkaDto
 * @author: Su, Hongyi
 * @description: 订单状态发生变化时
 * @date: 2025/3/24 12:19
 * @version: 1.0
 */
@Data
public class OrderStatusChangedKafkaDto {

    /**
     * 租户号
     */
    private Long TenantId;

    /**
     * 业务线编码
     */
    private String businessCode;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 变更后的状态
     */
    private Integer orderStatus;

    /**
     * 状态变更时间
     */
    private LocalDateTime updateTime;

    public static String getOrderStatusChangedKafkaDto(OrderInfoDO orderInfoDO){
        OrderStatusChangedKafkaDto dto = new OrderStatusChangedKafkaDto();
        dto.setTenantId(Long.valueOf(orderInfoDO.getTenantId()));
        dto.setBusinessCode(orderInfoDO.getBusinessCode());
        dto.setOrderCode(orderInfoDO.getOrderCode());
        dto.setOrderStatus(orderInfoDO.getOrderStatus());
        dto.setUpdateTime(orderInfoDO.getCompletedTime());
        return JSON.toJSONString(dto);
    }

}

package com.jlr.ecp.order.enums.order;



/**
 * 订单券状态枚举
 */
public enum OrderCouponStatusEnum {

    /**
     * 待支付
     */
    PENDING_PAYMENT(90101, "待支付"),

    /**
     * 待发放
     */
    PENDING_ISSUANCE(90201, "待发放"),

    /**
     * 待核销
     */
    PENDING_VERIFICATION(90202, "待核销"),

    /**
     * 已核销
     */
    VERIFIED(90301, "已核销"),

    /**
     * 订单关闭
     */
    ORDER_CLOSED(90401, "订单关闭"),

    /**
     * 售后处理中
     */
    AFTER_SALE_PROCESSING(90501, "售后处理中");

    private final Integer code;
    private final String name;

    OrderCouponStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据状态码获取枚举实例
     * @param code 状态编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static OrderCouponStatusEnum getByCode(Integer code) {
        for (OrderCouponStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    // 增强版方法（可选）
    public static OrderCouponStatusEnum getByCodeStrict(Integer code) {
        OrderCouponStatusEnum status = getByCode(code);
        if (status == null) {
            throw new IllegalArgumentException("无效的订单券状态码: " + code);
        }
        return status;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

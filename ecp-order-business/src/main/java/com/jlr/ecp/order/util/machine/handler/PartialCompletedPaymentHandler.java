package com.jlr.ecp.order.util.machine.handler;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.enums.order.RefundStatusEnum;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 完成部分退单事件处理
 * <AUTHOR>
 */
@Component
public class PartialCompletedPaymentHandler implements EventHandler {
    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO) {
        // 完成部分退单事件处理
        orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.PARTIAL_REFUND_COMPLETED.getCode());
        // 是否退款 0：否 1：是
        Integer refundMoney = orderRefundDO.getRefundMoney();
        if(Objects.equals(refundMoney, 0)){
            orderInfoDO.setRefundStatus(RefundStatusEnum.NO_REFUND.getCode());
            return;
        }
        // 判断退款金额和实际支付金额是否相等
        Integer refundMoneyAmount = orderRefundDO.getRefundMoneyAmount();
        if(Objects.isNull(refundMoneyAmount) || refundMoneyAmount == 0){
            orderInfoDO.setRefundStatus(RefundStatusEnum.NO_REFUND.getCode());
            return;
        }
        Integer costAmount = orderInfoDO.getCostAmount();
        RefundStatusEnum refundStatusEnum = refundMoneyAmount.equals(costAmount)?RefundStatusEnum.FULL_REFUND:RefundStatusEnum.PARTIAL_REFUND;
        orderInfoDO.setRefundStatus(refundStatusEnum.getCode());
    }
}

package com.jlr.ecp.order.enums.order;


/**
 * 专票状态
 */
public enum SpecialInvoiceStatusEnum {


        APPLY(1,"已申请"),
        SUCCESS(2,"已开票"),
        REVOKE(3,"已红冲"),
        REOPENED(4,"已重开");
        private final Integer code;
        private final String name;


    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    SpecialInvoiceStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
    public static SpecialInvoiceStatusEnum getByCode(int code) {
        for (SpecialInvoiceStatusEnum type : values()) {
            if (type.getCode() .equals( code)) {
                return type;
            }
        }
        return null; // 或抛出 IllegalArgumentException
    }

}

package com.jlr.ecp.order.controller.app.order;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.controller.app.order.dto.OrderLogisticsQryDTO;
import com.jlr.ecp.order.controller.app.order.vo.OrderLogisticsQryRespVO;
import com.jlr.ecp.order.service.order.OrderItemLogisticsDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

/**
 * OrderLogisticsAppController
 *
 * <AUTHOR> <PERSON>
 * @since 2025-04-03 14:42
 */
@Slf4j
@RestController
@Tag(name = "app 端 - BG 商品物流查询")
@RequestMapping("v1/order/logistics")
public class OrderLogisticsAppController {

    @Resource
    private OrderItemLogisticsDOService orderItemLogisticsDOService;

    @PermitAll
    @PostMapping("/queryOrderLogisticsInfo")
    @Operation(summary = "根据 Order Code 查询订单物流信息")
    CommonResult<OrderLogisticsQryRespVO> queryOrderLogisticsInfo(@Valid @RequestBody OrderLogisticsQryDTO orderLogisticsQryDTO) {
        log.info("查询订单物流信息, Request:{}", JSON.toJSONString(orderLogisticsQryDTO));
        OrderLogisticsQryRespVO result = orderItemLogisticsDOService.queryOrderLogisticsInfo(orderLogisticsQryDTO);
        return CommonResult.success(result);
    }
}

package com.jlr.ecp.order.enums.logistic;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.enums.logsitc
 * @className: DeliveryNotificationEnum
 * @author: gaoqig
 * @description: 发货通知类型枚举
 * @date: 2025/4/11 12:07
 * @version: 1.0
 */
@AllArgsConstructor
@Getter
public enum DeliveryNotificationEnum {
    //0-无需通知， 1- 全部发货通知 2-部分发货通知
    NONE(0, "无需通知"),
    FULLY_SHIPPED(1, "全部发货通知"),
    PARTIALLY_SHIPPED(2, "部分发货通知"),

    NONE_BUT_UPDATE(3, "无需通知，但需要更新数据");

    private final Integer code;
    private final String desc;

    public static DeliveryNotificationEnum getByCode(Integer code) {
        for (DeliveryNotificationEnum value : DeliveryNotificationEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}

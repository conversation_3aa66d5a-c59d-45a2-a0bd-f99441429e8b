package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_vcs_order_info
 *
 * <AUTHOR>
 * @TableName t_vcs_order_info
 */
@TableName(value = "t_vcs_order_info")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class VCSOrderInfoDO extends BaseDO {
    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * VCS订单编码;VCS订单编码
     */
    @TableField(value = "vcs_order_code")
    private String vcsOrderCode;

    /**
     * 订单编码
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 订单明细编号;订单明细编号，by商品维度记录履约订单
     */
    @TableField(value = "order_item_code")
    private String orderItemCode;

    /**
     * 用户编码;用户编码
     */
    @TableField(value = "consumer_code")
    private String consumerCode;

    /**
     * incontrol账号;incontrol账号
     */
    @TableField(value = "incontrol_id")
    private String incontrolId;

    /**
     * incontrol账号;incontrol账号
     */
    @TableField(value = "incontrol_id_md5")
    private String incontrolIdMd5;

    /**
     * incontrol账号;incontrol账号 半隐藏只展示@后面的部位
     */
    @TableField(value = "incontrol_id_mix")
    private String incontrolIdMix;


    /**
     * 车辆VIN;车辆VIN
     */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     * 车辆VIN;车辆VIN
     */
    @TableField(value = "car_vin_md5")
    private String carVinMd5;

    /**
     * 车辆VIN;车辆VIN 半隐藏只展示前6位
     */
    @TableField(value = "car_vin_mix")
    private String carVinMix;

    /**
     *  明文carVin
     * */
    @TableField(value = "car_vin_view")
    private String carVinView;

    /**
     * 服务开始时间;服务开始时间，计算得出
     */
    @TableField(value = "service_begin_date")
    private LocalDateTime serviceBeginDate;

    /**
     * 服务结束时间;服务结束时间，计算得出
     */
    @TableField(value = "service_end_date")
    private LocalDateTime serviceEndDate;



    /**
     * 品牌名;品牌名
     */
    @TableField(value = "series_name")
    private String seriesName;

    /**
     * 品牌code;品牌code
     */
    @TableField(value = "series_code")
    private String seriesCode;

    @TableField(exist = false)
    private Integer count;

    /**
     * 服务履约类型：1：远程车控Remote Service；2：PIVI Subscription Service；
     */
    @TableField(value = "service_type")
    private Integer serviceType;
}
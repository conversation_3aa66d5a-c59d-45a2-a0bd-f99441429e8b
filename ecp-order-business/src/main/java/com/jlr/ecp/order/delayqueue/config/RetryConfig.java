package com.jlr.ecp.order.delayqueue.config;

import com.jlr.ecp.order.delayqueue.enums.ErrorTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 重试配置类
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class RetryConfig {
    
    /**
     * 最大重试次数
     */
    public static final int MAX_RETRY_COUNT = 3;
    
    /**
     * 重试间隔配置（指数退避策略）
     * 第1次重试：1分钟后
     * 第2次重试：2分钟后
     * 第3次重试：4分钟后
     */
    private static final long[] RETRY_DELAYS = {
        1 * 60 * 1000L,    // 1分钟
        2 * 60 * 1000L,    // 2分钟
        4 * 60 * 1000L     // 4分钟
    };

    /**
     * 获取重试延迟时间
     * 
     * @param retryCount 重试次数（从1开始）
     * @return 延迟时间（毫秒）
     */
    public long getRetryDelay(int retryCount) {
        if (retryCount <= 0 || retryCount > RETRY_DELAYS.length) {
            log.warn("无效的重试次数: {}, 使用默认延迟时间", retryCount);
            return RETRY_DELAYS[0]; // 默认1分钟
        }
        return RETRY_DELAYS[retryCount - 1];
    }
    
    /**
     * 判断是否可以重试
     * 
     * @param currentRetryCount 当前重试次数
     * @param maxRetryCount 最大重试次数
     * @return true-可以重试，false-不能重试
     */
    public boolean canRetry(int currentRetryCount, int maxRetryCount) {
        return currentRetryCount < maxRetryCount;
    }
    
    /**
     * 判断异常是否可重试
     * 
     * @param exception 异常
     * @return true-可重试，false-不可重试
     */
    public boolean isRetryableException(Exception exception) {
        ErrorTypeEnum errorType = ErrorTypeEnum.fromException(exception);
        return errorType.isRetryable();
    }
    
    /**
     * 获取异常类型描述
     *
     * @param exception 异常
     * @return 异常类型描述
     */
    public String getErrorType(Exception exception) {
        ErrorTypeEnum errorType = ErrorTypeEnum.fromException(exception);
        return errorType.getCode();
    }

    /**
     * 获取异常类型枚举
     *
     * @param exception 异常
     * @return 异常类型枚举
     */
    public ErrorTypeEnum getErrorTypeEnum(Exception exception) {
        return ErrorTypeEnum.fromException(exception);
    }
}

package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName(value = "t_order_coupon_detail")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderCouponDetailDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 租户id
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

    /**
     * 订单编号
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 订单明细编号
     */
    @TableField(value = "order_item_code")
    private String orderItemCode;

    /**
     * 卡券模板code
     */
    @TableField(value = "coupon_model_code")
    private String couponModelCode;

    /**
     * 卡券code
     */
    @TableField(value = "coupon_code")
    private String couponCode;

    /**
     * 卡券状态: 1-入库; 2-未生效; 3-待使用; 4-已作废; 5-已核销; 6-已过期
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 分账状态
     */
    @TableField(value = "independent_status")
    private Integer independentStatus;

    /**
     * 分账成功时间
     */
    @TableField(value = "independent_succ_time")
    private LocalDateTime independentSuccTime;

    /**
     * 有效期开始时间
     */
    @TableField(value = "valid_start_time")
    private LocalDateTime validStartTime;

    /**
     * 有效期结束时间
     */
    @TableField(value = "valid_end_time")
    private LocalDateTime validEndTime;

    /**
     * 核销时间
     */
    @TableField(value = "used_time")
    private LocalDateTime usedTime;

    /**
     * 发券时间
     */
    @TableField(value = "send_time")
    private LocalDateTime sendTime;

}
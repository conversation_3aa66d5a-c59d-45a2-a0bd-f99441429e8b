package com.jlr.ecp.order.controller.admin.dashboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "dashboard-响应参数VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChartRespVo {

  @Schema(description = "X轴")
  private List<String> xAxis;

  @Schema(description = "图表数据")
  private List<ChartItemVo> dataList;

  @Schema(description = "dashboard-图表item参数VO")
  @Data
  @NoArgsConstructor
  @AllArgsConstructor
  public static class ChartItemVo {
    @Schema(description = "名称")
    private String name;

    @Schema(description = "数据")
    private List<String> data;
  }

}

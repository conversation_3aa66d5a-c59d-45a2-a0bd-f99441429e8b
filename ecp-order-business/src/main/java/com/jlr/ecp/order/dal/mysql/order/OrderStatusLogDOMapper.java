package com.jlr.ecp.order.dal.mysql.order;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_order_status_log(t_order_status_log)】的数据库操作Mapper
* @createDate 2023-12-28 20:48:17
* @Entity com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO
*/
@Mapper
public interface OrderStatusLogDOMapper extends BaseMapperX<OrderStatusLogDO> {

    /****
     * 查询上一个完成步骤
     * @param orderCode 订单号
     * @return List<OrderStatusLogDO>
     */
    default List<OrderStatusLogDO> findLastCompleteStep(String orderCode){
        List<Integer> statusCodes = new ArrayList<>();
        statusCodes.add(OrderStatusEnum.COMPLETED.getCode());
        statusCodes.add(OrderStatusEnum.PARTIAL_CANCELLED.getCode());
        statusCodes.add(OrderStatusEnum.FULLY_CANCELLED.getCode());
        statusCodes.add(OrderStatusEnum.PAID.getCode());

        return selectList(new LambdaQueryWrapperX<OrderStatusLogDO>()
                .eq(OrderStatusLogDO::getOrderCode, orderCode)
                .eq(BaseDO::getIsDeleted, false)
                .in(OrderStatusLogDO::getAfterStatus, statusCodes)
                .orderByDesc(OrderStatusLogDO::getChangeTime));
    }
}





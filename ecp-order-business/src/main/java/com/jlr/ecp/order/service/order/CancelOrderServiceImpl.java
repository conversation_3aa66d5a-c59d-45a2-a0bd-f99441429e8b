package com.jlr.ecp.order.service.order;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.util.json.JsonUtils;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.inventory.api.InventoryOrderApi;
import com.jlr.ecp.inventory.dto.qry.InventoryOrderUpdateDTO;
import com.jlr.ecp.order.api.order.vo.OrderCancelVO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderPaymentRecordsDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderPaymentRecordsMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusLogDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.order.*;
import com.jlr.ecp.order.kafka.message.CancelOrderMessage;
import com.jlr.ecp.order.util.JacksonUtil;
import com.jlr.ecp.order.util.RedisReentrantLockUtil;
import com.jlr.ecp.payment.api.order.PayCenterOrderApi;
import com.jlr.ecp.payment.api.order.dto.CancelPayOrderReqDTO;
import com.jlr.ecp.payment.api.order.dto.PcCancelPayOrderRespDTO;
import com.jlr.ecp.payment.enums.order.CancelOrderStatusEnum;
import com.jlr.ecp.payment.enums.order.PayOrderStatusEnum;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 取消订单逻辑服务
 */
@Service
@Slf4j
public class CancelOrderServiceImpl implements CancelOrderService {

    private static final long TIMEOUT_LIMIT_MILLS = 15 * 60 * 1000L;

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;
    @Resource
    private OrderItemDOMapper orderItemDOMapper;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private InventoryOrderApi inventoryOrderApi;
    @Resource
    private OrderPaymentRecordsMapper orderPaymentRecordsMapper;
    @Resource
    private PayCenterOrderApi payCenterOrderApi;
    @Resource
    private OrderStatusLogDOMapper orderStatusLogDOMapper;
    @Resource
    private ProducerTool producerTool;
    @Resource
    private Redisson redisson;
    @Resource
    private RedisReentrantLockUtil redisReentrantLockUtil;

    @Resource
    private OrderInfoDOService orderInfoDOService;

    /**
     * 小程序订单取消
     * VCS传入的是子单号
     * BG、LRE在拆单场景下传入的是父单号
     */
    @Override
    public OrderCancelVO orderCancel(String orderCode, OrderCloseReasonEnum closeReason) {
        log.info("取消订单开始，orderCode={}", orderCode);

        RLock rLock = redisson.getLock(Constants.REDIS_CANCEL_ORDER_LOCK_KEY + orderCode);
        if (!redisReentrantLockUtil.tryLock(rLock, 30,  60, TimeUnit.SECONDS)) {
            throw exception(ErrorCodeConstants.CANCEL_ORDER_ERROR_CAN_RETRY);
        }
        try {
            OrderInfoDO orderInfo = orderInfoDOMapper.queryOrderDoByOrderCode(orderCode);
            if (BusinessIdEnum.VCS.getCode().equals(orderInfo.getBusinessCode())) {
                if (!OrderStatusEnum.ORDERED.getCode().equals(orderInfo.getOrderStatus())) {
                    log.info("当前订单状态不是已下单，无法手动取消, orderInfo:{}", orderInfo);
                    throw exception(ErrorCodeConstants.CANCEL_ORDER_STATUE_ERROR);
                }
                //调用取消订单PC
                cancelPayOrderVCS(orderInfo, orderCode,closeReason);

            } else {
                List<OrderInfoDO> relationOrders = listRelationOrder(orderCode);
                for (OrderInfoDO order:relationOrders) {
                    if (!OrderStatusEnum.ORDERED.getCode().equals(order.getOrderStatus())) {
                        log.info("当前订单状态不是已下单，无法手动取消，orderCode={}，orderInfo:{}", orderCode, order);
                        throw exception(ErrorCodeConstants.CANCEL_ORDER_STATUE_ERROR);
                    }
                }

                // 实物订单（BG订单）退还库存（子单维度）
                returnInventory(orderCode, relationOrders);

                // 非VCS订单取消时才需要调用PC进行取消，尽快返回积分和优惠券（父单维度）
                cancelPayOrder(orderInfo, orderCode);

                // 修改订单状态为“订单关闭”，且增加状态变更记录（父单+子单）
                editStatusToClosed(relationOrders, closeReason);
            }

            OrderCancelVO cancelVO = new OrderCancelVO();
            cancelVO.setCustomerRemark(orderInfo.getCustomerRemark());
            return cancelVO;
        } finally {
            redisReentrantLockUtil.unlock(rLock, 3);
        }
    }

    private void cancelPayOrderVCS(OrderInfoDO orderInfoDo, String orderCode, OrderCloseReasonEnum closeReason) {
        OrderPaymentRecordsDO paymentRecord = orderPaymentRecordsMapper.queryLastByOrderCode(orderInfoDo.getOrderCode());
        if (paymentRecord != null) {
            log.info("有支付记录，开始调用关单接口，orderCode={}",orderCode);
            CancelPayOrderReqDTO cancelReq = new CancelPayOrderReqDTO();
            cancelReq.setCustomerCode(orderInfoDo.getConsumerCode());
            cancelReq.setCancelApplyNo("CANCEL" + orderInfoDo.getOrderCode());
            cancelReq.setOrderNo(orderInfoDo.getOrderCode());
            cancelReq.setPayApplyNo(paymentRecord.getPayApplyNo());
            cancelReq.setReason(OrderCloseReasonEnum.PROACTIVELY_CLOSE.getDesc());
            log.info("关闭订单开始，orderCode={}，req={}", orderCode, JacksonUtil.bean2Json(cancelReq));
            CommonResult<PcCancelPayOrderRespDTO> cancelRes = payCenterOrderApi.cancelPayOrderVCS(cancelReq);
            log.info("关闭订单开始结束，orderCode={}，res={}", orderCode, JacksonUtil.bean2Json(cancelRes));
            if (!cancelRes.isSuccess()) {
                log.error("支付单关闭异常，orderCode={}", orderCode);
                throw exception(ErrorCodeConstants.CANCEL_ORDER_ERROR_CAN_RETRY);
            }
            PcCancelPayOrderRespDTO data = cancelRes.getData();
            if(CancelOrderStatusEnum.SUCCESS.getCode().equals(data.getStatus())
                ||(CancelOrderStatusEnum.SUCCESS.getCode().equals(data.getCancelStatus()))){
                log.info("支付单关闭成功，orderCode={}", orderCode);
                //直接业务关单
                // 修改订单状态为“订单关闭”，且增加状态变更记录
                editStatusToClosed(List.of(orderInfoDo), closeReason);
                closePaymentRecordByApplyNo(paymentRecord.getPayApplyNo());
            }

            //因为支付成功所以关单失败的处理
            if (CancelOrderStatusEnum.FAIL.getCode().equals(data.getCancelStatus())){
                if (CancelOrderStatusEnum.SUCCESS.getCode().equals(data.getPayStatus())){
                    log.info("支付单关闭失败，订单已支付成功，orderCode={}", orderCode);
                    //关单失败，并且处理成为支付成功，调用支付成功
                    orderInfoDOService.updateOrderStatusOnSuccess(orderCode, data.getPayTime());
                    throw exception(ErrorCodeConstants.CANCEL_ORDER_ERROR_CAN_RETRY);
                }else if(PayOrderStatusEnum.CLOSED.getCode().equals(data.getPayStatus())){
                    log.info("支付单关闭失败，订单已支付关闭，orderCode={}", orderCode);
                    // 修改订单状态为“订单关闭”，且增加状态变更记录
                    editStatusToClosed(List.of(orderInfoDo), closeReason);
                    closePaymentRecordByApplyNo(paymentRecord.getPayApplyNo());
                }else {
                    log.info("支付单关闭失败，订单未支付成功，orderCode={}", orderCode);
                    throw exception(ErrorCodeConstants.CANCEL_ORDER_ERROR_CAN_RETRY);
                }
            }

            //没有支付记录直接关单
        }else {
            log.info("没有支付记录直接关单，orderCode={}",orderCode);
            // 修改订单状态为“订单关闭”，且增加状态变更记录
            editStatusToClosed(List.of(orderInfoDo), closeReason);
        }
    }

    private void closePaymentRecordByApplyNo(String payApplyNo) {
        //根据payApplyNo 更新orderPaymentRecords 为关闭状态
        OrderPaymentRecordsDO paymentRecord = new OrderPaymentRecordsDO();
        paymentRecord.setPayStatus(PayOrderStatusEnum.CLOSED.getStatus());
        orderPaymentRecordsMapper.update(paymentRecord, new LambdaUpdateWrapper<OrderPaymentRecordsDO>()
                .eq(OrderPaymentRecordsDO::getPayApplyNo, payApplyNo));
    }

    /**
     * 修改订单状态为“订单关闭”，且增加状态变更记录
     */
    private void editStatusToClosed(List<OrderInfoDO> orderInfos, OrderCloseReasonEnum closeReason) {
        LocalDateTime current = LocalDateTime.now();
        transactionTemplate.executeWithoutResult((status -> {
            addOrderStatusModifyLog(orderInfos, current);
            modifyOrderStatusToCancel(orderInfos, closeReason, current);
        }));
    }

    /**
     * 退还库存
     * @param relationOrders
     */
    private void returnInventory(String orderCode, List<OrderInfoDO> relationOrders) {
        List<String> orderCodeList = new ArrayList<>();
        for (OrderInfoDO order:relationOrders) {
            if (OrderTypeEnum.BRAND_GOOD.getCode().equals(order.getOrderType())) {
                orderCodeList.add(order.getOrderCode());
            }
        }

        if (CollUtil.isEmpty(orderCodeList)) {
            return;
        }

        InventoryOrderUpdateDTO req = new InventoryOrderUpdateDTO();
        req.setOrderCodeList(orderCodeList);
        log.info("取消订单，退还库存开始，orderCode={}，req={}", orderCode, JsonUtils.toJsonString(relationOrders));
        CommonResult<String> res = inventoryOrderApi.orderCancel(req);
        log.info("取消订单，退还库存结果，orderCode={}，res={}", orderCode, JsonUtils.toJsonString(res));
        if (!res.isSuccess()) {
            log.error("取消订单，退还库存失败，orderCode={}", orderCode);
            throw exception(ErrorCodeConstants.CANCEL_ORDER_ERROR_CAN_RETRY);
        }
    }

    /**
     * 取消支付单
     */
    private void cancelPayOrder(OrderInfoDO orderInfoDo, String orderCode) {
        OrderPaymentRecordsDO paymentRecord = orderPaymentRecordsMapper.queryLastByOrderCode(orderInfoDo.getOrderCode());
        if (paymentRecord != null) {
            CancelPayOrderReqDTO cancelReq = new CancelPayOrderReqDTO();
            cancelReq.setCustomerCode(orderInfoDo.getConsumerCode());
            cancelReq.setCancelApplyNo("CANCEL" + orderInfoDo.getOrderCode());
            cancelReq.setOrderNo(orderInfoDo.getOrderCode());
            cancelReq.setPayApplyNo(paymentRecord.getPayApplyNo());
            cancelReq.setReason(OrderCloseReasonEnum.PROACTIVELY_CLOSE.getDesc());
            log.info("取消订单，取消支付单开始，orderCode={}，req={}", orderCode, JacksonUtil.bean2Json(cancelReq));
            CommonResult<Boolean> cancelRes = payCenterOrderApi.cancelPayOrder(cancelReq);
            log.info("取消订单，取消支付单结束，orderCode={}，res={}", orderCode, JacksonUtil.bean2Json(cancelRes));
            if (!cancelRes.isSuccess()) {
                log.error("取消订单，取消支付单失败，orderCode={}", orderCode);
                throw exception(ErrorCodeConstants.CANCEL_ORDER_ERROR_CAN_RETRY);
            }
        }
    }

    /**
     * 获取关联的订单
     * @return
     */
    private List<OrderInfoDO> listRelationOrder(String orderCode) {
        List<OrderInfoDO> orderInfos = orderInfoDOMapper.queryOrderDoByParentCode(orderCode);
        if (CollUtil.isEmpty(orderInfos)) {
            return orderInfos;
        }
        return orderInfos;
    }

    /**
     * 修改订单状态为取消
     * @param orderInfos 订单信息
     */
    private void modifyOrderStatusToCancel(List<OrderInfoDO> orderInfos, OrderCloseReasonEnum closeReason, LocalDateTime current) {
        List<OrderInfoDO> orderInfoUpdateList = new ArrayList<>();
        for (OrderInfoDO orderInfo:orderInfos) {
            orderInfo.setOrderStatus(OrderStatusEnum.CLOSED.getCode());
            orderInfo.setOrderCloseReason(closeReason.getDesc());
            orderInfo.setClosedTime(current);
            orderInfo.setUpdatedTime(current);
            if (OrderTypeEnum.BRAND_GOOD.getCode().equals(orderInfo.getOrderType())) {
                orderInfo.setLogisticsStatus(OrderLogisticsStatusEnum.ORDER_CLOSED.getCode());
            } else if (OrderTypeEnum.ELECTRONIC_COUPON.getCode().equals(orderInfo.getOrderType())) {
                orderInfo.setCouponStatus(OrderCouponStatusEnum.ORDER_CLOSED.getCode());
            }
            orderInfoUpdateList.add(orderInfo);
        }
        orderInfoDOMapper.updateBatch(orderInfoUpdateList);
    }

    /**
     * 添加订单状态修改日志
     * @param orderInfos 订单信息
     */
    private void addOrderStatusModifyLog(List<OrderInfoDO> orderInfos, LocalDateTime current) {
        List<OrderStatusLogDO> statusLogs = new ArrayList<>();
        for (OrderInfoDO orderInfo:orderInfos) {
            OrderStatusLogDO statusLog = new OrderStatusLogDO();
            statusLog.setOrderCode(orderInfo.getOrderCode());
            statusLog.setBeforeStatus(orderInfo.getOrderStatus());
            statusLog.setAfterStatus(OrderStatusEnum.CLOSED.getCode());
            statusLog.setChangeTime(current);
            statusLog.setTenantId(TenantContextHolder.getTenantId().intValue());
            statusLog.setCreatedBy(orderInfo.getCreatedBy());
            statusLog.setCreatedTime(current);
            statusLog.setIsDeleted(Boolean.FALSE);
            statusLog.setRevision(1);
            statusLogs.add(statusLog);
        }
        orderStatusLogDOMapper.insertBatch(statusLogs);
    }

    /**
     * 消息处理
     * @param sortCancelMessages 取消订单消息
     */
    @Override
    public void processMessage(List<CancelOrderMessage> sortCancelMessages) {
        try {
            // 直接处理消息，不再需要Thread.sleep延迟
            // 延迟逻辑已经由Redis延迟队列处理
            for (CancelOrderMessage cancelOrderMessage : sortCancelMessages) {
                producerTool.sendMsg(KafkaConstants.REAL_ORDER_TIMEOUT_CANCEL_TOPIC, "", JSON.toJSONString(cancelOrderMessage));
                log.info("订单超时取消消息已发送，orderCode={}", cancelOrderMessage.getOrderCode());
            }
        } catch (Exception e) {
            log.error("订单超时自动取消，消息处理异常:", e);
            throw new RuntimeException("订单取消消息处理失败", e);
        }
    }

}

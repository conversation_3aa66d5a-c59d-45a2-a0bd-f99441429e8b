package com.jlr.ecp.order.controller.app.cart;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.EnumUtil;
import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.cart.dto.*;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppDetailPolicyRespVO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.app.cart.dto.CartGroupedBylineReq;
import com.jlr.ecp.order.controller.app.cart.vo.*;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.cart.ClientIdEnum;
import com.jlr.ecp.order.service.cart.ShoppingCarItemService;
import com.jlr.ecp.order.service.cart.ShoppingCarService;
import com.jlr.ecp.order.service.internal.price.CalculateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.pojo.CommonResult.success;
import static com.jlr.ecp.order.constant.Constants.JLR_ID_HEADER;

/**
 * 购物车
 * <AUTHOR>
 */
@Tag(name = "app端 - 购物车")
@RestController
@RequestMapping("v1/cart")
@Validated
@Slf4j
public class CartController {

    @Resource
    private ShoppingCarService shoppingCarService;

    @Resource
    private ShoppingCarItemService carItemService;

    @Resource
    private CalculateService calculateService;


    /**
     * 购物车 -添加
     *
     * @return List<ProductSpuInfoVO>
     */
    @PostMapping("/add")
    @Operation(summary = "加入购物车")
    @PermitAll
    CommonResult<String> add(@RequestBody CartCreatDTO cartCreatDTO,
                             @RequestHeader(value = "client-id",required = false) String clientId,
                             @RequestHeader(value = "jlrId") String jlrId) {
        cartCreatDTO.setConsumerCode(jlrId);
        log.info("加入购物车 cartCreatDTO :{},clientId:{}", JSON.toJSONString(cartCreatDTO),clientId);
        //解析品牌code,数据隔离
        String brandCode = null;
        if(EnumUtil.getBy(ClientIdEnum::getClientId,clientId) != null){
            brandCode = EnumUtil.getBy(ClientIdEnum::getClientId,clientId).getBrandCode();
        }else {
            return CommonResult.error(ErrorCodeConstants.CART_CREATE_FAIL);
        }
        if(shoppingCarService.addCart(cartCreatDTO,brandCode)){
            return CommonResult.success(Constants.CART_CREATE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.CART_CREATE_FAIL);
    }

    /**
     * 购物车 -添加
     *
     * @return List<ProductSpuInfoVO>
     */
    @PostMapping("/brandGoods/add")
    @Operation(summary = "加入购物车")
    @PermitAll
    CommonResult<String> brandGoodsAdd(@RequestBody CartCreatDTO cartCreatDTO,
                             @RequestHeader(value = "client-id",required = false) String clientId,
                             @RequestHeader(value = "jlrId") String jlrId) {
        cartCreatDTO.setConsumerCode(jlrId);
        log.info("加入购物车 cartCreatDTO :{},clientId:{}", JSON.toJSONString(cartCreatDTO),clientId);
        //解析品牌code,数据隔离
        String brandCode = null;
        if(EnumUtil.getBy(ClientIdEnum::getClientId,clientId) != null){
            brandCode = EnumUtil.getBy(ClientIdEnum::getClientId,clientId).getBrandCode();
        }else {
            return CommonResult.error(ErrorCodeConstants.CART_CREATE_FAIL);
        }
        if(Boolean.TRUE.equals(shoppingCarService.brandGoodsAddCart(cartCreatDTO,brandCode))){
            return CommonResult.success(Constants.CART_CREATE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.CART_CREATE_FAIL);
    }


    @PostMapping("/chooseSku")
    @Operation(summary = "选择sku和数量修改")
//    @PermitAll
    CommonResult<String> chooseSku(@RequestBody CartItemUpdateDTO cartItemUpdateDTO) {
        log.info("选择sku和数量修改 cartItemUpdateDTO :{}", JSON.toJSONString(cartItemUpdateDTO));
        if(Boolean.TRUE.equals(shoppingCarService.chooseSku(cartItemUpdateDTO))){
            return CommonResult.success(Constants.CART_UPDATE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.CART_UPDATE_FAIL);
    }

    @PostMapping("/brandGoods/chooseSku")
    @Operation(summary = "选择sku和数量修改")
//    @PermitAll
    CommonResult<String> brandGoodsChooseSku(@RequestBody CartItemUpdateDTO cartItemUpdateDTO,
                                            @RequestHeader(value = "jlrId") String jlrId) {
        log.info("选择sku和数量修改 cartItemUpdateDTO :{}", JSON.toJSONString(cartItemUpdateDTO));
        return shoppingCarService.brandGoodsChooseSku(cartItemUpdateDTO, jlrId);
    }




    @PostMapping("/delete")
    @Operation(summary = "删除购物车商品")
    CommonResult<String> delete(@RequestBody CartDelDTO cartItemCodes) {
        if(carItemService.delete(cartItemCodes.getCartItemCodes())){
            return CommonResult.success(Constants.CART_DELETE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.CART_DELETE_FAIL);
    }


    @GetMapping("/list")
    @Operation(summary = "购物车列表")
    @Parameter(name = "consumerCode", description = "用户编码")
    @PermitAll
    CommonResult<List<ShoppingCarItemVO>> list(@RequestParam(value = "incontrolName")String incontrolName,
                                               @RequestHeader(value = "client-id",required = false) String clientId,
                                               @RequestHeader(value = "jlrId") String jlrId) {
        log.info("获取购物车列表入参为,consumerCode={}, clientId={}, incontrolName={}", jlrId, clientId, incontrolName);
        //解析品牌code,数据隔离incontrolName
        String brandCode = null;
        if(EnumUtil.getBy(ClientIdEnum::getClientId,clientId) != null){
            brandCode = EnumUtil.getBy(ClientIdEnum::getClientId,clientId).getBrandCode();
        }
        return success(carItemService.findCartList(jlrId,brandCode, incontrolName));
    }

    @PermitAll
    @GetMapping("/productByServiceLine/list")
    @Operation(summary = "购物车列表 - 根据业务线分组")
    CommonResult<ShopCartMultiTypeProdVo> productByServiceLineList(
            @RequestParam(value = "incontrolNames", required = false) String incontrolNames,
            @RequestHeader(value = JLR_ID_HEADER) String jlrId) {

        log.info("获取购物车列表入参为, ConsumerCode:{}, IncontrolNames:{}", jlrId, incontrolNames);

        List<String> incontrolNameList = new ArrayList<>();
        if (StringUtils.isNotBlank(incontrolNames)) {
            incontrolNameList = List.of(incontrolNames.split(","));
        }

        // 过滤 EmptyString
        incontrolNameList = incontrolNameList.stream().filter(
                StringUtils::isNotBlank
        ).collect(Collectors.toList());

        CartGroupedBylineReq cartGroupedBylineReq = new CartGroupedBylineReq();
        cartGroupedBylineReq.setIncontrolNameList(incontrolNameList);

        return success(carItemService.productByServiceLineList(jlrId, cartGroupedBylineReq));
    }

    @GetMapping("/count")
    @Operation(summary = "购物车数量统计")
    @Parameter(name = "incontrolNames", description = "用户 ICR 账号 (使用 \",\" 分割多个账号)")
    CommonResult<Integer> count(
            @RequestParam(value = "incontrolNames", required = false) String incontrolNames,
            @RequestHeader(value = JLR_ID_HEADER) String jlrId) {
        return success(carItemService.countCartNum(jlrId, incontrolNames));
    }

    @PostMapping("/calculateAmount")
    @Operation(summary = "购物车计算优惠价")
    CommonResult<CalculateAmountVO> calculateAmount(@RequestBody CalculateAmountDTO calculateAmountDTO, @RequestHeader(value = "jlrId") String jlrId) {
        log.info("购物车计算优惠价,calculateAmountDTO={}, customer code={}", calculateAmountDTO, jlrId);

        if (CollUtil.isEmpty(calculateAmountDTO.getCartItemCodeList()) && CollUtil.isEmpty(calculateAmountDTO.getProductItems())){
            log.info("购物车计算优惠价,购物车code为空，且商品行信息也为空");
            return CommonResult.error(ErrorCodeConstants.CART_SKU_ERROR);
        }

        try {
            return success(calculateService.calculateAmount(calculateAmountDTO, jlrId));
        } catch (Exception e) {
            log.info("购物车计算优惠价失败", e);
            throw e;
        }
    }

}

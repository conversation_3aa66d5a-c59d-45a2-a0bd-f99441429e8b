package com.jlr.ecp.order.dal.mysql.refund;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.order.dto.OrderRefundStatusMapDTO;
import com.jlr.ecp.order.api.refund.dto.OrderRefundItemWithStatusDTO;
import com.jlr.ecp.order.api.refund.vo.RefundOrderItemVO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.ValetDashboardTempDTO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_order_refund_item(t_order_refund_item)】的数据库操作Mapper
 * @createDate 2024-01-15 11:28:44
 * @Entity com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO
 */
@Mapper
public interface OrderRefundItemDOMapper extends BaseMapperX<OrderRefundItemDO> {

    /**
     * 根据订单item编码查询退款订单状态
     * @param orderItemCode
     * @return
     */
//    @Select("SELECT r.refund_order_status " +
//            "FROM t_order_refund_item i " +
//            "LEFT JOIN t_order_refund r " +
//            "ON i.refund_order_code = r.refund_order_code " +
//            "WHERE i.order_item_code = #{orderItemCode} and i.is_deleted = 0 and r.is_deleted = 0")

//    @Select("SELECT r.refund_order_status " +
//            "FROM t_order_refund_item ri " +
//            "LEFT JOIN t_order_refund r ON r.refund_order_code = ri.refund_order_code " +
//            "WHERE ri.id IN ( " +
//            "    SELECT MAX(id) " +
//            "    FROM t_order_refund_item " +
//            "  where is_deleted = 0 and tenant_id = 1 " +
//            "    GROUP BY order_item_code " +
//            ") " +
//            "and ri.is_deleted = 0 and r.is_deleted = 0 and ri.tenant_id = 1 and order_item_code=#{orderItemCode}")
    Integer getRefundOrderStatusByOrderItemCode(@Param("orderItemCode") String orderItemCode);

    /**
     * 根据订单item编码列表 查询退款订单状态列表
     * @param orderItemCodeList
     * @return
     */
    List<OrderRefundStatusMapDTO> getRefundOrderStatusByOrderItemCodeList(@Param("orderItemCodeList") List<String> orderItemCodeList);

    /**
     * 根据订单item编码列表 查询退款订单相关信息列表
     * @param orderItemCodeList
     * @return
     */
    List<OrderRefundStatusMapDTO> getRefundInfoByOrderItemCodeList(@Param("orderItemCodeList") List<String> orderItemCodeList);
    /**
     * 查
     * 7.代客下单总退款额
     * 8.已支付状态下 订单 的总退款额
     */
    ValetDashboardTempDTO getRefundAmounts(@Param("orderChannel") String orderChannel, @Param("start") String start, @Param("end") String end);

    /**
     * 查
     * 8.已支付状态下 订单 的总退款额
     */
    ValetDashboardTempDTO getTotalRefundAmounts(@Param("start") String start, @Param("end") String end,@Param("businessCode") String businessCode);

    /**
     * 根据退款订单编码列表 查询退款订单商品行
     * @param refundOrderCodeList
     * @return
     */
    List<RefundOrderItemVO> getRefundOrderItemList(@Param("refundOrderCodeList") List<String> refundOrderCodeList);

    /**
     * 依据正向订单号+售后单状态查询退单行信息
     * @return
     */
    List<OrderRefundItemWithStatusDTO> listRefundByOrderCode(@Param("orderCodes") List<String> orderCodes, @Param("refundStatusList") List<Integer> refundStatusList);

    /**
     * 根据orderCode 查询仅退款的退单行总金额
     */
    Integer getOnlyRefundTotalMoneyByOrderCode(@Param("orderCode") String orderCode,@Param("refundLogisticsStatus")Integer refundLogisticsStatus);

    /**
     * 查询BG最大可退金额 需要排除掉已经撤销或者已经关闭的订单
     * @param orderItemCode
     * @param orderRefundItemId 是否需要排除的单据
     * @param refundLogisticsStatus
     * @return
     */
    Integer getLogisticsAlreadyRefundMoney(@Param("orderItemCode")String orderItemCode,@Param("orderRefundItemId")Long orderRefundItemId,@Param("refundLogisticsStatus") Integer refundLogisticsStatus);


    Integer getAllLogisticsAlreadyRefundMoney(@Param("orderItemCodeList") List<String> orderItemCodeList,@Param("refundLogisticsStatus") Integer refundLogisticsStatus);

    Integer getAllLogisticsAlreadyRefundMoneyIncludeFreight(@Param("orderItemCodeList") List<String> orderItemCodeList,@Param("refundLogisticsStatus") Integer refundLogisticsStatus);

    String getLogisticsCodeByOrderItemCode(@Param("orderItemCode") String orderItemCode);

    default Integer getTotalRefundMoney(String orderItemCode){
        List<OrderRefundItemDO> list =this.selectList(new LambdaQueryWrapperX<OrderRefundItemDO>()
                .eq(OrderRefundItemDO::getOrderItemCode,orderItemCode)
                .eq(OrderRefundItemDO::getIsDeleted,Boolean.FALSE));
        if(CollectionUtils.isNotEmpty(list)){
            //计算refundMoney的总和
            return list.stream().mapToInt(OrderRefundItemDO::getRefundMoney).sum();
        }else{
            return 0;
        }
    }

}
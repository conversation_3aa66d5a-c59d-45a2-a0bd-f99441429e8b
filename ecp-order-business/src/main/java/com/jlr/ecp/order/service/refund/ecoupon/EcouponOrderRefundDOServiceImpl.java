package com.jlr.ecp.order.service.refund.ecoupon;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.refund.dto.*;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.app.refund.vo.EcouponRefundItemDetailVO;
import com.jlr.ecp.order.dal.dataobject.order.*;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.mysql.order.*;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.handle.RefundHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.util.List;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @description 虚拟电子券类型退款
 * @createDate 20250306
 */
@Service
@Slf4j
public class EcouponOrderRefundDOServiceImpl extends ServiceImpl<OrderRefundDOMapper, OrderRefundDO>
        implements EcouponOrderRefundDOService {

    @Resource
    private RefundHandler refundHandler;

    @Resource
    private OrderRefundDOMapper orderRefundDOMapper;

    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Resource
    private OrderRefundItemDOMapper orderRefundItemDOMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String ecouponOrderRefundApply(List<BaseOrderRefundApplyDTO> refundApplyDTOs, Integer operationType) {
        return refundHandler.refundProcess(refundApplyDTOs,operationType);
    }

    @Override
    public EcouponRefundItemDetailVO getEcouponRefundItemDetail(String orderItemCode, String orderRefundCode) {
        EcouponRefundItemDetailVO detailVO = new EcouponRefundItemDetailVO();
        OrderRefundDO orderRefundDO = orderRefundDOMapper.selectOne
                (new LambdaQueryWrapperX<OrderRefundDO>().eq(OrderRefundDO::getRefundOrderCode, orderRefundCode)
                        .eq(BaseDO::getIsDeleted, false)
                        .orderByDesc(OrderRefundDO::getId)
                        .last(Constants.LIMIT_ONE));
        OrderItemDO orderItemDO = orderItemDOMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderItemCode, orderItemCode)
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(OrderItemDO::getId)
                .last(Constants.LIMIT_ONE));
        OrderRefundItemDO orderRefundItemDO = orderRefundItemDOMapper.selectOne(new LambdaQueryWrapperX<OrderRefundItemDO>()
                .eq(OrderRefundItemDO::getOrderItemCode, orderItemCode)
                .eq(OrderRefundItemDO::getRefundOrderCode, orderRefundCode)
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(OrderRefundItemDO::getId)
                .last(Constants.LIMIT_ONE));
        if(orderRefundDO == null || orderItemDO ==null || orderRefundItemDO == null){
            return detailVO;
        }
        refundHandler.buildRefundItemDetailVO(detailVO,orderRefundDO,orderItemDO,orderRefundItemDO);
        return detailVO;
    }




}





package com.jlr.ecp.order.controller.admin.dashboard;

import cn.hutool.core.bean.BeanUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.controller.admin.dashboard.dto.KpiQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.QueryReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductPreferenceRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesProportionByAttributesRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.service.dashboard.ProductPreferenceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 虚拟产品偏好
 *
 */
@Tag(name = "后台管理端 - 虚拟产品偏好仪表盘")
@RestController
@RequestMapping("v1/dashboard/productPreference")
@Validated
public class ProductPreferenceController {
    @Resource
    private ProductPreferenceService productPreferenceService;

    /**
     * 车型列表
     *
     */
    @GetMapping("/vehicleModelList")
    @Operation(summary = "车型列表")
    CommonResult<List<String>> vehicleModelList() {
        return productPreferenceService.vehicleModelList();
    }

    /**
     * 产品销售趋势KPI列表
     *
     */
    @GetMapping("/salesTrendKpiList")
    @Operation(summary = "产品销售趋势KPI列表")
    CommonResult<List<KpiQueryDTO>> salesTrendKpiList() {
        return productPreferenceService.salesTrendKpiList();
    }

    /**
     * 产品及不同属性销售占比KPI列表
     *
     */
    @GetMapping("/salesProportionByAttributesKpiList")
    @Operation(summary = "产品及不同属性销售占比KPI列表")
    CommonResult<List<KpiQueryDTO>> salesProportionByAttributesKpiList() {
        return productPreferenceService.salesProportionByAttributesKpiList();
    }

    /**
     * 产品销售趋势
     *
     * @param dto 参数
     * @return ProductSalesTrendRespVo
     */
    @PostMapping("/salesTrend")
    @Operation(summary = "产品销售趋势")
    @PreAuthorize("@ss.hasPermission('dashboard:virtualproducts:index')")
    CommonResult<ProductSalesTrendRespVo> salesTrend(@RequestBody @Validated QueryReqDTO dto) {
        return productPreferenceService.querySalesTrend(dto);
    }

    /**
     * 产品及不同属性销售占比
     *
     * @param dto 参数
     * @return ProductSalesTrendRespVo
     */
    @PostMapping("/salesProportionByAttributes")
    @Operation(summary = "产品及不同属性销售占比")
    @PreAuthorize("@ss.hasPermission('dashboard:virtualproducts:index')")
    CommonResult<ProductSalesProportionByAttributesRespVo> salesProportionByAttributes(@RequestBody @Validated QueryReqDTO dto) {
        return productPreferenceService.querySalesProportionByAttributes(dto);
    }

    /**
     * 虚拟产品偏好
     *
     * @param dto 参数
     * @return ProductSalesTrendRespVo
     */
    @PostMapping("/all")
    @Operation(summary = "虚拟产品偏好")
    @PreAuthorize("@ss.hasPermission('dashboard:virtualproducts:index')")
    CommonResult<ProductPreferenceRespVo> all(@RequestBody @Validated QueryReqDTO dto) {
        // kpi和rightKpi不能同时为空
        if (dto.getKpi() == null && dto.getRightKpi() == null) {
            return CommonResult.error(ErrorCodeConstants.TYPE_FILTER_ERROR);
        }
        QueryReqDTO rightQueryDTO = new QueryReqDTO();
        BeanUtil.copyProperties(dto, rightQueryDTO);
        rightQueryDTO.setKpi(dto.getRightKpi());

        ProductSalesTrendRespVo leftResp = new ProductSalesTrendRespVo();
        CommonResult<ProductSalesTrendRespVo> left = productPreferenceService.querySalesTrend(dto);
        if (left.isError()) {
            leftResp.setErrorMsg(left.getMsg());
        }else {
            leftResp = left.getData();
        }

        ProductSalesProportionByAttributesRespVo rightResp = new ProductSalesProportionByAttributesRespVo();
        CommonResult<ProductSalesProportionByAttributesRespVo> right = productPreferenceService.querySalesProportionByAttributes(rightQueryDTO);
        if (right.isError()) {
            rightResp.setErrorMsg(right.getMsg());
        }else {
            rightResp = right.getData();
        }

        return CommonResult.success(new ProductPreferenceRespVo(leftResp, rightResp));
    }
}

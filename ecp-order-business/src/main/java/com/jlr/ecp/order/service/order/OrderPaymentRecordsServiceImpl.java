package com.jlr.ecp.order.service.order;

import com.jlr.ecp.order.dal.dataobject.order.OrderPaymentRecordsDO;
import com.jlr.ecp.order.dal.mysql.order.OrderPaymentRecordsMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class OrderPaymentRecordsServiceImpl implements OrderPaymentRecordsService {

    @Resource
    private OrderPaymentRecordsMapper orderPaymentRecordsMapper;

    @Override
    public Long createOrderPaymentRecord(OrderPaymentRecordsDO orderPaymentRecordsDO) {
        orderPaymentRecordsMapper.insert(orderPaymentRecordsDO);
        return orderPaymentRecordsDO.getId();
    }
} 
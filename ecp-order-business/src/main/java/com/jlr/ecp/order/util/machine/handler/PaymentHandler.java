package com.jlr.ecp.order.util.machine.handler;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import org.springframework.stereotype.Component;

/**
 * 已废弃 -》没有中间态，可能导致开票出现问题
 * 支付回调 事件处理
 * <AUTHOR>
 */
@Component
public class PaymentHandler implements EventHandler {
    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO) {
        //原有逻辑
        orderInfoDO.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());

        //可能改造逻辑
//        orderInfoDO.setOrderStatus(OrderStatusEnum.PAID.getCode());
//        orderInfoDO.setPaymentStatus(PaymentStatusEnum.PAID.getCode());
    }
}

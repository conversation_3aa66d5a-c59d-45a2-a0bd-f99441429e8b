package com.jlr.ecp.order.dal.dataobject.cart;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.order.controller.app.cart.vo.ShoppingCarItemVO;
import lombok.*;

/**
 * t_shopping_car(ShoppingCar)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_shopping_car_item")
public class ShoppingCarItemDO extends BaseDO {

     /**
     * 租户号
     */    
    private Integer tenantId;

     /**
     * 主键
     */
     @TableId
    private Long id;

    /**
     * 用户编码
     */
    private String consumerCode;

    /**
     * incontrol账号
     */
    private String incontrolId;

    /**
     * 购物车code
     */
    private String cartCode;

    /**
     * 购物车商品编码
     */
    private String cartItemCode;

    /**
     * 购物车商品类型
     */
    private Integer cartItemType;


    /**
     * 商品编码
     */
    private String productCode;


    /**
     * 商品SKU编码
     */
    private String productSkuCode;


    /**
     * 商品数量
     */
    private Integer quantity;


    /**
     * 车型编码
     */
    private String seriesCode;


    /**
     * 车型名称
     */
    private String seriesName;


    /**
     * 车辆VIN码
     */
    private String carVin;

    /**
     * 品牌code
     */
    private String brandCode;


    public ShoppingCarItemVO coverToVO() {
        ShoppingCarItemVO vo = new ShoppingCarItemVO();
        vo.setId(this.id);
        vo.setConsumerCode(this.consumerCode);
        vo.setCartCode(this.cartCode);
        vo.setCartItemCode(this.cartItemCode);
        vo.setCartItemType(this.cartItemType);
        vo.setProductCode(this.productCode);
        vo.setProductSkuCode(this.productSkuCode);
        vo.setQuantity(this.quantity);
        vo.setSeriesCode(this.seriesCode);
        vo.setSeriesName(this.seriesName);
        vo.setCarVin(this.carVin);
        return vo;
    }
}


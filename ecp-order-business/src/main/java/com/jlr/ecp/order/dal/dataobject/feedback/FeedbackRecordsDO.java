package com.jlr.ecp.order.dal.dataobject.feedback;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
@TableName("t_feedback_records")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackRecordsDO extends BaseDO {
    /**
     * 记录ID
     */
    @TableId
    private Long id;

    /**
     * 评价记录Code，算法
     */
    @TableField("feedback_records_code")
    private String feedbackRecordsCode;

    /**
     * 评价维度：PM开头、已支付OR、订单完成、CL、订单整单取消
     */
    @TableField("feedback_dimensions")
    private String feedbackDimensions;

    /**
     * 评价编码：生成规则：feedback_dimensions+递增三位数编号（例如：PM001）
     */
    @TableField("feedback_code")
    private String feedbackCode;

    /**
     * 快照编码：版本号，启用时自动生成：feedback_code_PT+时间戳（YYYYMMDDHHMMSS）
     */
    @TableField("snapshot_code")
    private String snapshotCode;

    /**
     * 订单编码：订单唯一标识，编码规则：业务线code+时间戳（年月日时分秒）+6位随机数
     */
    @TableField("order_code")
    private String orderCode;

    /**
     * total                 int          not null comment '评价总分'
     */
    @TableField("total")
    private Integer total;

    /**
     * 评价结果内容JSON格式
     */
    @TableField("feedback_json")
    private String feedbackJson;

    /**
     * 输入框额外内容
     */
    @TableField("input_extra")
    private String inputExtra;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Integer tenantId;
}

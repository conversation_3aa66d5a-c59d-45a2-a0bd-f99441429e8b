package com.jlr.ecp.order.service.order;

import com.jlr.ecp.order.api.order.dto.OrderCarVinDTO;
import com.jlr.ecp.order.api.order.dto.OrderItemDTO;
import com.jlr.ecp.order.api.order.vo.base.OrderItemBaseVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_order_item(t_order_item)】的数据库操作Service
 * @createDate 2023-12-20 10:41:04
 */
public interface OrderItemDOService extends IService<OrderItemDO> {

    List<OrderItemBaseVO> getOrderItemInfo(List<String> orderItemCodeList);

    /**
     * 增加productCode
     * @param orderCarVinDTOList car的订单信息
     * @return List<OrderCarVinDTO>
     * */
    List<OrderCarVinDTO> addProductCode(List<OrderCarVinDTO> orderCarVinDTOList);

    /**
     *  通过订单编号获取OrderItem的list
     * @param orderCode order编号
     * @return List<OrderItemDTO>
     * */
    List<OrderItemDTO> queryOrderItemByOrderCode(String orderCode);
}

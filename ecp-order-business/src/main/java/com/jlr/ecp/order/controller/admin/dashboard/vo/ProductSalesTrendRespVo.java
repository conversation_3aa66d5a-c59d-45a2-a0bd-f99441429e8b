package com.jlr.ecp.order.controller.admin.dashboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "dashboard-产品销售趋势图")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSalesTrendRespVo {
  @Schema(description = "图表数据")
  private ChartRespVo chart;

  @Schema(description = "表格数据")
  private TableRespVo table;

  @Schema(description = "报错信息")
  private String errorMsg;
}

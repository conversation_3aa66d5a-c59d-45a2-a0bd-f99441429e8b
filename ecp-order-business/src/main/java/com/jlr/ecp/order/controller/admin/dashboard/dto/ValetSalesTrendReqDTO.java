package com.jlr.ecp.order.controller.admin.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Schema(description = "代客下单 销售趋势图 请求参数")
@Data
public class ValetSalesTrendReqDTO extends ValetDashboardReqDTO{
    @Schema(description = "指标, KpiEnum：" +
            "1总订单数；" +
            "2成交订单数；" +
            "3总订单交易额；" +
            "4总收入；" +
            "5总退款；" +
            "6成交客单价；")
    @Valid
    @NotNull(message = "指标不能为空")
    private Integer kpi;

}

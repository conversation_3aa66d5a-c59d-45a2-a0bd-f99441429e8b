package com.jlr.ecp.order.util.common;

/**
 * 一般业务code生成、解析器
 * <AUTHOR>
 */
public class PrefixCodeGenerator {

    /***
     * 生成下一个递增编码
     */
    public static String nextCode(String inputCode) {
        if (inputCode == null || inputCode.isEmpty()) {
            return null;
        }
        // 解析英文code和数字
        String codePrefix = inputCode.replaceAll("\\d", "");
        int number = Integer.parseInt(inputCode.replaceAll("\\D", ""));
        // 递增数字，并构建新的编码
        return codePrefix + String.format("%04d", number + 1);
    }

    /***
     * 根据前缀初始化编码
     */
    public static String initCodeWithPrefix(String prefix) {
        // 默认的前缀
        String defaultPrefix = "";
        // 在这里可以根据实际需求添加更多的英文枚举判断逻辑
        // 如果没有匹配到任何枚举，可以使用默认的前缀

        // 示例：判断入参的英文枚举
        if (prefix == null || prefix.isEmpty()) {
            // 默认使用 "UN" 前缀
            return defaultPrefix + String.format("%04d", 1);
        }

        // 如果没有匹配到任何枚举，使用默认的前缀
        return prefix + String.format("%04d", 1);
    }
}

package com.jlr.ecp.order.dal.mysql.order;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.order.dal.dataobject.order.OrderTermsDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【t_order_terms(t_order_terms)】的数据库操作Mapper
* @createDate 2023-12-20 10:41:04
* @Entity com.jlr.ecp.order.dal.dataobject.order.OrderTermsDO
*/
@Mapper
public interface OrderTermsDOMapper extends BaseMapperX<OrderTermsDO> {

}





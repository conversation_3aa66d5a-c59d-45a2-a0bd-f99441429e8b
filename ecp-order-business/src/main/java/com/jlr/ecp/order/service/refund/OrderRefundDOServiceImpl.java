package com.jlr.ecp.order.service.refund;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.security.core.service.SecurityFrameworkService;
import com.jlr.ecp.framework.security.core.util.SecurityFrameworkUtils;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.notification.api.dto.ShortLinkReqDto;
import com.jlr.ecp.notification.api.jaguarlandover.ShorLinkAPI;
import com.jlr.ecp.order.api.order.dto.CarVinAndServiceTypeDTO;
import com.jlr.ecp.order.api.order.vo.coupon.ECouponOrderDetailVO;
import com.jlr.ecp.order.api.order.vo.detail.ProductItemInfoAppVO;
import com.jlr.ecp.order.api.refund.dto.*;
import com.jlr.ecp.order.api.refund.vo.*;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.controller.app.refund.vo.RefundDetailLogVO;
import com.jlr.ecp.order.controller.app.refund.vo.RefundDetailVO;
import com.jlr.ecp.order.convert.OrderRefundDOConvert;
import com.jlr.ecp.order.convert.OrderRefundItemDOConvert;
import com.jlr.ecp.order.dal.dataobject.order.*;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundAttachmentDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundPaymentRecordsDO;
import com.jlr.ecp.order.dal.mysql.order.*;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundAttachmentDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundPaymentRecordsMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.enums.order.*;
import com.jlr.ecp.order.enums.payment.DiscountTypeEnum;
import com.jlr.ecp.order.enums.payment.PayTypeEnum;
import com.jlr.ecp.order.enums.payment.RefundTradeStatus;
import com.jlr.ecp.order.enums.phone.PhoneNumberDecodeUtil;
import com.jlr.ecp.order.enums.refund.*;
import com.jlr.ecp.order.enums.sms.ShortLinkPathEnum;
import com.jlr.ecp.order.handle.RefundHandler;
import com.jlr.ecp.order.kafka.*;
import com.jlr.ecp.order.service.refund.bg.BrandGoodsOrderRefundDOService;
import com.jlr.ecp.order.util.IdempotentCheckUtil;
import com.jlr.ecp.order.util.PIPLDataUtil;
import com.jlr.ecp.order.util.common.AttributeUtil;
import com.jlr.ecp.order.util.common.TimeFormatUtil;
import com.jlr.ecp.order.util.machine.EcouponOrderRefundStatusMachine;
import com.jlr.ecp.order.util.machine.LogisticsOrderRefundStatusMachine;
import com.jlr.ecp.order.util.machine.OrderRefundStatusMachine;
import com.jlr.ecp.order.util.money.MoneyUtil;
import com.jlr.ecp.order.util.refund.CodeGenerator;
import com.jlr.ecp.payment.api.order.PayCenterOrderApi;
import com.jlr.ecp.payment.api.order.dto.RefundRequestDTO;
import com.jlr.ecp.payment.api.order.vo.SubmitRefundOrderResp;
import com.jlr.ecp.payment.api.refund.dto.PayRefundCreateReqDTO;
import com.jlr.ecp.product.enums.product.ProductFulfilmentTypeEnum;
import com.jlr.ecp.subscription.api.icrvehicle.IcrVehicleApi;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleRespVO;
import com.jlr.ecp.subscription.api.subscripiton.SubscriptionServiceApi;
import com.jlr.ecp.subscription.api.subscripiton.dto.SubscriptionServiceQueryDTO;
import com.jlr.ecp.subscription.api.subscripiton.vo.SubscriptionServiceQueryVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.VcsOrderFulfilmentApi;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.VcsOrderFulfilmentRespVO;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.order.constant.Constants.CONCAT_SYMBOL;

/**
 * <AUTHOR>
 * @description 针对表【t_order_refund(t_order_refund)】的数据库操作Service实现
 * @createDate 2024-01-15 11:28:44
 */
@Service
@Slf4j
public class OrderRefundDOServiceImpl extends ServiceImpl<OrderRefundDOMapper, OrderRefundDO>
        implements OrderRefundDOService {

    @Resource
    private OrderRefundItemDOConvert orderRefundItemDOConvert;

    @Resource
    private OrderRefundDOConvert orderRefundDOConvert;

    @Resource
    private OrderRefundDOMapper orderRefundMapper;

    @Resource
    private OrderInfoDOMapper orderInfoMapper;

    @Resource
    private OrderRefundItemDOMapper refundItemMapper;

    @Resource
    private OrderStatusLogDOMapper statusLogMapper;

    @Resource
    private OrderRefundAttachmentDOMapper attachmentMapper;

    @Resource
    private OrderItemDOMapper orderItemMapper;

    @Resource
    private OrderRefundStatusMachine orderRefundStatusMachine;

    @Resource
    private VcsOrderInfoDOMapper vcsOrderInfoDOMapper;

    @Resource
    private PIPLDataUtil piplDataUtil;

    @Resource
    private ProducerTool producerTool;

    @Resource
    private OrderModifyDetailLogDOMapper modifyDetailLogMapper;


    @Resource
    private OrderItemLogisticsDOMapper orderItemLogisticsMapper;

    @Resource
    private OrderDiscountDetailDOMapper orderDiscountDetailDOMapper;

    @Value("${taskCode.order.cancel:order-cancel-code}")
    private String cancel;

    @Value("${order-successful-code:order-successful-code}")
    private String payment;

    @Value("${taskCode.service.activate:service-activate-code}")
    private String activate;

    @Resource
    private IcrVehicleApi icrVehicleApi;

    @Resource
    private SecurityFrameworkService securityFrameworkService;

    @Resource
    private OrderRefundItemDOMapper orderRefundItemMapper;

    @Resource
    private EcouponOrderRefundStatusMachine ecouponOrderRefundStatusMachine;

    @Resource
    private LogisticsOrderRefundStatusMachine logisticsOrderRefundStatusMachine;

    @Resource
    private RefundHandler refundHandler;

    @Resource
    private BrandGoodsOrderRefundDOService brandGoodsOrderRefundDOService;

    /***
     * 日期连字符
     */
    private static final String CONCAT = "至";

    /**
     * '移除在黑名单的vin' 消息发送的taskCode
     */
    private static final String REMOVE_BLACKLIST_VIN = "remove-blacklist-vin";


    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private SubscriptionServiceApi subscriptionServiceApi;

    @Resource
    private VcsOrderFulfilmentApi vcsOrderFulfilmentApi;

    @Resource
    private OrderStatusMappingDOMapper statusMappingMapper;

    @Resource
    private ShorLinkAPI shorLinkAPI;

    @Resource
    private PayCenterOrderApi payOrderApi;

    @Resource
    private PhoneNumberDecodeUtil phoneNumberDecodeUtil;

    @Resource
    private IdempotentCheckUtil idempotentCheckUtil;


    @Resource
    private OrderPaymentRecordsMapper orderPaymentRecordsMapper;


    @Resource
    private OrderRefundPaymentRecordsMapper orderRefundPaymentRecordsMapper;

    @Resource
    private OrderCouponDetailDOMapper orderCouponDetailDOMapper;

    @Resource
    private RedisService redisService;

    @Resource
    private OrderRefundDOMapper orderRefundDOMapper;


    @Override
    public PageResult<OrderRefundPageVO> getPage(OrderRefundPageReqDTO dto) {
        //orderCode 查询逻辑,先查询parentCode,以parentCode去匹配
        if (StringUtils.isNotBlank(dto.getOriginOrderCode())) {
            OrderInfoDO orderInfoDO = orderInfoMapper.getParentCode(dto.getOriginOrderCode());
            if (orderInfoDO != null) {
                dto.setOriginOrderCode(orderInfoDO.getParentOrderCode());
            }
        }
        if (StrUtil.isNotEmpty(dto.getRefundOrderStatus())) {
            List<Integer> refundOrderStatusList = Arrays.stream(dto.getRefundOrderStatus().split(","))
                    .map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
            dto.setStatusList(refundOrderStatusList);
        }
        if (StringUtils.isNotBlank(dto.getMobile())) {
            dto.setMobile(SecureUtil.md5(dto.getMobile()));
        }
        if (StringUtils.isNotBlank(dto.getInControlId())) {
            dto.setInControlId(SecureUtil.md5(dto.getInControlId()));
        }
        if (StringUtils.isNotBlank(dto.getVin())) {
            dto.setVin(SecureUtil.md5(dto.getVin()));
        }
        // 获取订单来源
        if (Objects.nonNull(dto.getOrderSource())) {
            List<Integer> orderChannel = OrderSourceEnum.buildOrderChannel(dto.getOrderSource());
            dto.setOrderChannelList(orderChannel);
        }
        //设置business code 只查询VCS数据
        dto.setBusinessCode(BusinessIdEnum.VCS.getCode());
        Page<OrderRefundPageVO> pageResult = orderRefundMapper.getPage(new Page<>(dto.getPageNo(), dto.getPageSize()), dto);
        List<OrderRefundPageVO> list = pageResult.getRecords();
//        handleList(list);
        return new PageResult<>(list, pageResult.getTotal());
    }

    private void handleList(List<OrderRefundPageVO> list) {
        Map<String, String> decodeContactText = getDecodedTextMap(list, OrderRefundPageVO::getContactPhoneCT, Constants.AUTH_CODE_VIEW_PHONE);
        Map<String, String> decodeIcrText = getDecodedTextMap(list, OrderRefundPageVO::getIcrCT, Constants.AUTH_CODE_VIEW_ICR);
        Map<String, String> decodeCarVinText = getDecodedTextMap(list, OrderRefundPageVO::getCarVinCT, Constants.AUTH_CODE_VIEW_VIN);
        for (OrderRefundPageVO vo : list) {
            vo.setContactPhone(Optional.ofNullable(decodeContactText).map(map -> map.get(vo.getContactPhoneCT())).orElse(vo.getContactPhone()));
            vo.setInControlId(Optional.ofNullable(decodeIcrText).map(map -> map.get(vo.getIcrCT())).orElse(vo.getInControlId()));
            vo.setCarVin(Optional.ofNullable(decodeCarVinText).map(map -> map.get(vo.getCarVinCT())).orElse(vo.getCarVin()));
        }
    }

    private Map<String, String> getDecodedTextMap(List<OrderRefundPageVO> list, Function<OrderRefundPageVO, String> mapper, String authCode) {
        if (securityFrameworkService.hasPermission(authCode)) {
            List<String> values = list.stream().map(mapper).distinct().collect(Collectors.toList());
            return piplDataUtil.getDecodeListText(values);
        }
        return Collections.emptyMap();
    }

    private boolean hasViewDecryptedPermission(String authCode) {
        Authentication authentication = SecurityFrameworkUtils.getAuthentication();
        // 使用 Spring Security 的权限检查
        return authentication.getAuthorities().stream()
                .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(authCode));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyOrderRefund(OrderRefundApplyDTO refundApplyDTO) {
        //1.校验逻辑：
        //TODO 1.1接口幂等校验防止重复提交
        String idempotentKey = Constants.REFUND_IDEMPOTENT_KEY + refundApplyDTO.getOriginOrderCode();
        if (!idempotentCheckUtil.checkIdempotent(idempotentKey)) {
            throw exception(ErrorCodeConstants.REPEAT_SUBMISSION);
        }
        //1.2数据真实性校验：订单是否存在
        OrderInfoDO orderInfoDO = checkDataReal(refundApplyDTO.getOriginOrderCode());
        if (orderInfoDO == null) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        // 1.3订单状态校验：订单当前状态能否符合可退单的状态（订单状态:订单完成或者已支付 ，支付状态：已支付）
        if (checkOrderStatus(orderInfoDO)) {
            throw exception(ErrorCodeConstants.REFUND_ORDER_STATUS_ERROR);
        }
        // 1.4 校验订单能否被取消：是否存在
        if (checkOrderEnableCancel(refundApplyDTO)) {
            return false;
        }
        //1.5.最大可退金额校验：校验计算。根据item表里面金额计算最大金额累计和提交金额对比，累计金额>=提交金额
        checkSubmitAmount(refundApplyDTO, orderInfoDO);

        //1.6 校验订阅服务这个车是否处于激活中 处于激活中就不可以提交申请
        List<LocalDateTime> seviceEndDateList = new ArrayList<>();
        for (ProductItemInfoDTO productItemInfoDTO : refundApplyDTO.getProductItemInfoList()) {
            if (CollectionUtils.isEmpty(productItemInfoDTO.getNext())) {
                seviceEndDateList.add(productItemInfoDTO.getServiceEndDate());
            } else {
                for (ProductItemInfoDTO next : productItemInfoDTO.getNext()) {
                    seviceEndDateList.add(next.getServiceEndDate());
                }
            }
        }
        checkFulfilmentActive(orderInfoDO.getOrderCode(), seviceEndDateList);


        //2.创建订单：(多张表修改，需要开启事务)
        //2.1构建t_order_refund退单主表，订单号规则：用"R"+子订单号+"三位数组"组合而成
        OrderRefundDO orderRefundDO = new OrderRefundDO();
        assembleOrderRefundDO(refundApplyDTO, orderRefundDO);
        int insert = orderRefundMapper.insert(orderRefundDO);

        //2.2.批量插入t_order_refund_attachment 退单订单订单附件
        List<OrderRefundAttachmentDO> refundAttachmentList = getOrderRefundAttachmentList(refundApplyDTO, orderRefundDO);
        if (!CollectionUtils.isEmpty(refundAttachmentList)) {
            attachmentMapper.insertBatch(refundAttachmentList);
        }

        //2.3.批量插入t_order_refund_item  对应该退单号对应的订单细则item_code
        List<OrderRefundItemDO> itemList = getOrderRefundItemList(refundApplyDTO, orderRefundDO);
        if (!CollectionUtils.isEmpty(itemList)) {
            refundItemMapper.insertBatch(itemList);
        }

        //3.修改订单状态：t_order_info 根据refundOrderStatus修改订单状态：1.发起整单退款申请 2.发起部分退款申请 ，和状态t_order_status_log记录
        Integer beforeStatus = orderInfoDO.getOrderStatus();
        Integer beforeRefundStatus = orderRefundDO.getRefundOrderStatus();
        Integer event;
        //整单取消还是部分取消
        if (refundApplyDTO.getRefundType().equals(RefundTypeEnum.FULL_REFUND.getCode())) {
            event = OrderRefundEventEnum.EVENT_FULL_REFUND_APPLY.getCode();
        } else {
            event = OrderRefundEventEnum.EVENT_PARTIAL_REFUND_APPLY.getCode();
        }
        //入参：当前订单信息，当前退单信息，触发事件编码
        orderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, orderRefundDO);
        orderInfoMapper.updateById(orderInfoDO);
        //记录订单状态变化
        OrderStatusLogDO orderStatusLog
                = assembleOrderStatusLogForCustom(orderInfoDO.getOrderCode(),
                beforeStatus, orderInfoDO.getOrderStatus(), orderRefundDO.getRefundOrderStatus());
        OrderStatusLogDO orderRefundStatusLog
                = assembleOrderStatusLogDO(orderRefundDO.getRefundOrderCode(),
                beforeRefundStatus, orderRefundDO.getRefundOrderStatus());

        statusLogMapper.insert(orderStatusLog);
        statusLogMapper.insert(orderRefundStatusLog);
        //4.记录订单操作日志 t_order_modify_log
        modifyDetailLogMapper.createModifyLog(orderInfoDO.getOrderCode(), OrderModifyLogEnum.getDescriptionByCode(refundApplyDTO.getRefundType()),
                null, null);

        return insert > 0;
    }


    @Override
    public Boolean checkOrderRefund(OrderRefundCheckDTO refundCheckDTO) {
        log.info("校验退单, refundCheckDTO:{}", refundCheckDTO);
        OrderInfoDO orderInfoDO = checkDataReal(refundCheckDTO.getOriginOrderCode());
        //校验数据真实性
        if (orderInfoDO == null) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        //校验是否是vcs订单
        List<ProductItemInfoDTO> productItemInfoList = new ArrayList<>();
        for (ProductItemInfoDTO productItemInfoDTO : refundCheckDTO.getProductItemInfoList()) {
            if (CollectionUtils.isEmpty(productItemInfoDTO.getNext())) {
                productItemInfoList.add(productItemInfoDTO);
            } else {
                productItemInfoList.addAll(productItemInfoDTO.getNext());
            }
        }
        checkProductItemInfoDTO(productItemInfoList, orderInfoDO);
        return true;
    }

    @Override
    public PageResult<OrderRefundPageRespNewVO> orderRefundPageNew(OrderRefundPageReqNewDTO dto) {
        //处理手机号
        handlePhone(dto);
        //todo 暂时保留
        if (StringUtils.isBlank(dto.getBusinessCode())) {
            dto.setBusinessCode(BusinessIdEnum.LRE.getCode());
        }
        Page<OrderRefundPageRespNewVO> orderPage = orderRefundMapper.getPageNew(new Page<>(dto.getPageNo(), dto.getPageSize()), dto);

        List<OrderRefundPageRespNewVO> orderList = orderPage.getRecords();
        if (CollUtil.isEmpty(orderList)) {
            return new PageResult<>(Collections.emptyList(), orderPage.getTotal());
        }
        // 根据退单号找出退单商品行
        List<RefundOrderItemVO> orderRefundItemDOList = refundItemMapper.getRefundOrderItemList(orderList.stream().map(OrderRefundPageRespNewVO::getRefundOrderCode).collect(Collectors.toList()));
        Map<String, List<RefundOrderItemVO>> orderRefundItemDOMap = orderRefundItemDOList.stream()
                .collect(Collectors.groupingBy(RefundOrderItemVO::getRefundOrderCode));

        for (OrderRefundPageRespNewVO orderRefundPageRespNewVO : orderList) {
            //处理创建方枚举
            orderRefundPageRespNewVO.setRefundSourceName(orderRefundPageRespNewVO.getRefundSource() == null ? "" : RefundOrderOperationTypeEnum.getFrontNameByCode(orderRefundPageRespNewVO.getRefundSource()));
            List<RefundOrderItemVO> refundOrderItemVOS = orderRefundItemDOMap.get(orderRefundPageRespNewVO.getRefundOrderCode());
            if (!CollectionUtils.isEmpty(refundOrderItemVOS)) {
                orderRefundPageRespNewVO.setRefundOrderItemVOList(refundOrderItemVOS);
            }
            //运费单位转换
            orderRefundPageRespNewVO.setFreightAmount(MoneyUtil.convertFromCents(new BigDecimal(StringUtils.isEmpty(orderRefundPageRespNewVO.getFreightAmount()) ? "0" : orderRefundPageRespNewVO.getFreightAmount())));
            orderRefundPageRespNewVO.setRefundFreight(MoneyUtil.convertFromCents(new BigDecimal(StringUtils.isEmpty(orderRefundPageRespNewVO.getRefundFreight()) ? "0" : orderRefundPageRespNewVO.getRefundFreight())));
        }
        return new PageResult<>(orderList, orderPage.getTotal());
    }

    private void handlePhone(OrderRefundPageReqNewDTO dto) {
        if (StringUtils.isNotBlank(dto.getWxPhone())) {
            dto.setWxPhone(SecureUtil.md5(dto.getWxPhone()));
        }
        if (StringUtils.isNotBlank(dto.getRecipientPhone())) {
            dto.setRecipientPhone(SecureUtil.md5(dto.getRecipientPhone()));
        }
        if (StringUtils.isNotBlank(dto.getContactPhone())) {
            dto.setContactPhone(SecureUtil.md5(dto.getContactPhone()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editOrderRefund(OrderRefundEditDTO orderRefundEditDTO) {
        OrderRefundDO orderRefundDO = orderRefundMapper.selectOne(new LambdaQueryWrapper<OrderRefundDO>().eq(OrderRefundDO::getRefundOrderCode, orderRefundEditDTO.getRefundOrderCode()));
        if (orderRefundDO == null) {
            //退单不存在
            throw exception(ErrorCodeConstants.ORDER_REFUND_NOT_EXIST);
        }
        //查询退单行
        List<OrderRefundItemDO> orderRefundItemDOList = refundItemMapper.selectList(new LambdaQueryWrapper<OrderRefundItemDO>()
                .eq(OrderRefundItemDO::getRefundOrderCode, orderRefundEditDTO.getRefundOrderCode())
                .orderByDesc(OrderRefundItemDO::getCreatedTime));
        if (CollectionUtils.isEmpty(orderRefundItemDOList)) {
            //退单订单行不存在
            throw exception(ErrorCodeConstants.ORDER_REFUND_ITEM_NOT_EXIST);
        }
        OrderRefundDO updateDO = new OrderRefundDO();
        updateDO.setRefundOrderCode(orderRefundEditDTO.getRefundOrderCode());
        updateDO.setRefundRemark(orderRefundEditDTO.getRefundRemark());
        updateDO.setLogisticsCode(orderRefundEditDTO.getLogisticsCode());
        updateDO.setLogisticsCompanyCode(orderRefundEditDTO.getLogisticsCompanyCode());
        updateDO.setLogisticsCompanyName(orderRefundEditDTO.getLogisticsCompanyName());
        int update = orderRefundMapper.update(updateDO, new LambdaUpdateWrapper<OrderRefundDO>()
                .eq(OrderRefundDO::getRefundOrderCode, orderRefundEditDTO.getRefundOrderCode())
                .set(StringUtils.isNotBlank(orderRefundEditDTO.getLogisticsCode()), OrderRefundDO::getLogisticsCode, orderRefundEditDTO.getLogisticsCode())
                .set(StringUtils.isNotBlank(orderRefundEditDTO.getLogisticsCompanyCode()), OrderRefundDO::getLogisticsCompanyCode, orderRefundEditDTO.getLogisticsCompanyCode())
                .set(StringUtils.isNotBlank(orderRefundEditDTO.getLogisticsCompanyName()), OrderRefundDO::getLogisticsCompanyName, orderRefundEditDTO.getLogisticsCompanyName())
                .set(StringUtils.isNotBlank(orderRefundEditDTO.getRefundRemark()), OrderRefundDO::getRefundRemark, orderRefundEditDTO.getRefundRemark()));
        if (update > 0) {
            if (OrderTypeEnum.ELECTRONIC_COUPON.getCode().equals(orderRefundDO.getRefundFulfilmentType())) {
                if (!Objects.equals(orderRefundEditDTO.getRefundRemark(), orderRefundDO.getRefundRemark())) {
                    insertModifyLog("运营人员备注", orderRefundDO.getRefundRemark(), orderRefundEditDTO.getRefundRemark(),
                            orderRefundEditDTO.getRefundOrderCode(), OrderModifyLogEnum.EDIT_ORDER_REFUND_REMARK.getDescription());
                }
            } else {
                // 如果是已审核，待发货状态，且已填写物流信息，则需要将售后单状态往后走一步
                if (Objects.equals(RefundLogisticsStatusEnum.PENDING_ITEM_RETURN.getCode(), orderRefundDO.getLogisticsRefundStatus())
                        && StringUtils.isNotBlank(orderRefundEditDTO.getLogisticsCode())
                        && StringUtils.isNotBlank(orderRefundEditDTO.getLogisticsCompanyCode())) {
                    OrderRefundLogisticsDTO orderRefundLogisticsDTO = new OrderRefundLogisticsDTO();
                    orderRefundLogisticsDTO.setRefundOrderCode(orderRefundEditDTO.getRefundOrderCode());
                    orderRefundLogisticsDTO.setLogisticsCode(orderRefundEditDTO.getLogisticsCode());
                    orderRefundLogisticsDTO.setLogisticsCompanyCode(orderRefundEditDTO.getLogisticsCompanyCode());
                    orderRefundLogisticsDTO.setLogisticsCompanyName(orderRefundEditDTO.getLogisticsCompanyName());
                    orderRefundLogisticsDTO.setRefundOrderItemCode(orderRefundItemDOList.get(0).getOrderItemCode());
                    brandGoodsOrderRefundDOService.submitLogisticsInfo(orderRefundLogisticsDTO);
                }

                doInsertModifyLog(orderRefundEditDTO, orderRefundDO);
            }

        }
        return true;
    }

    private void doInsertModifyLog(OrderRefundEditDTO orderRefundEditDTO, OrderRefundDO orderRefundDO) {
        if (!Objects.equals(orderRefundEditDTO.getRefundRemark(), orderRefundDO.getRefundRemark())) {
            insertModifyLog("运营人员备注", orderRefundDO.getRefundRemark(), orderRefundEditDTO.getRefundRemark(),
                    orderRefundEditDTO.getRefundOrderCode(), OrderModifyLogEnum.ORDER_REFUND_REMARK.getDescription());
        }
        if (!Objects.equals(orderRefundEditDTO.getLogisticsCode(), orderRefundDO.getLogisticsCode())) {
            insertModifyLog("寄回物流单号", orderRefundDO.getLogisticsCode(), orderRefundEditDTO.getLogisticsCode(),
                    orderRefundEditDTO.getRefundOrderCode(), OrderModifyLogEnum.CUSTOMER_INFO.getDescription());
        }
        if (!Objects.equals(orderRefundEditDTO.getLogisticsCompanyCode(), orderRefundDO.getLogisticsCompanyCode())) {
            insertModifyLog("寄回物流公司", orderRefundDO.getLogisticsCompanyName(), orderRefundEditDTO.getLogisticsCompanyName(),
                    orderRefundEditDTO.getRefundOrderCode(), OrderModifyLogEnum.CUSTOMER_INFO.getDescription());
        }
    }

    private void insertModifyLog(String description, String oldValStr, String newValStr, String refundOrderCode, String enumDescription) {
        //记录订单操作日志 t_order_modify_log 订单编辑
        JSONObject oldVal = new JSONObject();
        JSONObject newVal = new JSONObject();
        oldVal.put(description, oldValStr == null ? "" : oldValStr);
        newVal.put(description, newValStr == null ? "" : newValStr);
        modifyDetailLogMapper.createModifyLog(refundOrderCode, enumDescription, oldVal.toJSONString(), newVal.toJSONString());
    }

    @Override
    public OrderRefundDetailVO getOrderRefundDetailNew(String orderRefundCode) {
        OrderRefundDO orderRefundDO = orderRefundMapper.selectOne(
                new LambdaQueryWrapper<OrderRefundDO>().eq(OrderRefundDO::getRefundOrderCode, orderRefundCode));
        if (orderRefundDO == null) {
            throw exception(ErrorCodeConstants.ORDER_REFUND_NOT_EXIST);
        }
        //获取订单信息
        OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(
                new LambdaQueryWrapper<OrderInfoDO>().eq(OrderInfoDO::getOrderCode, orderRefundDO.getOriginOrderCode()));
        if (orderInfoDO == null) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        //获取退单商品行
        List<OrderRefundItemDO> orderRefundItemDOList = orderRefundItemMapper.selectList(
                new LambdaQueryWrapper<OrderRefundItemDO>().eq(OrderRefundItemDO::getRefundOrderCode, orderRefundDO.getRefundOrderCode()));
        //获取退单商品行对应的商品信息
        List<OrderItemDO> orderItemDOList = orderItemMapper.selectList(
                new LambdaQueryWrapper<OrderItemDO>().in(OrderItemDO::getOrderItemCode, orderRefundItemDOList.stream().map(OrderRefundItemDO::getOrderItemCode).collect(Collectors.toList())));

        OrderRefundDetailVO vo = new OrderRefundDetailVO();
        vo.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
        vo.setParentOrderCode(orderInfoDO.getParentOrderCode());
        vo.setOriginOrderCode(orderRefundDO.getOriginOrderCode());
        vo.setCreatedTime(orderRefundDO.getCreatedTime());
        vo.setCouponRefundStatus(orderRefundDO.getCouponRefundStatus());
        vo.setLogisticsRefundStatus(orderRefundDO.getLogisticsRefundStatus());
        if (orderRefundDO.getCouponRefundStatus() != null) {
            vo.setCouponRefundStatusName(RefundCouponStatusEnum.getByCode(orderRefundDO.getCouponRefundStatus()).getName());
        }
        vo.setSupDesc(orderRefundDO.getSupDesc());
        if (orderRefundDO.getRefundStatusSup() != null) {
            RefundStatusSupEnum refundStatusSupEnum = RefundStatusSupEnum.getByCode(orderRefundDO.getRefundStatusSup());
            vo.setRefundStatusSup(refundStatusSupEnum == null ? "" : refundStatusSupEnum.getName());
        }
        vo.setFulfillmentType(orderRefundDO.getRefundFulfilmentType());
        vo.setFulfillmentTypeName(ProductFulfilmentTypeEnum.description(orderRefundDO.getRefundFulfilmentType()));
        vo.setRefundSourceName(orderRefundDO.getRefundSource() == null ? "" : RefundOrderOperationTypeEnum.getFrontNameByCode(orderRefundDO.getRefundSource()));
        vo.setRefundType(orderRefundDO.getRefundOrderType());
        vo.setRefundTypeName(orderRefundDO.getRefundOrderType() == null ? "" : RefundOrderTypeEnum.getNameByCode(orderRefundDO.getRefundOrderType()));
        setRefundReason(orderRefundDO, vo);
        vo.setWxPhone(orderInfoDO.getWxPhone());
        vo.setWxPhoneMix(orderInfoDO.getWxPhoneMix());
        vo.setContactPhone(orderInfoDO.getContactPhone());
        vo.setContactPhoneMix(orderInfoDO.getContactPhoneMix());
        vo.setRefundRemark(orderRefundDO.getRefundRemark());
        vo.setLogisticsCode(orderRefundDO.getLogisticsCode());
        vo.setLogisticsCompanyCode(orderRefundDO.getLogisticsCompanyCode());
        vo.setLogisticsCompanyName(orderRefundDO.getLogisticsCompanyName());
        vo.setLogisticsAttachmentList(handleAttachment(orderRefundDO.getLogisticsAttachment()));
        vo.setReturnAuditRemark(orderRefundDO.getReturnAuditRemark());
        vo.setRefundAuditRemark(orderRefundDO.getRefundAuditRemark());
        vo.setRefundFreight(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderRefundDO.getRefundFreight() == null ? 0 : orderRefundDO.getRefundFreight())));
        List<OrderItemLogisticsDO> orderItemLogisticsDOList = orderItemLogisticsMapper.selectList(new LambdaQueryWrapper<OrderItemLogisticsDO>().eq(OrderItemLogisticsDO::getOrderCode, orderRefundDO.getOriginOrderCode()));
        if (!CollectionUtils.isEmpty(orderItemLogisticsDOList)) {
            vo.setRecipientPhone(orderItemLogisticsDOList.get(0).getRecipientPhone());
            vo.setRecipientPhoneMix(orderItemLogisticsDOList.get(0).getRecipientPhoneMix());
            vo.setRecipientAddress(orderItemLogisticsDOList.get(0).getDetailAddress());
            vo.setRecipientAddressMix(orderItemLogisticsDOList.get(0).getDetailAddressMix());
            vo.setRecipient(orderItemLogisticsDOList.get(0).getRecipient());
            vo.setRecipientMix(orderItemLogisticsDOList.get(0).getRecipientMix());
        }
        //退货商品详情
        assembleOrderRefundItemList(orderItemDOList, orderRefundItemDOList, orderRefundDO, vo);
        //查询退单状态记录
        List<OrderStatusLogDO> statusLogList = statusLogMapper
                .selectList(new LambdaQueryWrapperX<OrderStatusLogDO>()
                        .eq(OrderStatusLogDO::getOrderCode, orderRefundDO.getRefundOrderCode())
                        .eq(BaseDO::getIsDeleted, false)
                        .orderByAsc(OrderStatusLogDO::getId));
        List<OrderRefundDetailVO.RefundDetailLogVO> logList = buildStatusLogList(statusLogList, orderInfoDO.getOrderType());
        vo.setLogList(logList);
        //补充图片
        List<OrderRefundAttachmentDO> list = attachmentMapper.selectList(new LambdaQueryWrapperX<OrderRefundAttachmentDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundAttachmentDO::getRefundOrderCode, orderRefundCode));
        if (CollUtil.isNotEmpty(list)) {
            List<String> attachmentUrl = list.stream().map(OrderRefundAttachmentDO::getAttachmentUrl).collect(Collectors.toList());
            vo.setAttachmentUrl(attachmentUrl);
        }
        //支付信息
        assemblePaymentInfoVO(orderInfoDO, vo);
        return vo;
    }

    private static void setRefundReason(OrderRefundDO orderRefundDO, OrderRefundDetailVO vo) {
        if (OrderTypeEnum.ELECTRONIC_COUPON.getCode().equals(orderRefundDO.getRefundFulfilmentType())) {
            CouponRefundReasonEnum byCode = CouponRefundReasonEnum.getByCode(orderRefundDO.getRefundReason());
            if (byCode != null) {
                vo.setRefundReason(byCode.getName());
            }
        } else {
            LogisticsRefundReasonEnum byCode = LogisticsRefundReasonEnum.getByCode(orderRefundDO.getRefundReason());
            if (byCode != null) {
                vo.setRefundReason(byCode.getName());
            }
        }
    }

    private void assemblePaymentInfoVO(OrderInfoDO orderInfoDO, OrderRefundDetailVO vo) {
        List<OrderDiscountDetailDO> orderDiscountDetailDOList = orderDiscountDetailDOMapper.selectList(new LambdaQueryWrapper<OrderDiscountDetailDO>()
                .eq(OrderDiscountDetailDO::getOrderCode, orderInfoDO.getOrderCode())
                .eq(OrderDiscountDetailDO::getIsDeleted, false));

        OrderDiscountDetailDO orderDiscountDetailDOByCoupon = orderDiscountDetailDOList.stream().filter(item -> item.getDiscountType().equals(DiscountTypeEnum.COUPON_DISCOUNT.getType())).findFirst().orElse(null);

        OrderPaymentRecordsDO paymentRecord = orderPaymentRecordsMapper.queryLastByOrderCode(orderInfoDO.getOrderCode());

        List<String> paymentMethodList = new ArrayList<>();
        vo.setCostAmount(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderInfoDO.getCostAmount() + orderInfoDO.getFreightAmount())));
        if (vo.getCostAmount() != null) {
            paymentMethodList.add(PayTypeEnum.CASH.getDesc());
        }
        vo.setPointAmount(orderInfoDO.getPointAmount());
        vo.setFreightAmount(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderInfoDO.getFreightAmount() == null ? 0 : orderInfoDO.getFreightAmount())));
        if (vo.getPointAmount() != null && vo.getPointAmount() > 0) {
            paymentMethodList.add(PayTypeEnum.POINTS.getDesc());
        }
        if (orderDiscountDetailDOByCoupon != null && orderDiscountDetailDOByCoupon.getCouponCode() != null) {
            vo.setCouponType(CouponTypeEnum.getByType(orderDiscountDetailDOByCoupon.getCouponModelClassify()).getDesc());
            vo.setCouponName(orderDiscountDetailDOByCoupon.getCouponModelName());
            vo.setCouponCode(orderDiscountDetailDOByCoupon.getCouponCode());
        }
        if (vo.getCouponType() != null) {
            paymentMethodList.add(PayTypeEnum.COUPON.getDesc());
        }
        vo.setPaymentMethod(paymentMethodList.stream().collect(Collectors.joining("+")));
        if (paymentRecord != null && paymentRecord.getPayFinishTime() !=null) {
            vo.setPaymentTime(TimeFormatUtil.timeToStringByFormat(paymentRecord.getPayFinishTime(), TimeFormatUtil.formatter_6));
        }
    }

    private List<String> handleAttachment(String logisticsAttachment) {
        List<String> attachmentUrl = new ArrayList<>();
        if (StringUtils.isNotEmpty(logisticsAttachment)) {
            String[] split = logisticsAttachment.split(",");
            Collections.addAll(attachmentUrl, split);
            return attachmentUrl;
        }
        return attachmentUrl;
    }

    @Override
    public List<OrderRefundDto> getOrderRefundInfoByOrderCode(String orderCode) {

        log.info("查询的 Order Code:{}", orderCode);

        List<OrderRefundDto> orderRefundDtoList = new ArrayList<>();

        if (StringUtils.isEmpty(orderCode)) {
            return orderRefundDtoList;
        }

        // 在 DB 中查询
        QueryWrapper<OrderRefundDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderRefundDO::getOriginOrderCode, orderCode);
        queryWrapper.lambda().eq(OrderRefundDO::getIsDeleted, false);
        List<OrderRefundDO> orderRefundDOS = orderRefundDOMapper.selectList(queryWrapper);

        log.info("查询到的结果:{}", JSON.toJSONString(orderRefundDOS));

        // Convert to DTO
        return orderRefundDOConvert.toDtoList(orderRefundDOS);
    }

    @Override
    public List<OrderRefundItemDto> getOrderRefundItemByOrderRefundCode(List<String> orderRefundCodeList) {

        List<OrderRefundItemDto> orderRefundItemDtoList = new ArrayList<>();

        if (ObjectUtil.isEmpty(orderRefundCodeList)) {
            return orderRefundItemDtoList;
        }

        // 在 DB 中查询
        QueryWrapper<OrderRefundItemDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(OrderRefundItemDO::getRefundOrderCode, orderRefundCodeList);
        queryWrapper.lambda().eq(OrderRefundItemDO::getIsDeleted, false);
        List<OrderRefundItemDO> orderRefundItemDOS = orderRefundItemMapper.selectList(queryWrapper);

        // Convert to DTO
        return orderRefundItemDOConvert.toDtoList(orderRefundItemDOS);
    }

    private void assembleOrderRefundItemList(List<OrderItemDO> orderItemDOList, List<OrderRefundItemDO> orderRefundItemDOList,
                                             OrderRefundDO orderRefundDO, OrderRefundDetailVO vo) {
        List<RefundOrderProductInfoVO> refundOrderProductInfoVOList = orderItemDOList.stream().map(
                item -> {
                    RefundOrderProductInfoVO itemInfoVO = new RefundOrderProductInfoVO();
                    if (item == null) {
                        return itemInfoVO;
                    }
                    //退货商品信息
                    OrderRefundItemDO refundItemDO = orderRefundItemDOList.stream().filter(orderRefundItemDO -> orderRefundItemDO.getOrderItemCode().equals(item.getOrderItemCode())).findFirst().orElse(new OrderRefundItemDO());

                    itemInfoVO.setOrderItemCode(item.getOrderItemCode());
                    itemInfoVO.setProductImageUrl(item.getProductImageUrl());
                    // 虚拟商品卡券(模版)编码
                    itemInfoVO.setCouponModelCode(item.getCouponModelCode());
                    // 商品名称
                    itemInfoVO.setProductName(item.getProductName());
                    itemInfoVO.setProductCode(item.getProductCode());
                    itemInfoVO.setProductSkuCode(item.getProductSkuCode());
                    itemInfoVO.setKingdeeSkuCode(item.getKingdeeSkuCode());

                    List<OrderCouponDetailDO> orderCouponDetailDOList = orderCouponDetailDOMapper.selectList(new LambdaQueryWrapper<OrderCouponDetailDO>()
                            .eq(OrderCouponDetailDO::getOrderItemCode, item.getOrderItemCode())
                            .eq(OrderCouponDetailDO::getIsDeleted, false));

                    if (CollUtil.isNotEmpty(orderCouponDetailDOList)) {
                        // 兑换券列表
                        itemInfoVO.setCouponCodeList(orderCouponDetailDOList.stream().map(
                                orderCouponDetailDO -> {
                                    ECouponOrderDetailVO couponCodeVO = new ECouponOrderDetailVO();
                                    couponCodeVO.setCouponCode(orderCouponDetailDO.getCouponCode());
                                    couponCodeVO.setCouponModelCode(orderCouponDetailDO.getCouponModelCode());
                                    couponCodeVO.setStatus(orderCouponDetailDO.getStatus());
                                    couponCodeVO.setValidStartTime(String.valueOf(orderCouponDetailDO.getValidStartTime()));
                                    couponCodeVO.setValidEndTime(String.valueOf(orderCouponDetailDO.getValidEndTime()));
                                    couponCodeVO.setUsedTime(String.valueOf(orderCouponDetailDO.getUsedTime()));
                                    couponCodeVO.setSendTime(String.valueOf(orderCouponDetailDO.getSendTime()));
                                    return couponCodeVO;
                                }
                        ).collect(Collectors.toList()));
                        // 有效期开始时间
                        itemInfoVO.setValidStartTime(String.valueOf(orderCouponDetailDOList.get(0).getValidStartTime()));
                        // 有效期结束时间
                        itemInfoVO.setValidEndTime(String.valueOf(orderCouponDetailDOList.get(0).getValidEndTime()));
                    }
                    // 属性值列表
                    itemInfoVO.setProductAttribute(AttributeUtil.formatProductAttributes(item.getProductAttribute()));
                    // 购买数量
                    itemInfoVO.setProductQuantity(item.getProductQuantity());
                    // 已核销张数
                    itemInfoVO.setUsedCouponCount((int) orderCouponDetailDOList.stream().filter(orderCouponDetailDO -> orderCouponDetailDO.getStatus() == EcouponStatusEnum.VERIFIED.getCode()).count());
                    // 已退单张数
                    itemInfoVO.setRefundCouponCount((int) orderCouponDetailDOList.stream().filter(orderCouponDetailDO -> orderCouponDetailDO.getStatus() == EcouponStatusEnum.INVALIDATED.getCode()).count());
                    // 售后状态
                    itemInfoVO.setAfterSalesStatus(item.getAftersalesStatus());
                    // 标价（单价）
                    itemInfoVO.setProductMarketPrice(item.getProductMarketPrice() == null ? "0" : MoneyUtil.convertFromCents(new BigDecimal(item.getProductMarketPrice())));
                    // 售价(单价)
                    itemInfoVO.setProductSalePrice(item.getProductSalePrice() == null ? "0" : MoneyUtil.convertFromCents(new BigDecimal(item.getProductSalePrice())));
                    // 实付现金金额
                    itemInfoVO.setCostAmount(item.getCostAmount() == null ? "0" : MoneyUtil.convertFromCents(new BigDecimal(item.getCostAmount())));
                    //退款金额
                    itemInfoVO.setRefundMoney(refundItemDO.getRefundMoney() == null ? "0" : MoneyUtil.convertFromCents(BigDecimal.valueOf(refundItemDO.getRefundMoney())));
                    //退单实付积分
                    itemInfoVO.setRefundPoint(refundItemDO.getRefundPoint());

                    handleCouponInfo(orderRefundDO, item, itemInfoVO);
                    //最大可退金额
                    itemInfoVO.setMaxRefundMoney(MoneyUtil.convertFromCents(BigDecimal.valueOf(refundHandler.getLogisticsMaxRefundMoney(item,refundItemDO.getId()))));
                    return itemInfoVO;
                }).collect(Collectors.toList());
        vo.setRefundOrderProductInfoVOList(refundOrderProductInfoVOList);
    }

    private void handleCouponInfo(OrderRefundDO orderRefundDO, OrderItemDO item, RefundOrderProductInfoVO itemInfoVO) {
        OrderDiscountDetailDO orderDiscountDetailDO = orderDiscountDetailDOMapper.selectOne(new LambdaQueryWrapper<OrderDiscountDetailDO>()
                .eq(OrderDiscountDetailDO::getOrderItemCode, item.getOrderItemCode())
                .eq(OrderDiscountDetailDO::getIsDeleted, false)
                .last(Constants.LIMIT_ONE));
        if (orderDiscountDetailDO != null) {
            if (orderDiscountDetailDO.getDiscountType().equals(DiscountTypeEnum.COUPON_DISCOUNT.getType()) && orderDiscountDetailDO.getCouponCode() != null) {
                // 卡券类型
                // discountCouponType
                CouponTypeEnum byType = CouponTypeEnum.getByType(orderDiscountDetailDO.getCouponModelClassify());
                itemInfoVO.setDiscountCouponType(byType == null ? "" : byType.getDesc());
                // 卡券名称
                // discountCouponName
                itemInfoVO.setDiscountCouponName(orderDiscountDetailDO.getCouponModelName());
                // 卡券编码
                // discountCouponCode
                itemInfoVO.setDiscountCouponCode(orderDiscountDetailDO.getCouponCode());
                // 卡券分摊金额
                // discountAmount
                itemInfoVO.setDiscountAmount(MoneyUtil.convertFromCents(new BigDecimal(orderDiscountDetailDO.getDiscountAmount())));
            } else if (orderDiscountDetailDO.getDiscountType().equals(DiscountTypeEnum.POINT_DISCOUNT.getType())) {
                // 实付积分
                itemInfoVO.setPointAmount(orderDiscountDetailDO.getCostPoints());
            }
        }
        if (StringUtils.isNotBlank(orderRefundDO.getRefundCouponCode())) {
            itemInfoVO.setIsCouponRefunded(true);
        }
    }

    /**
     * 构建退单状态记录
     *
     * @param statusLogList
     * @param orderType
     * @return
     */
    private List<OrderRefundDetailVO.RefundDetailLogVO> buildStatusLogList(List<OrderStatusLogDO> statusLogList, Integer orderType) {
        List<OrderRefundDetailVO.RefundDetailLogVO> logList = new ArrayList<>();
        for (OrderStatusLogDO statusLog : statusLogList) {
            OrderRefundDetailVO.RefundDetailLogVO refundDetailLogVO = new OrderRefundDetailVO.RefundDetailLogVO();
            refundDetailLogVO.setChangeTime(statusLog.getChangeTime());
            refundDetailLogVO.setRefundOrderStatus(statusLog.getAfterStatus());
            if (OrderTypeEnum.ELECTRONIC_COUPON.getCode().equals(orderType)) {
                refundDetailLogVO.setText(RefundCouponStatusEnum.getByCode(statusLog.getAfterStatus()).getNameOnApp());
                refundDetailLogVO.setDetail(RefundCouponStatusEnum.getByCode(statusLog.getAfterStatus()).getDetailDescriptionOnFrontend());
            } else {
                refundDetailLogVO.setText(RefundLogisticsStatusEnum.getNameByCode(statusLog.getAfterStatus()));
                refundDetailLogVO.setDetail(RefundLogisticsStatusEnum.getFrontendNameByCode(statusLog.getAfterStatus()));
            }

            logList.add(refundDetailLogVO);
        }
        return logList;
    }

    /**
     * 校验商品项信息DTO列表与订单信息是否匹配。
     * 通过比较订单项中的vin和productCode在数据库中查询到的订单码，确保订单项与订单信息匹配且订单是最新的。
     * 如果存在不匹配或不是最新的订单，则抛出异常。
     *
     * @param productItemInfoList 商品项信息DTO列表，包含订单项码等信息。
     * @param orderInfoDO         待校验的订单信息。
     */
    private void checkProductItemInfoDTO(List<ProductItemInfoDTO> productItemInfoList, OrderInfoDO orderInfoDO) {

        List<String> orderItemCodes = productItemInfoList.stream().map(ProductItemInfoDTO::getOrderItemCode).collect(Collectors.toList());
        //1.查询根据orderItemCode查询vcsOrderInfo表,得出vin和productCode
        List<CarVinAndServiceTypeDTO> carVinAndServiceTypeDTOS = orderInfoMapper.queryCarVinAndServiceTypeByItemCodes(orderItemCodes);
        log.info("校验商品项信息DTO列表与订单信息是否匹配, productItemInfoList:{}, orderInfoDO:{}, carVinAndServiceTypeDTOS:{}",
                productItemInfoList, orderInfoDO, carVinAndServiceTypeDTOS);
        for (CarVinAndServiceTypeDTO carVinAndServiceTypeDTO : carVinAndServiceTypeDTOS) {
            //2.再根据vin和productCode反查vcsOrderInfo做时间排序取第一条
            String orderCode = orderInfoMapper.findOrderCodeByCarVinAndServiceType(carVinAndServiceTypeDTO);
            log.info("findOrderCodeByCarVinAndServiceType查询结果, orderCode:{}", orderCode);
            //3.对比第一条数据和入参的orderCode是否相等,不相等则验证不通过
            if (StringUtils.isNotBlank(orderCode)) {
                if (!orderInfoDO.getOrderCode().equals(orderCode)) {
                    throw exception(ErrorCodeConstants.VCS_ORDER_NOT_NEAREST);
                }
            }
        }
    }


    /**
     * 校验订阅服务这个车是否处于激活中 处于激活中就不可以提交申请
     */
    private void checkFulfilmentActive(String orderCode, List<LocalDateTime> serviceEndDateList) {
        //获取VCS订单
        List<VCSOrderInfoDO> vcsOrders = getVCSOrderListByOrderCode(orderCode);
        if (CollUtil.isEmpty(vcsOrders)) {
            log.info("vcs 订单 vcsOrders 为null");
            return;
        }
        LocalDateTime minServiceDate = vcsOrders.stream()
                .min(Comparator.comparing(VCSOrderInfoDO::getServiceBeginDate))
                .map(VCSOrderInfoDO::getServiceBeginDate)
                .orElse(null);
        //为null说明VCS
        if (minServiceDate != null) {
            for (LocalDateTime serviceEndDate : serviceEndDateList) {
                if (Objects.nonNull(serviceEndDate) && serviceEndDate.isBefore(minServiceDate)) {
                    throw exception(ErrorCodeConstants.REFUND_END_TIME_OUT_RANGE);
                }
            }
        }
        //发起调用查询
        if (!checkFulfilmentByVcsOrderList(vcsOrders)) {
            throw exception(ErrorCodeConstants.REFUND_APPLY_FULFILMENT_FAIL);
        }
    }

    /**
     * 检查给定的VCS订单列表是否满足特定条件。
     *
     * @param vcsOrderInfoDOS VCS订单信息列表，每个订单包含车辆VIN码和服务类型等信息。
     * @return 如果满足条件返回true，否则返回false。
     */
    private Boolean checkFulfilmentByVcsOrderList(List<VCSOrderInfoDO> vcsOrderInfoDOS) {
        log.info("检查给定的VCS订单列表是否满足特定条件, vcsOrderInfoDOS:{}", vcsOrderInfoDOS);
        // carVin::serviceType作为唯一key
        Set<String> carVinAndServiceTypeSet = new HashSet<>();
        for (VCSOrderInfoDO vcsOrderInfoDO : vcsOrderInfoDOS) {
            String carVin = piplDataUtil.getDecodeText(vcsOrderInfoDO.getCarVin());
            Integer serviceType = vcsOrderInfoDO.getServiceType();
            if (StringUtils.isNotBlank(carVin) && Objects.nonNull(serviceType)) {
                carVinAndServiceTypeSet.add(carVin + CONCAT_SYMBOL + serviceType);
            }
        }
        if (CollectionUtils.isEmpty(carVinAndServiceTypeSet)) {
            return true;
        }
        return checkForExistingVcsOrderConstraints(carVinAndServiceTypeSet);
    }

    /**
     * 通过carvinList查出t_subscription_service t_vcs_order_fufilment t_vcs_order_rollback表中的数据
     * 2.正在激活中 激活关闭中的服务不可购买
     *
     * @param carVinAndServiceTypeSet
     * @return
     */
    private Boolean checkForExistingVcsOrderConstraints(Set<String> carVinAndServiceTypeSet) {
        log.info("checkForExistingVcsOrderConstraints, , carVinAndServiceTypeSet:{}", carVinAndServiceTypeSet);
        List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList = carVinAndServiceTypeSet.stream().map(s -> {
            CarVinAndServiceTypeDTO dto = new CarVinAndServiceTypeDTO();
            String[] split = s.split(CONCAT_SYMBOL);
            dto.setCarVin(split[0]);
            dto.setServiceType(Integer.parseInt(split[1]));
            return dto;
        }).collect(Collectors.toList());
        List<com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO> dtoList = carVinAndServiceTypeList.stream().map(item -> {
            com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO dto = new com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO();
            BeanUtil.copyProperties(item, dto);
            return dto;
        }).collect(Collectors.toList());
        try {
            CommonResult<Boolean> booleanCommonResult = vcsOrderFulfilmentApi.checkCanBuyService(dtoList);
            if (booleanCommonResult != null && booleanCommonResult.getData() != null && !booleanCommonResult.getData()) {
                return false;
            }
        } catch (Exception e) {
            // 可能需要更具体的异常处理策略，例如记录日志并重新抛出异常
            log.error("检查VCS订单约束时发生异常", e);
            return false;
        }
        // 如果没有问题，则返回null，表示无需提前终止订单创建流程
        return true;
    }

    private boolean checkOrderStatus(OrderInfoDO orderInfoDO) {
        //需要订单支付状态为已支付
        if (!orderInfoDO.getPaymentStatus().equals(PaymentStatusEnum.PAID.getCode())) {
            return true;
        }
        //只有订单完成、部分取消的订单状态才能取消订单
        return !(orderInfoDO.getOrderStatus().equals(OrderStatusEnum.COMPLETED.getCode())
                || orderInfoDO.getOrderStatus().equals(OrderStatusEnum.PARTIAL_CANCELLED.getCode()));
    }

    @Override
    public RefundDetailRespVO getOrderRefundDetail(String orderRefundCode) {
        RefundDetailRespVO refundDetailRespVO = new RefundDetailRespVO();
        refundDetailRespVO.setRefundOrderCode(orderRefundCode);

        //查询orderRefundCode是否存在
        OrderRefundDO orderRefundDO = orderRefundMapper.selectOne(new LambdaQueryWrapper<OrderRefundDO>()
                .eq(OrderRefundDO::getRefundOrderCode, orderRefundCode)
                .eq(OrderRefundDO::getIsDeleted, false));
        if (orderRefundDO == null) {
            return refundDetailRespVO;
        }
        //设置基本出参信息
        BeanUtils.copyProperties(orderRefundDO, refundDetailRespVO);
        refundDetailRespVO.setRefundOrderStatusText(OrderRefundStatusEnum.getDescriptionCode(orderRefundDO.getRefundOrderStatus()));
        //出参金额
        refundDetailRespVO.setRefundMoneyAmount(MoneyUtil.convertFromCents(new BigDecimal(orderRefundDO.getRefundMoneyAmount())));
        //设置附件信息
        refundDetailRespVO.setAttachmentUrls(assembleAttachmentUrls(orderRefundCode));
        //设置主商品信息
        refundDetailRespVO.setProductItemInfoList(getProductInfoAppVO(orderRefundCode));

        return refundDetailRespVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean orderRefundApprove(OrderRefundApproveDTO refundApproveDTO) {
        //1.校验逻辑：
        //1.1接口幂等校验防止重复提交
        String idempotentKey = Constants.REFUND_IDEMPOTENT_KEY + refundApproveDTO.getRefundOrderCode();
        if (!idempotentCheckUtil.checkIdempotent(idempotentKey)) {
            throw exception(ErrorCodeConstants.REPEAT_SUBMISSION);
        }
        //1.2数据真实性校验：该退单订单是否存在
        String refundOrderCode = refundApproveDTO.getRefundOrderCode();
        OrderRefundDO orderRefundDO = orderRefundMapper.selectOne(new LambdaQueryWrapperX<OrderRefundDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundDO::getRefundOrderCode, refundOrderCode));
        if (orderRefundDO == null) {
            return false;
        }
        OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderInfoDO::getOrderCode, orderRefundDO.getOriginOrderCode()));
        //关联原订单不存在
        if (orderInfoDO == null) {
            return false;
        }
        OrderPaymentRecordsDO orderPaymentRecordsDO = orderPaymentRecordsMapper.queryByOrderCode(orderInfoDO.getOrderCode(), orderInfoDO.getParentOrderCode());
        if (orderPaymentRecordsDO == null) {
            log.info("关联的的支付记录不存在");
            return false;
        }
        //记录当前状态
        Integer beforeStatus = orderInfoDO.getOrderStatus();
        Integer beforeRefundStatus = orderRefundDO.getRefundOrderStatus();
        //如果已经是已经审核的状态，就return
        if (OrderRefundStatusEnum.isApproved(beforeRefundStatus)) {
            throw exception(ErrorCodeConstants.REFUND_APPROVE_IS_COMPLETE);
        }
        //获取当前事件:根据整单取消还是部分取消
        Integer event = getNextEventByNowRefundStatus(beforeRefundStatus, refundApproveDTO.getApproveStatus());
        //状态机去变更状态处理逻辑
        orderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, orderRefundDO);
        //设置拒绝理由
        orderRefundDO.setRejectReason(refundApproveDTO.getRejectReason());


        orderRefundMapper.updateById(orderRefundDO);
        orderInfoMapper.updateById(orderInfoDO);
        //记录订单状态变化
        OrderStatusLogDO orderStatusLog
                = assembleOrderStatusLogForCustom(orderInfoDO.getOrderCode(),
                beforeStatus, orderInfoDO.getOrderStatus(), orderRefundDO.getRefundOrderStatus());
        OrderStatusLogDO orderRefundStatusLog
                = assembleOrderStatusLogDO(orderRefundDO.getRefundOrderCode(),
                beforeRefundStatus, orderRefundDO.getRefundOrderStatus());
        statusLogMapper.insert(orderStatusLog);
        statusLogMapper.insert(orderRefundStatusLog);
        //同意退款
        executeApprove(refundApproveDTO, refundOrderCode, orderRefundDO, orderInfoDO, orderPaymentRecordsDO);

        //4.记录订单操作日志 t_order_modify_log
        modifyDetailLogMapper.createModifyLog(orderInfoDO.getOrderCode(), OrderModifyLogEnum.getDescriptionByCode(orderRefundDO.getRefundOrderStatus()),
                null, null);
        modifyDetailLogMapper.createModifyLog(orderRefundDO.getRefundOrderCode(), OrderModifyLogEnum.getDescriptionByCode(orderRefundDO.getRefundOrderStatus()),
                null, null);

        return true;
    }

    private void executeApprove(OrderRefundApproveDTO refundApproveDTO, String refundOrderCode, OrderRefundDO orderRefundDO, OrderInfoDO orderInfoDO, OrderPaymentRecordsDO orderPaymentRecordsDO) {
        if (refundApproveDTO.getApproveStatus()) {
            //判断是否发生了退款 ：
            if (orderRefundDO.getRefundMoney().equals(RefundMoneyEnum.REFUNDED.getCode())) {
                //有退款就发起退款api
                PayRefundCreateReqDTO payRefundCreateReqDTO = new PayRefundCreateReqDTO();
                payRefundCreateReqDTO.setOrderCode(orderRefundDO.getOriginOrderCode());
                payRefundCreateReqDTO.setApplyNo(orderRefundDO.getRefundOrderCode());
//                payRefundCreateReqDTO.setAppNo(PaymentAppEnum.ECP.getAppNo());
                payRefundCreateReqDTO.setParentCode(orderInfoDO.getParentOrderCode());
                payRefundCreateReqDTO.setRefundAmount(orderRefundDO.getRefundMoneyAmount());
                payRefundCreateReqDTO.setPayApplyNo(orderPaymentRecordsDO.getPayApplyNo());
                RefundRequestDTO refundRequest = new RefundRequestDTO();
                refundRequest.setOrderNo(orderRefundDO.getOriginOrderCode());
                refundRequest.setApplyNo(orderRefundDO.getRefundOrderCode());
                refundRequest.setAmount(String.valueOf(orderRefundDO.getRefundMoneyAmount()));
//                refundRequest.setDescription(orderRefundDO.getRefundRemark());
                refundRequest.setOpUserCode(orderRefundDO.getSubmitUser());
                refundRequest.setOpUserName(WebFrameworkUtils.getLoginUserName());
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, String> requestBodyMap = objectMapper.convertValue(refundRequest, Map.class);
                payRefundCreateReqDTO.setChannelExtras(requestBodyMap);
                //调用退款接口
                callRefundPayment(refundApproveDTO, orderRefundDO, payRefundCreateReqDTO);

            } else {
                //没退款 发起退单kafka 调用TSDP
                sendRollbackFulfilmentKafkaMsg(orderRefundDO);
            }

            List<OrderRefundItemDO> list = refundItemMapper.selectList(new LambdaQueryWrapperX<OrderRefundItemDO>()
                    .eq(BaseDO::getIsDeleted, false)
                    .eq(OrderRefundItemDO::getRefundOrderCode, refundOrderCode));
            handleApproveDate(list);

            List<String> itemCodes = list.stream().map(OrderRefundItemDO::getOrderItemCode).collect(Collectors.toList());
            List<OrderItemDO> itemList = orderItemMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                    .eq(BaseDO::getIsDeleted, false)
                    .in(OrderItemDO::getOrderItemCode, itemCodes));

            //send kafka msg
            Map<String, OrderItemDO> orderItemDOMap = getOrderItemMap(itemList);

            //获取vin
            List<VCSOrderInfoDO> vcsOrderInfoDOS = vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapperX<VCSOrderInfoDO>().eq(BaseDO::getIsDeleted, false).eq(VCSOrderInfoDO::getOrderCode, orderInfoDO.getOrderCode()));
            String vin = piplDataUtil.getDecodeText(vcsOrderInfoDOS.get(0).getCarVin());

            try {
                if (!CollectionUtils.isEmpty(orderItemDOMap)) {
                    for (Map.Entry<String, OrderItemDO> entry : orderItemDOMap.entrySet()) {
                        sendKafka(orderInfoDO, entry.getValue(), vin);
                    }
                }

            } catch (Exception e) {
                log.info(Constants.SEND_KAFKA_ERROR, e.getMessage());
            }
        }
    }

    private void callRefundPayment(OrderRefundApproveDTO refundApproveDTO, OrderRefundDO orderRefundDO, PayRefundCreateReqDTO payRefundCreateReqDTO) {
        log.info("发起调用退款接口 ,入参：{}", JSON.toJSONString(payRefundCreateReqDTO));
        CommonResult<SubmitRefundOrderResp> commonResult = payOrderApi.submitRefundOrder(payRefundCreateReqDTO);
        if (commonResult != null && commonResult.isSuccess() && Objects.nonNull(commonResult.getData())) {
            SubmitRefundOrderResp resp = commonResult.getData();
            if (StringUtils.isNotBlank(resp.getRefundApplyNo())) {
                OrderRefundPaymentRecordsDO recordsDO = new OrderRefundPaymentRecordsDO();
                recordsDO.setOrderCode(orderRefundDO.getOriginOrderCode());
                recordsDO.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
                recordsDO.setPayApplyNo(payRefundCreateReqDTO.getPayApplyNo());
                recordsDO.setRefundApplyNo(resp.getRefundApplyNo());
                recordsDO.setTradeStatus(RefundTradeStatus.PENDING.getCode());
                orderRefundPaymentRecordsMapper.insert(recordsDO);
            }
        } else {
            log.info("发起调用退款接口 ,异常：{}", refundApproveDTO);
            throw exception(ErrorCodeConstants.REFUND_APPROVE_SEND_REFUND);
        }
    }

    private void handleApproveDate(List<OrderRefundItemDO> list) {
        LocalDateTime now = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).withNano(0);
        for (OrderRefundItemDO orderRefundItemDO : list) {
            //退单时间业务新增判断-看到期时间是否大于当前时间，小于当前时间的话就修改为当天时间
            LocalDateTime serviceEndDate = orderRefundItemDO.getServiceEndDate();
            if (serviceEndDate != null && serviceEndDate.isBefore(now)) {
                orderRefundItemDO.setServiceEndDate(now);
            }
        }
        refundItemMapper.updateBatch(list);
    }

    /**
     * 将订单项列表转换为订单项映射表，以订单代码为键。
     *
     * @param itemList 订单项列表。
     * @return 包含订单项的映射表，键为订单代码。
     */
    private Map<String, OrderItemDO> getOrderItemMap(List<OrderItemDO> itemList) {
        log.info("将订单项列表转换为订单项映射表, itemList:{}", itemList);
        Map<String, OrderItemDO> itemMap = new HashMap<>();
        for (OrderItemDO itemDO : itemList) {
            if (OrderItemSpuTypeEnum.BUNDLE_GOOD.getCode().equals(itemDO.getOrderItemSpuType())) {
                itemMap.put(itemDO.getOrderCode(), itemDO);
            }
        }
        // 组合商品
        if (!CollectionUtils.isEmpty(itemMap)) {
            return itemMap;
        }
        // 普通商品
        for (OrderItemDO itemDO : itemList) {
            String key = itemDO.getOrderCode() + "," + itemDO.getOrderItemCode();
            if (itemMap.containsKey(key)) {
                continue;
            }
            itemMap.put(key, itemDO);
        }
        return itemMap;
    }

    private void sendKafka(OrderInfoDO orderInfoDO, OrderItemDO orderItemDO, String vin) {
        log.info("同意取消订单，发送短信kafka, orderInfoDO:{}, orderItemDO:{}, vin:{}", orderInfoDO, orderItemDO, vin);
        if (Objects.isNull(orderInfoDO) || Objects.isNull(orderItemDO)) {
            return;
        }
        OrderRefundSuccessMessage orderRefundSuccessMessage = new OrderRefundSuccessMessage();
        orderRefundSuccessMessage.setCarVin(vin);
        orderRefundSuccessMessage.setOrderNumber(orderInfoDO.getOrderCode());
        orderRefundSuccessMessage.setServiceName(orderItemDO.getProductName());
        orderRefundSuccessMessage.setParentOrderCode(orderInfoDO.getParentOrderCode());

        // 解密手机号
        String phone = phoneNumberDecodeUtil.getDecodePhone(orderInfoDO.getContactPhone());
        orderRefundSuccessMessage.setPhoneNumber(phone);

        orderRefundSuccessMessage.setTaskCode(cancel);
        orderRefundSuccessMessage.setMessageId(ecpIdUtil.nextIdStr());
        orderRefundSuccessMessage.setTenantId(TenantContextHolder.getTenantId());

        // 通过 订单号的前两位获取品牌编码 LR or JA
        String brandCode = StringUtils.left(orderInfoDO.getOrderCode(), 2);
        orderRefundSuccessMessage.setBrandCode(BrandCodeEnum.getBrandIdByBrandCode(brandCode));

        // 生成短链
        // 品牌编码为LR 的短链接生成逻辑
        CommonResult<String> stringCommonResult = null;
        if (brandCode.equals(BrandCodeEnum.LAND_ROVER.getBrandCode())) {
            String path = ShortLinkPathEnum.ORDER_DETAIL.getPath() + orderInfoDO.getOrderCode() + Constants.SHORT_LINK_QUERY;
            ShortLinkReqDto shortLinkReqDto = new ShortLinkReqDto();
            shortLinkReqDto.setPath(path);
            try {
                stringCommonResult = shorLinkAPI.genShortLink(shortLinkReqDto);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (stringCommonResult != null && stringCommonResult.getData() != null) {
                orderRefundSuccessMessage.setWxUrl(stringCommonResult.getData());
            }
        } else {
            String path = ShortLinkPathEnum.JAGUAR_ORDER_DETAIL.getPath();
            String query = "orderCode=" + orderInfoDO.getOrderCode() + Constants.SHORT_LINK_QUERY;
            log.info("generate JaguarLink path:{} query:{}", path, query);
            ShortLinkReqDto shortLinkReqDto = new ShortLinkReqDto();
            shortLinkReqDto.setPath(path);
            shortLinkReqDto.setQuery(query);
            try {
                stringCommonResult = shorLinkAPI.genJaguarLink(shortLinkReqDto);
            } catch (Exception e) {
                log.error("generate JaguarLink error:{}", e.getMessage());
                e.printStackTrace();
            }
            if (stringCommonResult != null && stringCommonResult.getData() != null) {
                orderRefundSuccessMessage.setWxUrl(stringCommonResult.getData());
            }
        }

        producerTool.sendMsg(KafkaConstants.ORDER_CANCEL_TOPIC, "", JSON.toJSONString(orderRefundSuccessMessage));

        log.info("send Order cancel kafka msg success:{}", orderRefundSuccessMessage);
    }

    @Override
    public RefundDetailVO getOrderRefundDetailByOrderCode(String orderCode) {
        RefundDetailVO refundDetailVO = new RefundDetailVO();
        OrderRefundDO orderRefundDO = orderRefundMapper.selectOne(new LambdaQueryWrapperX<OrderRefundDO>()
                .eq(OrderRefundDO::getOriginOrderCode, orderCode)
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(OrderRefundDO::getId)
                .last(Constants.LIMIT_ONE));
        //判断orderCode有没有关联数据
        if (orderRefundDO == null) {
            return refundDetailVO;
        }
        BeanUtils.copyProperties(orderRefundDO, refundDetailVO);
        refundDetailVO.setRefundMoneyAmount(
                MoneyUtil.convertFromCents(BigDecimal.valueOf(orderRefundDO.getRefundMoneyAmount())));
        //查询退单状态记录
        List<OrderStatusLogDO> statusLogList = statusLogMapper
                .selectList(new LambdaQueryWrapperX<OrderStatusLogDO>()
                        .eq(OrderStatusLogDO::getOrderCode, orderRefundDO.getRefundOrderCode())
                        .eq(BaseDO::getIsDeleted, false)
                        .orderByAsc(OrderStatusLogDO::getId));
        List<OrderStatusMappingDO> orderStatusMapping = statusMappingMapper.selectList(new LambdaQueryWrapperX<OrderStatusMappingDO>()
                .eq(BaseDO::getIsDeleted, false));
        //组装出参数据
        List<RefundDetailLogVO> logList = statusLogList.stream()
                .map(log -> {
                    RefundDetailLogVO refundDetailLogVO = new RefundDetailLogVO();
                    refundDetailLogVO.setChangeTime(log.getChangeTime());
                    String text = orderStatusMapping.stream()
                            .filter(vo -> vo.getRefundOrderStatus().equals(log.getAfterStatus()))
                            .map(OrderStatusMappingDO::getCustomerRefundOrderStatusView)
                            .findFirst().orElse(null);
                    if (text == null) {
                        text = OrderRefundStatusEnum.getDescriptionCode(log.getAfterStatus());
                    }
                    refundDetailLogVO.setText(text);
                    refundDetailLogVO.setDetail(OrderRefundDetailMappingEnum.getDescriptionByCode(log.getAfterStatus()));
                    refundDetailLogVO.setRefundOrderStatus(log.getAfterStatus());
                    return refundDetailLogVO;
                }).collect(Collectors.toList());
        refundDetailVO.setLogList(logList);
        //组装商品item信息
        refundDetailVO.setProductItemInfoList(getProductInfoAppVO(orderRefundDO.getRefundOrderCode()));
        VCSOrderInfoDO vcsOrderInfoDO = vcsOrderInfoDOMapper.selectOne(new LambdaQueryWrapperX<VCSOrderInfoDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(VCSOrderInfoDO::getOrderCode, orderCode)
                .select(VCSOrderInfoDO::getId, VCSOrderInfoDO::getCarVin, BaseDO::getCreatedTime)
                .orderByDesc(VCSOrderInfoDO::getId)
                .last(Constants.LIMIT_ONE));
        if (vcsOrderInfoDO != null) {
            refundDetailVO.setOrderTime(TimeFormatUtil.timeToStringByFormat(vcsOrderInfoDO.getCreatedTime(), TimeFormatUtil.formatter_1));
            String decryptCarVin = piplDataUtil.getDecodeText(vcsOrderInfoDO.getCarVin());
            CommonResult<IcrVehicleRespVO> commonResult = icrVehicleApi.view(decryptCarVin);
            if (commonResult != null && commonResult.isSuccess()) {
                IcrVehicleRespVO icrVehicleRespVO = commonResult.getData();
                refundDetailVO.setIcrVehicleRespVO(icrVehicleRespVO);
            }
        }
        OrderInfoDO orderInfoDO = orderInfoMapper.queryOrderDoByOrderCode(orderCode);
        if (Objects.nonNull(orderInfoDO)) {
            refundDetailVO.setCustomerRemark(orderInfoDO.getCustomerRemark());
        }
        return refundDetailVO;
    }

    @Override
    public Integer getCount() {
        List<Integer> status = new ArrayList<>();
        status.add(OrderRefundStatusEnum.PARTIAL_REFUND_APPLY.getCode());
        status.add(OrderRefundStatusEnum.FULL_REFUND_APPLY.getCode());
        Long count = orderRefundMapper.selectCount(new LambdaQueryWrapperX<OrderRefundDO>()
                .eq(BaseDO::getIsDeleted, false)
                .in(OrderRefundDO::getRefundOrderStatus, status));
        return count.intValue();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer paymentCallbackProcess(String orderRefundCode) {
        log.info("接收到退款结果回调，orderRefundCode={}", orderRefundCode);
        //1.2数据真实性校验：该退单订单是否存在
        OrderRefundDO orderRefundDO = orderRefundMapper.selectOne(new LambdaQueryWrapperX<OrderRefundDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundDO::getRefundOrderCode, orderRefundCode));
        if (orderRefundDO == null) {
            log.info("接收到退款结果回调，退单数据不存在,orderRefundCode={}", orderRefundCode);
            return -1;
        }
        //关联的不存在
        OrderRefundPaymentRecordsDO orderRefundPaymentRecords
                = orderRefundPaymentRecordsMapper.selectByRefundOrderCode(orderRefundCode);
        if (orderRefundPaymentRecords == null) {
            log.info("接收到退款结果回调，OrderRefundPaymentRecordsDO数据不存在,orderRefundCode={}", orderRefundCode);
            return -1;
        }

        OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderInfoDO::getOrderCode, orderRefundDO.getOriginOrderCode()));
        //关联原订单不存在
        if (orderInfoDO == null) {
            log.info("接收到退款结果回调，orderInfoDO数据不存在,orderRefundCode={}", orderRefundCode);
            return -1;
        }
        Integer orderType = orderInfoDO.getOrderType();
        if (OrderTypeEnum.ELECTRONIC_COUPON.getCode().equals(orderType)) {//虚拟电子券处理
            log.info("接收到退款结果回调，虚拟电子券退款回调处理中,orderRefundCode={}", orderRefundCode);
            //幂等检查
            if(EcouponOrderRefundEventEnum.ECOUPON_REFUND_COMPLETED.getCode().equals(orderRefundDO.getCouponRefundStatus())){
                //退单已经完成 直接返回正确结果
                log.info("接收到退款结果回调，虚拟电子券退单已经完成,orderRefundCode={}", orderRefundCode);
                return 0;
            }
            refundPaymentCallbackProcess(orderRefundDO,orderInfoDO);
        } else if (OrderTypeEnum.BRAND_GOOD.getCode().equals(orderType)) {//实物商品
            log.info("接收到退款结果回调，实物商品退款回调处理中,orderRefundCode={}", orderRefundCode);
            //幂等检查
            if(LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_COMPLETED.getCode().equals(orderRefundDO.getLogisticsRefundStatus())){
                //退单已经完成 直接返回正确结果
                log.info("接收到退款结果回调，实物商品退单已经完成,orderRefundCode={}", orderRefundCode);
                return 0;
            }
            refundPaymentCallbackProcess(orderRefundDO,orderInfoDO);
        } else {
            //PIVI
            List<String> orderItemCodes = getOrderItemCodes(orderRefundCode);
            if (CollUtil.isEmpty(orderItemCodes)) {
                log.info("refundItemDOS is empty,orderRefundCode:{}", orderRefundCode);
                return null;
            }
            if (refundVcs(orderItemCodes, orderInfoDO, orderRefundDO)) {
                return tsdpRefundCallBack(orderRefundCode, true);
            }
        }
        orderRefundPaymentRecords.setTradeStatus(RefundTradeStatus.SUCCESS.getCode());
        orderRefundPaymentRecords.setUpdatedTime(LocalDateTime.now());
        orderRefundPaymentRecordsMapper.updateById(orderRefundPaymentRecords);
        sendRollbackFulfilmentKafkaMsg(orderRefundDO);

        return 0;
    }

    private boolean refundVcs(List<String> orderItemCodes, OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO) {
        CommonResult<List<VcsOrderFulfilmentRespVO>> commonResult = vcsOrderFulfilmentApi.fulfilmentViewList(orderItemCodes);
        if (commonResult != null && commonResult.isSuccess()) {
            List<VcsOrderFulfilmentRespVO> list = commonResult.getData();
            if (CollUtil.isEmpty(list)) {
                return true;
            }
            for (VcsOrderFulfilmentRespVO vcsOrderFulfilmentRespVO : list) {
                if (FufilmentServiceStatusEnum.ACTIVATE_FAILURE.getStatus().equals(vcsOrderFulfilmentRespVO.getServiceStatus())) {
                    return true;
                }
            }
        }
        //记录当前状态
        Integer beforeStatus = orderInfoDO.getOrderStatus();
        Integer beforeRefundStatus = orderRefundDO.getRefundOrderStatus();
        Integer event = OrderRefundEventEnum.EVENT_FULL_REFUND_COMPLETED_PAYMENT.getCode();
        //获取当前事件:根据整单取消还是部分取消
        if (beforeRefundStatus.equals(OrderRefundStatusEnum.PARTIAL_REFUND_APPROVE.getCode())) {
            event = OrderRefundEventEnum.EVENT_PARTIAL_REFUND_COMPLETED_PAYMENT.getCode();
        }
        //已经处理状态为退款完成的直接返回
        if(OrderRefundStatusEnum.FULL_REFUND_COMPLETED.getCode().equals(beforeRefundStatus)||
                OrderRefundStatusEnum.PARTIAL_REFUND_COMPLETED.getCode().equals(beforeRefundStatus)){
            return true;
        }
        //状态机去变更状态处理逻辑
        orderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, orderRefundDO);

        orderRefundMapper.updateById(orderRefundDO);
        orderInfoMapper.updateById(orderInfoDO);
        //记录订单状态变化
        OrderStatusLogDO orderStatusLog
                = assembleOrderStatusLogForCustom(orderInfoDO.getOrderCode(),
                beforeRefundStatus, orderInfoDO.getOrderStatus(), orderRefundDO.getRefundOrderStatus());

        //状态变化需要判断
        statusLogMapper.insert(orderStatusLog);
        if (!beforeRefundStatus.equals(orderRefundDO.getRefundOrderStatus())) {
            OrderStatusLogDO orderRefundStatusLog
                    = assembleOrderStatusLogDO(orderRefundDO.getRefundOrderCode(),
                    beforeRefundStatus, orderRefundDO.getRefundOrderStatus());
            statusLogMapper.insert(orderRefundStatusLog);
        }
        return false;
    }

    private void refundPaymentCallbackProcess(OrderRefundDO orderRefundDO,OrderInfoDO orderInfoDO) {
        log.info("refundPaymentCallbackProcess 退款回调处理中 orderRefundDO:{},orderInfoDO:{}", orderRefundDO, orderInfoDO);
        //查找退款订单行 这里只会有一条
        OrderRefundItemDO orderRefundItemDO = orderRefundItemMapper.selectOne(new LambdaQueryWrapperX<OrderRefundItemDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundItemDO::getRefundOrderCode, orderRefundDO.getRefundOrderCode())
                .last(Constants.LIMIT_ONE));
        //查找订单行
        OrderItemDO orderItemDO = orderItemMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderItemDO::getOrderCode, orderRefundDO.getOriginOrderCode())
                .eq(OrderItemDO::getOrderItemCode, orderRefundItemDO.getOrderItemCode()));
        if(orderInfoDO.getOrderType().equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())){
            int event = EcouponOrderRefundEventEnum.ECOUPON_REFUND_COMPLETED.getCode();
            ecouponOrderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, orderRefundDO, orderItemDO);
            log.info("refundPaymentCallbackProcess 电子券退款回调处理成功,orderRefundCode:{}", orderRefundDO.getRefundOrderCode());
        }else if(orderInfoDO.getOrderType().equals(OrderTypeEnum.BRAND_GOOD.getCode())){
            int event = LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_COMPLETED.getCode();
            logisticsOrderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, orderRefundDO, orderItemDO);
            log.info("refundPaymentCallbackProcess 实物商品退款回调处理成功,orderRefundCode:{}", orderRefundDO.getRefundOrderCode());
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer transPaymentCallbackProcess(String orderRefundCode) {
        log.info("[OrderRefundDOServiceImpl.transPaymentCallbackProcess] Start processing refund callback, orderRefundCode: {}", orderRefundCode);
        
        //1.2数据真实性校验：该退单订单是否存在
        OrderRefundDO orderRefundDO = orderRefundMapper.selectOne(new LambdaQueryWrapperX<OrderRefundDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundDO::getRefundOrderCode, orderRefundCode));
        if (orderRefundDO == null) {
            log.info("transPaymentCallbackProcess退单数据不存在,orderRefundCode:{}", orderRefundCode);
            return -1;
        }

        OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderInfoDO::getOrderCode, orderRefundDO.getOriginOrderCode()));
        //关联原订单不存在
        if (orderInfoDO == null) {
            log.info("transPaymentCallbackProcess orderInfoDO数据不存在,orderRefundCode:{}", orderRefundCode);
            return -1;
        }
        //查找退款订单行 这里只会有一条
        OrderRefundItemDO orderRefundItemDO = orderRefundItemMapper.selectOne(new LambdaQueryWrapperX<OrderRefundItemDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundItemDO::getRefundOrderCode, orderRefundCode).last(Constants.LIMIT_ONE));
        //查找订单行
        OrderItemDO orderItemDO = orderItemMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderItemDO::getOrderCode, orderRefundDO.getOriginOrderCode())
                .eq(OrderItemDO::getOrderItemCode, orderRefundItemDO.getOrderItemCode()));
        /*int event = EcouponOrderRefundEventEnum.ECOUPON_REFUND_PROCESSING.getCode();
        ecouponOrderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, orderRefundDO, orderItemDO);*/
        //继续处理退款流程
        OrderPaymentRecordsDO orderPaymentRecordsDO = orderPaymentRecordsMapper.queryByOrderCode(orderInfoDO.getOrderCode(), orderInfoDO.getParentOrderCode());
        if (orderPaymentRecordsDO == null) {
            log.error("transPaymentCallbackProcess- 关联的支付记录不存在, 订单号: {}", orderInfoDO.getOrderCode());
        }
        log.info("[OrderRefundDOServiceImpl.transPaymentCallbackProcess] Start processing refund callback for code: {}", orderRefundCode);
        refundHandler.refundMoneyProcess(orderRefundItemDO,orderItemDO, orderRefundDO, orderInfoDO, orderPaymentRecordsDO);
        
        log.info("[OrderRefundDOServiceImpl.transPaymentCallbackProcess] Completed processing refund callback for code: {}", orderRefundCode);
        return 0;
    }

    private void sendRollbackFulfilmentKafkaMsg(OrderRefundDO orderRefundDO) {
        // send kafka msg
        // 根据orderRefundCode 去查询 所有的退款商品item
        List<VCSOrderInfoDO> vcsOrderInfoList = getVCSOrderListByOrderRefundCode(orderRefundDO.getRefundOrderCode());
        log.info("sendRollbackFulfilmentKafkaMsg, vcsOrderInfoList:{}", vcsOrderInfoList);
        if (CollUtil.isEmpty(vcsOrderInfoList)) {
            return;
        }
        Map<String, LocalDateTime> refundItemMap = getItemCodeToServiceEndDateMap(orderRefundDO.getRefundOrderCode());
        //发送rollback Kafka
        for (VCSOrderInfoDO vcsOrderInfoDO : vcsOrderInfoList) {
            RollbackSuccessFulfilmentMessage rollbackSuccessFulfilmentMessage = new RollbackSuccessFulfilmentMessage();
            rollbackSuccessFulfilmentMessage.setMessageId(ecpIdUtil.nextIdStr());
            rollbackSuccessFulfilmentMessage.setVcsOrderCode(vcsOrderInfoDO.getVcsOrderCode());
            rollbackSuccessFulfilmentMessage.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
            if (Objects.nonNull(refundItemMap.get(vcsOrderInfoDO.getOrderItemCode()))) {
                rollbackSuccessFulfilmentMessage.setServiceEndDate(refundItemMap.get(vcsOrderInfoDO.getOrderItemCode()));
            }
            rollbackSuccessFulfilmentMessage.setTenantId(TenantContextHolder.getTenantId());
            rollbackSuccessFulfilmentMessage.setCreatedBy(WebFrameworkUtils.getLoginUserName());
            rollbackSuccessFulfilmentMessage.setServiceType(vcsOrderInfoDO.getServiceType());

            producerTool.sendMsg(KafkaConstants.ORDER_REFUND_SUCCESS_TOPIC, "", JSON.toJSONString(rollbackSuccessFulfilmentMessage));
            log.info("send refund success fulfillment msg:{}", JSON.toJSONString(rollbackSuccessFulfilmentMessage));
        }
    }

    private List<VCSOrderInfoDO> getVCSOrderListByOrderRefundCode(String orderRefundCode) {
        List<String> orderItemCodes = getOrderItemCodes(orderRefundCode);
        if (CollUtil.isEmpty(orderItemCodes)) {
            log.info("refundItemDOS is empty");
            return null;
        }
        // 通过orderItemCodes 去查询所有的VCSOrderInfoDO
        return vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapper<VCSOrderInfoDO>()
                .in(VCSOrderInfoDO::getOrderItemCode, orderItemCodes)
                .eq(VCSOrderInfoDO::getIsDeleted, false)
        );
    }

    private List<String> getOrderItemCodes(String orderRefundCode) {
        List<OrderRefundItemDO> orderRefundItemDOS = refundItemMapper.selectList(new LambdaQueryWrapper<OrderRefundItemDO>()
                .eq(OrderRefundItemDO::getRefundOrderCode, orderRefundCode)
                .eq(OrderRefundItemDO::getIsDeleted, false)
        );
        if (CollUtil.isEmpty(orderRefundItemDOS)) {
            log.info("refundItemDOS is empty");
            return null;
        }
        // 将所有的退款商品item中的OrderItemCode收集起来
        return orderRefundItemDOS.stream()
                .map(OrderRefundItemDO::getOrderItemCode)
                .collect(Collectors.toList());
    }


    private List<VCSOrderInfoDO> getVCSOrderListByOrderCode(String orderCode) {

        List<OrderItemDO> orderItemDOS = orderItemMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, orderCode)
                .eq(OrderItemDO::getIsDeleted, false)
        );
        if (CollUtil.isEmpty(orderItemDOS)) {
            log.info("refundItemDOS is empty");
            return null;
        }
        // 将所有的退款商品item中的OrderItemCode收集起来
        List<String> orderItemCodes = orderItemDOS.stream()
                .map(OrderItemDO::getOrderItemCode)
                .collect(Collectors.toList());
        // 通过orderItemCodes 去查询所有的VCSOrderInfoDO
        return vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapper<VCSOrderInfoDO>()
                .in(VCSOrderInfoDO::getOrderItemCode, orderItemCodes)
                .eq(VCSOrderInfoDO::getIsDeleted, false)
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer tsdpRefundCallBack(String orderRefundCode, Boolean updateStatus) {
        //1.2数据真实性校验：该退单订单是否存在
        OrderRefundDO orderRefundDO = orderRefundMapper.selectOne(new LambdaQueryWrapperX<OrderRefundDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundDO::getRefundOrderCode, orderRefundCode));
        if (orderRefundDO == null) {
            return -1;
        }
        OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderInfoDO::getOrderCode, orderRefundDO.getOriginOrderCode()));
        //关联原订单不存在
        if (orderInfoDO == null) {
            return -1;
        }
        //记录当前状态
        Integer beforeStatus = orderInfoDO.getOrderStatus();

        if (OrderStatusEnum.FULLY_CANCELLED.getCode().equals(beforeStatus) ||
                OrderStatusEnum.PARTIAL_CANCELLED.getCode().equals(beforeStatus)) {
            log.info("订单已经状态同步过，不用再次修改,业务上做幂等");
            return 0;
        }

        Integer beforeRefundStatus = orderRefundDO.getRefundOrderStatus();
        //获取当前事件:根据整单取消还是部分取消
        Integer event = OrderRefundEventEnum.EVENT_FULL_REFUND_COMPLETED_TSDP.getCode();
        //获取当前事件:根据整单取消还是部分取消
        if (beforeRefundStatus.equals(OrderRefundStatusEnum.PARTIAL_REFUND_APPROVE.getCode())
                || beforeRefundStatus.equals(OrderRefundStatusEnum.PARTIAL_REFUND_COMPLETED.getCode())) {
            event = OrderRefundEventEnum.EVENT_PARTIAL_REFUND_COMPLETED_TSDP.getCode();
        }
        int i = 0;
        if (updateStatus) {
            //状态机去变更状态处理逻辑
            orderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, orderRefundDO);

            orderRefundMapper.updateById(orderRefundDO);
            i = orderInfoMapper.updateById(orderInfoDO);
            //记录订单状态变化
            OrderStatusLogDO orderStatusLog
                    = assembleOrderStatusLogForCustom(orderInfoDO.getOrderCode(),
                    beforeStatus, orderInfoDO.getOrderStatus(), orderRefundDO.getRefundOrderStatus());
            statusLogMapper.insert(orderStatusLog);
            if (!beforeRefundStatus.equals(orderRefundDO.getRefundOrderStatus())) {
                OrderStatusLogDO orderRefundStatusLog
                        = assembleOrderStatusLogDO(orderRefundDO.getRefundOrderCode(),
                        beforeRefundStatus, orderRefundDO.getRefundOrderStatus());
                statusLogMapper.insert(orderRefundStatusLog);
            }
        }

        return i;
    }


    /**
     * tsdp成功回调
     *
     * @param vcsOrderCode 订单编码
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer tsdpCallBack(String vcsOrderCode, Boolean updateStatus) {
        log.info("tsdp成功CallBack vcsOrderCode:{}, updateStatus:{}", vcsOrderCode, updateStatus);

        VCSOrderInfoDO vcsOrderInfoDO = vcsOrderInfoDOMapper.selectOne(new LambdaQueryWrapperX<VCSOrderInfoDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(VCSOrderInfoDO::getVcsOrderCode, vcsOrderCode));
        //vcs订单不存在
        if (vcsOrderInfoDO == null) {
            log.info("vcs订单不存在 vcsOrderCode:{}", vcsOrderCode);
            return 0;
        }
        OrderItemDO orderItemDO = orderItemMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderItemDO::getOrderItemCode, vcsOrderInfoDO.getOrderItemCode()));
        // orderItemDO 订单不存在
        if (orderItemDO == null) {
            log.info("orderItemDO订单不存在 OrderItemCode:{}", vcsOrderInfoDO.getOrderItemCode());
            return 0;
        }
        OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderInfoDO::getOrderCode, vcsOrderInfoDO.getOrderCode()));
        //关联原订单不存在
        if (orderInfoDO == null) {
            log.info("关联原订单不存在 OrderCode:{}", vcsOrderInfoDO.getOrderCode());
            return 0;
        }
        log.info("tsdp成功CallBack,vcsOrderCode:{},OrderItemCode:{},OrderCode:{}"
                , vcsOrderCode, vcsOrderInfoDO.getOrderItemCode(), vcsOrderInfoDO.getOrderCode());

        //记录当前状态
        Integer beforeStatus = orderInfoDO.getOrderStatus();

        if (OrderStatusEnum.COMPLETED.getCode().equals(beforeStatus)) {
            log.info("状态已经是订单完成无需变更,订单号：{}", orderInfoDO.getOrderCode());
            //状态已经是订单完成则直接返回0
            return 0;
        }
        //获取当前事件:TSDP激活成功 回调
        Integer event = OrderEventEnum.EVENT_TSDP_CALLBACK.getCode();
        int i = 0;
        //判断是否有状态变更
        if (updateStatus) {
            //状态机去变更状态处理逻辑
            orderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, null);
            i = orderInfoMapper.updateById(orderInfoDO);
            log.info("订单状态变更, 变化前状态:{} , 变化后状态:{}, updateById方法返回值:{} ",
                    beforeStatus, orderInfoDO.getOrderStatus(), i);
            //判断状态变化，如果
            if (!beforeStatus.equals(orderInfoDO.getOrderStatus())) {
                //a. 记录订单状态变化
                OrderStatusLogDO orderStatusLog
                        = assembleOrderStatusLogDO(orderInfoDO.getOrderCode(),
                        beforeStatus, orderInfoDO.getOrderStatus());
                statusLogMapper.insert(orderStatusLog);

                //b. 订单状态变为完成，购买PIVI服务-》'移除在黑名单的vin' 消息发送
                //查看 orderCode 下是否存在 PIVI订单，如果有就发送消息 '移除在黑名单的vin'
                log.info("订单状态变更，订单号：{}", orderInfoDO.getOrderCode());
                List<VCSOrderInfoDO> vcsOrderInfoDOS = vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapperX<VCSOrderInfoDO>()
                        .eq(VCSOrderInfoDO::getOrderCode, orderInfoDO.getOrderCode()));
                log.info("此订单下:{}，挂载的 vcsOrderCode list:{}", orderInfoDO.getOrderCode(), vcsOrderInfoDOS);

                for (VCSOrderInfoDO tempDO : vcsOrderInfoDOS) {
                    if (OrderServiceTypeEnum.PIVI.getType().equals(tempDO.getServiceType())) {
                        removeVininBlackList(tempDO.getCarVin());
                    }
                }
                sendWeChatMsg(orderInfoDO);
            }
        }

        log.info("进入激活服务成功sms 消息发送");
        try {
            sendFulfilmentSms(orderInfoDO, vcsOrderInfoDO, orderItemDO);
        } catch (Exception e) {
            log.info(Constants.SEND_KAFKA_ERROR, e.getMessage());
        }
        log.info("激活服务成功sms 消息发送 结束");
        return i;
    }

    private void sendWeChatMsg(OrderInfoDO orderInfoDO) {
        ActivateMessage activateMessage = new ActivateMessage();
        activateMessage.setMessageId(ecpIdUtil.nextIdStr());
        activateMessage.setOrderNumber(orderInfoDO.getOrderCode());
        activateMessage.setParentOrderCode(orderInfoDO.getParentOrderCode());
        activateMessage.setTenantId(TenantContextHolder.getTenantId());
        activateMessage.setCompleted(true);

        // 通过 订单号的前两位获取品牌编码 LR or JA
        String brandCode = StringUtils.left(orderInfoDO.getOrderCode(), 2);
        activateMessage.setBrandCode(BrandCodeEnum.getBrandIdByBrandCode(brandCode));
        producerTool.sendMsg(KafkaConstants.SERVICE_ACTIVATE_TOPIC, "", JSON.toJSONString(activateMessage));

        log.info("发送 微信通知消息kafka msg:{}", JSON.toJSONString(activateMessage));
    }

    /**
     * 用户自主在小程序购买PIVI-subscription（在线服务）商品后，
     * 当该订单的状态变为"订单完成"后
     * 移除391个CRC接到客诉后做过续期的客户，不再对其通知发送进行限制
     *
     * @param carVin
     */
    private void removeVininBlackList(String carVin) {
        log.info("'移除在黑名单的vin' 消息发送, 移除黑名单vin:{}，此时是加密过的vin", carVin);
        try {
            sendRemoveVinToBlackList(carVin);
        } catch (Exception e) {
            log.info(Constants.SEND_KAFKA_ERROR, e.getMessage());
        }
        log.info("'移除在黑名单的vin' 消息发送 结束");
    }

    private void sendRemoveVinToBlackList(String carVin) {
        ActivateMessage activateMessage = new ActivateMessage();
        activateMessage.setTaskCode(REMOVE_BLACKLIST_VIN);
        activateMessage.setMessageId(ecpIdUtil.nextIdStr());
        activateMessage.setTenantId(TenantContextHolder.getTenantId());
        activateMessage.setCarVin(piplDataUtil.getDecodeText(carVin));

        producerTool.sendMsg(KafkaConstants.SERVICE_ACTIVATE_TOPIC, "", JSON.toJSONString(activateMessage));
        log.info("send RemoveVinToBlackListKafka msg:{}", JSON.toJSONString(activateMessage));
    }

    /**
     * 激活服务成功sms消息发送
     *
     * @param orderInfoDO
     * @param vcsOrderInfoDO
     * @param orderItemDO
     */
    private void sendFulfilmentSms(OrderInfoDO orderInfoDO, VCSOrderInfoDO vcsOrderInfoDO, OrderItemDO orderItemDO) {
        ActivateMessage activateMessage = new ActivateMessage();
        String carVin = piplDataUtil.getDecodeText(vcsOrderInfoDO.getCarVin());
        activateMessage.setCarVin(carVin);
        activateMessage.setOrderNumber(orderInfoDO.getOrderCode());
        activateMessage.setServiceName(orderItemDO.getProductName());
        activateMessage.setParentOrderCode(orderInfoDO.getParentOrderCode());
        // 解密手机号
        String phone = phoneNumberDecodeUtil.getDecodePhone(orderInfoDO.getContactPhone());

        activateMessage.setPhoneNumber(phone);
        //组装开始时间和结束时间 例如 2023年12月30日至2024年12月30日
        String endDate = TimeFormatUtil.timeToStringByFormat(vcsOrderInfoDO.getServiceEndDate(), TimeFormatUtil.formatter_4);
        String beginDate = TimeFormatUtil.timeToStringByFormat(vcsOrderInfoDO.getServiceBeginDate(), TimeFormatUtil.formatter_4);
        String serviceMsg = beginDate + CONCAT + endDate;
        activateMessage.setValidityDate(serviceMsg);

        activateMessage.setTaskCode(activate);
        activateMessage.setMessageId(ecpIdUtil.nextIdStr());
        activateMessage.setTenantId(TenantContextHolder.getTenantId());

        // 通过 订单号的前两位获取品牌编码 LR or JA
        String brandCode = StringUtils.left(orderInfoDO.getOrderCode(), 2);
        activateMessage.setBrandCode(BrandCodeEnum.getBrandIdByBrandCode(brandCode));

        producerTool.sendMsg(KafkaConstants.SERVICE_ACTIVATE_TOPIC, "", JSON.toJSONString(activateMessage));

        log.info("发送 服务激活成功kafka msg:{}", JSON.toJSONString(activateMessage));

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer paymentMock(String orderCode, String parentOrderCode) {
        log.info("paymentMock 开始");
        log.info("paymentMock 入参 orderCode:{}", orderCode);
        if (StringUtils.isNotBlank(parentOrderCode)) {
            List<OrderInfoDO> list = orderInfoMapper.selectList(new LambdaQueryWrapperX<OrderInfoDO>()
                    .eq(BaseDO::getIsDeleted, false)
                    .eq(OrderInfoDO::getParentOrderCode, parentOrderCode)
                    .ne(OrderInfoDO::getOrderType, 0));
            log.info("paymentMock orderList:{}", JSON.toJSONString(list));
            for (OrderInfoDO orderInfoDO : list) {
                mockoperate(orderInfoDO);
            }
        } else {
            //
            OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                    .eq(BaseDO::getIsDeleted, false)
                    .eq(OrderInfoDO::getOrderCode, orderCode));
            //关联原订单不存在
            if (orderInfoDO == null) {
                return -1;
            }
            mockoperate(orderInfoDO);
        }

        log.info("paymentMock 结束");
        return 0;
    }


    private void mockoperate(OrderInfoDO orderInfoDO) {
        //发起支付流程：
        //调用payment-service
        Integer beforeStatus = orderInfoDO.getOrderStatus();
        Integer event = OrderEventEnum.EVENT_PAYMENT_SUCCESS_CALLBACK.getCode();
        //获取当前事件:根据整单取消还是部分取消
        //状态机去变更状态处理逻辑
        orderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, null);
        //记录订单状态变化
        OrderStatusLogDO orderStatusLog
                = assembleOrderStatusLogDO(orderInfoDO.getOrderCode(),
                beforeStatus, orderInfoDO.getOrderStatus());
        statusLogMapper.insert(orderStatusLog);

        int i = orderInfoMapper.updateById(orderInfoDO);

        //支付回调流程：
        Integer beforeStatusCallback = orderInfoDO.getOrderStatus();
        Integer eventCallBack = OrderEventEnum.EVENT_PAYMENT.getCode();
        //状态机去变更状态处理逻辑
        orderRefundStatusMachine.changeOrderStatus(eventCallBack, orderInfoDO, null);
        //记录订单状态变化
//            OrderStatusLogDO orderStatusLogCallback
//                    = assembleOrderStatusLogDO(orderInfoDO.getOrderCode(),
//                    beforeStatusCallback,orderInfoDO.getOrderStatus());
//            statusLogMapper.insert(orderStatusLogCallback);
        List<OrderItemDO> itemDOList = findItemByOrderCode(orderInfoDO.getOrderCode());
        List<VCSOrderInfoDO> vcsOrderInfoList = vcsOrderInfoDOMapper.selectList(new LambdaQueryWrapperX<VCSOrderInfoDO>()
                .eq(BaseDO::getIsDeleted, false)
                .in(VCSOrderInfoDO::getOrderCode, orderInfoDO.getOrderCode()));
//            i = orderInfoMapper.updateById(orderInfoDO);
        //send kafka msg
        log.info("进入支付sms 消息发送");
        try {
            if (!CollectionUtils.isEmpty(itemDOList)) {
                for (OrderItemDO orderItemDO : itemDOList) {
                    sendPaymentKafka(orderInfoDO, orderItemDO);
                }
            }
        } catch (Exception e) {
            log.info(Constants.SEND_KAFKA_ERROR, e.getMessage());
        }
        log.info("进入支付sms 消息发送 结束");
        //send sub service msg

        log.info("进入支付mock 服务包的 消息发送 ");
        try {
            if (!CollectionUtils.isEmpty(vcsOrderInfoList)) {
                for (VCSOrderInfoDO vcsOrderInfoDO : vcsOrderInfoList) {
                    sendFulfilmentKafka(vcsOrderInfoDO);
                }
            }
        } catch (Exception e) {
            log.info(Constants.SEND_KAFKA_ERROR, e.getMessage());
        }
        log.info("进入支付mock 服务包的 消息发送 结束");
    }

    private void sendFulfilmentKafka(VCSOrderInfoDO vcsOrderInfoDO) {
        log.info("进入send fulfilment Kafka 发送方法:{}", JSON.toJSONString(vcsOrderInfoDO));
        FulfilmentMessage fulfilmentMessage = new FulfilmentMessage();
        fulfilmentMessage.setMessageId(ecpIdUtil.nextIdStr());
        fulfilmentMessage.setOrderCode(vcsOrderInfoDO.getOrderCode());
        fulfilmentMessage.setOrderItemCode(vcsOrderInfoDO.getOrderItemCode());
        fulfilmentMessage.setVcsOrderCode(vcsOrderInfoDO.getVcsOrderCode());
        SubscriptionServiceQueryDTO serviceQueryDTO = new SubscriptionServiceQueryDTO();
        // 解密后设置入参
        serviceQueryDTO.setCarVin(piplDataUtil.getDecodeText(vcsOrderInfoDO.getCarVin()));
        serviceQueryDTO.setIncontrolId(piplDataUtil.getDecodeText(vcsOrderInfoDO.getIncontrolId()));

        LocalDateTime expiryDate = LocalDateTime.now();
        try {
            CommonResult<SubscriptionServiceQueryVO> serviceExpiryDate = subscriptionServiceApi.findServiceExpiryDate(serviceQueryDTO);
            log.info("sendFulfilmentKafka CommonResult<SubscriptionServiceQueryVO> :{}", JSON.toJSONString(serviceExpiryDate));
            SubscriptionServiceQueryVO data = serviceExpiryDate.getData();
            expiryDate = data.getExpiryDate();
        } catch (Exception e) {
            log.error("subscriptionServiceApi.findServiceExpiryDate error:{}", e.getMessage());
        }

        //limit 1 做脏数据容错处理
        OrderItemDO orderItemDO = orderItemMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderItemDO::getOrderItemCode, vcsOrderInfoDO.getOrderItemCode())
                .orderByDesc(OrderItemDO::getId).last(Constants.LIMIT_ONE));

        if (orderItemDO != null) {

            String time = AttributeUtil.formatProductAttributes(orderItemDO.getProductAttribute());
            log.info("sendFulfilmentKafka orderItemDO:{}", JSON.toJSONString(orderItemDO));
            long year;
            switch (time) {
                case "二年":
                    year = 2;
                    break;
                case "三年":
                    year = 3;
                    break;
                case "四年":
                    year = 4;
                    break;
                case "五年":
                    year = 5;
                    break;
                case "六年":
                    year = 6;
                    break;
                case "十年":
                    year = 10;
                    break;
                default:
                    year = 1;
            }
            //
            LocalDateTime now = LocalDateTime.now();
            if (expiryDate.isBefore(now)) {
                expiryDate = now;
            }
            LocalDateTime endTime = expiryDate.plusYears(year);
            vcsOrderInfoDO.setServiceBeginDate(expiryDate);
            vcsOrderInfoDO.setServiceEndDate(endTime);
            vcsOrderInfoDOMapper.updateById(vcsOrderInfoDO);
            fulfilmentMessage.setVin(piplDataUtil.getDecodeText(vcsOrderInfoDO.getCarVin()));
            fulfilmentMessage.setServiceBeginDate(expiryDate);
            fulfilmentMessage.setServiceEndDate(endTime);
            fulfilmentMessage.setTenantId(TenantContextHolder.getTenantId());
            fulfilmentMessage.setCreateBy(WebFrameworkUtils.getLoginUserName());
            producerTool.sendMsg(KafkaConstants.ORDER_FUFILMENT_TOPIC, "", JSON.toJSONString(fulfilmentMessage));
            log.info("send payment vcs service msg:{}", JSON.toJSONString(fulfilmentMessage));
        }
    }

    private void sendPaymentKafka(OrderInfoDO orderInfoDO, OrderItemDO orderItemDO) {
        OrderPaymentSuccessMessage orderPaymentSuccessMessage = new OrderPaymentSuccessMessage();
        orderPaymentSuccessMessage.setOrderNumber(orderInfoDO.getOrderCode());
        orderPaymentSuccessMessage.setServiceName(orderItemDO.getProductName());

        // 解密手机号
        String phone = phoneNumberDecodeUtil.getDecodePhone(orderInfoDO.getContactPhone());
        orderPaymentSuccessMessage.setPhoneNumber(phone);

        orderPaymentSuccessMessage.setTaskCode(payment);
        orderPaymentSuccessMessage.setMessageId(ecpIdUtil.nextIdStr());
        orderPaymentSuccessMessage.setTenantId(TenantContextHolder.getTenantId());

        // 通过 订单号的前两位获取品牌编码 LR or JA
        String brandCode = StringUtils.left(orderInfoDO.getOrderCode(), 2);
        orderPaymentSuccessMessage.setBrandCode(BrandCodeEnum.getBrandIdByBrandCode(brandCode));

        // 生成短链
        // 品牌编码为LR 的短链接生成逻辑
        CommonResult<String> stringCommonResult = null;
        if (brandCode.equals(BrandCodeEnum.LAND_ROVER.getBrandCode())) {
            String path = ShortLinkPathEnum.ORDER_DETAIL.getPath() + orderInfoDO.getOrderCode();
            ShortLinkReqDto shortLinkReqDto = new ShortLinkReqDto();
            shortLinkReqDto.setPath(path);
            try {
                stringCommonResult = shorLinkAPI.genShortLink(shortLinkReqDto);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (stringCommonResult != null && stringCommonResult.getData() != null) {
                orderPaymentSuccessMessage.setWxUrl(stringCommonResult.getData());
            }
        } else {
            String path = ShortLinkPathEnum.JAGUAR_ORDER_DETAIL.getPath();
            String query = "orderCode=" + orderInfoDO.getOrderCode();
            log.info("generate JaguarLink path:{} query:{}", path, query);
            ShortLinkReqDto shortLinkReqDto = new ShortLinkReqDto();
            shortLinkReqDto.setPath(path);
            shortLinkReqDto.setQuery(query);
            try {
                stringCommonResult = shorLinkAPI.genJaguarLink(shortLinkReqDto);
            } catch (Exception e) {
                log.error("generate JaguarLink error:{}", e.getMessage());
                e.printStackTrace();
            }
            if (stringCommonResult != null && stringCommonResult.getData() != null) {
                orderPaymentSuccessMessage.setWxUrl(stringCommonResult.getData());
            }
        }

        producerTool.sendMsg(KafkaConstants.ORDER_SUCCESSFUL_TOPIC, "", JSON.toJSONString(orderPaymentSuccessMessage));

        log.info("send PaymentKafka success msg:{}", JSON.toJSONString(orderPaymentSuccessMessage));
    }

    private List<OrderItemDO> findItemByOrderCode(String orderCode) {
        return orderItemMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderItemDO::getOrderCode, orderCode));
    }


    private Integer getNextEventByNowRefundStatus(Integer beforeRefundStatus, Boolean approve) {
        //审核同意
        if (approve) {
            if (OrderRefundStatusEnum.FULL_REFUND_APPLY.getCode().equals(beforeRefundStatus)) {
                return OrderRefundEventEnum.EVENT_FULL_REFUND_APPROVE.getCode();
            } else {
                return OrderRefundEventEnum.EVENT_PARTIAL_REFUND_APPROVE.getCode();
            }
        } else {
            //审核拒绝
            if (OrderRefundStatusEnum.FULL_REFUND_APPLY.getCode().equals(beforeRefundStatus)) {
                return OrderRefundEventEnum.EVENT_FULL_REFUND_REFUSE.getCode();
            } else {
                return OrderRefundEventEnum.EVENT_PARTIAL_REFUND_REFUSE.getCode();
            }
        }
    }


    private List<String> assembleAttachmentUrls(String orderRefundCode) {
        List<String> attachmentUrls = new ArrayList<>();
        List<OrderRefundAttachmentDO> list = attachmentMapper.selectList(new LambdaQueryWrapperX<OrderRefundAttachmentDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundAttachmentDO::getRefundOrderCode, orderRefundCode));
        if (!CollectionUtils.isEmpty(list)) {
            attachmentUrls = list.stream().map(OrderRefundAttachmentDO::getAttachmentUrl).collect(Collectors.toList());
        }
        return attachmentUrls;
    }

    /**
     * 根据退单代码获取商品信息VO。
     * 该方法通过退单项代码查询相关的商品信息，并组装成ProductItemInfoAppVO对象返回。
     * 如果查询不到相关数据，则返回一个空的ProductItemInfoAppVO对象。
     *
     * @param orderRefundCode 退单代码，用于查询退单项和商品信息。
     * @return ProductItemInfoAppVO 对象，包含查询到的商品信息；如果未查询到数据，则返回空对象。
     */
    private List<ProductItemInfoAppVO> getProductInfoAppVO(String orderRefundCode) {
        ProductItemInfoAppVO rootItemVO = null;
        List<ProductItemInfoAppVO> next;
        //找到退单相关的itemCode
        List<OrderRefundItemDO> refundItemList = refundItemMapper.selectList(new LambdaQueryWrapperX<OrderRefundItemDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundItemDO::getRefundOrderCode, orderRefundCode));

        // 检查是否查询到退单相关的itemCode
        if (refundItemList == null || refundItemList.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> itemCodes = refundItemList.stream().map(OrderRefundItemDO::getOrderItemCode).collect(Collectors.toList());
        // 根据订单号查询所有相关的商品项
        List<OrderItemDO> orderItems = orderItemMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                .in(OrderItemDO::getOrderItemCode, itemCodes)
                .eq(OrderItemDO::getIsDeleted, false));

        // 检查是否查询到商品项
        if (orderItems == null || orderItems.isEmpty()) {
            return new ArrayList<>();
        }
        Map<String, LocalDateTime> serviceEndDateMap = getItemCodeToServiceEndDateMap(orderRefundCode);
        List<OrderItemDO> orderItemDOList = new ArrayList<>();
        for (OrderItemDO item : orderItems) {
            if (OrderItemSpuTypeEnum.BUNDLE_GOOD.getCode().equals(item.getOrderItemSpuType())) {
                rootItemVO = getProductItemInfoAppVO(item, serviceEndDateMap);
                //root节点不展示服务开始结束时间
                rootItemVO.setServiceBeginDate(null);
                rootItemVO.setServiceEndDate(null);
            } else {
                orderItemDOList.add(item);
            }
        }
        next = getProductInfoVoNext(serviceEndDateMap, orderItemDOList);
        if (Objects.isNull(rootItemVO)) {
            return next;
        }
        rootItemVO.setNext(next);
        return List.of(rootItemVO);
    }

    /**
     * 根据订单项和售后服务结束时间映射，获取产品信息的视图对象列表。
     *
     * @param serviceEndDateMap 一个映射，键为商品ID，值为对应的售后服务结束时间，用于后续处理。
     * @param orderItems        订单项列表，包含所有购买的商品项。
     * @return 返回一个产品信息视图对象的列表，仅包含非捆绑商品的信息。
     */
    private List<ProductItemInfoAppVO> getProductInfoVoNext(Map<String, LocalDateTime> serviceEndDateMap,
                                                            List<OrderItemDO> orderItems) {
        List<ProductItemInfoAppVO> productItemInfoList = new ArrayList<>();
        // 遍历所有商品项
        for (OrderItemDO item : orderItems) {
            if (OrderItemSpuTypeEnum.BUNDLE_GOOD.getCode().equals(item.getOrderItemSpuType())) {
                continue;
            }
            ProductItemInfoAppVO itemVO = getProductItemInfoAppVO(item, serviceEndDateMap);
            // 将ProductItemInfoVO对象添加到列表中
            productItemInfoList.add(itemVO);
        }
        return productItemInfoList;
    }

    /**
     * 根据订单项信息和额外的服务结束时间映射，构造产品项信息的应用层VO对象。
     *
     * @param item              订单项的DO对象，包含了产品项的详细信息。
     * @param serviceEndDateMap 一个映射，包含订单项代码和服务结束时间，用于设置产品项的服务期限信息。
     * @return 返回一个ProductItemInfoAppVO对象，包含了从订单项DO对象中转化而来的产品项信息。
     */
    private ProductItemInfoAppVO getProductItemInfoAppVO(OrderItemDO item, Map<String, LocalDateTime> serviceEndDateMap) {
        // 创建ProductItemInfoVO对象并设置属性
        ProductItemInfoAppVO itemVO = new ProductItemInfoAppVO();
        itemVO.setCostAmount(MoneyUtil.convertFromCents(BigDecimal.valueOf(item.getCostAmount())));
        itemVO.setOrderItemCode(item.getOrderItemCode());
        itemVO.setProductVersionCode(item.getProductVersionCode());
        itemVO.setProductCode(item.getProductCode());
        itemVO.setProductSkuCode(item.getProductSkuCode());
        itemVO.setProductName(item.getProductName());
        itemVO.setProductQuantity(item.getProductQuantity());
        itemVO.setProductAttribute(AttributeUtil.formatProductAttributes(item.getProductAttribute()));
        if (item.getProductMarketPrice() != null) {
            itemVO.setProductMarketPrice(MoneyUtil.convertFromCents(BigDecimal.valueOf(item.getProductMarketPrice())));
        }
        itemVO.setProductSalePrice(MoneyUtil.convertFromCents(BigDecimal.valueOf(item.getProductSalePrice())));
        itemVO.setProductImageUrl(item.getProductImageUrl());

        //获取服务启停时间 和状态
        setServiceTimeAndStatusForItem(item.getOrderCode(), itemVO);
        //实际截至时间
        if (Objects.nonNull(serviceEndDateMap.get(item.getOrderItemCode()))) {
            itemVO.setActualEndDate(serviceEndDateMap.get(item.getOrderItemCode()));
        }
        return itemVO;
    }

    private void setServiceTimeAndStatusForItem(String orderCode, ProductItemInfoAppVO itemVO) {
        VCSOrderInfoDO vcsOrderInfoDO = vcsOrderInfoDOMapper.selectOne(new LambdaQueryWrapperX<VCSOrderInfoDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(VCSOrderInfoDO::getOrderCode, orderCode)
                .eq(VCSOrderInfoDO::getOrderItemCode, itemVO.getOrderItemCode())
                .orderByDesc(VCSOrderInfoDO::getId)
                .last(Constants.LIMIT_ONE));
        if (vcsOrderInfoDO != null) {
            itemVO.setServiceBeginDate(vcsOrderInfoDO.getServiceBeginDate());
            itemVO.setServiceEndDate(vcsOrderInfoDO.getServiceEndDate());
        }
    }


    private OrderStatusLogDO assembleOrderStatusLogDO(String code, Integer beforeStatus, Integer afterStatus) {
        OrderStatusLogDO orderStatusLog = new OrderStatusLogDO();
        orderStatusLog.setOrderCode(code);
        orderStatusLog.setBeforeStatus(beforeStatus);
        orderStatusLog.setAfterStatus(afterStatus);
        orderStatusLog.setChangeTime(LocalDateTime.now());
        return orderStatusLog;
    }

    private OrderStatusLogDO assembleOrderStatusLogForCustom(String code, Integer beforeStatus, Integer afterStatus, Integer refundStatus) {
        OrderStatusLogDO orderStatusLog = new OrderStatusLogDO();
        orderStatusLog.setOrderCode(code);
        orderStatusLog.setBeforeStatus(beforeStatus);
        orderStatusLog.setAfterStatus(OrderStatusLogEnum.getLogStatusByOrderAndRefund(afterStatus, refundStatus));
        orderStatusLog.setChangeTime(LocalDateTime.now());
        return orderStatusLog;
    }

    /**
     * 根据退款申请信息和订单退款信息，生成订单退款项列表。
     *
     * @param refundApplyDTO 退款申请的DTO，包含商品项信息。
     * @param orderRefundDO  订单退款的信息DO。
     * @return 返回一个订单退款项的列表。
     */
    private List<OrderRefundItemDO> getOrderRefundItemList(OrderRefundApplyDTO refundApplyDTO, OrderRefundDO orderRefundDO) {
        List<OrderRefundItemDO> itemList = new ArrayList<>();
        for (ProductItemInfoDTO productItemInfoDTO : refundApplyDTO.getProductItemInfoList()) {
            OrderRefundItemDO rootRefundItem = getOrderRefundItemDO(productItemInfoDTO, orderRefundDO);
            itemList.add(rootRefundItem);
            if (!CollectionUtils.isEmpty(productItemInfoDTO.getNext())) {
                for (ProductItemInfoDTO next : productItemInfoDTO.getNext()) {
                    OrderRefundItemDO childRefundItem = getOrderRefundItemDO(next, orderRefundDO);
                    itemList.add(childRefundItem);
                }
            }
        }
        return itemList;
    }

    /**
     * 根据退款申请信息和商品项信息，创建退款商品项对象。
     *
     * @param productItemInfoDTO 商品项信息数据传输对象，包含商品项的相关详细信息。
     * @param orderRefundDO      退款申请对象，包含退款申请的相关详细信息。
     * @return OrderRefundItemDO 退款商品项对象，包含了从参数中获取的退款申请代码、商品项代码和服务结束日期。
     */
    private OrderRefundItemDO getOrderRefundItemDO(ProductItemInfoDTO productItemInfoDTO, OrderRefundDO orderRefundDO) {
        OrderRefundItemDO refundItemDO = new OrderRefundItemDO();
        refundItemDO.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
        refundItemDO.setOrderItemCode(productItemInfoDTO.getOrderItemCode());
        refundItemDO.setServiceEndDate(productItemInfoDTO.getServiceEndDate());
        return refundItemDO;
    }


    private List<OrderRefundAttachmentDO> getOrderRefundAttachmentList(OrderRefundApplyDTO refundApplyDTO, OrderRefundDO orderRefundDO) {
        List<String> attachmentUrls = refundApplyDTO.getAttachmentUrls();
        List<OrderRefundAttachmentDO> refundAttachmentList = new ArrayList<>();
        for (String attachmentUrl : attachmentUrls) {
            OrderRefundAttachmentDO refundAttachmentDO = new OrderRefundAttachmentDO();
            refundAttachmentDO.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
            refundAttachmentDO.setAttachmentUrl(attachmentUrl);
            refundAttachmentList.add(refundAttachmentDO);
        }
        return refundAttachmentList;
    }

    /**
     * 校验订单能否被取消：
     */
    private Boolean checkOrderEnableCancel(OrderRefundApplyDTO refundApplyDTO) {
        List<Integer> status = new ArrayList<>();
        status.add(OrderRefundStatusEnum.FULL_REFUND_APPLY.getCode());
        status.add(OrderRefundStatusEnum.FULL_REFUND_APPROVE.getCode());
        status.add(OrderRefundStatusEnum.FULL_REFUND_COMPLETED.getCode());
        status.add(OrderRefundStatusEnum.PARTIAL_REFUND_APPLY.getCode());
        status.add(OrderRefundStatusEnum.PARTIAL_REFUND_APPROVE.getCode());
        status.add(OrderRefundStatusEnum.PARTIAL_REFUND_COMPLETED.getCode());

        List<OrderRefundDO> refundList = orderRefundMapper
                .selectList(new LambdaQueryWrapperX<OrderRefundDO>()
                        .eq(BaseDO::getIsDeleted, false)
                        .eq(OrderRefundDO::getOriginOrderCode, refundApplyDTO.getOriginOrderCode())
                        .in(OrderRefundDO::getRefundOrderStatus, status));
        //1.判断能不能整单取消:查询是否存在正在取消;
        if (!CollectionUtils.isEmpty(refundList)) {
            if (refundApplyDTO.getRefundType().equals(RefundTypeEnum.FULL_REFUND.getCode())) {
                throw exception(ErrorCodeConstants.REFUND_ALL_ERROR);
            }
            //2.部分取消校验逻辑：判断存不存在当前itemCode
            if (refundApplyDTO.getRefundType().equals(RefundTypeEnum.PARTIAL_REFUND.getCode())) {
                List<ProductItemInfoDTO> productItemInfoList = getProductItemInfoDTOList(refundApplyDTO);
                List<String> itemCodes = refundList.stream().map(OrderRefundDO::getRefundOrderCode).collect(Collectors.toList());
                List<OrderRefundItemDO> list = refundItemMapper.selectList(new LambdaQueryWrapperX<OrderRefundItemDO>()
                        .eq(BaseDO::getIsDeleted, false)
                        .in(OrderRefundItemDO::getOrderItemCode, itemCodes));
                validOrderItemCode(productItemInfoList, list);
            }
        }
        return false;
    }

    /**
     * 根据退款申请DTO获取商品项信息DTO列表。
     *
     * @param refundApplyDTO 退款申请的详情，包含商品项信息和其他相关信息。
     * @return 返回一个商品项信息的列表，根据退款申请的情况可能包含一个或多个商品项。
     */
    private List<ProductItemInfoDTO> getProductItemInfoDTOList(OrderRefundApplyDTO refundApplyDTO) {
        List<ProductItemInfoDTO> productItemInfoList = new ArrayList<>();
        for (ProductItemInfoDTO productItemInfoDTO : refundApplyDTO.getProductItemInfoList()) {
            productItemInfoList.add(productItemInfoDTO);
            if (!CollectionUtils.isEmpty(productItemInfoDTO.getNext())) {
                productItemInfoList.addAll(productItemInfoDTO.getNext());
            }
        }
        return productItemInfoList;
    }

    /**
     * 校验订单item编号
     */
    private static void validOrderItemCode(List<ProductItemInfoDTO> productItemInfoList, List<OrderRefundItemDO> list) {
        for (ProductItemInfoDTO productItemInfoDTO : productItemInfoList) {
            for (OrderRefundItemDO refundItemDO : list) {
                if (productItemInfoDTO.getOrderItemCode().equals(refundItemDO.getOrderItemCode())) {
                    throw exception(ErrorCodeConstants.REFUND_PART_ERROR);
                }
            }
        }
    }

    private OrderRefundDO assembleOrderRefundDO(OrderRefundApplyDTO refundApplyDTO, OrderRefundDO orderRefundDO) {
        //订单号规则：用"R"+子订单号+"三位数组"组合而成
        orderRefundDO.setRefundOrderCode(setMaxRefundOrderCode(refundApplyDTO.getOriginOrderCode()));
        orderRefundDO.setRefundMoney(refundApplyDTO.getRefundMoney());
        orderRefundDO.setRefundMoneyAmount(MoneyUtil.convertToCents(refundApplyDTO.getRefundMoneyAmount()).intValue());
        orderRefundDO.setOriginOrderCode(refundApplyDTO.getOriginOrderCode());
        orderRefundDO.setRefundRemark(refundApplyDTO.getRemark());
        orderRefundDO.setSubmitUser(WebFrameworkUtils.getLoginUserName());
        //整单取消还是部分取消
        if (refundApplyDTO.getRefundType().equals(RefundTypeEnum.FULL_REFUND.getCode())) {
            orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.FULL_REFUND_APPLY.getCode());
        } else {
            orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.PARTIAL_REFUND_APPLY.getCode());
        }
        return orderRefundDO;
    }

    private String setMaxRefundOrderCode(String originOrderCode) {
        //查询最大RefundOrderCode
        OrderRefundDO maxDO
                = orderRefundMapper.selectOne(new LambdaQueryWrapperX<OrderRefundDO>()
                .eq(OrderRefundDO::getOriginOrderCode, originOrderCode)
                .orderByDesc(OrderRefundDO::getId)
                .last(Constants.LIMIT_ONE));
        String prefix = "R" + originOrderCode;
        if (maxDO != null && StringUtils.isNotBlank(maxDO.getRefundOrderCode())
                && maxDO.getRefundOrderCode().startsWith(prefix)) {
            prefix = maxDO.getRefundOrderCode();
            return CodeGenerator.nextCode(prefix);
        } else {
            return CodeGenerator.initCodeWithPrefix(prefix);
        }
    }


    /**
     * 最大可退金额校验
     */
    private void checkSubmitAmount(OrderRefundApplyDTO refundApplyDTO, OrderInfoDO orderInfoDO) {
        if (refundApplyDTO.getRefundMoney().equals(1)) {
            if (StringUtils.isBlank(refundApplyDTO.getRefundMoneyAmount())) {
                //需要退款 但传值退款金额为空
                throw exception(ErrorCodeConstants.REFUND_MONEY_EMPTY);
            }
            BigDecimal refundMoney = MoneyUtil.convertToCents(refundApplyDTO.getRefundMoneyAmount());
            BigDecimal bigDecimal = new BigDecimal("0");
            //需要退款 但传值退款金额为0
            if (refundMoney.compareTo(bigDecimal) == 0 || Objects.isNull(orderInfoDO)) {
                throw exception(ErrorCodeConstants.REFUND_MONEY_ZERO);
            }
            bigDecimal = MoneyUtil.convertToCents(orderInfoDO.getCostAmount().toString());
            //比较大小
            if (refundMoney.compareTo(bigDecimal) > 0) {
                throw exception(ErrorCodeConstants.REFUND_MONEY_OVER_TOTAL);
            }
        }
    }

    private OrderInfoDO checkDataReal(String orderCode) {
        //查询原始订单师父存在
        return orderInfoMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(BaseDO::getIsDeleted, false));
    }

    /**
     * 根据退款订单代码获取商品代码到服务结束日期的映射。
     * 这个映射用于快速查找特定商品的最新服务结束日期。
     *
     * @param refundOrderCode 退款订单的唯一标识代码。
     * @return 返回一个映射，其中包含商品代码到最新服务结束日期的映射。
     * 如果退款订单中没有商品，或者订单列表为空，则返回一个空映射。
     */
    private Map<String, LocalDateTime> getItemCodeToServiceEndDateMap(String refundOrderCode) {
        List<OrderRefundItemDO> orderRefundItemDOS = getRefundItemListByRefundOrder(refundOrderCode);
        if (CollectionUtils.isEmpty(orderRefundItemDOS)) {
            return Collections.emptyMap();
        }
        Map<String, LocalDateTime> map = new HashMap<>();
        for (OrderRefundItemDO orderRefundItemDO : orderRefundItemDOS) {
            map.put(orderRefundItemDO.getOrderItemCode(), orderRefundItemDO.getServiceEndDate());
        }
        return map;
    }

    /**
     * 根据退款订单码获取退款物品列表。
     *
     * @param refundOrderCode 退款订单码，用于查询退款物品信息。
     * @return 退款物品列表。如果退款订单码为空或无效，则返回空列表。
     * 该列表包含所有与给定退款订单码相关且未被删除的退款物品项。
     */
    private List<OrderRefundItemDO> getRefundItemListByRefundOrder(String refundOrderCode) {
        if (StringUtils.isBlank(refundOrderCode)) {
            return new ArrayList<>();
        }
        try {
            return refundItemMapper.selectList(new LambdaQueryWrapperX<OrderRefundItemDO>()
                    .eq(OrderRefundItemDO::getRefundOrderCode, refundOrderCode)
                    .eq(BaseDO::getIsDeleted, false));
        } catch (Exception e) {
            log.error("根据退款订单码获取退款物品列表失败:", e);
        }
        return new ArrayList<>();
    }
}





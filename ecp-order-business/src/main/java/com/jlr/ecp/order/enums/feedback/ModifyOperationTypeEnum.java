package com.jlr.ecp.order.enums.feedback;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * k
 */
@AllArgsConstructor
@Getter
public enum ModifyOperationTypeEnum {


    EDIT(0, "编辑"),

    ENABLE(1, "启用"),

    DISABLE(2, "停用");


    private final Integer code;

    private final String name;



    // 根据code获取对应的枚举实例
    public static String fromCode(Integer code) {
        for (ModifyOperationTypeEnum type : ModifyOperationTypeEnum.values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type.getName();
            }
        }
        return null;
    }
}

package com.jlr.ecp.order.util.money;

import cn.hutool.core.util.NumberUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 */
public class MoneyUtil {

    /**
     * 入参的时候处理
     * 将金额字符串转换为以分为单位的BigDecimal。
     *
     * @param amountStr 金额字符串（如"700.00"）
     * @return 以分为单位的金额（如70000），如果入参为空或不合法，则返回null
     */
    public static BigDecimal convertToCents(String amountStr) {
        if (amountStr == null || amountStr.trim().isEmpty()) {
            // 返回null或者抛出IllegalArgumentException根据您的业务需求而定
            return null;
        }
        try {
            return new BigDecimal(amountStr).multiply(BigDecimal.valueOf(100));
        } catch (NumberFormatException e) {
            // 处理非法的数字格式
            return null;
        }
    }


    /**
     * 出参的时候处理
     * 将以分为单位的BigDecimal 转换为金额字符串。
     *
     * @param amount 以分为单位的金额（如70000）
     * @return 金额字符串（如"700.00"），如果入参为空或不合法，则返回null
     */
    public static String convertFromCents(BigDecimal amount) {
        if (amount == null) {
            return null;
        }
        try {
            return amount.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString();
        } catch (NumberFormatException e) {
            // 处理非法的算术运算
            return null;
        }
    }

    public static String convertToCentsFromStr(String amountStr){
        if (amountStr == null || amountStr.trim().isEmpty()) {
            return null;
        }
        try {
            BigDecimal amountInYuan = new BigDecimal(amountStr);
            BigDecimal amountInCents = amountInYuan.multiply(BigDecimal.valueOf(100));
            return amountInCents.setScale(2, RoundingMode.HALF_UP).toString();
        } catch (NumberFormatException e) {
            // 处理非法的数字格式
            return null;
        }
    }

    public static String convertToYuanFromStr(String amountStr){
        if (amountStr == null || amountStr.trim().isEmpty()) {
            return null;
        }
        try {
            BigDecimal amountInYuan = new BigDecimal(amountStr);
            BigDecimal amountInCents = amountInYuan.divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_UP);
            return amountInCents.setScale(2, RoundingMode.HALF_UP).toString();
        } catch (NumberFormatException e) {
            // 处理非法的数字格式
            return null;
        }
    }


    /**
     * 将以分为单位的金额转换为元的BigDecimal
     *
     * @param amountInCents 以分为单位的金额（如70000）
     * @return 转换后的元金额（如700.00），若输入无效则返回null
     */
    public static BigDecimal convertFromCentsToYuanBigDecimal(BigDecimal amountInCents) {
        if (amountInCents == null) {
            return null;
        }
        try {
            return amountInCents.divide(BigDecimal.valueOf(100L), 2, RoundingMode.HALF_UP)
                    .setScale(2, RoundingMode.HALF_UP);
        } catch (NumberFormatException | ArithmeticException e) {
            // 处理非法数值或算术异常（如除以0，但此处除数固定为100）
            return null;
        }
    }

    /**
     * 计算不含税总金额
     *
     * @param taxRate 税率
     * @param totalAmount 金额
     * @return 不含税总金额
     */
    public static int calculateExcludeTaxAmount(BigDecimal taxRate, Integer totalAmount) {
        BigDecimal newTaxRate = taxRate == null ? BigDecimal.ZERO : taxRate.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
        return new BigDecimal(totalAmount)
                .divide(BigDecimal.ONE.add(newTaxRate), RoundingMode.HALF_UP).setScale(0, RoundingMode.HALF_UP).intValue();
    }

    /**
     * 出参的时候处理（第三位小数及以后不要）
     * 将以分为单位的BigDecimal 转换为金额字符串。
     *
     * @param amount 以分为单位的金额（如70000398）
     * @return 金额字符串（如"700.00"），如果入参为空或不合法，则返回null
     */
    public static String convertFromCentsDown(BigDecimal amount) {
        if (amount == null) {
            return null;
        }
        try {
            return amount.divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN).toString();
        } catch (NumberFormatException e) {
            // 处理非法的算术运算
            return null;
        }
    }


    /**
     * String转BigDecimal，为空的时返回BigDecimal.ZERO
     * @param amount
     * @return
     */
    public static BigDecimal getBigDecimal(String amount) {
        return (amount == null || amount.isEmpty())
                ? BigDecimal.ZERO
                : new BigDecimal(amount);
    }

    /**
     * Integer转BigDecimal，为空的时返回BigDecimal.ZERO
     * @param amount
     * @return
     */
    public static BigDecimal getBigDecimal(Integer amount) {
        return amount == null
                ? BigDecimal.ZERO
                : BigDecimal.valueOf(amount);
    }


    public static void main(String[] args) {
        int div3 = NumberUtil.div("0", "0",2).multiply(BigDecimal.valueOf(100)).intValue();
        System.out.println("div3 = " + div3);

        // 正常情况
        System.out.println("convertToCents(\"700.12\") = " + convertToCents("700.12"));
        // 空字符串
        System.out.println("convertToCents(\"\") = " + convertToCents(""));
        // null
        System.out.println("convertToCents(null) = " + convertToCents(null));
        // 非数字字符串
        System.out.println("convertToCents(\"abc\") = " + convertToCents("abc"));

        System.out.println("===============================");

        // 正常情况
        System.out.println("convertFromCents(70000) = " + convertFromCents(new BigDecimal("70000")));
        // 空字符串
//        System.out.println("convertFromCents(\"\") = " + convertFromCents(new BigDecimal("")));
        // null值
        System.out.println("convertFromCents(null) = " + convertFromCents(null));
        // 非数字字符串和非法金额
        System.out.println("convertFromCents(new BigDecimal(\"abc\")) = " + convertFromCents(new BigDecimal("abc")));
    }
}
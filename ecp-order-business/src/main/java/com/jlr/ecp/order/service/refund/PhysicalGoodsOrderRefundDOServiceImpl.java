package com.jlr.ecp.order.service.refund;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @description 实物商品类型退款
 * @createDate 20250306
 */
@Service
@Slf4j
public class PhysicalGoodsOrderRefundDOServiceImpl extends ServiceImpl<OrderRefundDOMapper, OrderRefundDO>
        implements PhysicalGoodsOrderRefundDOService {

    @Resource
    private OrderInfoDOMapper orderInfoMapper;



    private OrderInfoDO checkIfDataExist(String orderCode) {
        //查询原始订单师父存在
        return orderInfoMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(BaseDO::getIsDeleted, false));
    }
}





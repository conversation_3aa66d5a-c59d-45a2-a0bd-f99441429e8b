package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 */
@AllArgsConstructor
@Getter
public enum LogisticsOrderRefundEventEnum {


    /****
     * 触发事件：
     * 1：发起退款申请
     * 2：退款处理中
     * 3：退款审核中
     * 4：分账退款中
     * 5: 售后完成
     * 6: 退款审核拒绝
     * 7: 买家撤销申请
     * 8: 主订单关闭
     * 9: 退货审核同意
     */
    LOGISTICS_REFUND_APPLY(1, "发起退款申请"),
    LOGISTICS_REFUND_AUDIT(2, "退款审核"),
    LOGISTICS_REFUND_PROCESSING(3, "退款处理中"),
    LOGISTICS_REFUND_INDEPENDENT(4, "分账退款中"),
    LOGISTICS_REFUND_COMPLETED(5, "售后完成"),
    LOGISTICS_REFUND_REJECT(6, "退款审核拒绝"),
    LOGISTICS_REFUND_BUYER_CANCEL(7, "买家撤销申请"),
    LOGISTICS_REFUND_ORDER_CLOSED(8, "主订单关闭"),
    LOGISTICS_RETURN_AUDIT(9, "退货审核同意"),
    LOGISTICS_RETURN_REJECT(10, "退货审核拒绝")
    ;


    /**
     * code
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;


}

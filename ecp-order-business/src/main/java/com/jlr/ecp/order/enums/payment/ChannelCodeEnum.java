package com.jlr.ecp.order.enums.payment;

import lombok.Getter;

/**
 * ChannelCodeEnum
 *
 * <AUTHOR> <PERSON>
 * @since 2025-03-13 14:12:12
 */
@Getter
public enum ChannelCodeEnum {

    CUSC_JAGUAR("CUSC-JAGUAR", "CUSC_JAGUAR"),
    CUSC_LANDROVER("CUSC-LANDROVER", "CUSC_LANDROVER"),
    HUIFU("HUIFU-DEFAULT", "HUIFU-DEFAULT");

    ChannelCodeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    private final String code;

    private final String description;

}

package com.jlr.ecp.order.enums.feedback.modifyLog;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 评价维度配置-修改日志-修改模块，修改字段 枚举
 */
@AllArgsConstructor
@Getter
public enum EvaluationDimensionFieldEnum {



    // 添加评价维度 module
    ADD_DIMENSION(0, "新增评价维度", InfoModule.MODULE_ADD_DIMENSION, false),

    // 编辑评价维度 module
    EDIT_DIMENSION_NAME(1, "修改评价维度名称", InfoModule.MODULE_EDIT_DIMENSION, false),
    EDIT_DIMENSION_TYPE(2, "修改维度类型", InfoModule.MODULE_EDIT_DIMENSION, false),
    EDIT_REQUIRED_1(3, "修改是否必填", InfoModule.MODULE_EDIT_DIMENSION, false),
    EDIT_REMARK(4, "修改备注", InfoModule.MODULE_EDIT_DIMENSION, false),
    DELETE_DIMENSION(5, "删除评价维度", InfoModule.MODULE_EDIT_DIMENSION, true),
    SORT_DIMENSION(6, "调整评价维度排序", InfoModule.MODULE_EDIT_DIMENSION, false),

    // 编辑评价输入框配置 module
    EDIT_ENABLE_INPUT(7, "修改是否设置评价输入框", InfoModule.MODULE_EDIT_INPUT_CONFIG, true),
    EDIT_REQUIRED_2(8, "修改评价输入框是否必填", InfoModule.MODULE_EDIT_INPUT_CONFIG, true),
    EDIT_INPUT_PLACEHOLDER(9, "修改评价输入框中的提示文案", InfoModule.MODULE_EDIT_INPUT_CONFIG, true),

    // 编辑评价问卷说明 module
    EDIT_DESCRIPTION(10, "修改说明", InfoModule.MODULE_EDIT_DESCRIPTION, true),

    // 编辑启用状态 module
    EDIT_ENABLE_STATUS(11, "编辑评价问卷启用状态", InfoModule.MODULE_EDIT_ENABLE_STATUS, true);


    private final Integer code;
    private final String fieldName;
    private final String moduleName;
    private final Boolean showDetail;  // 是否显示修改详情

    /**
     * 根据 code 获取对应的枚举实例
     */
    public static EvaluationDimensionFieldEnum fromCode(Integer code) {
        for (EvaluationDimensionFieldEnum field : values()) {
            if (field.getCode().equals(code)) {
                return field;
            }
        }
        return null;
    }

    public static Set<String> getAllDetailFields() {
        return Arrays.stream(values())
                .filter(e -> e.showDetail)
                .map(e -> e.fieldName)
                .collect(Collectors.toSet());
    }

    public static class InfoModule {
        // 模块名称常量
        private static final String MODULE_ADD_DIMENSION = "添加评价维度";
        private static final String MODULE_EDIT_DIMENSION = "编辑评价维度";
        private static final String MODULE_EDIT_INPUT_CONFIG = "编辑评价输入框配置";
        private static final String MODULE_EDIT_DESCRIPTION = "编辑评价问卷说明";
        private static final String MODULE_EDIT_ENABLE_STATUS = "编辑启用状态";
    }
}
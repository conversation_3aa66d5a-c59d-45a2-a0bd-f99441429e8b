package com.jlr.ecp.order.service.dashboard.impl;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.EnumUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.controller.admin.dashboard.dto.*;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ValetDashboardRespVO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.dashboard.KpiEnum;
import com.jlr.ecp.order.enums.dashboard.KpiEnumForValet;
import com.jlr.ecp.order.service.dashboard.ValetDashboardService;
import com.jlr.ecp.order.util.dashboard.DashboardUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.jlr.ecp.order.util.dashboard.DashboardBuilder.*;
import static com.jlr.ecp.order.util.money.MoneyUtil.convertFromCentsToYuanBigDecimal;

// Service实现类
@Service
@Slf4j
public class valetDashboardServiceImpl implements ValetDashboardService {

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;

    @Resource
    private OrderRefundItemDOMapper orderRefundItemDOMapper;

    @Override
    public ValetDashboardRespVO getValetOrderStatistics(ValetDashboardReqDTO reqDTO) {
        log.info("==================== 代客下单统计开始 ====================");
        log.info("统计请求参数: startTime={}, endTime={}, orderChannel={}", reqDTO.getStartTime(), reqDTO.getEndTime(), reqDTO.getOrderChannel());
        String orderChannel = reqDTO.getOrderChannel(); //1.全部渠道 传null 2.路虎驾享商城 传LR 3.捷豹驾享商城 传JA
        String start = reqDTO.getStartTime();
        String end = reqDTO.getEndTime();
        ValetDashboardRespVO vo = new ValetDashboardRespVO();
        StopWatch stopWatch = new StopWatch();

        // 订单数量统计
        //1.代客下单订单数 （order_channel为5代客下单）；2.订单总数量；代客下单订单数 占全部订单的 百分比
        stopWatch.start();
        ValetDashboardTempDTO temp = orderInfoDOMapper.countOrderByChannel(orderChannel, start, end);
        stopWatch.stop();
        log.info("orderInfoDOMapper.countOrderByChannel 查询耗时，{}s", stopWatch.getTotalTimeSeconds());
        if (Objects.isNull(temp)) {
            log.info("case1-查询订单统计信息为空");
            return emptyStatics(vo, reqDTO);
        }
        setOrderCount(vo, temp);

        // 支付订单统计
        //3.代客下单成交数（支付状态为“已支付” payment_status为1：已支付）；4.已支付状态下的订单总数量；代客下单成交数 占 订单成交数的 百分比
        stopWatch.start();
        ValetDashboardTempDTO temp2 = orderInfoDOMapper.countOrderByPaymentStatus(orderChannel, start, end);
        stopWatch.stop();
        log.info("orderInfoDOMapper.countOrderByPaymentStatus 查询耗时，{}s", stopWatch.getTotalTimeSeconds());
        if (Objects.isNull(temp2)) {
            log.info("case2-查询订单统计信息为空");
            return emptyStatics(vo, reqDTO);
        }
        setPaidCount(vo, temp2);

        // 交易额统计
        //5.代客下单交易额（支付状态为“已支付”状态的所有”代客下单实付cost_amount 总金额（含税）“之和）；6.已支付状态下 的 所有订单交易额；代客下单交易额占 订单交易额的 百分比
        BigDecimal valetGmv = convertFromCentsToYuanBigDecimal(temp2.getValetGmv());
        BigDecimal totalGmv = convertFromCentsToYuanBigDecimal(temp2.getTotalGmv());
        setGmv(vo, valetGmv, totalGmv);

        // 退款统计 & 总收入统计
        //代客下单总收入 = 5- 7.代客下单总退款额; 所有订单总收入 = 6- 8.已支付状态下 订单 的总退款额; 代客下单总收入 占 所有订单总收入 的百分比
        stopWatch.start();
        ValetDashboardTempDTO temp3 = orderRefundItemDOMapper.getRefundAmounts(orderChannel, start, end); // 查询 代客下单总退款额 和 所有订单的总退款额
        stopWatch.stop();
        log.info("orderRefundItemDOMapper.getRefundAmounts 查询耗时，{}s", stopWatch.getTotalTimeSeconds());
        setRefundAndIncome(temp3, vo, valetGmv, totalGmv);

        // 客单价统计
        //代客下单成交客单价 = 5 / 3; 订单成交客单价=6 / 4; 展示“比小程序下单”多的百分比，计算方式：代客下单成交客单价/所有订单成交客单价-1
        setCustomerPrice(vo, valetGmv, temp2, totalGmv);

        // 商品数量统计 sprint49 移除
        //9.已支付状态下 代客下单成交商品总数；10.已支付状态下 所有订单成交商品总数；代客下单成交商品总数 占 所有订单成交商品总数 的百分比
//        stopWatch.start();
//        ValetDashboardTempDTO temp4 = orderInfoDOMapper.countOrderItemByChannel(orderChannel, start, end);
//        stopWatch.stop();
//        log.info("orderInfoDOMapper.countOrderItemByChannel 查询耗时，{}s", stopWatch.getTotalTimeSeconds());
//        setProductCount(vo, temp4);

        log.info("==================== 代客下单统计完成 ====================");
        return vo;
    }

    @Override
    public CommonResult<ProductSalesTrendRespVo> getSalesTrend(ValetSalesTrendReqDTO reqDTO) {
        LocalDate startDate = LocalDate.parse(reqDTO.getStartTime());
        LocalDate endDate = LocalDate.parse(reqDTO.getEndTime());
        // 开始日期不能大于结束日期
        if (startDate.isAfter(endDate)) {
            return CommonResult.error(ErrorCodeConstants.START_TIME_GREATER_THAN_END_TIME);
        }

        // 设置查询参数
        SqlQueryDTO sqlQueryDTO = new SqlQueryDTO();
        sqlQueryDTO.setStartDate(startDate);
        sqlQueryDTO.setOrderChannel(reqDTO.getOrderChannel());
        sqlQueryDTO.setKpi(reqDTO.getKpi());

        List<String> tableHeaders = new ArrayList<>();
        String dateFormat = DashboardUtils.calculateTimeInterval(startDate, endDate, tableHeaders);
        sqlQueryDTO.setDateFormat(dateFormat);
        sqlQueryDTO.setEndDate(endDate.plusDays(1)); // dateFormat处理结束，日期需+1
        //设置 折线图x轴；table标题；得到查询的 分组dateFormat
        List<String> xAxis = new ArrayList<>(tableHeaders);
        List<SqlResultDTO> resultDTOS;
        List<SqlResultDTO> refundResultDTOS;
        switch (EnumUtil.getBy(KpiEnumForValet::getCode, reqDTO.getKpi())) {
            case TOTAL_ORDER_NUMBER:
            case DEAL_ORDER_NUMBER:
                resultDTOS = orderInfoDOMapper.getSalesTrendByChannelByValet(sqlQueryDTO);
                return buildLineChartAndTableDataByCount(tableHeaders, resultDTOS, xAxis,true);
            case TOTAL_ORDER_AMOUNT:
                resultDTOS = orderInfoDOMapper.getSalesTrendByChannelByValet(sqlQueryDTO);
                return buildLineChartAndTableDataByAmount(tableHeaders, resultDTOS, xAxis,true);
            case UNIT_PRICE_PER_CUSTOMER:
                resultDTOS = orderInfoDOMapper.getSalesTrendByChannelByValet(sqlQueryDTO);
                return buildLineChartAndTableDataByPer(tableHeaders, resultDTOS, xAxis,true);
            case INCOME:
                resultDTOS = orderInfoDOMapper.getSalesTrendByChannelByValet(sqlQueryDTO);
                refundResultDTOS = orderInfoDOMapper.getSalesTrendRefundByChannelByValet(sqlQueryDTO);
                return buildLineChartAndTableDataByIncome(tableHeaders, resultDTOS, xAxis, refundResultDTOS,true);
            case REFUND:
                refundResultDTOS = orderInfoDOMapper.getSalesTrendRefundByChannelByValet(sqlQueryDTO);
                return buildLineChartAndTableDataByRefund(tableHeaders, refundResultDTOS, xAxis, true);
            default:
                return CommonResult.error(ErrorCodeConstants.TYPE_FILTER_ERROR);
        }
    }

    @Override
    public CommonResult<List<KpiQueryDTO>> getValetKpiList() {
        List<KpiQueryDTO> collect = Arrays.stream(KpiEnumForValet.values())
                .map(KpiQueryDTO::new)
                .collect(Collectors.toList());
        return CommonResult.success(collect);
    }

    private void setProductCount(ValetDashboardRespVO vo, ValetDashboardTempDTO temp4) {
        vo.setValetProductCount(temp4.getValetProductCount());
        vo.setTotalProductCount(temp4.getTotalProductCount());
        vo.setProductCountPercentage(calculatePercentage(temp4.getValetProductCount(), temp4.getTotalProductCount()));
        log.info("商品数量统计完成: 代客商品数={}, 总商品数={}, 占比{}", temp4.getValetProductCount(), temp4.getTotalProductCount(), vo.getProductCountPercentage());
    }

    private void setCustomerPrice(ValetDashboardRespVO vo, BigDecimal valetGmv, ValetDashboardTempDTO temp2, BigDecimal totalGmv) {
        vo.setValetCustomerPrice(calculateCustomerPrice(valetGmv, temp2.getValetPaidCount()));
        vo.setTotalCustomerPrice(calculateCustomerPrice(totalGmv, temp2.getTotalPaidCount()));
        vo.setCustomerPriceDiff(calculatePriceDiff(vo.getValetCustomerPrice(), vo.getTotalCustomerPrice()));
        log.info("客单价计算结果: valetCustomerPrice={}, totalCustomerPrice={}", vo.getValetCustomerPrice(), vo.getTotalCustomerPrice());
    }

    private void setRefundAndIncome(ValetDashboardTempDTO temp3, ValetDashboardRespVO vo, BigDecimal valetGmv, BigDecimal totalGmv) {
        BigDecimal valetRefundAmount = convertFromCentsToYuanBigDecimal(temp3.getValetRefundAmount()); // 7.代客下单总退款额
        BigDecimal totalRefundAmount = convertFromCentsToYuanBigDecimal(temp3.getTotalRefundAmount()); // 8.已支付状态下 订单 的总退款额
        // 退款维度 卡片数据：
        vo.setValetRefundAmount(valetRefundAmount);
        vo.setTotalRefundAmount(totalRefundAmount);
        vo.setRefundPercentage(calculatePercentage(valetRefundAmount, totalRefundAmount));
        log.info("退款计算结果: valetRefundAmount={}, totalRefundAmount={}", valetRefundAmount, totalRefundAmount);
        // 收入设置 卡片数据：
        vo.setValetIncome(valetGmv.subtract(valetRefundAmount));
        vo.setTotalIncome(totalGmv.subtract(totalRefundAmount));
        vo.setIncomePercentage(calculatePercentage(vo.getValetIncome(), vo.getTotalIncome()));
        log.info("收入计算结果: valetIncome={}, totalIncome={}", vo.getValetIncome(), vo.getTotalIncome());
    }

    private void setGmv(ValetDashboardRespVO vo, BigDecimal valetGmv, BigDecimal totalGmv) {
        vo.setValetGmv(valetGmv);
        vo.setTotalGmv(totalGmv);
        vo.setGmvPercentage(calculatePercentage(valetGmv, totalGmv));
        log.info("交易额统计完成: 代客GMV={}元, 总GMV={}元, 占比{}", valetGmv, totalGmv, vo.getGmvPercentage());
    }

    private void setPaidCount(ValetDashboardRespVO vo, ValetDashboardTempDTO temp2) {
        vo.setValetPaidCount(temp2.getValetPaidCount());
        vo.setTotalPaidCount(temp2.getTotalPaidCount());
        vo.setPaidPercentage(calculatePercentage(temp2.getValetPaidCount(), temp2.getTotalPaidCount()));
        log.info("支付订单统计完成: 代客支付数={}, 总支付数={}, 占比{}", temp2.getValetPaidCount(), temp2.getTotalPaidCount(), vo.getPaidPercentage());
    }

    private void setOrderCount(ValetDashboardRespVO vo, ValetDashboardTempDTO temp) {
        vo.setValetOrderCount(temp.getValetOrderCount());
        vo.setTotalOrderCount(temp.getTotalOrderCount());
        vo.setValetOrderPercentage(calculatePercentage(temp.getValetOrderCount(), temp.getTotalOrderCount()));
        log.info("订单数量统计完成: 代客订单数={}, 总订单数={}, 占比{}", temp.getValetOrderCount(), temp.getTotalOrderCount(), vo.getValetOrderPercentage());
    }

    /**
     * 统计为空时，返回空数据
     */
    private ValetDashboardRespVO emptyStatics(ValetDashboardRespVO vo, ValetDashboardReqDTO reqDTO) {
        vo.setStartTime(reqDTO.getStartTime());
        vo.setEndTime(reqDTO.getEndTime());
        return vo;
    }

    /**
     * 计算百分比
     */
    private String calculatePercentage(BigDecimal part, BigDecimal total) {
        if (total.compareTo(BigDecimal.ZERO) == 0) return "0%";
        return part.multiply(new BigDecimal("100"))
                .divide(total, 4, RoundingMode.HALF_UP)
                .setScale(2, RoundingMode.HALF_UP)
                + "%";
    }

    /**
     * 计算客单价
     */
    private BigDecimal calculateCustomerPrice(BigDecimal gmv, BigDecimal count) {
        if (count == null || count.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return gmv.divide(count, 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算比小程序下单的百分比
     */
    private String calculatePriceDiff(BigDecimal valet, BigDecimal total) {
        if (total.compareTo(BigDecimal.ZERO) == 0) return "0%";
        BigDecimal diff = valet.divide(total, 4, RoundingMode.HALF_UP)
                .subtract(BigDecimal.ONE)
                .multiply(new BigDecimal(100));
        return diff.setScale(2, RoundingMode.HALF_UP) + "%";
    }
}
package com.jlr.ecp.order.controller.app.order.vo.Integration;


import com.jlr.ecp.framework.common.pojo.PageResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "小程序端 - 最近三年历史订单的信息 Resp VO")
@Data
public class OrderIntegrationLatestOrderRespVO {
    /**
     * 1.最近三年订单的分页数据
     */
    @Schema(description = "最近三年订单的分页数据")
    PageResult<OrderIntegrationPageRespVO> pageResult;

    /**
     * 2.查询手机号 通过consumer_code 查询 t_consumer_info 表中phone_encrypt 进行解密
     */
    @Schema(description = "查询手机号 通过consumer_code 查询 t_consumer_info 表中phone_encrypt 进行解密")
    private String phoneNumber;
}

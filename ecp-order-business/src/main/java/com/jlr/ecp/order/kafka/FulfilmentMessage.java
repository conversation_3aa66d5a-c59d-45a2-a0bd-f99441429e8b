package com.jlr.ecp.order.kafka;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class FulfilmentMessage implements Serializable {

    /**
     * 消息id，具有唯一性
     * */
    private String messageId;

    /**
     * 订单号
     * */
    private String orderCode;

    /**
     *
     * */
    private String vcsOrderCode;

    /**
     * 订单item编码;订单item编码
     */
    private String orderItemCode;

    /**
     *  车辆VIN
     * */
    private String vin;

    /**
     * 服务起始时间;服务起始时间
     */
    private LocalDateTime serviceBeginDate;

    /**
     * 服务结束时间;服务结束时间
     */
    private LocalDateTime serviceEndDate;

    /**
     * 高德服务结束时间;高德服务结束时间
     */
    private LocalDateTime aMapServiceEndDate;

    /**
     * 租户号
     * */
    private Long tenantId;

    /**
     * 创建人
     * */
    private String createBy;

    /**
     * 服务履约类型：1：远程车控Remote Service；2：PIVI Subscription Service；
     */
    private Integer serviceType;
}

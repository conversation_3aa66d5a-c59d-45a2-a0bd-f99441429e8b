package com.jlr.ecp.order.controller.app.refund.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jlr.ecp.order.api.order.vo.detail.ProductItemInfoAppVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


@Data
@Schema(description = "App端 - 退款详情基类VO")
public class BaseRefundItemDetailVO {

    @Schema(description = "退单订单号")
    private String refundOrderCode;

    @Schema(description = "原始订单号")
    private String originOrderCode;

    @Schema(description = "退款金额")
    private String refundMoneyAmount;

    @Schema(description = "退款积分")
    private Integer refundPoints;

    @Schema(description = "退单状态")
    private Integer refundOrderStatus;

    @Schema(description = "售后状态(补充说明)")
    private Integer refundStatusSup;

    @Schema(description = "售后状态(补充说明-文字)")
    private String refundStatusSupStr;

    @Schema(description = "退单原因")
    private Integer refundReason;

    @Schema(description = "退单原因(中文)")
    private String refundReasonStr;

    @Schema(description = "退单数量")
    private Integer refundQuantity;

    @Schema(description = "通过创建时间排序方式（asc：升序，desc：降序）")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;


    @Schema(description = "附件")
    private List<String> attachmentUrls;

    /**
     * 商品item
     */
    @Schema(description = "商品信息")
    ProductItemInfoAppVO productItemInfo;

    @Schema(description = "状态记录")
    List<RefundDetailLogVO> logList;


    @Schema(description = "下单时间")
    private String orderTime;

    @Schema(description = "补充描述")
    private String SupDesc;

    @Schema(description = "退单发起来源 1: 用户发起 2：系统自动 3：运营发起")
    private Integer refundSource;
}

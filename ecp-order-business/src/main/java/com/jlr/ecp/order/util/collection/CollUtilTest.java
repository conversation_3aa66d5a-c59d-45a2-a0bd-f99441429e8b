package com.jlr.ecp.order.util.collection;

import cn.hutool.core.collection.CollUtil;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedbackOptionsDTO;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class CollUtilTest {


    private static void testEqualList(List<FeedbackOptionsDTO> list1, 
                                     List<FeedbackOptionsDTO> list2, 
                                     boolean expectedResult) {
        boolean result = CollUtil.isEqualList(list1, list2);
        System.out.println("测试结果: " + result + " | 期望结果: " + expectedResult);
        System.out.println("测试" + (result == expectedResult ? "通过" : "失败") + "\n");
    }
}

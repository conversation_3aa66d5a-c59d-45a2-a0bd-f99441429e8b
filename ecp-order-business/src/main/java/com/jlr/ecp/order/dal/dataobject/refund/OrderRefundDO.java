package com.jlr.ecp.order.dal.dataobject.refund;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_order_refund
 * <AUTHOR>
 * @TableName t_order_refund
 */
@TableName(value ="t_order_refund")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderRefundDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 退单订单号;退单订单号,订单号规则：R+子单号+3位数
     */
    @TableField(value = "refund_order_code")
    private String refundOrderCode;

    /**
     * 原始订单号;原始订单号
     */
    @TableField(value = "origin_order_code")
    private String originOrderCode;

    /**
     退单订单状态：
     1：发起整单退款申请
     2：发起部分退款申请
     3：同意整单退款申请
     4：同意部分退款申请
     5：订单整单退款完成
     6：订单部分退款完成
     7：拒绝整单退单申请
     8：拒绝部分退款申请

     */
    @TableField(value = "refund_order_status")
    private Integer refundOrderStatus;

    /**
     * 提交人;提交人
     */
    @TableField(value = "submit_user")
    private String submitUser;

    /**
     * 是否退款;是否退款 0：否 1：是
     */
    @TableField(value = "refund_money")
    private Integer refundMoney;

    /**
     * 退款金额;退款金额(单位分)
     */
    @TableField(value = "refund_money_amount")
    private Integer refundMoneyAmount;


    /**
     * 服务结束时间;服务结束时间
     */
    @TableField(value = "service_end_date")
    private LocalDateTime serviceEndDate;

    /**
     * 退款备注信息;退款备注信息
     */
    @TableField(value = "refund_remark")
    private String refundRemark;

    /**
     * 补充描述;补充描述
     */
    @TableField(value = "sup_desc")
    private String supDesc;


    /**
     * 审批拒绝理由
     */
    @TableField(value = "reject_reason")
    private String rejectReason;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;
    

    /**
     * 1:  用户发起
     * 2：系统自动
     * 3：运营发起
     */
    @TableField(value = "refund_source")
    private Integer refundSource;

    /**
     * 售后类型
     * 1: 退货退款
     * 2: 仅退款
     */
    @TableField(value = "refund_order_type")
    private Integer refundOrderType;

    /**
     * 退款原因
     */
    @TableField(value = "refund_reason")
    private Integer refundReason;

    /**
     * 物流退单状态;
     * 90101
     * 待退货审核
     * 90102
     * 待商品寄回
     * 90103
     * 待退款审核
     * 90301
     * 分账退款中
     * 90302
     * 退款处理中
     * 90501
     * 售后完成，已退款
     * 90701
     * 售后关闭，拒绝退款申请
     * 90702
     * 售后关闭，买家已撤销申请
     */
    @TableField(value = "logistics_refund_status")
    private Integer logisticsRefundStatus;

    /**
     * 优惠券退单状态;
     * 90101       发起退款申请
     * 90701       拒绝退款申请
     * 90301       退款处理中
     * 90501       退款完成售后关闭
     * 90702       买家已撤销退款申请
     */
    @TableField(value = "coupon_refund_status")
    private Integer couponRefundStatus;

    /**
     * 满减优惠券code
     */
    @TableField(value = "refund_coupon_code")
    private String refundCouponCode;

    /**
     *
     * 退单履约类型;
     * 1：远程车控REMOTE SERVICE； 2：PIVI Subscription   3: 实物商品；4：组合商品 5：优惠券商品
     */
    @TableField(value = "refund_order_fufilment_type")
    private Integer refundFulfilmentType;

    /**
     * 售后状态(补充说明)  1-券码逾期 2-券码逾期(部分) 3-主动退款 4-主动退款(部分)
     */
    @TableField(value = "refund_status_sup")
    private Integer refundStatusSup;

    /**
     * 物流单号
     */
    @TableField(value = "logistics_code")
    private String logisticsCode;

    /**
     * 物流公司code
     */
    @TableField(value = "logistics_company_code")
    private String logisticsCompanyCode;

    /**
     * 物流公司名称
     */
    @TableField(value = "logistics_company_name")
    private String logisticsCompanyName;

    /**
     * 退货审核备注
     */
    @TableField(value = "return_audit_remark")
    private String returnAuditRemark;

    /**
     * 退款审核备注
     */
    @TableField(value = "refund_audit_remark")
    private String refundAuditRemark;

    /**
     * 物流凭证图片地址 多个图片用,分隔开
     */
    @TableField(value = "logistics_attachment")
    private String logisticsAttachment;

    /**
     * 退单运费(单位分)
     */
    @TableField(value = "refund_freight")
    private Integer refundFreight;

    /**
     * 分账退款标识 1-分账退款 0-未分账退款 OrderRefundIndependentTypeEnum
     */
    @TableField(value = "refund_independent_type")
    private Integer refundIndependentType;
}
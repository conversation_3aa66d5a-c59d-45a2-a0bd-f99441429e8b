package com.jlr.ecp.order.util.refund;

/**
 * 一般业务code生成、解析器
 * <AUTHOR>
 */
public class CodeGenerator {

    /***
     * 生成下一个递增编码
     */
    public static String nextCode(String inputCode) {
        if (inputCode == null || inputCode.isEmpty()) {
            return null;
        }
        // 截取后三位数字
        String numberStr = inputCode.replaceAll("\\D", "");
        if (numberStr.length() > 3) {
            numberStr = numberStr.substring(numberStr.length() - 3);
        }
        int number = Integer.parseInt(numberStr);
        // 递增数字，并构建新的编码
        return inputCode.substring(0, inputCode.length() - numberStr.length()) + String.format("%03d", number + 1);
    }

    /***
     * 根据前缀初始化编码
     */
    public static String initCodeWithPrefix(String prefix) {
        // 默认的前缀
        String defaultPrefix = "";
        // 在这里可以根据实际需求添加更多的英文枚举判断逻辑
        // 如果没有匹配到任何枚举，可以使用默认的前缀

        // 示例：判断入参的英文枚举
        if (prefix == null || prefix.isEmpty()) {
            // 默认使用 "UN" 前缀
            return defaultPrefix + String.format("%03d", 1);
        }

        // 如果没有匹配到任何枚举，使用默认的前缀
        return prefix + String.format("%03d", 1);
    }

    public static void main(String[] args) {
        System.out.println(initCodeWithPrefix("LRM00120240118111809001XXXXXX"));
        System.out.println( nextCode("LRM00120240118111809001XXXXXX001"));
    }
}

package com.jlr.ecp.order.enums.order;

/**
 * 售后补充状态枚举
 * @author: lislu
 */
public enum RefundStatusSupEnum {

    /**
     * 券码逾期
     */
    COUPON_EXPIRED(1, "券码逾期"),

    /**
     * 券码逾期(部分)
     */
    COUPON_EXPIRED_PARTIAL(2, "券码逾期(部分)"),

    /**
     * 主动退款
     */
    ACTIVE_REFUND(3, "主动退款"),

    /**
     * 主动退款(部分)
     */
    ACTIVE_REFUND_PARTIAL(4, "主动退款(部分)");

    private final int code;
    private final String name;

    RefundStatusSupEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据状态码获取枚举实例
     * @param code 状态编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static RefundStatusSupEnum getByCode(int code) {
        for (RefundStatusSupEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 严格校验模式（可选）
     * @throws IllegalArgumentException 当code无效时抛出异常
     */
    public static RefundStatusSupEnum getByCodeStrict(int code) {
        RefundStatusSupEnum status = getByCode(code);
        if (status == null) {
            throw new IllegalArgumentException("无效的售后补充状态码: " + code);
        }
        return status;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

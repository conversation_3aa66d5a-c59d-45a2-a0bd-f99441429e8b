package com.jlr.ecp.order.service.feedback;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedbackPageReqDTO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedbackModifyDetailVO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedbackModifyLogVO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackModifyLogDO;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackModifyLogDOMapper;
import com.jlr.ecp.order.enums.feedback.ModifyOperationTypeEnum;
import com.jlr.ecp.order.enums.feedback.modifyLog.EvaluationDimensionFieldEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 评价配置操作记录表ServiceImpl
 */
@Service
public class FeedbackModifyLogDOServiceImpl extends ServiceImpl<FeedbackModifyLogDOMapper, FeedbackModifyLogDO> implements FeedbackModifyLogDOService {

    @Resource
    private FeedbackModifyLogDOMapper feedbackModifyLogDOMapper;

    @Override
    public PageResult<FeedbackModifyLogVO> selectModifyPage(FeedbackPageReqDTO dto) {
        PageResult<FeedbackModifyLogDO> pageResult = feedbackModifyLogDOMapper.selectPage(dto, new LambdaQueryWrapperX<FeedbackModifyLogDO>()
                .eq(FeedbackModifyLogDO::getFeedbackCode, dto.getFeedbackCode())
                .eq(FeedbackModifyLogDO::getIsDeleted, false)
                .orderByDesc(FeedbackModifyLogDO::getCreatedTime));

        Set<String> allDetailFields = EvaluationDimensionFieldEnum.getAllDetailFields();

        List<FeedbackModifyLogVO> list = pageResult.getList().stream().map(item -> {
            FeedbackModifyLogVO vo = new FeedbackModifyLogVO();
            BeanUtils.copyProperties(item, vo);



            // 设置修改字段和详情显示标志
            String oldValue = item.getModifyFieldOldValue();
            if (StrUtil.isNotBlank(oldValue)) {
                Set<String> oldSet = JSON.parseObject(oldValue, Feature.OrderedField).keySet();
                vo.setModifyField(String.join("、", oldSet));
                vo.setModifyDetail(oldSet.stream().anyMatch(allDetailFields::contains));
            } else {
                vo.setModifyField("");
                vo.setModifyDetail(false);
            }

            // 解析 JSON 字段
//            JSONObject oldJson = JSONUtil.parseObj(item.getModifyFieldOldValue());
//            JSONObject newJson = JSONUtil.parseObj(item.getModifyFieldNewValue());
            // 构建变更详情列表
//            List<FeedbackModifyLogVO.FieldChange> changes = new ArrayList<>();
//            oldJson.forEach((key, oldValue) -> {
//                String newValue = newJson.getStr(key);
//                changes.add(new FeedbackModifyLogVO.FieldChange(key, oldValue.toString(), newValue));
//            });
//            vo.setChanges(changes);
            return vo;
        }).collect(Collectors.toList());

        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public FeedbackModifyDetailVO getLogDetail(Long id) {
        FeedbackModifyLogDO logDO = this.getById(id);
        if (logDO == null) {
            return null;
        }
        FeedbackModifyDetailVO vo = new FeedbackModifyDetailVO();
        BeanUtils.copyProperties(logDO, vo);

        // 解析 JSON 字段为 FieldChange 列表
        JSONObject oldJson = JSONUtil.parseObj(logDO.getModifyFieldOldValue());
        JSONObject newJson = JSONUtil.parseObj(logDO.getModifyFieldNewValue());

        List<FeedbackModifyDetailVO.FieldChange> changes = new ArrayList<>();
        oldJson.forEach((key, oldValue) -> {
            String newValue = newJson.getStr(key);
            changes.add(new FeedbackModifyDetailVO.FieldChange(key, oldValue.toString(), newValue));
        });
        vo.setChanges(changes);
        return vo;
    }
}

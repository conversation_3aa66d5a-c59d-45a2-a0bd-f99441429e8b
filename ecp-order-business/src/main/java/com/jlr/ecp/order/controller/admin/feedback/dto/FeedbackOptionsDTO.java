package com.jlr.ecp.order.controller.admin.feedback.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Schema(description = "管理后台 -  评价维选项DTO ")
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class FeedbackOptionsDTO {



    /**
     * 名称
     */
    @Schema(description = "选项名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "选项名称不能为空")
    private String option;


    /**
     * 排序字段
     */
    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

}

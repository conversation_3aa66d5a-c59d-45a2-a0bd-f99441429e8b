package com.jlr.ecp.order.dal.dataobject.independent;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@TableName(value = "t_order_independent_order_item")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderIndependentOrderItemDO extends BaseDO {

    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 分账申请单号
     */
    @TableField(value = "independent_code")
    private String independentCode;

    /**
     * 分账类型
     */
    @TableField(value = "independent_type")
    private String independentType;

    /**
     * 订单号
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 订单item编码
     */
    @TableField(value = "order_item_code")
    private String orderItemCode;

    /**
     * 分账金额，单位分
     */
    @TableField(value = "div_amt")
    private Integer divAmt;

    /**
     * 商户号
     */
    @TableField(value = "merchant_account_no")
    private String merchantAccountNo;

}

package com.jlr.ecp.order.service.cart;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.cart.dto.CalculateAmountDTO;
import com.jlr.ecp.order.api.cart.dto.CartCreatDTO;
import com.jlr.ecp.order.api.cart.dto.CartItemUpdateDTO;
import com.jlr.ecp.order.controller.app.cart.vo.CalculateAmountVO;

/**
 * 购物车主表表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-18
 */
public interface ShoppingCarService {


    /**
     * 添加购物车
     * @param cartCreatDTO 添加DTO购物车
     * @return String
     */
    Boolean addCart(CartCreatDTO cartCreatDTO, String brandCode);

    /**
     * 添加购物车
     * @param cartCreatDTO 添加DTO购物车
     * @return String
     */
    Boolean brandGoodsAddCart(CartCreatDTO cartCreatDTO, String brandCode);


    /***
     * 修改sku和数量
     *  @param cartItemUpdateDTO 购物车栏目
     * @return String
     */
    Boolean chooseSku(CartItemUpdateDTO cartItemUpdateDTO);

    /***
     * 修改sku和数量
     *  @param cartItemUpdateDTO 购物车栏目
     * @return String
     */
    CommonResult<String> brandGoodsChooseSku(CartItemUpdateDTO cartItemUpdateDTO, String jlrId);

}


package com.jlr.ecp.order.dal.dataobject.independent;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

@TableName(value = "t_order_independent")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderIndependentDO extends BaseDO {

    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 分账申请单号
     */
    @TableField(value = "independent_code")
    private String independentCode;

    /**
     * 分账类型
     */
    @TableField(value = "independent_type")
    private String independentType;

    /**
     * 订单号
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 券编码，分账类型为“券核销”时有值
     */
    @TableField(value = "coupon_code")
    private String couponCode;

    /**
     * 用户编码
     */
    @TableField(value = "consumer_code")
    private String consumerCode;

    /**
     * 支付中心交易流水号
     */
    @TableField(value = "pay_apply_no")
    private String payApplyNo;

    /**
     * 分账金额，单位分
     */
    @TableField(value = "total_div_amt")
    private Integer totalDivAmt;

    /**
     * 业务线编码
     */
    @TableField(value = "business_code")
    private String businessCode;

    /**
     * 分账状态
     */
    @TableField(value = "independent_status")
    private Integer independentStatus;

    /**
     * 支付中心分账申请编号
     */
    @TableField(value = "trans_apply_no")
    private String transApplyNo;

    /**
     * 分账成功时间
     */
    @TableField(value = "independent_succ_time")
    private LocalDateTime independentSuccTime;

}

package com.jlr.ecp.order.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jlr.ecp.framework.common.exception.ErrorCode;
import com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.common.util.json.JsonUtils;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.inventory.api.InventoryOrderApi;
import com.jlr.ecp.inventory.dto.qry.InventoryOrderSkuDTO;
import com.jlr.ecp.logistics.api.freighttemplate.FreightTemplateApi;
import com.jlr.ecp.logistics.api.freighttemplate.dto.FreightCalculateReqDTO;
import com.jlr.ecp.logistics.api.freighttemplate.dto.FreightCalculateRespDTO;
import com.jlr.ecp.logistics.api.freighttemplate.dto.GoodsInfoDTO;
import com.jlr.ecp.order.api.cart.dto.CalculateAmountDTO;
import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.ProductSpuSkuDTO;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.api.order.dto.*;
import com.jlr.ecp.order.api.order.dto.address.OrderGiftAddressDTO;
import com.jlr.ecp.order.api.order.vo.BrandGoodsOrderInfoPageVO;
import com.jlr.ecp.order.api.order.vo.BrandGoodsOrderItemVO;
import com.jlr.ecp.order.api.order.vo.OrderCreateRespVO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.controller.app.cart.vo.CalculateAmountVO;
import com.jlr.ecp.order.controller.app.cart.vo.ShoppingCarItemVO;
import com.jlr.ecp.order.dal.dataobject.order.*;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.mysql.order.*;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.cart.CarPaymentTypeEnum;
import com.jlr.ecp.order.enums.cart.CartItemTypeEnum;
import com.jlr.ecp.order.enums.independent.OrderIndependentStatusEnum;
import com.jlr.ecp.order.enums.order.*;
import com.jlr.ecp.order.enums.refund.RefundLogisticsStatusEnum;
import com.jlr.ecp.order.kafka.message.CancelOrderMessage;
import com.jlr.ecp.order.service.cart.ShoppingCarItemService;
import com.jlr.ecp.order.service.internal.price.CalculateService;
import com.jlr.ecp.order.util.PIPLDataUtil;
import com.jlr.ecp.order.util.money.MoneyUtil;
import com.jlr.ecp.order.util.order.OrderCodeGenerator;
import com.jlr.ecp.order.util.order.SequenceNumberGenerator;
import com.jlr.ecp.product.api.sku.ProductSkuApi;
import com.jlr.ecp.product.api.sku.dto.AllPaymentInfoDTO;
import com.jlr.ecp.product.api.sku.dto.CartItemDTO;
import com.jlr.ecp.product.api.sku.dto.OrderValidationInfo;
import com.jlr.ecp.product.api.sku.dto.ProductSnapshotDTO;
import com.jlr.ecp.product.enums.product.ProductFulfilmentTypeEnum;
import com.jlr.ecp.subscription.api.consumer.ConsumerServiceApi;
import com.jlr.ecp.system.api.address.AddressConfigApi;
import com.jlr.ecp.system.api.address.dto.AddressNameDTO;
import com.jlr.ecp.system.api.permission.PermissionApi;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jlr.ecp.order.constant.Constants.CONCAT_SYMBOL;
import static com.jlr.ecp.order.constant.Constants.CUSTOMER_SERVICE_ORDER;
import static com.jlr.ecp.order.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @description 针对表【t_order_info(t_order_info)】的数据库操作Service实现
 * @createDate 2023-12-20 10:41:04
 */
@Service
@Slf4j
public class BrandGoodsOrderInfoDOServiceImpl extends ServiceImpl<OrderInfoDOMapper, OrderInfoDO> implements BrandGoodsOrderInfoDOService {

    @Value("${confirm.receipt.maxTime:2}")
    private Integer maxTime;

    @Value("${confirm.receipt.minTime:15}")
    private Integer minTime;

    @Resource
    private ProductSkuApi productSkuApi;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;

    @Resource
    private OrderItemDOService orderItemDOService;

    @Resource
    private OrderStatusLogDOMapper orderStatusLogDOMapper;

    @Resource
    private OrderItemLogisticsDOMapper orderItemLogisticsDOMapper;

    @Resource
    private OrderTermsDOService orderTermsDOService;

    @Resource
    private ShoppingCarItemService shoppingCarItemService;

    @Resource
    private ProducerTool producerTool;

    @Resource
    private VcsOrderStatisticDOMapper vcsOrderStatisticDOMapper;

    @Resource
    private OrderStatisticDOMapper orderStatisticDOMapper;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private ConsumerServiceApi consumerServiceApi;

    @Resource
    private CalculateService calculateService;

    @Resource
    private OrderDiscountDetailDOMapper orderDiscountDetailDOMapper;
    
    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Resource
    private PIPLDataUtil piplDataUtil;

    @Resource
    private InventoryOrderApi inventoryOrderApi;

    @Resource
    private AddressConfigApi addressConfigApi;

    @Resource
    private FreightTemplateApi freightTemplateApi;

    @Resource
    private OrderRefundDOMapper orderRefundDOMapper;

    @Resource
    private OrderRefundItemDOMapper orderRefundItemDOMapper;

    private final ExecutorService customExecutor = Executors.newFixedThreadPool(2);


    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderCreateRespVO createOrderInfo(BrandGoodsOrderCreateDTO orderCreateDTO) {
        log.info("service层创建订单，orderCreateDTO:{}", JSON.toJSONString(orderCreateDTO));

        // 计算订单价格
        CalculateAmountVO calculateAmountVO = getCalculateAmountVO(orderCreateDTO);
        validCalculateAmount(orderCreateDTO, calculateAmountVO);

        // 原有下单接口前置校验
        OrderCreateRespVO checkResult = performPreOrderValidations(orderCreateDTO);
        if (checkResult != null) {
            return checkResult;
        }

        // 校验运费
        validFreightAmount(orderCreateDTO, calculateAmountVO);

        //生成订单
        String oneID = orderCreateDTO.getGlobalInfoDTO().getConsumerCode(); // 现在oneID 是作为consumerCode进行传入
        LocalDateTime now = LocalDateTime.now(); //当前时间
        OrderCreateRespVO response = processSplit(orderCreateDTO, oneID, now, calculateAmountVO);
        //异步清除订单提交内的购物车商品项
        asyncClearShoppingCartItems(orderCreateDTO);

        return response;
    }

    private void validFreightAmount(BrandGoodsOrderCreateDTO orderCreateDTO, CalculateAmountVO calculateAmountVO) {
        boolean containsBg = calculateAmountVO.getItemList().stream()
                .anyMatch(item -> BusinessIdEnum.BRAND_GOODS.getCode().equals(item.getBusinessCode()));
        if (containsBg) {
            FreightCalculateReqDTO freightCalculateReqDTO = new FreightCalculateReqDTO();
            List<GoodsInfoDTO> goddsInfoList = new ArrayList<>();
            for (ShoppingCarItemVO shoppingCarItemVO : calculateAmountVO.getItemList()) {
                if (BusinessIdEnum.BRAND_GOODS.getCode().equals(shoppingCarItemVO.getBusinessCode())) {
                    GoodsInfoDTO goodsInfoDTO = new GoodsInfoDTO();
                    goodsInfoDTO.setNumber(shoppingCarItemVO.getQuantity());
                    goodsInfoDTO.setCostAmount(shoppingCarItemVO.getCostAmount());
                    goodsInfoDTO.setWeight(shoppingCarItemVO.getWeight());
                    goodsInfoDTO.setTemplateCode(shoppingCarItemVO.getFreightTemplateCode());
                    goddsInfoList.add(goodsInfoDTO);
                }
            }
            freightCalculateReqDTO.setGoddsInfoList(goddsInfoList);
            freightCalculateReqDTO.setAreaCode(orderCreateDTO.getGiftInfoDTO().getAdCode());
            log.info("运费计算请求：{}", JSON.toJSONString(freightCalculateReqDTO));
            CommonResult<FreightCalculateRespDTO> result = freightTemplateApi.freightCalculate(freightCalculateReqDTO);
            log.info("运费计算请求结果：{}", JSON.toJSONString(result));
            if (result.isSuccess()) {
                FreightCalculateRespDTO freightCalculateRespDTO = result.getData();
                String freight = freightCalculateRespDTO.getFreight();
                if (!compareAmount(freight, orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO().getFreightAmount())) {
                    log.error("运费校验失败，运费不相等");
                    throw ServiceExceptionUtil.exception(CHECK_ORDER_AMOUNT_ERROR);
                }
            } else {
                log.error("运费计算调用失败");
                throw ServiceExceptionUtil.exception(new ErrorCode(result.getCode(), result.getMsg()));
            }
        }
    }

    @Override
    public PageResult<BrandGoodsOrderInfoPageVO> getPage(BrandGoodsOrderPageReqDTO dto) {
        // 1. 基于查询条件查询
        Page<BrandGoodsOrderInfoPageVO> orderPage = orderInfoDOMapper.getBrandGoodsOrdersWithPaging(new Page<>(dto.getPageNo(), dto.getPageSize()), dto);
        List<BrandGoodsOrderInfoPageVO> orderList = orderPage.getRecords();
        if (CollectionUtils.isEmpty(orderList)) {
            return new PageResult<>(Collections.emptyList(), orderPage.getTotal());
        }

        // 改成收集所有符合条件的orderCode的 orderItem
        List<String> orderCodes = orderList.stream()
                .map(BrandGoodsOrderInfoPageVO::getOrderCode)
                .collect(Collectors.toList());
        Map<String, OrderItemLogisticsDO> logisticsMap = orderItemLogisticsDOMapper.mapByOrderCode(orderCodes);

        List<OrderItemDO> allOrderItems = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .in(OrderItemDO::getOrderCode, orderCodes)
                .eq(OrderItemDO::getIsDeleted, 0));
        List<String> allOrderItemCodes = allOrderItems.stream()
                .map(OrderItemDO::getOrderItemCode)
                .collect(Collectors.toList());

        // 2. 使用收集到的orderItemCodes查询订单项详细信息
        List<BrandGoodsOrderItemVO> orderItems = orderInfoDOMapper.getBrandGoodsOrderDetailsByCodes(allOrderItemCodes);
        
        // 创建查找映射，将订单项按orderItemCode索引，避免重复查找
        Map<String, List<BrandGoodsOrderItemVO>> orderItemMap = orderItems.stream()
                .collect(Collectors.groupingBy(BrandGoodsOrderItemVO::getOrderCode));
        
        // 3. 组装订单项到订单中，同时处理 displayOrderStatus 和orderItem的 displayItemStatus 
        for (BrandGoodsOrderInfoPageVO order : orderList) {
            OrderItemLogisticsDO logistics = logisticsMap.get(order.getOrderCode());
            if (logistics != null) {
                order.setRecipient(logistics.getRecipient());
                order.setRecipientPhoneMix(logistics.getRecipientPhoneMix());
                order.setRecipientPhone(logistics.getRecipientPhone());
                order.setProvinceCode(logistics.getProvinceCode());
                order.setCityCode(logistics.getCityCode());
                order.setAreaCode(logistics.getAreaCode());
                order.setDetailAddress(logistics.getDetailAddress());
                order.setPostCode(logistics.getPostCode());
                order.setDeliveryNo(logistics.getLogisticsNo());
                order.setDeliveryStatus(logistics.getLogisticsStatus());
                order.setLogisticsCompany(logistics.getLogisticsCompany());
            }

            // 默认设置displayOrderStatus等于原始orderStatus
            order.setDisplayLogisticsOrderStatus(order.getLogisticsOrderStatus());
            order.setOrderItems(new ArrayList<>());
            
            List<BrandGoodsOrderItemVO> orderItemList = orderItemMap.get(order.getOrderCode());
            if (CollectionUtils.isNotEmpty(orderItemList)) {
                List<String> orderItemCodes = orderItemList.stream().map(BrandGoodsOrderItemVO::getOrderItemCode).collect(Collectors.toList());
                order.setOrderItemCodes(String.join(",", orderItemCodes));

                boolean hasAfterSalesInProgress = false;
                
                // 处理每个订单项，同时检查是否有售后中的项
                hasAfterSalesInProgress = isHasAfterSalesInProgress(orderItemList, hasAfterSalesInProgress);

                // 设置订单项列表
                order.setOrderItems(orderItemList);
                
                // 如果有售后中的订单项，将订单displayLogisticsOrderStatus设为"售后处理中"(90501)
                if (hasAfterSalesInProgress) {
                    order.setDisplayLogisticsOrderStatus(90501);
                }
            }
        }
        
        // 4. 返回分页结果
        return new PageResult<>(orderList, orderPage.getTotal());
    }

    private static boolean isHasAfterSalesInProgress(List<BrandGoodsOrderItemVO> orderItemList, boolean hasAfterSalesInProgress) {
        for (BrandGoodsOrderItemVO item : orderItemList) {
            // 检查是否在售后中
            boolean isInAfterSales = item.getRefundOrderStatus() != null &&
                    com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum.isRefunding(item.getRefundOrderStatus());

            if (isInAfterSales) {
                hasAfterSalesInProgress = true;
            }
        }
        return hasAfterSalesInProgress;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Void confirmReceipt(BrandGoodsOrderConfirmReceiptDTO reqDto) {
        OrderInfoDO orderInfoDOS = orderInfoDOMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, reqDto.getOrderCode())
                .eq(OrderInfoDO::getIsDeleted, false));
        List<OrderItemDO> orderItemDOList = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, reqDto.getOrderCode())
                .eq(OrderItemDO::getIsDeleted, false));

        orderInfoCheck(orderInfoDOS, orderItemDOList, reqDto.getOrderCode());

        //更新t_order_item_logistics的logistics_status为已收货;更新confirm_package_time为当前时间
        OrderItemLogisticsDO orderItemLogisticsDO = new OrderItemLogisticsDO();
        orderItemLogisticsDO.setLogisticsStatus(BgOrderItemLogisticsStatusEnum.SIGNED.getCode());
        orderItemLogisticsDO.setConfirmPackageTime(LocalDateTime.now());
        orderItemLogisticsDOMapper.update(orderItemLogisticsDO, new LambdaQueryWrapperX<OrderItemLogisticsDO>()
                .eq(OrderItemLogisticsDO::getOrderCode, reqDto.getOrderCode()));
        //更新t_order_item的item_status为已收货, 但如果订单行本身是售后已完成，则不更新
        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setItemStatus(OrderItemLogisticsStatusEnum.RECEIVED.getCode());
        List<String> orderItemCodeList = orderItemDOList.stream()
                .filter(item -> !OrderItemLogisticsStatusEnum.CLOSED.getCode().equals(item.getItemStatus()))
                .map(OrderItemDO::getOrderItemCode).collect(Collectors.toList());
        orderItemDOMapper.update(orderItemDO, new LambdaQueryWrapperX<OrderItemDO>()
                .in(CollectionUtils.isNotEmpty(orderItemCodeList), OrderItemDO::getOrderItemCode, orderItemCodeList));
        //更新t_order_info的logistics_status为已完成； 更新order_status为已完成; completed_time为当前时间
        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setLogisticsStatus(OrderLogisticsStatusEnum.ORDER_COMPLETED.getCode());
        orderInfoDO.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
        orderInfoDO.setCompletedTime(LocalDateTime.now());
        orderInfoDOMapper.update(orderInfoDO, new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, reqDto.getOrderCode()));
        return null;
    }

    @Override
    public Boolean confirmReceiptList(ConfirmReceiptJobReqDTO reqDTO) {
        log.info("自动确认收货开始，req={},时间：{}", JsonUtils.toJsonString(reqDTO), LocalDateTime.now());

        // 分页参数
        int pageSize = 500;
        int currentPage = 1;

        List<String> updateOrderCodeList = new ArrayList<>();
        LocalDateTime nowTime = LocalDateTime.now();
        while (true) {
            // 获取需要执行确认收货的订单，查询条件为创建时间 > 15 天， 且物流状态处于部分发货或者已发货
            Page<OrderInfoDO> orderInfoPage = orderInfoDOMapper.selectPage(new Page<>(currentPage, pageSize), new LambdaQueryWrapperX<OrderInfoDO>()
                    .in(CollectionUtils.isNotEmpty(reqDTO.getOrderCodeList()), OrderInfoDO::getOrderCode, reqDTO.getOrderCodeList())
                    .lt(CollectionUtils.isEmpty(reqDTO.getOrderCodeList()), OrderInfoDO::getCreatedTime, nowTime.minusDays(minTime))
                    .ge(CollectionUtils.isEmpty(reqDTO.getOrderCodeList()), OrderInfoDO::getCreatedTime, nowTime.minusMonths(maxTime))
                    .in(OrderInfoDO::getLogisticsStatus, List.of(OrderLogisticsStatusEnum.PARTIALLY_SHIPPED.getCode(), OrderLogisticsStatusEnum.FULLY_SHIPPED.getCode()))
                    .eq(OrderInfoDO::getIsDeleted, false)
                    .orderByDesc(OrderInfoDO::getCreatedTime)
            );
            List<OrderInfoDO> orderInfoDOS = orderInfoPage.getRecords();
            if (CollUtil.isEmpty(orderInfoDOS)) {
                log.info("自动确认收货完成，orderInfoDOS为空，req={},时间：{}", JsonUtils.toJsonString(reqDTO), LocalDateTime.now());
                break;
            }

            // 筛选满足条件执行的订单:1.所有已发货时间(多订单行时, 最晚发货时间)大于15天; 2.所有订单行物流状态为已签收(已妥投)
            List<String> orderCodeList = orderInfoDOS.stream().map(OrderInfoDO::getOrderCode).collect(Collectors.toList());
            List<OrderItemLogisticsDO> orderItemLogisticsDOList = orderItemLogisticsDOMapper.selectList(new LambdaQueryWrapperX<OrderItemLogisticsDO>()
                    .in(OrderItemLogisticsDO::getOrderCode, orderCodeList)
                    .eq(OrderItemLogisticsDO::getIsDeleted, false)
            );
            if (CollUtil.isEmpty(orderItemLogisticsDOList)) {
                log.info("自动确认收货完成，orderItemLogisticsDOList为空，req={},时间：{}", JsonUtils.toJsonString(reqDTO), LocalDateTime.now());
                break;
            }
            log.info("自动确认收货，orderItemLogisticsDOList的数量：{}", orderItemLogisticsDOList.size());
            List<OrderItemLogisticsDO> filterOrderItemLogistics = filterRefundFinishOrderItem(orderItemLogisticsDOList);
            if (CollUtil.isEmpty(filterOrderItemLogistics)) {
                log.info("自动确认收货完成，filterOrderItemLogistics为空");
                break;
            }
            log.info("自动确认收货，filterOrderItemLogistics的数量：{}", filterOrderItemLogistics.size());
            Map<String, List<OrderItemLogisticsDO>> orderItemLogisticsMap = filterOrderItemLogistics.stream().collect(Collectors.groupingBy(OrderItemLogisticsDO::getOrderCode));
            List<String> logisticsOrderCode = new ArrayList<>();
            for (Map.Entry<String, List<OrderItemLogisticsDO>> entry : orderItemLogisticsMap.entrySet()) {
                String orderCode = entry.getKey();
                List<OrderItemLogisticsDO> logisticsList = entry.getValue();
                log.info("自动确认收货，orderCode:{}, logisticsList的数量：{}", orderCode, logisticsList.size());
                // 检查该订单的所有订单行是否都已签收
                boolean allSigned = logisticsList.stream()
                        .allMatch(logistics -> {
                            Integer status = logistics.getLogisticsStatus();
                            return status != null && status == BgOrderItemLogisticsStatusEnum.PROBLEMATIC.getCode();
                        });

                // 找出该订单中最晚的发货时间
                Optional<LocalDateTime> latestSendTime = logisticsList.stream()
                        .map(OrderItemLogisticsDO::getSendTime)
                        .filter(Objects::nonNull) // 过滤掉 null
                        .max(LocalDateTime::compareTo);

                if (allSigned && latestSendTime.isPresent() && latestSendTime.get().isBefore(LocalDateTime.now().minusDays(minTime))) {
                    logisticsOrderCode.add(orderCode);
                } else {
                    log.info("无法进行自动确认收货, orderCode:{}, allSigned:{}, latestSendTime:{}", orderCode, allSigned, latestSendTime);
                }
            }
            if (CollUtil.isEmpty(logisticsOrderCode)) {
                log.info("自动确认收货完成，logisticsOrderCode为空，req={},时间：{}", JsonUtils.toJsonString(reqDTO), LocalDateTime.now());
                break;
            }
            updateOrderCodeList.addAll(logisticsOrderCode);
            // 如果当前页没有更多数据，退出循环
            if (orderInfoPage.getTotal() <= (long) currentPage * pageSize) {
                break;
            }
            currentPage++;
        }
        //异步执行收货
        synConfirmReceipt(updateOrderCodeList);
        log.info("自动确认收货完成，req={},时间：{}", JsonUtils.toJsonString(reqDTO), LocalDateTime.now());
        return true;
    }


    /**
     * 过滤掉已退款的订单项
     *
     * @param orderItemLogisticsDOList 订单项物流信息列表
     * @return 过滤后的订单项物流信息列表，不包含已退款的订单项
     */
    private List<OrderItemLogisticsDO> filterRefundFinishOrderItem(List<OrderItemLogisticsDO> orderItemLogisticsDOList) {
        List<OrderItemLogisticsDO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(orderItemLogisticsDOList)) {
            log.info("过滤掉已退款的订单项, orderItemLogisticsDOList为空");
            return resp;
        }
        log.info("过滤掉已退款的订单项, 待过滤orderItemLogisticsDOList数量:{}", orderItemLogisticsDOList.size());
        List<String> orderItemCodeList = orderItemLogisticsDOList.stream()
                .map(OrderItemLogisticsDO::getOrderItemCode)
                .collect(Collectors.toList());
        Set<String> refundFinishItemCodeSet = getRefundFinishItemCodeSet(orderItemCodeList);
        log.info("过滤掉已退款的订单项, refundFinishItemCodeSet数量:{}", refundFinishItemCodeSet.size());
        for (OrderItemLogisticsDO orderItemLogisticsDO : orderItemLogisticsDOList) {
            if (refundFinishItemCodeSet.contains(orderItemLogisticsDO.getOrderItemCode())) {
                log.info("过滤掉已退款的订单项, orderItemLogisticsDO:{}", orderItemLogisticsDO);
                continue;
            }
            resp.add(orderItemLogisticsDO);
        }
        log.info("过滤掉已退款的订单项, 过滤后orderItemLogisticsDOList数量:{}", resp.size());
        return resp;
    }

    /**
     * 获取已完成退款的订单项Set
     *
     * @param orderItemCodeList 订单项代码列表，用于查询对应的退款信息
     * @return 包含已完成退款的订单项代码的集合
     */
    private Set<String> getRefundFinishItemCodeSet(List<String> orderItemCodeList) {
        Set<String> refundFinishItemCodeSet = new HashSet<>();
        if (CollUtil.isEmpty(orderItemCodeList)) {
            log.info("获取已完成退款的订单项Set, orderItemCodeList为空");
            return refundFinishItemCodeSet;
        }
        log.info("获取已完成退款的订单项Set, orderItemCodeLists数量：{}", orderItemCodeList.size());
        List<OrderRefundItemDO> orderRefundItemDOList = orderRefundItemDOMapper.selectList(new LambdaQueryWrapperX<OrderRefundItemDO>()
                .in(OrderRefundItemDO::getOrderItemCode, orderItemCodeList)
                .eq(OrderRefundItemDO::getIsDeleted, false)
        );
        if (CollUtil.isEmpty(orderRefundItemDOList)) {
            log.info("获取已完成退款的订单项Set, orderRefundItemDOList为空");
            return refundFinishItemCodeSet;
        }
        log.info("获取已完成退款的订单项Set, 待查询的orderRefundItemDOList数量：{}", orderRefundItemDOList.size());
        List<String> refundOrderCodeList = orderRefundItemDOList.stream()
                .map(OrderRefundItemDO::getRefundOrderCode)
                .collect(Collectors.toList());
        List<OrderRefundDO> orderRefundDOList = orderRefundDOMapper.selectList(new LambdaQueryWrapperX<OrderRefundDO>()
                .in(OrderRefundDO::getRefundOrderCode, refundOrderCodeList)
                .eq(OrderRefundDO::getLogisticsRefundStatus, RefundLogisticsStatusEnum.REFUND_COMPLETED.getCode())
                .eq(OrderRefundDO::getIsDeleted, false)
        );
        if (CollUtil.isEmpty(orderRefundDOList)) {
            log.info("获取已完成退款的订单项Set, orderRefundDOList为空");
            return  refundFinishItemCodeSet;
        }
        log.info("获取已完成退款的订单项Set, 待查询的orderRefundDOList数量：{}", orderRefundDOList.size());
        Map<String, OrderRefundDO> refundDOMap = orderRefundDOList.stream()
                .collect(Collectors.toMap(OrderRefundDO::getRefundOrderCode, Function.identity(), (v1, v2) -> v1));
        for (OrderRefundItemDO refundItemDO : orderRefundItemDOList) {
            if (refundDOMap.containsKey(refundItemDO.getRefundOrderCode())) {
                refundFinishItemCodeSet.add(refundItemDO.getOrderItemCode());
            }
        }
        return refundFinishItemCodeSet;
    }

    private void synConfirmReceipt(List<String> updateOrderCodeList) {
        if (CollUtil.isEmpty(updateOrderCodeList)) {
            log.info("自动确认收货完成，updateOrderCodeList为空,时间：{}", LocalDateTime.now());
            return;
        }
        log.info("批量异步执行自动确认收货, updateOrderCodeList的数量:{}, updateOrderCodeList:{}", updateOrderCodeList, updateOrderCodeList.size());
        CompletableFuture.runAsync(() -> {
                    // 设置当前线程的租户上下文为提取到的租户ID
                    TenantContextHolder.setTenantId(1L);
                    try {
                        for (String orderCode : updateOrderCodeList) {
                            try {
                                confirmReceipt(orderCode);
                            } catch (Exception e) {
                                log.error("自动确认收货失败，订单号：{}", orderCode, e);
                            }
                        }
                    } catch (Exception e) {
                        log.error("自动确认收货失败，订单号：{}", updateOrderCodeList, e);
                    } finally {
                        TenantContextHolder.clear();
                    }
                }, customExecutor)
                .exceptionally(ex -> {
                    log.error("异步执行收货失败", ex);
                    return null;
                });
    }

    private void confirmReceipt(String orderCode) {
        log.info("执行自动确认收货, orderCode:{}", orderCode);
        // 查询是否有售后处理中，如果有，则不处理
        List<OrderItemDO> orderItemDOList = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, orderCode)
                .eq(OrderItemDO::getAftersalesStatus, OrderItemAftersalesStatusEnum.PROCESSING.getCode())
                .eq(OrderItemDO::getIsDeleted, false)
        );
        if (CollUtil.isNotEmpty(orderItemDOList)) {
            log.info("订单{}有售后处理中，不执行收货", orderCode);
            return;
        }
        //更新t_order_item_logistics的logistics_status为已收货;更新confirm_package_time为当前时间
        OrderItemLogisticsDO orderItemLogisticsDO = new OrderItemLogisticsDO();
        orderItemLogisticsDO.setLogisticsStatus(BgOrderItemLogisticsStatusEnum.SIGNED.getCode());
        orderItemLogisticsDO.setConfirmPackageTime(LocalDateTime.now());
        orderItemLogisticsDOMapper.update(orderItemLogisticsDO, new LambdaQueryWrapperX<OrderItemLogisticsDO>()
                .eq(OrderItemLogisticsDO::getOrderCode, orderCode));
        //更新t_order_item的item_status为已收货, 但如果订单行本身是售后已完成，则不更新
        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setItemStatus(OrderItemLogisticsStatusEnum.RECEIVED.getCode());
        orderItemDOMapper.update(orderItemDO, new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, orderCode)
                .ne(OrderItemDO::getItemStatus, OrderItemLogisticsStatusEnum.CLOSED.getCode()));
        //更新t_order_info的logistics_status为已完成； 更新order_status为已完成;
        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setLogisticsStatus(OrderLogisticsStatusEnum.ORDER_COMPLETED.getCode());
        orderInfoDO.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
        orderInfoDO.setCompletedTime(LocalDateTime.now());
        orderInfoDOMapper.update(orderInfoDO, new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCode));
    }

    private void orderInfoCheck(OrderInfoDO orderInfoDO, List<OrderItemDO> orderItemDOList, String orderCode) {
        if (orderInfoDO == null) {
            log.info("订单orderCode={}不存在", orderCode);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_INFO_LOST);
        }
        if (CollUtil.isEmpty(orderItemDOList)) {
            log.info("订单行信息缺失orderCode={}", orderCode);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_INFO_LOST);
        }
        //只有已发货或者部分发货的才能发起确认收货
        if (!(OrderLogisticsStatusEnum.PARTIALLY_SHIPPED.getCode() == orderInfoDO.getLogisticsStatus() ||
                OrderLogisticsStatusEnum.FULLY_SHIPPED.getCode() == orderInfoDO.getLogisticsStatus())) {
            log.info("订单orderCode={}物流状态不正确", orderCode);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_LOGISTICS_STATUS_EXCEPTION);
        }

        //订单行中有任意一笔还在退款中的订单行，则不能进行确认收货
        boolean isRefunding = orderItemDOList.stream()
                .anyMatch(orderItemDO -> OrderItemAftersalesStatusEnum.PROCESSING.getCode() == orderItemDO.getAftersalesStatus());
        if (isRefunding) {
            log.info("订单行中有任意一笔还在退款中的订单orderCode={}", orderCode);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_ITEM_REFUNDING);
        }

    }

    /**
     * 校验优惠价计算
     * @param orderCreateDTO
     * @param calculateAmountVO
     */
    private void validCalculateAmount(BrandGoodsOrderCreateDTO orderCreateDTO, CalculateAmountVO calculateAmountVO) {
        for (ShoppingCarItemVO shoppingCarItemVO : calculateAmountVO.getItemList()) {
            if (shoppingCarItemVO.isUnPurchasableFlag()) {
                log.error("商品库存不足，sku=" + shoppingCarItemVO.getProductSkuCode());
                throw ServiceExceptionUtil.exception(ORDER_PRODUCT_STOCK_QUANTITY_ERROR);
            }
        }
        PaymentInfoDTO paymentInfoDTO = orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO();
        if (!compareAmount(calculateAmountVO.getCostAmount(), paymentInfoDTO.getFeeTotalAmount())) {
            log.error("计算价格校验失败, 应付金额不相等");
            throw ServiceExceptionUtil.exception(CHECK_ORDER_AMOUNT_ERROR);
        }
        if (!compareAmount(calculateAmountVO.getDiscountTotalAmount(), paymentInfoDTO.getDiscountTotalAmount())) {
            log.error("计算价格校验失败, 折扣金额不相等");
            throw ServiceExceptionUtil.exception(CHECK_ORDER_AMOUNT_ERROR);
        }
        // 优惠计算有花费积分，但是与前端花费积分不相等
        if (calculateAmountVO.getCostPoints() != null && !calculateAmountVO.getCostPoints().equals(paymentInfoDTO.getPoints())) {
            log.error("计算价格校验失败, 花费积分不相等");
            throw ServiceExceptionUtil.exception(CHECK_ORDER_AMOUNT_ERROR);
        }
        // 优惠计算没有花费积分，但是前端有花费积分
        if (calculateAmountVO.getCostPoints() == null && paymentInfoDTO.getPoints() != null && paymentInfoDTO.getPoints() != 0) {
            log.error("计算价格校验失败, 花费积分不相等");
            throw ServiceExceptionUtil.exception(CHECK_ORDER_AMOUNT_ERROR);
        }
        // 销售总价
        BigDecimal salePriceTotal = calculateAmountVO.getItemList().stream()
                .map(item -> MoneyUtil.getBigDecimal(item.getSalePriceYuanStr()).multiply(MoneyUtil.getBigDecimal(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 应付总价
        BigDecimal costAmountTotal = calculateAmountVO.getItemList().stream()
                .map(item -> MoneyUtil.getBigDecimal(item.getCostAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 优惠总价
        BigDecimal discountAmountTotal = calculateAmountVO.getItemList().stream()
                .map(item -> MoneyUtil.getBigDecimal(item.getDiscountTotalAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 花费总积分
        BigDecimal costPoint = calculateAmountVO.getItemList().stream()
                .map(item -> MoneyUtil.getBigDecimal(item.getCostPoints()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (salePriceTotal.compareTo(MoneyUtil.getBigDecimal(calculateAmountVO.getTotalAmount()))!= 0) {
            log.error("计算价格校验失败, 售价合计不相等");
            throw ServiceExceptionUtil.exception(CHECK_ORDER_AMOUNT_ERROR);
        }
        if (costAmountTotal.compareTo(MoneyUtil.getBigDecimal(calculateAmountVO.getCostAmount())) != 0) {
            log.error("计算价格校验失败, 应付金额合计不相等");
            throw ServiceExceptionUtil.exception(CHECK_ORDER_AMOUNT_ERROR);
        }
        if (discountAmountTotal.compareTo(MoneyUtil.getBigDecimal(calculateAmountVO.getDiscountTotalAmount())) != 0) {
            log.error("计算价格校验失败, 折扣金额合计不相等");
            throw ServiceExceptionUtil.exception(CHECK_ORDER_AMOUNT_ERROR);
        }
        if (costPoint.compareTo(MoneyUtil.getBigDecimal(calculateAmountVO.getCostPoints())) != 0) {
            log.error("计算价格校验失败, 花费积分合计不相等");
            throw ServiceExceptionUtil.exception(CHECK_ORDER_AMOUNT_ERROR);
        }
        if (salePriceTotal.compareTo(costAmountTotal.add(discountAmountTotal)) != 0) {
            log.error("计算价格校验失败, 销售总价 != 应付金额 + 折扣金额");
            throw ServiceExceptionUtil.exception(CHECK_ORDER_AMOUNT_ERROR);
        }
    }

    private CalculateAmountVO getCalculateAmountVO(BrandGoodsOrderCreateDTO orderCreateDTO) {
        CalculateAmountDTO calculateAmountDTO = new CalculateAmountDTO();
        calculateAmountDTO.setCouponCode(orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO().getCouponCode());
        calculateAmountDTO.setCouponType(orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO().getCouponType());
        List<ProductSpuSkuDTO> productItems = new ArrayList<>();
        Set<String> productSkuCodeSet = new HashSet<>();
        for (BrandGoodsOrderShopCarItemDTO orderShopCarItemDTO : orderCreateDTO.getShopCarItemList()) {
            ProductSpuSkuDTO productSpuSkuDTO = new ProductSpuSkuDTO();
            String productSkuCode = orderShopCarItemDTO.getProductSkuCode();
            Integer quantity = orderShopCarItemDTO.getQuantity();
            productSpuSkuDTO.setProductSpuCode(orderShopCarItemDTO.getProductCode());
            productSpuSkuDTO.setProductSkuCode(productSkuCode);
            productSpuSkuDTO.setQuantity(quantity);
            productItems.add(productSpuSkuDTO);
            if (productSkuCodeSet.contains(productSkuCode)) {
                throw ServiceExceptionUtil.exception(CHECK_ORDER_CART_SKU_ERROR);
            }
            productSkuCodeSet.add(productSkuCode);
        }

        calculateAmountDTO.setProductItems(productItems);
        List<CartProductSkuInfo> productItemList = new ArrayList<>();
        if (orderCreateDTO.getChooseProductSkuCodeList() != null) {
            for (String skuCode : orderCreateDTO.getChooseProductSkuCodeList()) {
                CartProductSkuInfo cartProductSkuInfo = new CartProductSkuInfo();
                cartProductSkuInfo.setProductSkuCode(skuCode);
                cartProductSkuInfo.setChooseFlag(true);
                productItemList.add(cartProductSkuInfo);
            }
        }
        // 不计算优惠价
        if (productItemList.isEmpty() && StringUtils.isEmpty(calculateAmountDTO.getCouponCode())) {
            calculateAmountDTO.setIgnoreDiscount(true);
        }
        calculateAmountDTO.setProductItemList(productItemList);
        calculateAmountDTO.setVinList(orderCreateDTO.getVinList());
        log.info("创建订单--计算价格入参, calculateAmountDTO:{}", JSON.toJSONString(calculateAmountDTO));
        CalculateAmountVO calculateAmountVO = calculateService.calculateAmount(calculateAmountDTO, orderCreateDTO.getGlobalInfoDTO().getConsumerCode());
        log.info("创建订单--计算价格结果, calculateAmountVO:{}", JSON.toJSONString(calculateAmountVO));
        return calculateAmountVO;
    }

    private boolean compareAmount(String amount, String targetAmount) {
        BigDecimal amountBigDecimal = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(amount)) {
            amountBigDecimal = new BigDecimal(amount);
        }
        BigDecimal targetAmountBigDecimal = BigDecimal.ZERO;
        if (StringUtils.isNotBlank(targetAmount)) {
            targetAmountBigDecimal = new BigDecimal(targetAmount);
        }
        return amountBigDecimal.compareTo(targetAmountBigDecimal) == 0;
    }

    /**
     * 原有下单接口前置校验
     * <p>
     * - globalBrandCode、channelCode需一一对应      eg.LR对应MLR; 代客下单时，globalBrandCode为LR or JA or JLR，channelCode为CS
     * - 购物车itemList.size、单个item.quantity 都需>0
     * - 通过ConsumerCode、clientId去sub服务查看此时'登陆绑定的icr'和'下单的icr'是否一致   eg.代客下单为常量值CUSTOMER_SERVICE_ORDER跳过校验
     * - 抽取商品、金额相关参数，在product服务进行校验，返回 skuCode和对应的信息包括一个商品对应的子(组合商品）的信息
     * <p>
     * - 检查是否存在未支付或售后处理中的商品订单
     * - 同一辆vin不能重复下单的情况  正在激活中 激活关闭中的服务不可购买
     * <p>
     * - 为代客下单时，调用 代客下单页面商品选购 校验接口
     */
    private OrderCreateRespVO performPreOrderValidations(BrandGoodsOrderCreateDTO orderCreateDTO) {

        // - 在处理订单之前先验证全局信息中的渠道编码
        if (!orderCreateDTO.getGlobalInfoDTO().validate()) {
            throw ServiceExceptionUtil.exception(CHANNEL_CODE_ERROR);
        }

        // - 购物车参数校验
        validate(orderCreateDTO.getShopCarItemList());

        List<BrandGoodsOrderShopCarItemDTO> shopCarItemList = orderCreateDTO.getShopCarItemList();

        //a.从前端传入的参数中提取校验所需的信息
        OrderValidationInfo orderValidationInfo = extractOrderValidationInfo(orderCreateDTO);
        orderValidationInfo.setIgnorePaymentAmount(true);
        log.info("extractOrderValidationInfo,orderValidationInfo:{}", JSON.toJSONString(orderValidationInfo));
        //b.调用远程服务
        CommonResult commonResult = productSkuApi.verifyOrderProducts(orderValidationInfo);
        if (commonResult.getCode() != 0) {
            throw ServiceExceptionUtil.exception(new ErrorCode(commonResult.getCode(), commonResult.getMsg()));
        }
        //c.取出最新版本的商品信息 key为sku
        Map<String, ProductSnapshotDTO> latestSnapshotMap = new HashMap<>();
        Map<String, LinkedHashMap> rawDataMap = (Map<String, LinkedHashMap>) commonResult.getData();
        for (Map.Entry<String, LinkedHashMap> entry : rawDataMap.entrySet()) {
            ProductSnapshotDTO snapshot = objectMapper.convertValue(entry.getValue(), ProductSnapshotDTO.class);
            latestSnapshotMap.put(entry.getKey(), snapshot);
        }
        log.info("最新版本的商品map，latestSnapshotMap:{}", JSON.toJSONString(latestSnapshotMap));

        // 遍历 shopCarItemList 并更新其属性
        buildShopCarItemList(orderCreateDTO, latestSnapshotMap);

        // 业务线校验
        validateBusiness(orderCreateDTO);

        boolean containsBg = shopCarItemList.stream()
                .anyMatch(item -> BusinessIdEnum.BRAND_GOODS.getCode().equals(item.getBusinessCode()));

        if (containsBg) {
            validate(orderCreateDTO.getGiftInfoDTO());
        }

        return null;
    }


    /**
     * 根据购物车项和最新的商品快照，构建购物车商品项列表。
     *
     * @param orderCreateDTO             订单创建DTO，包含购物车商品项列表。
     * @param latestSnapshotMap          最新的商品快照映射表，用于查找商品快照。
     */
    private static boolean buildShopCarItemList(BrandGoodsOrderCreateDTO orderCreateDTO, Map<String, ProductSnapshotDTO> latestSnapshotMap) {
        Map<String, Integer> vinCountMap = new HashMap<>();
        for (BrandGoodsOrderShopCarItemDTO item : orderCreateDTO.getShopCarItemList()) {
            String productSkuCode = item.getProductSkuCode();
            log.info("Processing shop cart item with SKU code: {}", productSkuCode);

            ProductSnapshotDTO snapshot = latestSnapshotMap.get(item.getProductSkuCode());
            if (snapshot != null) {
                log.info("Found latest snapshot for SKU code: {}", productSkuCode);

                // *如果是捆绑商品, 新增组合商品item
                List<BrandGoodsOrderShopCarItemDTO> childList = addChildShopCarItem(item, snapshot);
                log.info("Added child shop cart items: {}", JSON.toJSONString(childList));

                // 更新 OrderShopCarItemDTO 的属性
                BeanUtil.copyProperties(snapshot, item, "productAttribute");
                item.setSalePrice(MoneyUtil.convertFromCents(new BigDecimal(snapshot.getProductSalePrice())));
                if (snapshot.getProductMarketPrice() != null) {
                    item.setMarketPrice(MoneyUtil.convertFromCents(new BigDecimal(snapshot.getProductMarketPrice())));
                }
                item.setChildList(childList);
            }
        }
        return true;
    }

    /**
     * 新增组合商品下的item，并计算每个子项的payPrice。
     */
    private static List<BrandGoodsOrderShopCarItemDTO> addChildShopCarItem(BrandGoodsOrderShopCarItemDTO parentItem, ProductSnapshotDTO snapshot) {
        log.info("新增组合商品下的parentItem:{}", JSON.toJSONString(parentItem));

        // 组合商品下子商品list
        List<ProductSnapshotDTO> childProductSnapshotList = snapshot.getChildProductSnapshotList();
        if (CollUtil.isEmpty(childProductSnapshotList)) {
            log.info("组合商品下没有子商品，parentItem:{}", JSON.toJSONString(parentItem));
            return Collections.emptyList();
        }

        // 计算组合商品下 SUM(item.salePrice * item.productQuantity)
        BigDecimal totalSalePrice = childProductSnapshotList.stream()
                .filter(Objects::nonNull)
                .map(dto -> new BigDecimal(dto.getProductSalePrice()).multiply(BigDecimal.valueOf(dto.getProductQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("组合商品下 SUM(item.salePrice * item.productQuantity) = {}", totalSalePrice);

        // 前端传过来的payPrice转换成分 eg.前端传入700.00 转换成 70000
        BigDecimal parentPayPrice = MoneyUtil.convertToCents(parentItem.getPayPrice());
        log.info("前端传过来的payPrice转换成分 = {}", parentPayPrice);

        BigDecimal remainingPayPrice = parentPayPrice;
        List<BrandGoodsOrderShopCarItemDTO> calculatedChildItems = new ArrayList<>();

        // 如果是捆绑商品, 新增组合商品item
        for (int i = 0; i < childProductSnapshotList.size(); i++) {
            ProductSnapshotDTO dto = childProductSnapshotList.get(i);
            if (dto == null) {
                continue;
            }
            BrandGoodsOrderShopCarItemDTO childItem = new BrandGoodsOrderShopCarItemDTO();
            BeanUtil.copyProperties(parentItem, childItem, "cartItemType", "marketPrice");
            childItem.setQuantity(parentItem.getQuantity() * dto.getProductQuantity());
            BeanUtil.copyProperties(dto, childItem);
            childItem.setCartItemType(dto.getFulfilmentType());

            // 子商品的单价*子商品的数量
            BigDecimal salePrice = new BigDecimal(dto.getProductSalePrice()).multiply(BigDecimal.valueOf(dto.getProductQuantity()));
            log.info("子商品的单价*子商品的数量 = {}", salePrice);

            if (i < childProductSnapshotList.size() - 1) {
                // 按照销售价格的比例分配实付价格
                BigDecimal ratio = salePrice.divide(totalSalePrice, 8, RoundingMode.HALF_UP);
                log.info("按照销售价格的比例 = {}", ratio);
                BigDecimal payPrice = parentPayPrice.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                log.info("按照销售价格的比例分配实付价格 = {}", payPrice);
                childItem.setPayPrice(MoneyUtil.convertFromCents(payPrice)); // 将实付价格转换为分 字符串 700.00
                remainingPayPrice = remainingPayPrice.subtract(payPrice);
                log.info("执行第{} 次,剩余的实付价格 = {}", i + 1, remainingPayPrice);
            } else {
                // 最后一个子项，使用剩余的实付价格
                childItem.setPayPrice(MoneyUtil.convertFromCents(remainingPayPrice));
                log.info("最后一个子项，使用剩余的实付价格 = {}", remainingPayPrice);
            }

            childItem.setSalePrice(MoneyUtil.convertFromCents(new BigDecimal(dto.getProductSalePrice())));
            if (dto.getProductMarketPrice() != null) {
                childItem.setMarketPrice(MoneyUtil.convertFromCents(new BigDecimal(dto.getProductMarketPrice())));
            }
            calculatedChildItems.add(childItem);
        }
        log.info("新增组合商品下的childItem:{}", JSON.toJSONString(calculatedChildItems));
        return calculatedChildItems;
    }

    /**
     * 发送订单超时，取消订单的消息
     *
     * @param orderInfo 订单info
     */
    private void sendTimeoutCancelOrderMsg(OrderInfoDO orderInfo) {
        if (StringUtils.isBlank(orderInfo.getOrderCode())) {
            log.error("订单超时，发送延迟消息，订单号为空");
            return;
        }
        CancelOrderMessage cancelOrderMessage = new CancelOrderMessage();
        cancelOrderMessage.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        cancelOrderMessage.setParentOrderCode(orderInfo.getParentOrderCode());
        cancelOrderMessage.setOrderCode(orderInfo.getOrderCode());
        cancelOrderMessage.setTenantId(TenantContextHolder.getTenantId());
        cancelOrderMessage.setSendTime(orderInfo.getOrderTime());
        producerTool.sendMsg(KafkaConstants.ORDER_TIMEOUT_CANCEL_TOPIC, "", JSON.toJSONString(cancelOrderMessage));
        log.info("订单超时取消，发送消息, cancelOrderMessage：{}", cancelOrderMessage);
    }


    /**
     * 异步清除订单提交内的购物车商品项。
     * 此方法将从订单中提取购物车商品项的编码，并异步调用购物车服务以清除这些项。
     *
     * @param orderCreateDTO 订单创建的传输对象
     */
    private void asyncClearShoppingCartItems(BrandGoodsOrderCreateDTO orderCreateDTO) {
        //当前租户id
        Long tenantId = TenantContextHolder.getTenantId();
        if (orderCreateDTO != null && !orderCreateDTO.getShopCarItemList().isEmpty()) {
            List<String> cartItemCodes = orderCreateDTO.getShopCarItemList().stream()
                    .map(BrandGoodsOrderShopCarItemDTO::getCartItemCode)
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(cartItemCodes)) {
                // 异步执行删除操作
                CompletableFuture.runAsync(() -> {
                            // 设置当前线程的租户上下文为提取到的租户ID
                            TenantContextHolder.setTenantId(tenantId);
                            shoppingCarItemService.delete(cartItemCodes);
                        })
                        .exceptionally(ex -> {
                            log.error("Failed to clear shopping cart items asynchronously", ex);
                            return null;
                        });
            }
        }
    }

    /**
     * 处理拆单逻辑
     */
    private OrderCreateRespVO processSplit(BrandGoodsOrderCreateDTO orderCreateDTO, String oneID,
                                              LocalDateTime now, CalculateAmountVO calculateAmountVO) {
        String channelCode = orderCreateDTO.getGlobalInfoDTO().getChannelCode();
        String consumerCode = orderCreateDTO.getGlobalInfoDTO().getConsumerCode();

        // 按业务线商品类型分组,key是车辆业务线::商品类型，value是具有相同业务线和商品类型的OrderShopCarItemDTO对象列表
        Map<String, List<ShoppingCarItemVO>> splitMap = calculateAmountVO.getItemList().stream()
                .collect(Collectors.groupingBy(item -> item.getBusinessCode() + CONCAT_SYMBOL + item.getCartItemType()));

        String parentOrderCode = null;
        OrderInfoDO parentOrder = null;
        if (splitMap.size() > 1) {
            // 生成父订单
            parentOrder = createParentOrder(orderCreateDTO, calculateAmountVO, oneID, now);
            parentOrder.setFreightAmount(MoneyUtil.convertToCents(orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO().getFreightAmount()).intValue());
            parentOrderCode = parentOrder.getOrderCode();
        }

        // 初始化序列号 for订单号
        int sequenceNumber = 1;
        //创建入库对象容器
        List<OrderInfoDO> allSubOrders = new ArrayList<>();
        List<OrderStatusLogDO> allOrderStatusLogList = new ArrayList<>();

        List<OrderItemDO> allOrderItemList = new ArrayList<>();
        List<OrderTermsDO> allOrderTermsList = new ArrayList<>();
        List<OrderItemLogisticsDO> orderBgLogisticsDOList = new ArrayList<>();
        List<OrderDiscountDetailDO> orderDiscountDetailDOList = new ArrayList<>();

        Map<String, BrandGoodsOrderShopCarItemDTO> bgItemMap = orderCreateDTO.getShopCarItemList().stream()
                .collect(Collectors.toMap(
                        BrandGoodsOrderShopCarItemDTO::getProductSkuCode,
                        item -> item
                ));
        for (Map.Entry<String, List<ShoppingCarItemVO>> entry : splitMap.entrySet()) {

            // 为取履约类型，因为会按商品类型分组，所以这里取第一个元素
            String skuCode = entry.getValue().get(0).getProductSkuCode();
            BrandGoodsOrderShopCarItemDTO bgItem = bgItemMap.get(skuCode);
            Integer cartItemType = bgItem.getCartItemType();

            // 为取业务线，因为会按业务线分组，所以这里取第一个元素
            String businessCode = entry.getValue().get(0).getBusinessCode();

            String brandCode = getBrandCode(businessCode);

            // 生成子单号并组装t_order_info
            String childOrderCode = OrderCodeGenerator.generateChildOrderCode(
                    oneID,
                    brandCode,
                    channelCode,
                    String.valueOf(cartItemType),
                    SequenceNumberGenerator.generate(sequenceNumber++)
            );
            // 组装子订单
            OrderInfoDO childOrderInfo = buildOrderInfo(orderCreateDTO, childOrderCode, parentOrderCode, now);
            // 设置运费,税收编码,运费税率,税率更新时间
            if (Objects.equals(cartItemType, CartItemTypeEnum.BRAND_GOODS.getCode())) {
                childOrderInfo.setFreightCode(bgItem.getTaxCode());
                childOrderInfo.setFreightTax(bgItem.getTaxRate());
                childOrderInfo.setFreightTaxUpdateTime(now);
                childOrderInfo.setFreightAmount(MoneyUtil.convertToCents(orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO().getFreightAmount()).intValue());
            } else {
                childOrderInfo.setFreightAmount(0);
            }
            //重新设置子订单的金额
            List<ShoppingCarItemVO> carItemDTOList = entry.getValue();
            BigDecimal salePriceTotal = carItemDTOList.stream()
                    .map(item -> MoneyUtil.convertToCents(item.getSalePriceYuanStr()).multiply(BigDecimal.valueOf(item.getQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal payPriceTotal = carItemDTOList.stream()
                    .map(item -> MoneyUtil.convertToCents(item.getCostAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal discountTotalAmount = carItemDTOList.stream()
                    .map(item -> MoneyUtil.convertToCents(item.getDiscountTotalAmount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal costPoint = carItemDTOList.stream()
                    .map(item -> MoneyUtil.getBigDecimal(item.getCostPoints()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal productQuantity = carItemDTOList.stream()
                    .map(item -> MoneyUtil.getBigDecimal(item.getQuantity()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            childOrderInfo.setOriginalFeeTotalAmount(salePriceTotal.intValue()); // 原始订单金额
            childOrderInfo.setFeeTotalAmount(payPriceTotal.intValue()); // 应付金额
            childOrderInfo.setCostAmount(payPriceTotal.intValue()); // 实付金额
            childOrderInfo.setDiscountTotalAmount(discountTotalAmount.intValue()); // 折扣金额
            childOrderInfo.setPointAmount(costPoint.intValue());
            childOrderInfo.setProductQuantity(productQuantity.intValue());

            // 初始状态为"已下单"
            childOrderInfo.setOrderStatus(OrderStatusEnum.ORDERED.getCode());
            if (CartItemTypeEnum.BRAND_GOODS.getCode().equals(cartItemType)) {
                childOrderInfo.setLogisticsStatus(OrderLogisticsStatusEnum.PENDING_PAYMENT.getCode());
                childOrderInfo.setCustomerRemark(orderCreateDTO.getOrderInfoDTO().getContactInfoDTO().getOperatorBgRemark());
            } else if (CartItemTypeEnum.E_COUPON.getCode().equals(cartItemType)) {
                childOrderInfo.setCouponStatus(OrderCouponStatusEnum.PENDING_PAYMENT.getCode());
            }
            childOrderInfo.setOrderType(cartItemType);
            childOrderInfo.setBusinessCode(businessCode);

            // 组装t_order_status_log
            OrderStatusLogDO orderStatusLogDO = buildOrderStatusLog(childOrderCode, now);

            int excludeTaxAmount = 0;
            int originalExcludeTaxAmount = 0;
            // 组装t_order_item，存储当前子订单的所有 OrderItemDO 对象
            for (ShoppingCarItemVO shoppingCarItemVO : entry.getValue()) {
                BrandGoodsOrderShopCarItemDTO itemDTO = bgItemMap.get(shoppingCarItemVO.getProductSkuCode());
                OrderItemDO orderItemDO = buildOrderItem(shoppingCarItemVO, itemDTO, childOrderCode);
                String orderItemCode = orderItemDO.getOrderItemCode();

                //组装t_order_terms
                List<OrderTermsDO> orderTermsDOS = buildOrderTerms(consumerCode, itemDTO, childOrderCode, now, orderItemCode);

                // 添加到对象容器
                allOrderItemList.add(orderItemDO);
                allOrderTermsList.addAll(orderTermsDOS);

                // 组装t_order_bg_logistics
                OrderItemLogisticsDO orderBgLogisticsDO = buildOrderBgLogistics(orderItemDO, orderCreateDTO.getGiftInfoDTO());
                if (orderBgLogisticsDO != null) {
                    orderBgLogisticsDOList.add(orderBgLogisticsDO);
                }

                // 优惠分摊明细
                setOrderDiscountDetail(calculateAmountVO, shoppingCarItemVO, orderItemDO, orderDiscountDetailDOList, childOrderInfo);

                excludeTaxAmount += MoneyUtil.calculateExcludeTaxAmount(itemDTO.getTaxRate(), MoneyUtil.convertToCents(shoppingCarItemVO.getCostAmount()).intValue());
                originalExcludeTaxAmount += MoneyUtil.calculateExcludeTaxAmount(itemDTO.getTaxRate(), MoneyUtil.convertToCents(shoppingCarItemVO.getSalePriceYuanStr()).multiply(BigDecimal.valueOf(shoppingCarItemVO.getQuantity())).intValue());
            }
            //设置税额相关两个字段
            Integer taxAmount = childOrderInfo.getCostAmount() - excludeTaxAmount;
            childOrderInfo.setExcludeTaxAmount(excludeTaxAmount);
            childOrderInfo.setTaxAmount(taxAmount);
            childOrderInfo.setOriginalExcludeTaxAmount(originalExcludeTaxAmount);

            allSubOrders.add(childOrderInfo);
            allOrderStatusLogList.add(orderStatusLogDO);
        }
        // 扣减库存数量
        orderDeduct(allSubOrders, allOrderItemList);

        insertParentOrder(parentOrder, allSubOrders);

        // 批量插入五个主表
        orderInfoDOMapper.insertBatch(allSubOrders);
        orderStatusLogDOMapper.insertBatch(allOrderStatusLogList);
        orderItemDOService.saveBatch(allOrderItemList);
        orderTermsDOService.saveBatch(allOrderTermsList);
        // 插入优惠明细表
        if (CollUtil.isNotEmpty(orderDiscountDetailDOList)) {
            orderDiscountDetailDOMapper.insertBatch(orderDiscountDetailDOList);
        }
        // 插入订单物流表
        if (CollUtil.isNotEmpty(orderBgLogisticsDOList)) {
            orderItemLogisticsDOMapper.insertBatch(orderBgLogisticsDOList);
        }

        // 发送超时取消订单消息
        sendTimeoutCancelOrderMsg(parentOrder, allSubOrders);

        // 构造响应对象
        OrderCreateRespVO response = new OrderCreateRespVO();
        response.setOrderCodeList(allSubOrders.stream().map(OrderInfoDO::getOrderCode).collect(Collectors.toList()));
        response.setParentOrderCode(parentOrderCode);
        return response;
    }

    private void sendTimeoutCancelOrderMsg(OrderInfoDO parentOrder, List<OrderInfoDO> allSubOrders) {
        if (parentOrder != null) {
            sendTimeoutCancelOrderMsg(parentOrder);
        } else {
            for (OrderInfoDO orderInfoDO : allSubOrders) {
                sendTimeoutCancelOrderMsg(orderInfoDO);
            }
        }
    }

    private void orderDeduct(List<OrderInfoDO> allSubOrders, List<OrderItemDO> allOrderItemList) {
        List<OrderInfoDO> bgOrderList = allSubOrders.stream()
                .filter(orderInfoDO -> Objects.equals(orderInfoDO.getOrderType(), CartItemTypeEnum.BRAND_GOODS.getCode()))
                .collect(Collectors.toList());
        for (OrderInfoDO orderInfoDO : bgOrderList) {
            // 目前来说只有一个bg订单，只会发起一次扣减库存请求，如果以后有多个bg订单，需要考虑事务一致性
            InventoryOrderSkuDTO inventoryOrderSkuDTO = new InventoryOrderSkuDTO();
            inventoryOrderSkuDTO.setOrderCode(orderInfoDO.getOrderCode());
            Map<String, InventoryOrderSkuDTO.ProductItem> productItemMap = new HashMap<>();
            List<OrderItemDO> orderItemList = allOrderItemList.stream()
                    .filter(item -> Objects.equals(item.getOrderCode(), orderInfoDO.getOrderCode()))
                    .collect(Collectors.toList());
            for (OrderItemDO item : orderItemList) {
                InventoryOrderSkuDTO.ProductItem productItem = productItemMap.get(item.getKingdeeSkuCode());
                if (productItem == null) {
                    productItem = new InventoryOrderSkuDTO.ProductItem();
                    productItem.setProductSkuCode(item.getKingdeeSkuCode());
                    productItem.setChangeNum(0);
                    productItemMap.put(item.getKingdeeSkuCode(), productItem);
                }
                productItem.setChangeNum(productItem.getChangeNum()+item.getProductQuantity());
            }
            // 合并相同的KingdeeSkuCode
            inventoryOrderSkuDTO.setProductItems(new ArrayList<>(productItemMap.values()));
            log.info("发送库存扣减请求：{}", JSON.toJSONString(inventoryOrderSkuDTO));
            CommonResult<String> result = inventoryOrderApi.orderDeduct(inventoryOrderSkuDTO);
            if (result.isError()) {
                log.error("库存扣减失败：{}", result.getMsg());
                throw ServiceExceptionUtil.exception(ORDER_DEDUCT_ERROR);
            }
        }
    }

    private void insertParentOrder(OrderInfoDO parentOrder, List<OrderInfoDO> allSubOrders) {
        if (parentOrder != null) {
            int originalFeeTotalAmount = allSubOrders.stream()
                    .mapToInt(OrderInfoDO::getOriginalFeeTotalAmount)
                    .sum();
            int feeTotalAmount = allSubOrders.stream()
                    .mapToInt(OrderInfoDO::getFeeTotalAmount)
                    .sum();
            int totalExcludeTaxAmount = allSubOrders.stream()
                    .mapToInt(OrderInfoDO::getExcludeTaxAmount)
                    .sum();
            int totalTaxAmount = allSubOrders.stream()
                    .mapToInt(OrderInfoDO::getTaxAmount)
                    .sum();
            int originalExcludeTaxAmount = allSubOrders.stream()
                    .mapToInt(OrderInfoDO::getOriginalExcludeTaxAmount)
                    .sum();
            int productQuantity = allSubOrders.stream()
                    .mapToInt(OrderInfoDO::getProductQuantity)
                    .sum();
            int discountTotalAmount = allSubOrders.stream()
                    .mapToInt(OrderInfoDO::getDiscountTotalAmount)
                    .sum();
            int costAmount = allSubOrders.stream()
                    .mapToInt(OrderInfoDO::getCostAmount)
                    .sum();
            int pointAmount = allSubOrders.stream()
                    .mapToInt(OrderInfoDO::getPointAmount)
                    .sum();
            parentOrder.setOriginalFeeTotalAmount(originalFeeTotalAmount);
            parentOrder.setFeeTotalAmount(feeTotalAmount);
            parentOrder.setExcludeTaxAmount(totalExcludeTaxAmount);
            parentOrder.setTaxAmount(totalTaxAmount);
            parentOrder.setOriginalExcludeTaxAmount(originalExcludeTaxAmount);
            parentOrder.setProductQuantity(productQuantity);
            parentOrder.setDiscountTotalAmount(discountTotalAmount);
            parentOrder.setCostAmount(costAmount);
            parentOrder.setPointAmount(pointAmount);
            orderInfoDOMapper.insert(parentOrder);
        }
    }

    private static void setOrderDiscountDetail(CalculateAmountVO calculateAmountVO, ShoppingCarItemVO shoppingCarItemVO, OrderItemDO orderItemDO, List<OrderDiscountDetailDO> orderDiscountDetailDOList, OrderInfoDO childOrderInfo) {
        if (Objects.equals(CarPaymentTypeEnum.POINT.getCode(), shoppingCarItemVO.getPaymentType())) {
            OrderDiscountDetailDO discountDetailDO = new OrderDiscountDetailDO();
            discountDetailDO.setOrderCode(orderItemDO.getOrderCode());
            discountDetailDO.setOrderItemCode(orderItemDO.getOrderItemCode());
            discountDetailDO.setDiscountType(OrderDiscountTypeEnum.POINT.getCode());
            discountDetailDO.setCostPoints(shoppingCarItemVO.getCostPoints());
            discountDetailDO.setTenantId(orderItemDO.getTenantId());
            discountDetailDO.setDiscountAmount(MoneyUtil.convertToCents(shoppingCarItemVO.getDiscountTotalAmount()).intValue());
            orderDiscountDetailDOList.add(discountDetailDO);
        } else if (Objects.equals(CarPaymentTypeEnum.COUPON.getCode(), shoppingCarItemVO.getPaymentType())) {
            PromotionDto chooseCoupon = calculateAmountVO.getChooseCoupon();
            OrderDiscountDetailDO discountDetailDO = new OrderDiscountDetailDO();
            discountDetailDO.setOrderCode(orderItemDO.getOrderCode());
            discountDetailDO.setOrderItemCode(orderItemDO.getOrderItemCode());
            discountDetailDO.setDiscountType(OrderDiscountTypeEnum.COUPON.getCode());
            discountDetailDO.setCouponCode(chooseCoupon.getCouponCode());
            discountDetailDO.setCouponModelCode(chooseCoupon.getCouponModelCode());
            discountDetailDO.setCouponModelName(chooseCoupon.getCouponModelName());
            discountDetailDO.setCouponModelClassify(chooseCoupon.getCouponModelClassify());
            discountDetailDO.setTenantId(childOrderInfo.getTenantId());
            discountDetailDO.setDiscountAmount(MoneyUtil.convertToCents(shoppingCarItemVO.getDiscountTotalAmount()).intValue());
            orderDiscountDetailDOList.add(discountDetailDO);
        }
    }

    private String getBrandCode(String businessCode) {
        String brandCode = null;
        if (BusinessIdEnum.BRAND_GOODS.getCode().equals(businessCode)) {
            brandCode = "BG";
        } else if (BusinessIdEnum.LRE.getCode().equals(businessCode)) {
            brandCode = "LE";
        }
        return brandCode;
    }

    /**
     * @param orderCreateDTO
     * @param oneID
     * @param now
     * @return
     */
    private OrderInfoDO createParentOrder(BrandGoodsOrderCreateDTO orderCreateDTO, CalculateAmountVO calculateAmountVO, String oneID, LocalDateTime now) {
        // 获取品牌编码和渠道编码
        String brandCode = "BL";
        String channelCode = orderCreateDTO.getGlobalInfoDTO().getChannelCode();
        BrandGoodsOrderShopCarItemDTO carItemDTO = orderCreateDTO.getShopCarItemList().get(0);

        // 父订单号的生成逻辑
        String parentOrderCode = OrderCodeGenerator.generateParentOrderCode(
                oneID,
                brandCode,
                channelCode
        );

        OrderInfoDO parentOrderInfo = buildOrderInfo(orderCreateDTO, parentOrderCode, parentOrderCode, now);
        parentOrderInfo.setIndependentStatus(OrderIndependentStatusEnum.NO_NEED.getStatus());
        parentOrderInfo.setOriginalFeeTotalAmount(MoneyUtil.convertToCents(calculateAmountVO.getTotalAmount()).intValue());
        parentOrderInfo.setFeeTotalAmount(MoneyUtil.convertToCents(calculateAmountVO.getCostAmount()).intValue());
        parentOrderInfo.setCostAmount(MoneyUtil.convertToCents(calculateAmountVO.getCostAmount()).intValue());
        parentOrderInfo.setDiscountTotalAmount(MoneyUtil.convertToCents(calculateAmountVO.getDiscountTotalAmount()).intValue());
        parentOrderInfo.setPointAmount(orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO().getPoints());
        // 初始状态为"已下单"
        parentOrderInfo.setOrderStatus(OrderStatusEnum.ORDERED.getCode());
        parentOrderInfo.setLogisticsStatus(null);
        parentOrderInfo.setCouponStatus(null);
        // 当cartItemType为0：虚拟组合商品时，orderType要设置为4
        parentOrderInfo.setOrderType(OrderFulfillmentTypeEnum.AGGREGATE_PARENT_ORDER.getFulfilmentType());
        // 父单不赋值business_code
        parentOrderInfo.setBusinessCode(null);

        return parentOrderInfo;
    }


    private OrderItemLogisticsDO buildOrderBgLogistics(OrderItemDO orderItemDO, OrderGiftAddressDTO giftInfoDTO) {
        if (ProductFulfilmentTypeEnum.PHYSICAL_DELIVERY.getCode().equals(orderItemDO.getItemFulfillmentType())) {
            OrderItemLogisticsDO orderItemLogisticsDO = new OrderItemLogisticsDO();
            orderItemLogisticsDO.setOrderCode(orderItemDO.getOrderCode());
            orderItemLogisticsDO.setOrderItemCode(orderItemDO.getOrderItemCode());
            orderItemLogisticsDO.setRecipient(piplDataUtil.getEncryptText(giftInfoDTO.getRecipient()));
            if (StringUtils.isNotBlank(giftInfoDTO.getRecipient())) {
                int begin = 1;
                if (giftInfoDTO.getRecipient().length() == 1) {
                    begin = 0;
                }
                orderItemLogisticsDO.setRecipientMix(StrUtil.replace(giftInfoDTO.getRecipient(), begin, giftInfoDTO.getRecipient().length(), '*'));
            }
            orderItemLogisticsDO.setLogisticsStatus(OrderItemLogisticsStatusEnum.PENDING_SHIPMENT.getCode());
            orderItemLogisticsDO.setRecipientPhone(piplDataUtil.getEncryptText(giftInfoDTO.getRecipientPhone()));
            orderItemLogisticsDO.setRecipientPhoneMix((String) PhoneUtil.hideBetween(giftInfoDTO.getRecipientPhone()));
            orderItemLogisticsDO.setRecipientPhoneMd5(SecureUtil.md5(giftInfoDTO.getRecipientPhone()));
            orderItemLogisticsDO.setProvinceCode(giftInfoDTO.getProvinceCode());
            orderItemLogisticsDO.setCityCode(giftInfoDTO.getCityCode());
            orderItemLogisticsDO.setAreaCode(giftInfoDTO.getAreaCode());
            orderItemLogisticsDO.setDetailAddress(piplDataUtil.getEncryptText(giftInfoDTO.getDetailAddress()));
            orderItemLogisticsDO.setPostCode(null);
            try {
                CommonResult<AddressNameDTO> commonResult = addressConfigApi.getNameByCode(giftInfoDTO.getProvinceCode(), giftInfoDTO.getCityCode(), giftInfoDTO.getAreaCode());
                if (commonResult != null && commonResult.getData() != null) {
                    AddressNameDTO addressNameDTO = commonResult.getData();
                    String fullDetailAddress = addressNameDTO.getProvince()+addressNameDTO.getCity()+addressNameDTO.getArea()+giftInfoDTO.getDetailAddress();
                    orderItemLogisticsDO.setFullDetailAddress(piplDataUtil.getEncryptText(fullDetailAddress));
                    log.info("调用system api查询省市区的名称成功");
                }
            } catch (Exception e) {
                log.warn("调用system api查询省市区的名称异常", e);
            }
            log.info("调用system api查询查询省市区名称失败或为空");
            return orderItemLogisticsDO;
        }
        return null;
    }

    /**
     * 条款与子订单中的商品进行挂钩
     * @return
     */
    private List<OrderTermsDO> buildOrderTerms(String consumerCode, BrandGoodsOrderShopCarItemDTO itemDTO, String orderCode, LocalDateTime now, String orderItemCode) {
        // 初始化OrderTermsDO列表
        List<OrderTermsDO> orderTermsList = new ArrayList<>();

        for (String policyCode : itemDTO.getPolicyCodeList()) {
            OrderTermsDO orderTerms = new OrderTermsDO();

            orderTerms.setOrderCode(orderCode);
            orderTerms.setOrderItemCode(orderItemCode);
            orderTerms.setTermsCode(policyCode);
            orderTerms.setSignTime(now);
            orderTerms.setConsumerCode(consumerCode);

            // 添加到对象列表
            orderTermsList.add(orderTerms);
        }

        return orderTermsList;
    }

    private OrderStatusLogDO buildOrderStatusLog(String orderCode, LocalDateTime now) {
        OrderStatusLogDO orderStatusLogDO = new OrderStatusLogDO();

        orderStatusLogDO.setOrderCode(orderCode);
        // 第一次下单 初始 before、after状态都是ORDERED
        orderStatusLogDO.setBeforeStatus(OrderStatusEnum.ORDERED.getCode());
        orderStatusLogDO.setAfterStatus(OrderStatusEnum.ORDERED.getCode());
        orderStatusLogDO.setChangeTime(now);

        return orderStatusLogDO;
    }

    /**
     * 检查订单中的购物车项是否全部属于同一业务线。
     *
     * @param orderCreateDTO 订单创建传输对象
     * @return 如果所有购物车项都有相同的业务线，则返回 true；否则返回 false。
     */
    private boolean isSingleBusiness(OrderCreateDTO orderCreateDTO) {
        // 使用 Stream API 从订单的购物车项中提取业务线。
        // 这里使用 map 方法将每个购物车项映射到其业务线。

        long distinctBusinessCount = orderCreateDTO.getShopCarItemList().stream()
                .map(OrderShopCarItemDTO::getBusinessCode)
                // 使用 distinct 方法移除重复的业务线。
                .distinct()
                // 使用 count 方法计算不同业务线的数量。
                .count();

        // 如果不同业务线的数量大于 1，则表示有多种业务线，需要拆分。
        return distinctBusinessCount <= 1;
    }

    /**
     * 从OrderShopCarItemDTO构建OrderItemDO
     */
    private OrderItemDO buildOrderItem(ShoppingCarItemVO shoppingCarItemVO, BrandGoodsOrderShopCarItemDTO itemDTO, String orderCode) {
        // 插库对象
        OrderItemDO orderItem = new OrderItemDO();

        orderItem.setOrderItemCode(String.valueOf(ecpIdUtil.nextId()));
        orderItem.setOrderCode(orderCode);

        orderItem.setProductVersionCode(itemDTO.getProductVersionCode());
        orderItem.setProductCode(itemDTO.getProductCode());
        orderItem.setProductSkuCode(itemDTO.getProductSkuCode());

        orderItem.setProductName(itemDTO.getProductName());
        orderItem.setProductImageUrl(itemDTO.getProductImageUrl());

        orderItem.setProductAttribute(itemDTO.getProductAttribute());
        if (StringUtils.isNotBlank(shoppingCarItemVO.getMarketPriceYuanStr())) {
            orderItem.setProductMarketPrice(MoneyUtil.convertToCents(shoppingCarItemVO.getMarketPriceYuanStr()).intValue());
        }
        orderItem.setProductSalePrice(MoneyUtil.convertToCents(shoppingCarItemVO.getSalePriceYuanStr()).intValue());
        orderItem.setProductQuantity(shoppingCarItemVO.getQuantity());
        orderItem.setPointAmount(shoppingCarItemVO.getCostPoints());

        //总金额
        orderItem.setTotalAmount(MoneyUtil.convertToCents(shoppingCarItemVO.getSalePriceYuanStr()).multiply(BigDecimal.valueOf(shoppingCarItemVO.getQuantity())).intValue());
        //实付金额
        orderItem.setCostAmount(MoneyUtil.convertToCents(shoppingCarItemVO.getCostAmount()).intValue());
        //折扣金额
        orderItem.setDiscountFeeAmount(MoneyUtil.convertToCents(shoppingCarItemVO.getDiscountTotalAmount()).intValue());
        // 设置不含税总金额和税费总金额
        Integer excludeTaxAmount = MoneyUtil.calculateExcludeTaxAmount(itemDTO.getTaxRate(), orderItem.getCostAmount());
        Integer taxAmount = orderItem.getCostAmount() - excludeTaxAmount;
        orderItem.setExcludeTaxTotalAmount(excludeTaxAmount);
        orderItem.setTaxAmount(taxAmount);
        orderItem.setTaxCode(itemDTO.getTaxCode());
        orderItem.setTaxRate(itemDTO.getTaxRate());
        orderItem.setMerchantAccountNo(itemDTO.getMerchantAccountNo());
        orderItem.setOrderItemSpuType(OrderItemSpuTypeEnum.getSpuType(itemDTO.getCartItemType()));
        orderItem.setItemFulfillmentType(itemDTO.getCartItemType());
        if (Objects.equals(itemDTO.getCartItemType(), OrderTypeEnum.BRAND_GOOD.getCode())) {
            orderItem.setKingdeeSkuCode(itemDTO.getModelCode());
            orderItem.setItemStatus(OrderItemLogisticsStatusEnum.PENDING_SHIPMENT.getCode());
        } else if (Objects.equals(itemDTO.getCartItemType(), OrderTypeEnum.ELECTRONIC_COUPON.getCode())) {
            orderItem.setCouponModelCode(itemDTO.getModelCode());
            orderItem.setItemStatus(OrderItemCouponStatusEnum.PENDING_ISSUANCE.getCode());
        }
        orderItem.setAftersalesStatus(OrderItemAftersalesStatusEnum.NO_AFTERSALES.getCode());
        return orderItem;
    }


    /**
     * 从DTO中提取信息并组装成OrderInfoDO对象
     *
     * @param orderCreateDTO  订单创建的DTO
     * @param orderCode       订单号
     * @param parentOrderCode 父订单号
     * @return OrderInfoDO 包含订单信息的DO对象
     */
    private OrderInfoDO buildOrderInfo(BrandGoodsOrderCreateDTO orderCreateDTO,
                                       String orderCode, String parentOrderCode, LocalDateTime now) {
        OrderInfoDO orderInfo = new OrderInfoDO();

        orderInfo.setConsumerCode(orderCreateDTO.getGlobalInfoDTO().getConsumerCode());
        orderInfo.setOrderCode(orderCode);

        orderInfo.setParentOrderCode(parentOrderCode != null ? parentOrderCode : orderCode);

        // 初始支付状态为"未支付"
        orderInfo.setPaymentStatus(PaymentStatusEnum.TO_BE_PAID.getCode());
        orderInfo.setOrderTime(now);
        orderInfo.setOrderChannel(OrderChannelCodeEnum.getOrderChannelCodeByChannelCode(orderCreateDTO.getGlobalInfoDTO().getChannelCode()));


        orderInfo.setWxNickName(orderCreateDTO.getOrderInfoDTO().getContactInfoDTO().getWxNickName());
        String wxPhone = orderCreateDTO.getOrderInfoDTO().getContactInfoDTO().getWxPhone();
        String contactPhone = orderCreateDTO.getOrderInfoDTO().getContactInfoDTO().getContactPhone();
        if (orderCreateDTO.getGiftInfoDTO() != null && StringUtils.isNotBlank(orderCreateDTO.getGiftInfoDTO().getRecipientPhone())) {
            contactPhone = orderCreateDTO.getGiftInfoDTO().getRecipientPhone();
        }
        // 为代客下单时 t_order_info表不保存wx_phone、contact_phone
        String consumerCode = orderCreateDTO.getGlobalInfoDTO().getConsumerCode();
        boolean isCustomerServiceOrder = CUSTOMER_SERVICE_ORDER.equals(consumerCode);
        if(isCustomerServiceOrder) {
            wxPhone = null;
            contactPhone = null;
        }

        // 设置 phone
        orderInfo.setWxPhoneMix((String) PhoneUtil.hideBetween(wxPhone));
        orderInfo.setWxPhoneMd5(SecureUtil.md5(wxPhone));
        orderInfo.setContactPhoneMix((String) PhoneUtil.hideBetween(contactPhone));
        orderInfo.setContactPhoneMd5(SecureUtil.md5(contactPhone));
        // 加密手机号
        String encryptWxPhone = null;
        String encryptPhone = null;
        CommonResult<String> commonResult1 = null;
        CommonResult<String> commonResult2 = null;

        try {
            commonResult1 = permissionApi.getEncryptText(wxPhone);
            commonResult2 = permissionApi.getEncryptText(contactPhone);
        } catch (Exception e) {
            log.error("permissionApi解密失败", e);
        }

        if (commonResult1 != null && commonResult1.getData() != null) {
            encryptWxPhone = commonResult1.getData();
            orderInfo.setWxPhone(encryptWxPhone);
        }
        if (commonResult2 != null && commonResult2.getData() != null) {
            encryptPhone = commonResult2.getData();
            orderInfo.setContactPhone(encryptPhone);
        }

        orderInfo.setCustomerRemark(orderCreateDTO.getOrderInfoDTO().getContactInfoDTO().getOperatorRemark());

        orderInfo.setRefundStatus(RefundStatusEnum.NO_REFUND.getCode());
        orderInfo.setIndependentStatus(OrderIndependentStatusEnum.TODO.getStatus());

        return orderInfo;
    }

    /**
     * 从OrderCreateDTO提取订单验证信息。
     *
     * @param orderCreateDTO 订单创建的DTO
     * @return OrderValidationInfo 包含订单验证所需的信息
     */
    private OrderValidationInfo extractOrderValidationInfo(BrandGoodsOrderCreateDTO orderCreateDTO) {
        // 初始化订单验证信息对象
        OrderValidationInfo orderValidationInfo = new OrderValidationInfo();

        // 初始化购物车项列表
        List<CartItemDTO> cartItems = new ArrayList<>();

        // 初始化快速校验映射
        Map<String, CartItemDTO> cartItemMap = new HashMap<>();

        // 遍历OrderCreateDTO中的购物车项
        for (BrandGoodsOrderShopCarItemDTO shopCarItem : orderCreateDTO.getShopCarItemList()) {

            CartItemDTO cartItem = new CartItemDTO();
            // 设置商品SPU编码
            cartItem.setProductSpuCode(shopCarItem.getProductCode());
            // 设置商品SKU编码
            cartItem.setProductSkuCode(shopCarItem.getProductSkuCode());
            // 设置销售价格
            cartItem.setSalePrice(shopCarItem.getSalePrice());
            // 设置实付价格：代客下单时这个字段 0<实付价格<=salePrice ；原有小程序端创建订接口，实付价格=salePrice
            cartItem.setPayPrice(shopCarItem.getPayPrice());
            // 设置商品数量
            cartItem.setQuantity(shopCarItem.getQuantity());
            //金蝶SKU编码/卡券模板编码
            cartItem.setModelCode(shopCarItem.getModelCode());
            //现金+积分方式：积分
            cartItem.setSalePoints(shopCarItem.getSalePoints());
            //现金+积分方式：销售金额
            cartItem.setSalePointsPrice(shopCarItem.getSalePointsPrice());

            cartItems.add(cartItem);

            // 添加到快速校验映射
            cartItemMap.put(shopCarItem.getProductSkuCode(), cartItem);
        }

        // 设置订单金额信息
        AllPaymentInfoDTO allPaymentInfoDTO = new AllPaymentInfoDTO();
        BeanUtils.copyProperties(orderCreateDTO.getOrderInfoDTO().getPaymentInfoDTO(), allPaymentInfoDTO);
        orderValidationInfo.setPaymentInfoDTO(allPaymentInfoDTO);

        // 设置购物车项列表 和 映射
        orderValidationInfo.setCartItems(cartItems);
        orderValidationInfo.setCartItemMap(cartItemMap);

        return orderValidationInfo;
    }

    /**
     * 购物车参数校验
     *
     * @param shopCarItemList
     */
    private void validate(List<BrandGoodsOrderShopCarItemDTO> shopCarItemList) {
        if (CollUtil.isEmpty(shopCarItemList)) {
            throw ServiceExceptionUtil.exception(SHOP_CAR_ITEM_EMPTY);
        }
        boolean match = shopCarItemList.stream().anyMatch(item -> item.getQuantity() <= 0);
        if (match) {
            throw ServiceExceptionUtil.exception(QUANTITY_INVALID);
        }
    }

    /**
     * 业务线校验
     */
    private void validateBusiness(BrandGoodsOrderCreateDTO orderCreateDTO) {
        boolean hasNotVCS = orderCreateDTO.getShopCarItemList().stream()
                .filter(item -> StringUtils.isBlank(item.getBusinessCode())
                        || (!BusinessIdEnum.BRAND_GOODS.getCode().equals(item.getBusinessCode())
                        && !BusinessIdEnum.LRE.getCode().equals(item.getBusinessCode())))
                .distinct()
                .count() > 0;
        if (hasNotVCS) {
            throw ServiceExceptionUtil.exception(BUSINESS_INVALID);
        }
    }

    /**
     * 收货地址参数校验
     */
    private void validate(OrderGiftAddressDTO giftInfoDTO) {
        log.info("下单时的地址参数：{}", JSON.toJSONString(giftInfoDTO));

        // ======== 核心转换逻辑：adCode → 省市区编码 ========
        String adCode = giftInfoDTO.getAdCode();
        if (StrUtil.isBlank(adCode) || adCode.length() != 6) {
            throw ServiceExceptionUtil.exception(GIFT_ADDRESS_INVALID, "adCode格式错误，必须为6位数字");
        }

        // 拆分逻辑
        String provinceCode = adCode.substring(0, 2);  // 取前2位（省级）
        String cityCode = adCode.substring(0, 4);      // 取前4位（省+市）
        String areaCode = adCode;                      // 完整adCode（区级）

        // 设置到DTO
        giftInfoDTO.setProvinceCode(provinceCode);
        giftInfoDTO.setCityCode(cityCode);
        giftInfoDTO.setAreaCode(areaCode);
        // ==================================================

        // 校验参数
        if (StrUtil.isBlank(giftInfoDTO.getProvinceCode()) || StrUtil.isBlank(giftInfoDTO.getCityCode()) || StrUtil.isBlank(giftInfoDTO.getAreaCode())) {
            throw ServiceExceptionUtil.exception(GIFT_ADDRESS_INVALID);
        }
        if (StrUtil.isBlank(giftInfoDTO.getDetailAddress()) || StrUtil.isBlank(giftInfoDTO.getRecipient()) || StrUtil.isBlank(giftInfoDTO.getRecipientPhone())) {
            throw ServiceExceptionUtil.exception(GIFT_ADDRESS_INVALID);
        }
    }

}





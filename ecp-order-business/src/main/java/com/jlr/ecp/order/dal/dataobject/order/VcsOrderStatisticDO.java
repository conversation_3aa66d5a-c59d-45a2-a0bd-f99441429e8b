package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_vcs_order_statistic
 * @TableName t_vcs_order_statistic
 */
@TableName(value ="t_vcs_order_statistic")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class VcsOrderStatisticDO extends BaseDO {
    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 用户编码;用户编码
     */
    @TableField(value = "consumer_code")
    private String consumerCode;

    /**
     * 品牌名;品牌名
     */
    @TableField(value = "series_name")
    private String seriesName;

    /**
     * 品牌code;品牌code
     */
    @TableField(value = "series_code")
    private String seriesCode;

    /**
     * 订单数量;订单数量
     */
    @TableField(value = "order_count")
    private Integer orderCount;

    /**
     * 品牌编码
     */
    @TableField(value = "brand_code")
    private String brandCode;

}
package com.jlr.ecp.order.handle.impl;

import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.enums.redis.DelayQueuePrefixEnum;
import com.jlr.ecp.order.handle.RedisDelayQueueHandle;
import com.jlr.ecp.order.service.feedback.FeedbackConfigDOService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 创建评价定时上架处理类
 */
@Component("feedbackScheduledLaunchBean")
@Slf4j
public class FeedbackScheduledLaunch implements RedisDelayQueueHandle<String> {
    /**
     * 定义常量，存储评价上架操作的消息队列前缀
     */
    private static final String VALUE_PREFIX = DelayQueuePrefixEnum.FEEDBACK_UP.getCode();

    /**
     * 预编译正则表达式，用于匹配消息队列中存储的完整评价上架信息（包括租户ID和评价编码） 例如：feedback:up:1:PM001
     */
    private static final Pattern VALUE_PATTERN = Pattern.compile(VALUE_PREFIX + "(\\d+):(\\w+)");


    /**
     * 这是处理评价相关操作的服务
     */
    @Resource
    private FeedbackConfigDOService feedbackConfigDOService;

    /**
     * 调用评价上架方法
     *
     * @param value
     */
    @Override
    public void execute(String value) {

        log.info("Processing scheduled feedback launch: {}", value);
        // 使用预编译的正则表达式对value进行匹配
        Matcher matcher = VALUE_PATTERN.matcher(value);

        // 检查value是否符合预期的格式
        if (!matcher.matches()) {
            log.error("Invalid value format in the delay queue: {}", value);
            return;
        }

        // 从匹配的结果中提取租户ID，正则表达式中的第一个括号对应第一个捕获组
        Long tenantId = Long.parseLong(matcher.group(1));


        // 从匹配的结果中提取评价编码，正则表达式中的第二个括号对应第二个捕获组
        String feedbackCode = matcher.group(2);

        // 设置当前线程的租户上下文为提取到的租户ID
        TenantContextHolder.setTenantId(tenantId);

        try {
            //
            feedbackConfigDOService.launchFeedback(feedbackCode);
        } finally {
            // 清除当前线程的租户上下文，以防止线程重用时的上下文污染
            TenantContextHolder.clear();
        }
    }


}
package com.jlr.ecp.order.dal.mysql.test;


import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
//import com.jlr.ecp.product.controller.admin.test.vo.TestDemoExportReqVO;
//import com.jlr.ecp.product.controller.admin.test.vo.TestDemoPageReqVO;
import com.jlr.ecp.order.dal.dataobject.test.TestDemoDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 字典类型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TestDemoMapper extends BaseMapperX<TestDemoDO> {

//    default PageResult<TestDemoDO> selectPage(TestDemoPageReqVO reqVO) {
//        return selectPage(reqVO, new LambdaQueryWrapperX<TestDemoDO>()
//                .likeIfPresent(TestDemoDO::getName, reqVO.getName())
//                .eqIfPresent(TestDemoDO::getStatus, reqVO.getStatus())
//                .eqIfPresent(TestDemoDO::getType, reqVO.getType())
//                .eqIfPresent(TestDemoDO::getCategory, reqVO.getCategory())
//                .eqIfPresent(TestDemoDO::getRemark, reqVO.getRemark())
//                .betweenIfPresent(TestDemoDO::getCreatedTime, reqVO.getCreateTime())
//                .orderByDesc(TestDemoDO::getId));
//    }
//
//    default List<TestDemoDO> selectList(TestDemoExportReqVO reqVO) {
//        return selectList(new LambdaQueryWrapperX<TestDemoDO>()
//                .likeIfPresent(TestDemoDO::getName, reqVO.getName())
//                .eqIfPresent(TestDemoDO::getStatus, reqVO.getStatus())
//                .eqIfPresent(TestDemoDO::getType, reqVO.getType())
//                .eqIfPresent(TestDemoDO::getCategory, reqVO.getCategory())
//                .eqIfPresent(TestDemoDO::getRemark, reqVO.getRemark())
//                .betweenIfPresent(TestDemoDO::getCreatedTime, reqVO.getCreateTime())
//                .orderByDesc(TestDemoDO::getId));
//    }
//
//    List<TestDemoDO> selectList2();

}

package com.jlr.ecp.order.controller.app.feedback.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "查询评价设置的DTO")
public class FeedbackQueryDTO {

    /**
     * 评价维度：PM开头、已支付OR、订单完成、CL、订单整单取消
     */
    @Schema(description = "评价适用环节：PM开头、已支付OR、订单完成、CL、订单整单取消", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "评价适用环节不能为空")
    private String feedbackDimensions;

}
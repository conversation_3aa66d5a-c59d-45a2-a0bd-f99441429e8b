package com.jlr.ecp.order.dal.dataobject.feedback;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@TableName("t_feedback_snapshot")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackSnapshotDO extends BaseDO {
    /**
     * 快照ID
     */
    @TableId
    private Long id;

    /**
     * 业务线
     */
    @TableField("business_code")
    private String businessCode;

    /**
     * 评价维度：PM开头、已支付OR、订单完成、CL、订单整单取消
     */
    @TableField("feedback_dimensions")
    private String feedbackDimensions;

    /**
     * 评价编码：生成规则：feedback_dimensions+递增三位数编号（例如：PM001）
     */
    @TableField("feedback_code")
    private String feedbackCode;

    /**
     * 快照编码：版本号，启用时自动生成：feedback_code_PT+时间戳（YYYYMMDDHHMMSS）
     */
    @TableField("snapshot_code")
    private String snapshotCode;


    /**
     * 快照内容JSON格式
     */
    @TableField("snapshot_json")
    private String snapshotJson;

    /**
     * 是否允许输入框：0=否；1=是
     */
    @TableField("enable_input")
    private Integer enableInput;

    /**
     * 输入框提示信息
     */
    @TableField("input_text")
    private String inputText;

    /**
     * 备注说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 启用时间
     */
    @TableField("enable_time")
    private LocalDateTime enableTime;
    /**
     * 停用时间
     */
    @TableField("downtime")
    private LocalDateTime downtime;

    /**
     * 提交数量；停用的时候统计；当前正在使用的快照为null直接查询records表
     */
    @TableField("submit_num")
    private Integer submitNum;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Integer tenantId;


}

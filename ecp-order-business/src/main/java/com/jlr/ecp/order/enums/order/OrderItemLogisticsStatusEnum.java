package com.jlr.ecp.order.enums.order;

/**
 * 物流履约订单状态枚举
 */
public enum OrderItemLogisticsStatusEnum {

    /**
     * 待发货
     */
    PENDING_SHIPMENT(1, "待发货"),

    /**
     * 已发货
     */
    SHIPPED(2, "已发货"),

    /**
     * 已妥投
     */
    DELIVERED(3, "已妥投"),

    /**
     * 已收货
     */
    RECEIVED(4, "已收货"),

    /**
     * 已关闭
     */
    CLOSED(5,"已关闭");

    private final Integer code;
    private final String name;

    OrderItemLogisticsStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据状态码获取枚举实例
     * @param code 状态编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static OrderItemLogisticsStatusEnum getByCode(Integer code) {
        for (OrderItemLogisticsStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }


    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

package com.jlr.ecp.order.dal.mysql.feedback;

/**
 * 评价配置操作记录表Mapper
 */


import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackModifyLogDO;
import com.jlr.ecp.order.enums.feedback.ModifyOperationTypeEnum;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Mapper
public interface FeedbackModifyLogDOMapper extends BaseMapperX<FeedbackModifyLogDO> {
    default List<FeedbackModifyLogDO> selectListByFeedBackCode(String feedbackCode){
        return selectList(new LambdaQueryWrapperX<FeedbackModifyLogDO>()
                .eq(FeedbackModifyLogDO::getFeedbackCode, feedbackCode)
                .eq(BaseDO::getIsDeleted,false)
                .orderByDesc(FeedbackModifyLogDO::getCreatedTime));
    }

    default void insertModifyLog(String feedbackCode,
                                 String moduleName,
                                 int modifyFieldCount,
                                 Map<String, Object> oldValues,
                                 Map<String, Object> newValues) {

        FeedbackModifyLogDO logDO = new FeedbackModifyLogDO();

        // 构建JSON数据
        JSONObject oldVal = new JSONObject(oldValues != null ? oldValues : Collections.emptyMap());
        JSONObject newVal = new JSONObject(newValues != null ? newValues : Collections.emptyMap());

        logDO.setFeedbackCode(feedbackCode);
        logDO.setModifyModule(moduleName);
        logDO.setModifyFieldCount(modifyFieldCount);
        logDO.setModifyFieldOldValue(oldVal.toJSONString());
        logDO.setModifyFieldNewValue(newVal.toJSONString());
        logDO.setOperateUser(WebFrameworkUtils.getLoginUserName());
        logDO.setOperateTime(LocalDateTime.now());

        insert(logDO);
    }
}

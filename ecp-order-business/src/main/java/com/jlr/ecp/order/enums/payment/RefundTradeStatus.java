package com.jlr.ecp.order.enums.payment;

public enum RefundTradeStatus {
    PENDING("PENDING", "交易处理中"),
    SUCCESS("SUCCESS", "交易成功"),
    FAIL("FAIL", "交易失败");

    private final String code;
    private final String description;

    RefundTradeStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static RefundTradeStatus fromCode(String code) {
        for (RefundTradeStatus status : RefundTradeStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown refund trade status code: " + code);
    }
}

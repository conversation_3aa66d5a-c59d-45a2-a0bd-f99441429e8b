package com.jlr.ecp.order.util.machine;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.enums.order.EcouponOrderRefundEventEnum;
import com.jlr.ecp.order.util.machine.handler.*;
import com.jlr.ecp.order.util.machine.handler.ecoupon.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 单例电子券退单状态变更
 * <AUTHOR>
 */
@Component
public class EcouponOrderRefundStatusMachine {

    private Map<Integer, EcouponEventHandler> eventHandlerMap;

    @Autowired
    public EcouponOrderRefundStatusMachine(EcouponRefundApplyHandler eCouponRefundApplyHandler,
                                           EcouponRefundProcessingHandler ecouponRefundProcessingHandler,
                                           EcouponRefundApproveHandler ecouponRefundApproveHandler,
                                           EcouponRefundIndependentHandler ecouponRefundIndependentHandler,
                                           EcouponRefundCompletedHandler ecouponRefundCompletedHandler,
                                           EcouponRefundRejectHandler ecouponRefundRejectHandler,
                                           EcouponRefundBuyerCancelHandler ecouponRefundBuyerCancelHandler,
                                           EcouponRefundOrderClosedHandler ecouponRefundOrderClosedHandler
                                           ) {
        // 初始化事件处理器映射
        eventHandlerMap = new HashMap<>();

        // 将handler绑定对应的事件 添加到map中
        eventHandlerMap.put(EcouponOrderRefundEventEnum.ECOUPON_REFUND_APPLY.getCode(), eCouponRefundApplyHandler);
        eventHandlerMap.put(EcouponOrderRefundEventEnum.ECOUPON_REFUND_PROCESSING.getCode(), ecouponRefundProcessingHandler);
        eventHandlerMap.put(EcouponOrderRefundEventEnum.ECOUPON_REFUND_AUDIT.getCode(), ecouponRefundApproveHandler);
        eventHandlerMap.put(EcouponOrderRefundEventEnum.ECOUPON_REFUND_INDEPENDENT.getCode(), ecouponRefundIndependentHandler);
        eventHandlerMap.put(EcouponOrderRefundEventEnum.ECOUPON_REFUND_COMPLETED.getCode(), ecouponRefundCompletedHandler);
        eventHandlerMap.put(EcouponOrderRefundEventEnum.ECOUPON_REFUND_REJECT.getCode(), ecouponRefundRejectHandler);
        eventHandlerMap.put(EcouponOrderRefundEventEnum.ECOUPON_REFUND_BUYER_CANCLE.getCode(), ecouponRefundBuyerCancelHandler);
        eventHandlerMap.put(EcouponOrderRefundEventEnum.ECOUPON_REFUND_ORDER_CLOSED.getCode(), ecouponRefundOrderClosedHandler);

    }

    public void changeOrderStatus(Integer event,OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO) {
        EcouponEventHandler eventHandler = eventHandlerMap.get(event);
        if (eventHandler != null) {
            orderRefundDO.setUpdatedTime(LocalDateTime.now());
            eventHandler.handleEvent(orderInfoDO, orderRefundDO, orderItemDO);
        } else {
            // 处理默认情况
        }
    }

}

package com.jlr.ecp.order.enums.order;

/**
 * 卡券状态枚举
 */
public enum EcouponStatusEnum {

    WAREHOUSING(1, "入库"),
    NOT_ACTIVE(2, "未生效"),
    PENDING_USE(3, "待使用"),
    INVALIDATED(4, "已作废"),
    VERIFIED(5, "已核销"),
    EXPIRED(6, "已过期");

    private final int code;
    private final String name;

    EcouponStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code获取枚举实例
     * @param code 状态编码
     * @return 对应的枚举实例，未找到返回null
     */
    public static EcouponStatusEnum getByCode(int code) {
        for (EcouponStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

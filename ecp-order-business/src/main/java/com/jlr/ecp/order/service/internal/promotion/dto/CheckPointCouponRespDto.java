package com.jlr.ecp.order.service.internal.promotion.dto;


import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import lombok.Getter;
import lombok.Setter;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.internal.promotion.dto
 * @className: CheckPointCouponRespDto
 * @author: gaoqig
 * @description: 检查优惠券和积分的响应对象
 * @date: 2025/3/7 11:25
 * @version: 1.0
 */
@Getter
@Setter
public class CheckPointCouponRespDto {
    /**
     * 优惠券对象
     */
    private PromotionDto promotion;

    /**
     * 参于优惠商品的总金额
     */
    private String totalSalePrice;
}

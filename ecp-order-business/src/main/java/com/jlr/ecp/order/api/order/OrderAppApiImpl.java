package com.jlr.ecp.order.api.order;

import cn.hutool.crypto.SecureUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.order.dto.*;
import com.jlr.ecp.order.api.order.vo.base.OrderItemBaseVO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.service.feedback.FeedbackConfigDOService;
import com.jlr.ecp.order.service.order.BrandGoodsOrderInfoDOService;
import com.jlr.ecp.order.service.order.OrderInfoDOService;
import com.jlr.ecp.order.service.order.OrderItemDOService;
import com.jlr.ecp.order.service.refund.OrderRefundDOService;
import org.apache.tika.utils.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

import static com.jlr.ecp.framework.common.util.collection.CollectionUtils.convertList;

/**
 * <AUTHOR>
 */
@RestController
@Validated
public class OrderAppApiImpl implements OrderAppApi {
    @Resource
    private OrderItemDOService orderItemDOService;

    @Resource
    private OrderRefundDOService orderRefundService;

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;

    @Resource
    private OrderInfoDOService orderInfoDOService;

    @Resource
    private FeedbackConfigDOService feedbackConfigDOService;

    @Resource
    private BrandGoodsOrderInfoDOService brandGoodsOrderInfoDOService;

    @Override
    public CommonResult<Integer> updateOrderStatus(String orderCode) {
        Integer success = orderInfoDOService.updateOrderStatus(orderCode);
        return CommonResult.success(success);
    }

    @Override
    public CommonResult<Integer> updateOrderStatusOnSuccess(String orderCode, String payFinishTime) {
        Integer success = orderInfoDOService.updateOrderStatusOnSuccess(orderCode, payFinishTime);
        return CommonResult.success(success);
    }

    @Override
    public CommonResult<List<OrderItemBaseVO>> getOrderItemInfo(OrderItemCodeListDTO dto) {
        List<String> orderItemCodeList = dto.getOrderItemCodeList();

        List<OrderItemBaseVO> list = orderItemDOService.getOrderItemInfo(orderItemCodeList);
        return CommonResult.success(list);
    }

//    @Override
//    public CommonResult<OrderIntegrationRespVO> getOrder(String orderCode, String jlrId) {
//        return null;
//    }

//    @Override
//    public CommonResult<PageResult<OrderIntegrationPageRespVO>> getlatestOrders(String jlrId, String createdTimeSort, Integer pageNo, Integer pageSize) {
//        return null;
//    }

    @Override
    public CommonResult<Integer> tsdpRefundCallBack(String orderRefundCode,Boolean updateStatus) {
        Integer success = orderRefundService.tsdpRefundCallBack(orderRefundCode,updateStatus);
        return CommonResult.success(success);
    }


    @Override
    public CommonResult<Integer> tsdpCallBack(String vcsOrderCode,Boolean updateStatus) {
        Integer success = orderRefundService.tsdpCallBack(vcsOrderCode,updateStatus);
        return CommonResult.success(success);
    }

    /**
     * 通过carVin批量获取手机列表
     *
     * @param carVinList carVin列表数据
     * @return CommonResult<List < PhoneByCarVinDTO>>
     */
    @Override
    public CommonResult<List<OrderCarVinDTO>> getPhoneByCarVin(List<String> carVinList) {
        // 用加密值进行查询
        carVinList = convertList(carVinList, SecureUtil::md5);
        List<OrderCarVinDTO> phoneByCarVinDTOS = orderInfoDOMapper.queryPhoneListByCarVin(carVinList);
        List<OrderCarVinDTO> resp = orderItemDOService.addProductCode(phoneByCarVinDTOS);
        return CommonResult.success(resp);
    }

    /**
     *  使用orderCode获取订单下单时间
     * @param orderCode 订单编号
     * @return CommonResult<OrderIntegrationRespVO>
     * */
    @Override
    public CommonResult<OrderInvoiceDTO> getOrderDoByOrderCode(String orderCode) {
        if (StringUtils.isBlank(orderCode)) {
            return CommonResult.success(null);
        }
        return orderInfoDOService.queryOrderInfoByOrderCode(orderCode);
    }

    /**
     * 通过orderCode获取orderItem的信息
     * @param orderCode 订单编号
     * @return CommonResult<List<OrderItemDTO>>
     * */
    @Override
    public CommonResult<List<OrderItemDTO>> getOrderItemListByOrderCode(String orderCode) {
        List<OrderItemDTO> orderItemDTOList = orderItemDOService.queryOrderItemByOrderCode(orderCode);
        return CommonResult.success(orderItemDTOList);
    }

    /**
     *  订单支付超时，自动取消job
     * @param days 天数
     *
     * */
    @Override
    public Integer orderTimeoutCancel(Integer days) {
        return orderInfoDOService.orderTimeoutCancel(days);
    }

    @Override
    public CommonResult<Set<String>> getAfterSalesOrderCode(List<String> orderCodeList) {
        return CommonResult.success(orderInfoDOService.getAfterSalesOrderCode(orderCodeList));
    }

    @Override
    public CommonResult<Boolean> checkOrderInTransit(String carVin, Integer serviceType) {
        return CommonResult.success(orderInfoDOService.checkOrderInTransit(carVin, serviceType));
    }

    @Override
    public CommonResult<List<String>> checkOrderInTransitByVinList(OrderInTransitReqDTO reqDTO) {
        return CommonResult.success(orderInfoDOService.checkOrderInTransitByVinList(reqDTO.getCarVinList(), reqDTO.getServiceType()));
    }


    @Override
    public CommonResult<List<String>> getPassTimeUnEnableFeedback(Integer pageSize) {
        return CommonResult.success(feedbackConfigDOService.getPassTimeUnEnableFeedback(pageSize));

    }

    @Override
    public CommonResult<Boolean> handPassUnEnableFeedback(String feedbackCode) {

        return CommonResult.success(feedbackConfigDOService.handPassUnEnableFeedback(feedbackCode));
    }

    @Override
    public CommonResult<Boolean> confirmReceiptList(ConfirmReceiptJobReqDTO reqDTO) {
        return CommonResult.success(brandGoodsOrderInfoDOService.confirmReceiptList(reqDTO));
    }
}

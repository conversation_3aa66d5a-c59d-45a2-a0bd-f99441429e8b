package com.jlr.ecp.order.controller.admin.feedback.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "管理后台 -  评价维度配置创建DTO ")
@ToString(callSuper = true)
public class FeedbackDimensionsDTO {
//    /**
//     * 维度Code，雪花算法
//     */
//    @TableField("dimensions_code")
    @Schema(description = "评价维度编码 维度Code，雪花算法", requiredMode = Schema.RequiredMode.REQUIRED)
    private String dimensionsCode;


    /**
     * 名称
     */
    @Schema(description = "评价维度名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "评价维度名称不能为空")
    private String name;

    /**
     * 维度类型：0：星级（5分制）；1：单选题；2：多选题
     */
    @Schema(description = "评价维度类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "评价维度类型不能为空")
    private Integer type;

    /**
     * 选项JSON格式："ICR APP","sort":"1"或"广告","sort":"2"
     */
    @Schema(description = "选项内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<FeedbackOptionsDTO> optionList;

    /**
     * 是否必填：0=否；1=是
     */
    @Schema(description = "是否必填", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否必填不能为空")
    private Integer mustInput;

    /**
     * 备注
     */
    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    @Length(max = 50, message = "备注不能超过50个字符")
    private String remark;

    /**
     * 排序字段
     */
    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;


}

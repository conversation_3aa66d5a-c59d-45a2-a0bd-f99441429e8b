package com.jlr.ecp.order.util.machine.handler;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import org.springframework.stereotype.Component;


/**
 * 处理部分退单审批
 * <AUTHOR>
 */
@Component
public class PartialApproveHandler implements EventHandler {
    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO) {
        // 处理部分退单审批
        orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.PARTIAL_REFUND_APPROVE.getCode());
        orderInfoDO.setOrderStatus(OrderStatusEnum.AFTER_SALES.getCode());
    }
}

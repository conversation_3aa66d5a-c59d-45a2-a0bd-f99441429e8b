package com.jlr.ecp.order.kafka.listener;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.order.BusinessCodeEnum;
import com.jlr.ecp.order.enums.order.OrderCloseReasonEnum;
import com.jlr.ecp.order.kafka.message.CancelOrderMessage;
import com.jlr.ecp.order.service.order.CancelOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 *  订单超时未支付自动取消订单
 */
@Component
@Slf4j
public class RealCancelOrderListener {


    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private CancelOrderService cancelOrderService;
    
    @KafkaListener(topics = KafkaConstants.REAL_ORDER_TIMEOUT_CANCEL_TOPIC,
            groupId = "order-timeout-real-cancel-group",
            batch = "true", properties = {"max.poll.records:1"})
    public void onMessage(String messageStr) {
        log.info("收到订单超时自动取消消息，messageStr={}", messageStr);

        CancelOrderMessage message = JSON.parseObject(messageStr, CancelOrderMessage.class);
        String orderCode = message.getOrderCode();
        log.info("订单超时自动取消开始，orderCode={}，message={}", orderCode, messageStr);

        // 校验是否达到15分钟超时时间（减3秒，是为了兼容个服务器时间不完全一致），未到达超时时间，不能自动取消
        LocalDateTime sendTime = message.getSendTime();
        Duration duration = Duration.between(sendTime, LocalDateTime.now());
        long CANCEL_ORDER_TIMEOUT_MILLS ;
        if(BusinessCodeEnum.VCS.getCode().equals(message.getBusinessCode())){
            CANCEL_ORDER_TIMEOUT_MILLS = Constants.VCS_PAY_TIMEOUT_MILLS;
        }else {
            CANCEL_ORDER_TIMEOUT_MILLS = Constants.BG_LRE_PAY_TIMEOUT_MILLS;
        }
        long delayTime = CANCEL_ORDER_TIMEOUT_MILLS - 3 * 1000L - duration.toMillis();
        if (delayTime > 0) {
            log.warn("订单超时自动取消，未到达超时时间，不能自动取消，orderCode={}，messageStr={}", message.getOrderCode(), messageStr);
            return;
        }

        String redisKey = Constants.PAYING_ORDER_KEY + orderCode;
        Object value = redisTemplate.opsForValue().get(redisKey);
        if (Objects.nonNull(value)) {
            log.info("订单超时自动取消，当前订单正在支付中，orderCode={}", orderCode);
            return;
        }

        TenantContextHolder.setTenantId(message.getTenantId());
        try {
            cancelOrderService.orderCancel(message.getOrderCode(), OrderCloseReasonEnum.PAY_TIMEOUT_CLOSE);
            log.info("订单超时自动取消完成，orderCode={}", message.getOrderCode());
        } catch (ServiceException e) {
            // 可重试则进行重试
            if (ErrorCodeConstants.CANCEL_ORDER_ERROR_CAN_RETRY.getCode().equals(e.getCode())) {
                throw e;
            }
        } catch (Exception e) {
            log.error("订单超时自动取消异常，messageStr={}", messageStr);
            throw e;
        } finally {
            TenantContextHolder.clear();
        }
    }
    
}

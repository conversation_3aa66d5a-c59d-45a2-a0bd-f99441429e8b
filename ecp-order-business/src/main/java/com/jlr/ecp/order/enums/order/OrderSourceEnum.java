package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 *  订单来源
 * */
@AllArgsConstructor
@Getter
public enum OrderSourceEnum {
    CUSTOMER_ORDER(1, "客服下单"),
    MINI_PROGRAM_ORDER(2, "小程序下单");

    private final Integer code;

    private final String desc;

    /**
     * 转换为订单渠道
     */
    public static List<Integer> buildOrderChannel(Integer code) {
        List<Integer> channelList = new ArrayList<>();
        if (OrderSourceEnum.CUSTOMER_ORDER.getCode().equals(code)) {
            channelList.add(OrderChannelCodeEnum.CUSTOMER_SERVICE.getOrderChannelCode());
        } else if (OrderSourceEnum.MINI_PROGRAM_ORDER.getCode().equals(code)) {
            channelList.add(OrderChannelCodeEnum.LR_WECHAT.getOrderChannelCode());
            channelList.add(OrderChannelCodeEnum.JA_WECHAT.getOrderChannelCode());
        }
        return channelList;
    }
}
package com.jlr.ecp.order.service.order;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusMappingDO;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusMappingDOMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_order_status_mapping(t_order_status_mapping)】的数据库操作Service实现
* @createDate 2024-01-21 20:29:33
*/
@Service
public class OrderStatusMappingDOServiceImpl extends ServiceImpl<OrderStatusMappingDOMapper, OrderStatusMappingDO>
    implements OrderStatusMappingDOService{

}





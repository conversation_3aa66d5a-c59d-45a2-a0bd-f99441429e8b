package com.jlr.ecp.order.delayqueue.dto;

import com.jlr.ecp.order.delayqueue.enums.ErrorTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 取消订单延迟任务
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CancelOrderTask implements Serializable {

    private static final long serialVersionUID = 1L;
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 订单编码
     */
    private String orderCode;
    
    /**
     * 父订单编码
     */
    private String parentOrderCode;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 发送时间
     */
    private LocalDateTime sendTime;
    
    /**
     * 业务线编码
     */
    private String businessCode;
    
    /**
     * 任务创建时间
     */
    private LocalDateTime createTime;

    /**
     * 当前重试次数
     */
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;

    /**
     * 最后一次错误信息
     */
    private String lastErrorMessage;

    /**
     * 错误类型
     */
    private ErrorTypeEnum errorType;
}

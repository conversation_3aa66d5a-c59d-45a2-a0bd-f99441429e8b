package com.jlr.ecp.order.service.refund;

import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundPaymentRecordsDO;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundPaymentRecordsMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class OrderRefundPaymentRecordsServiceImpl implements OrderRefundPaymentRecordsService {

    @Resource
    private OrderRefundPaymentRecordsMapper orderRefundPaymentRecordsMapper;

    @Override
    public Long createOrderRefundPaymentRecord(OrderRefundPaymentRecordsDO orderRefundPaymentRecordsDO) {
        orderRefundPaymentRecordsMapper.insert(orderRefundPaymentRecordsDO);
        return orderRefundPaymentRecordsDO.getId();
    }
} 
package com.jlr.ecp.order.service.order;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.order.dto.OrderCarVinDTO;
import com.jlr.ecp.order.api.order.dto.OrderItemDTO;
import com.jlr.ecp.order.api.order.vo.base.OrderItemBaseVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.enums.phone.PhoneNumberDecodeUtil;
import com.jlr.ecp.order.util.common.AttributeUtil;
import com.jlr.ecp.order.util.money.MoneyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【t_order_item(t_order_item)】的数据库操作Service实现
 * @createDate 2023-12-20 10:41:04
 */
@Service
@Slf4j
public class OrderItemDOServiceImpl extends ServiceImpl<OrderItemDOMapper, OrderItemDO> implements OrderItemDOService {

    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Resource
    private PhoneNumberDecodeUtil phoneNumberDecodeUtil;

    @Override
    public List<OrderItemBaseVO> getOrderItemInfo(List<String> orderItemCodeList) {
        List<OrderItemDO> orderItemDOList = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .in(OrderItemDO::getOrderItemCode, orderItemCodeList)
                .eq(OrderItemDO::getIsDeleted, false)
        );

        return orderItemDOList.stream().map(orderItemDO -> {
            if(orderItemDO == null) {
                return null;
            }
            OrderItemBaseVO orderItemBaseVO = new OrderItemBaseVO();

            BeanUtils.copyProperties(orderItemDO, orderItemBaseVO);
            orderItemBaseVO.setProductAttribute(AttributeUtil.formatProductAttributes(orderItemDO.getProductAttribute()));
            if(orderItemDO.getProductMarketPrice() != null){
                orderItemBaseVO.setProductMarketPrice(MoneyUtil.convertFromCents(new BigDecimal(orderItemDO.getProductMarketPrice())));
            }
            orderItemBaseVO.setProductSalePrice(MoneyUtil.convertFromCents(new BigDecimal(orderItemDO.getProductSalePrice())));

            return orderItemBaseVO;
        }).collect(Collectors.toList());
    }

    /**
     * 增加productCode
     * @param orderCarVinDTOList car的订单信息
     * @return List<OrderCarVinDTO>
     * */
    @Override
    public List<OrderCarVinDTO> addProductCode(List<OrderCarVinDTO> orderCarVinDTOList) {
        if (CollectionUtils.isEmpty(orderCarVinDTOList)) {
            return orderCarVinDTOList;
        }
        List<String> orderCodeList = orderCarVinDTOList.stream().map(OrderCarVinDTO::getOrderCode)
                .collect(Collectors.toList());
        List<OrderItemDO> orderItemDOList = getOrderItemDoByOrderCodes(orderCodeList);
        Map<String, OrderItemDO> map = getOrderItemMap(orderItemDOList);
        if (CollectionUtils.isEmpty(map)) {
            return orderCarVinDTOList;
        }
        List<OrderCarVinDTO> resp = new ArrayList<>();
        for (OrderCarVinDTO orderCarVinDTO : orderCarVinDTOList) {
            if (map.containsKey(orderCarVinDTO.getOrderCode())
                    && Objects.nonNull(map.get(orderCarVinDTO.getOrderCode()))) {
                orderCarVinDTO.setProductCode(map.get(orderCarVinDTO.getOrderCode()).getProductCode());
            }
            orderCarVinDTO.setWxPhone(phoneNumberDecodeUtil.getDecodePhone(orderCarVinDTO.getWxPhone()));
            orderCarVinDTO.setContactPhone(phoneNumberDecodeUtil.getDecodePhone(orderCarVinDTO.getContactPhone()));
            resp.add(orderCarVinDTO);
        }
        return resp;
    }

    /**
     *  通过订单编号获取OrderItem的list
     * @param orderCode order编号
     * @return List<OrderItemDTO>
     * */
    @Override
    public List<OrderItemDTO> queryOrderItemByOrderCode(String orderCode) {
        if (StringUtils.isBlank(orderCode)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<OrderItemDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderItemDO::getOrderCode, orderCode)
                .eq(OrderItemDO::getIsDeleted, false);
        List<OrderItemDO> orderItemDOList = orderItemDOMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(orderItemDOList)) {
            log.info("查询OrderItemList为空, orderCode:{}", orderCode);
            return new ArrayList<>();
        }
        List<OrderItemDTO> resp = new ArrayList<>();
        for (OrderItemDO orderItemDO : orderItemDOList) {
            OrderItemDTO dto = new OrderItemDTO();
            BeanUtils.copyProperties(orderItemDO, dto);
            resp.add(dto);
        }
        return resp;
    }

    /**
     * 按照OrderItemDO的列表返回map
     * @param orderItemDOList 的列表
     * @return Map<String, OrderItemDO>
     * */
    private Map<String, OrderItemDO> getOrderItemMap(List<OrderItemDO> orderItemDOList) {
        Map<String, OrderItemDO> map = new HashMap<>();
        if (CollectionUtils.isEmpty(orderItemDOList)) {
            return map;
        }
        for (OrderItemDO orderItemDO : orderItemDOList) {
            map.put(orderItemDO.getOrderCode(), orderItemDO);
        }
        return map;
    }

    /**
     * 通过orderCode列表批量返回OrderItem
     * */
    private List<OrderItemDO> getOrderItemDoByOrderCodes(List<String> orderCodes) {
        if (CollectionUtils.isEmpty(orderCodes)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<OrderItemDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderItemDO::getOrderCode, orderCodes)
                .eq(OrderItemDO::getIsDeleted, false);
        return orderItemDOMapper.selectList(queryWrapper);
    }
}





package com.jlr.ecp.order.util.machine.handler;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import org.springframework.stereotype.Component;

/**
 * 整单退单审批同意事件处理
 * <AUTHOR>
 */
@Component
public class FullApproveHandler implements EventHandler {
    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO) {
        // 整单退单审批事件处理的逻辑
        orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.FULL_REFUND_APPROVE.getCode());
        orderInfoDO.setOrderStatus(OrderStatusEnum.AFTER_SALES.getCode());
    }
}

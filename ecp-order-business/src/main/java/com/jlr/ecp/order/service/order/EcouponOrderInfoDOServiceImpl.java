package com.jlr.ecp.order.service.order;

import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.order.dto.ECouponOrderPageReqDTO;
import com.jlr.ecp.order.api.order.vo.ECouponOrderInfoPageVO;
import com.jlr.ecp.order.api.order.vo.ECouponOrderItemVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EcouponOrderInfoDOServiceImpl extends ServiceImpl<OrderInfoDOMapper, OrderInfoDO> implements EcouponOrderInfoDOService {

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;

    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Override
    public PageResult<ECouponOrderInfoPageVO> getPage(ECouponOrderPageReqDTO dto) {
        parseCommaSeparatedIntegers(dto.getCouponStatus(), dto::setCouponStatusList);
        parseCommaSeparatedIntegers(dto.getAfterSalesStatus(), dto::setAfterSalesStatusList);
        applyMd5IfNotBlank(dto.getWxPhone(), dto::setWxPhone);
        applyMd5IfNotBlank(dto.getContactPhone(), dto::setContactPhone);
        
        // 1. 基于查询条件查询
        Page<ECouponOrderInfoPageVO> orderPage = orderInfoDOMapper.getEcouponOrdersWithPaging(new Page<>(dto.getPageNo(), dto.getPageSize()), dto);
        List<ECouponOrderInfoPageVO> orderList = orderPage.getRecords();
        if (CollectionUtils.isEmpty(orderList)) {
            return new PageResult<>(Collections.emptyList(), orderPage.getTotal());
        }

        // 改成收集所有符合条件的orderCode的 orderItem
        List<String> orderCodes = orderList.stream()
                .map(ECouponOrderInfoPageVO::getOrderCode)
                .collect(Collectors.toList());
        List<OrderItemDO> allOrderItems = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .in(OrderItemDO::getOrderCode, orderCodes)
                .eq(OrderItemDO::getIsDeleted, 0));
        List<String> allOrderItemCodes = allOrderItems.stream()
                .map(OrderItemDO::getOrderItemCode)
                .collect(Collectors.toList());

        
        // 2. 使用收集到的orderItemCodes查询订单项详细信息
        List<ECouponOrderItemVO> orderItems = orderInfoDOMapper.getEcouponOrderDetailsByCodes(allOrderItemCodes);
        
        // 创建查找映射，将订单项按orderItemCode索引，避免重复查找
        Map<String, List<ECouponOrderItemVO>> orderItemMap = orderItems.stream()
                .collect(Collectors.groupingBy(ECouponOrderItemVO::getOrderCode));
        
        // 3. 组装订单项到订单中
        for (ECouponOrderInfoPageVO order : orderList) {
            order.setOrderItems(new ArrayList<>());
            List<ECouponOrderItemVO> orderItemList = orderItemMap.get(order.getOrderCode());
            if (CollectionUtils.isNotEmpty(orderItemList)) {
                List<String> orderItemCodes = orderItemList.stream().map(ECouponOrderItemVO::getOrderItemCode).collect(Collectors.toList());
                order.setOrderItems(orderItemList);
                order.setOrderItemCodes(String.join(",", orderItemCodes));
            }
        }
        
        // 4. 返回分页结果
        return new PageResult<>(orderList, orderPage.getTotal());
    }

    /**
     * 解析逗号分隔的整数字符串到整数列表
     * 
     * @param commaSeparatedStr 逗号分隔的字符串
     * @param listSetter 设置解析后列表的函数
     */
    private void parseCommaSeparatedIntegers(String commaSeparatedStr, Consumer<List<Integer>> listSetter) {
        if (StringUtils.isNotBlank(commaSeparatedStr)) {
            try {
                List<Integer> intList = Arrays.stream(commaSeparatedStr.split(","))
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList());
                listSetter.accept(intList);
            } catch (NumberFormatException e) {
                log.warn("解析状态值失败: {}", commaSeparatedStr, e);
                // 可以选择抛出自定义异常或者使用默认空列表
                listSetter.accept(Collections.emptyList());
            }
        }
    }

    /**
     * 对非空字符串应用MD5加密
     *
     * @param value 原始字符串
     * @param setter 设置加密后字符串的函数
     */
    private void applyMd5IfNotBlank(String value, Consumer<String> setter) {
        if (StringUtils.isNotBlank(value)) {
            setter.accept(SecureUtil.md5(value));
        }
    }
}

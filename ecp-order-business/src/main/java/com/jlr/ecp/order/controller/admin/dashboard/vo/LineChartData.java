package com.jlr.ecp.order.controller.admin.dashboard.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LineChartData {
    // x轴数据
    private List<String> xAxisData;


    private String title;

    private List<BigDecimal> seriesData; // key为维度名称，value为对应的评分数据

}

package com.jlr.ecp.order.controller.app.order.vo;

import com.jlr.ecp.payment.api.order.vo.PayOrderSubmitRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "支付订单提交 Response VO")
public class PayOrderRespVO extends PayOrderSubmitRespVO {

    /**
     * 是否可以唤起支付
     */
    @Schema(description = "是否可以唤起支付")
    private boolean isInvoke;

    /**
     * 零元支付状态 (枚举类型): <br/>
     * SUCCESS-成功; FAIL-失败; NA-不适用
     */
    @Schema(description = "零元支付状态")
    private String zeroPayStatus;
}

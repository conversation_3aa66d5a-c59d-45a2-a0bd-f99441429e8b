package com.jlr.ecp.order.kafka.message.customer;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ValetOrderMessage {

    /**
     * 订单来源
     */
    private Integer orderChannel;

    /**
     *  跳转路由
     * */
    private String url;
 
 
    /**
     *  跳转标识 默认为1
     * */
    private Integer shortLink;
 
    /**
     * 消息Id
     * */
    private String messageId;
 
    /**
     * 手机号码
     */
    private String phoneNumber;
 
    /**
     *  模板code
     * */
    private String templateCode;
 
    /**
     * 租户号
     * */
    private Long tenantId;
 
    /**
     *  服务名称
     * */
    private String serviceName;
 
    /**
     *  品牌code 1:路虎 2：捷豹
     * */
    private Integer brandCode;
 
    /**
     * 订单编号
     * */
    private String orderNumber;
 
    /**
     * 车辆vin码
     */
    private String carVin;
}
package com.jlr.ecp.order.service.order;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.order.dto.ECouponOrderPageReqDTO;
import com.jlr.ecp.order.api.order.vo.ECouponOrderInfoPageVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;

/**
 * <AUTHOR>
 * @description 针对表【t_order_info(t_order_info)】的数据库操作Service 电子券(LRE)订单
 * @createDate 2025-03-13 18:30:00
 */
public interface EcouponOrderInfoDOService extends IService<OrderInfoDO> {
    
    /**
     * 订单分页列表
     *
     * @param dto 分页入参
     * @return PageResult<OrderInfoPageVO>
     */
    PageResult<ECouponOrderInfoPageVO> getPage(ECouponOrderPageReqDTO dto);
}
package com.jlr.ecp.order.service.refund.bg;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.order.api.refund.dto.BaseOrderRefundApplyDTO;
import com.jlr.ecp.order.api.refund.dto.LogisticsOrderRefundApproveDTO;
import com.jlr.ecp.order.api.refund.dto.LogisticsOrderReturnApproveDTO;
import com.jlr.ecp.order.api.refund.dto.OrderRefundLogisticsDTO;
import com.jlr.ecp.order.controller.app.refund.vo.LogisticsRefundItemDetailVO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;

import java.util.List;

/**
* <AUTHOR>
* @description 实物商品类型退款
* @createDate 20250401
*/
public interface BrandGoodsOrderRefundDOService extends IService<OrderRefundDO> {

    /***
     * 实物商品发起退款申请(如果是仅退款则没有审核流,如果是退货退款则进行待退货审核)
     * @param refundApplyDTOs
       "操作类型 1-用户发起 2-系统自动 3-运营发起" RefundOrderOperationTypeEnum
     * @return Boolean
     */
    String logisticsOrderRefundApply(List<BaseOrderRefundApplyDTO> refundApplyDTOs,Integer operationType);

    /**
     * 退款审核
     * @param orderRefundApproveDTO
     * @return
     */
    String logisticsOrderRefundAuditApprove(LogisticsOrderRefundApproveDTO orderRefundApproveDTO);

    /**
     * 退货审核
     * @param orderRefundApproveDTO
     * @return
     */
    String logisticsOrderReturnAuditApprove(LogisticsOrderReturnApproveDTO orderRefundApproveDTO);

    /**
     * 用户取消退款
     * @param orderRefundApproveDTO
     * @return
     */
    String logisticsOrderRefundUserCancel(LogisticsOrderRefundApproveDTO orderRefundApproveDTO);

    /**
     * 用户填写物流信息
     * @param orderRefundLogisticsDTO
     * @return
     */
    String submitLogisticsInfo(OrderRefundLogisticsDTO orderRefundLogisticsDTO);

    /**
     * 实物商品退款详情
     * @param orderItemCode
     * @param orderRefundCode
     * @return
     */
    LogisticsRefundItemDetailVO getLogisticsRefundItemDetail(String orderItemCode, String orderRefundCode);


}

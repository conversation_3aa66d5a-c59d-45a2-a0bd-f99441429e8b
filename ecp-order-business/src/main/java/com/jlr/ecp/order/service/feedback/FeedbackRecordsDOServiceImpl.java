package com.jlr.ecp.order.service.feedback;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.order.vo.feedback.OrderFeedbackDimension;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.admin.dashboard.dto.FeedbackStatisticQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.vo.*;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedbackOptionsDTO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedbackDimensionsVO;
import com.jlr.ecp.order.controller.app.feedback.dto.FeedbackSubmitDTO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackConfigDO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackRecordsDO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackSnapshotDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackConfigDOMapper;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackRecordsDOMapper;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackSnapshotDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.feedback.FeedBackEnableStatusEnum;
import com.jlr.ecp.order.enums.feedback.FeedDimensionsTypeEnum;
import com.jlr.ecp.order.enums.order.BusinessNameEnum;
import com.jlr.ecp.order.enums.order.OrderTypeEnum;
import com.jlr.ecp.order.util.redis.RedisCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.databind.type.LogicalType.Collection;
import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 评价记录表ServiceImpl
 */
@Service
@Slf4j
public class FeedbackRecordsDOServiceImpl extends ServiceImpl<FeedbackRecordsDOMapper, FeedbackRecordsDO> implements FeedbackRecordsDOService {

    @Resource
    private FeedbackConfigDOMapper feedbackConfigDOMapper;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;

    @Resource
    private RedisService redisService;

    @Resource
    private FeedbackSnapshotDOMapper feedbackSnapshotDOMapper;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final  String KEY  = "feedback-order-pm:";

    // 使用FastJSON的TypeReference预编译类型信息
    private static final ThreadLocal<TypeReference<List<OrderFeedbackDimension>>> TYPE_REF_CACHE =
            ThreadLocal.withInitial(() -> new TypeReference<List<OrderFeedbackDimension>>() {});
    private static final String TOTAL = "total";

    // 在类中定义线程池
    private static final ExecutorService JSON_PARSE_EXECUTOR = Executors.newFixedThreadPool(
            //CPU密集型
            Runtime.getRuntime().availableProcessors() * 2,
            new ThreadFactoryBuilder()
                    .setNameFormat("json-parse-pool-%d")
                    .setUncaughtExceptionHandler((t, e) ->
                            log.error("JSON解析线程异常: {}", t.getName(), e))
                    .build()
    );
    @PreDestroy
    public void destroy() {
        if (!JSON_PARSE_EXECUTOR.isShutdown()) {
            JSON_PARSE_EXECUTOR.shutdown();
            try {
                if (!JSON_PARSE_EXECUTOR.awaitTermination(20, TimeUnit.SECONDS)) {
                    JSON_PARSE_EXECUTOR.shutdownNow();
                }
            } catch (InterruptedException e) {
                JSON_PARSE_EXECUTOR.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    @Override
    public Boolean submitFeedback(FeedbackSubmitDTO dto) {

        String feedbackCode = dto.getFeedbackCode();
        try {
            //分布式锁，力度orderCode + 环节
            boolean checkRes = redisTemplate.opsForValue().setIfAbsent(KEY+dto.getOrderCode()+dto.getFeedbackDimensions(), "1", 1, TimeUnit.MINUTES);
            if (!checkRes) {
                log.info("重复提交评价feedbackCode:{}", feedbackCode);
                return false;
            }
            FeedbackConfigDO feedbackConfigDO = feedbackConfigDOMapper.selectOneByFeedBackCode(feedbackCode);
            if (feedbackConfigDO == null){
                log.info("没有对应的评价配置,提交失败,feedbackCode:{}",feedbackCode);
                return false;
            }

            if(!FeedBackEnableStatusEnum.ENABLE.getCode().equals(feedbackConfigDO.getEnableStatus())){
                log.info("该评价配置不是启用状态,feedbackConfigDO:{}",feedbackConfigDO);
                return false;
            }

            String orderCode = dto.getOrderCode();
            OrderInfoDO orderInfoDO = orderInfoDOMapper.queryOrderDoByOrderCode(orderCode);
            List<String> orderCodes = new ArrayList<>();
            if(orderInfoDO == null){
                log.info("改评价关联的订单不存在orderCode:{}",orderCode);
                return false;
            }

            if (OrderTypeEnum.PARENT.getCode().equals(orderInfoDO.getOrderType())){
                log.info("该评价关联的订单是父订单,orderCode:{}",orderCode);
                List<OrderInfoDO> orderCodeList = orderInfoDOMapper.queryVCSSubOrderInfoByParentCode(orderCode, RedisCommonUtil.findBusinessKeysWithValue(redisService,Constants.BUSINESS_CACHE_KEY,BusinessNameEnum.VCS.getName()));
                if(CollUtil.isNotEmpty(orderCodeList)){
                    orderCodes = orderCodeList.stream().map(OrderInfoDO::getOrderCode).collect(Collectors.toList());
                }
            }else {
                orderCodes.add(orderInfoDO.getOrderCode());
            }
            List<OrderFeedbackDimension> dimensionsContent = dto.getDimensionsContent();
            //校验dimensionsContent
            checkResult(dimensionsContent);
            String jsonString = JSON.toJSONString(dimensionsContent);
            List<FeedbackRecordsDO> list = new ArrayList<>();
            for (String subCode : orderCodes) {
                FeedbackRecordsDO feedbackRecordsDO = new FeedbackRecordsDO();
                feedbackRecordsDO.setFeedbackRecordsCode(ecpIdUtil.nextIdStr());
                BeanUtil.copyProperties(dto,feedbackRecordsDO);
                feedbackRecordsDO.setOrderCode(subCode);
                feedbackRecordsDO.setFeedbackJson(jsonString);
                list.add(feedbackRecordsDO);
            }

            baseMapper.insertBatch(list);
        }catch (Exception e){
            log.error("提交评价异常",e);
        }
        return true;
    }

    public Map<String, Object> scoreCount(FeedbackStatisticQueryDTO dto) {
        // 1. 获取并验证快照数据
        FeedbackSnapshotDO feedbackSnapshotDO = feedbackSnapshotDOMapper.selectOneBySnapshotCode(dto.getSnapshotCode());
        if (feedbackSnapshotDO == null) {
            log.info("历史版本号不对");
            return null;
        }

        // 2. 解析维度数据
        List<FeedbackDimensionsVO> feedbackDimensionsVOList = JSON.parseArray(feedbackSnapshotDO.getSnapshotJson(), FeedbackDimensionsVO.class);
        List<String> starRatingName = getStarRatingNames(feedbackDimensionsVOList);
        List<FeedbackDimensionsVO> singleChoiceName = getSingleChoiceNames(feedbackDimensionsVOList);
        List<FeedbackDimensionsVO> multipleChoiceName = getMultipleChoiceNames(feedbackDimensionsVOList);

        // 3. 查询评价记录
        List<FeedbackRecordsDO> list = queryFeedbackRecords(dto);

        // 4. 初始化时间轴数据
        LocalDateTime startTime = feedbackSnapshotDO.getEnableTime();
        LocalDateTime endTime = feedbackSnapshotDO.getDowntime() == null ? LocalDateTime.now() : feedbackSnapshotDO.getDowntime();

        // 忽略时分秒，仅比较日期部分
        LocalDateTime startDate = startTime.withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endDate = endTime.withHour(0).withMinute(0).withSecond(0);

        // 计算日期差
        long duration = ChronoUnit.DAYS.between(startDate, endDate);

        List<String> xAxisData = new ArrayList<>();
        List<LocalDateTime> timePoints = new ArrayList<>();
        initializeTimeAxis(startDate, endDate, duration, xAxisData, timePoints);

        // 5. 初始化统计数据结构
        Map<String, Map<LocalDateTime, List<BigDecimal>>> dimensionTimeScores = initializeDimensionTimeScores(starRatingName, timePoints);
        Map<String, BigDecimal> resultMap = new HashMap<>();
        Map<String, Map<String, Integer>> singleChoiceStats = new HashMap<>();
        Map<String, Map<String, Integer>> multipleChoiceStats = new HashMap<>();
        Map<String, Integer> starRatingCountMap = new HashMap<>();

        // 6. 处理评价记录数据
        processRecords(list, starRatingName, singleChoiceName, multipleChoiceName, duration,
                dimensionTimeScores, resultMap, singleChoiceStats, multipleChoiceStats, starRatingCountMap);

        // 7. 构建评分结果
        List<ScoreVO> scoreVOList = buildScoreResults(list.size(), resultMap, starRatingName, starRatingCountMap);

        // 8. 构建图表数据
        List<FeedbackPieChartRespVo> pieChartRespVo = buildPieChartData(singleChoiceName, singleChoiceStats);
        List<MultiChoiceBarChartRespVo> multiChoiceBarChartRespVo = buildBarChartData(multipleChoiceName, multipleChoiceStats);
        FeedbackLineChartDataVO feedbackLineChartDataVO = buildLineChartData(starRatingName, timePoints,
                dimensionTimeScores, xAxisData);

        // 9. 构建表格数据
        TableRespVo tableRespVo = convertLineChartDataToTableRespVo(feedbackLineChartDataVO.getList(), xAxisData);

        // 10. 返回结果
        return buildFinalResult(scoreVOList, pieChartRespVo, multiChoiceBarChartRespVo,
                feedbackLineChartDataVO, tableRespVo);
    }


    private List<String> getStarRatingNames(List<FeedbackDimensionsVO> feedbackDimensionsVOList) {
        return feedbackDimensionsVOList.stream()
                .filter(vo -> vo.getType().equals(FeedDimensionsTypeEnum.STAR_RATING.getCode()))
                .map(FeedbackDimensionsVO::getName)
                .collect(Collectors.toList());
    }

    private List<FeedbackDimensionsVO> getSingleChoiceNames(List<FeedbackDimensionsVO> feedbackDimensionsVOList) {
        return feedbackDimensionsVOList.stream()
                .filter(vo -> vo.getType().equals(FeedDimensionsTypeEnum.SINGLE_CHOICE.getCode()))
                .collect(Collectors.toList());
    }

    private List<FeedbackDimensionsVO> getMultipleChoiceNames(List<FeedbackDimensionsVO> feedbackDimensionsVOList) {
        return feedbackDimensionsVOList.stream()
                .filter(vo -> vo.getType().equals(FeedDimensionsTypeEnum.MULTIPLE_CHOICE.getCode()))
                .collect(Collectors.toList());
    }

    private List<FeedbackRecordsDO> queryFeedbackRecords(FeedbackStatisticQueryDTO dto) {
        return baseMapper.selectList(new LambdaQueryWrapperX<FeedbackRecordsDO>()
                .eq(FeedbackRecordsDO::getFeedbackDimensions, dto.getFeedbackDimensions())
                .likeIfPresent(FeedbackRecordsDO::getOrderCode, dto.getOrderChannel())
                .eq(FeedbackRecordsDO::getSnapshotCode, dto.getSnapshotCode())
                .eq(BaseDO::getIsDeleted, false)
                .select(FeedbackRecordsDO::getId,FeedbackRecordsDO::getSnapshotCode,FeedbackRecordsDO::getFeedbackJson
                        ,FeedbackRecordsDO::getTotal,BaseDO::getCreatedTime));
    }

    //初始化X轴数据
    private void initializeTimeAxis(LocalDateTime startTime, LocalDateTime endTime, long duration,
                                    List<String> xAxisData, List<LocalDateTime> timePoints) {
        DateTimeFormatter formatter;
        if (duration < 1) { // 按小时对齐
            formatter = DateTimeFormatter.ofPattern("h a");
            for (int i = 0; i <= 23; i++) {
                LocalDateTime time = startTime.withHour(i).withMinute(0).withSecond(0);
                timePoints.add(time);
                xAxisData.add(time.format(formatter));
            }
        } else if (duration < 31) { // 按天对齐
            formatter = DateTimeFormatter.ofPattern("MMM dd", Locale.ENGLISH);
            for (int i = 0; i <= duration; i++) {
                LocalDateTime time = startTime.withHour(0).withMinute(0).withSecond(0).plusDays(i);
                timePoints.add(time);
                xAxisData.add(time.format(formatter));
            }
        } else if (duration < 366) { // 按月对齐
            formatter = DateTimeFormatter.ofPattern("yyyy/MM");
            for (LocalDateTime time = startTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
                 !time.isAfter(endTime); time = time.plusMonths(1)) {
                timePoints.add(time);
                xAxisData.add(time.format(formatter));
            }
        } else { // 按年对齐
            formatter = DateTimeFormatter.ofPattern("yyyy");
            for (LocalDateTime time = startTime.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0);
                 !time.isAfter(endTime); time = time.plusYears(1)) {
                timePoints.add(time);
                xAxisData.add(time.format(formatter));
            }
        }
//        log.info("xAxisData: {}", xAxisData); // 打印时间轴数据
//        log.info("timePoints: {}", timePoints); // 打印时间点
    }




    private Map<String, Map<LocalDateTime, List<BigDecimal>>> initializeDimensionTimeScores(
            List<String> starRatingName, List<LocalDateTime> timePoints) {
        Map<String, Map<LocalDateTime, List<BigDecimal>>> dimensionTimeScores = new HashMap<>();
        for (String name : starRatingName) {
            Map<LocalDateTime, List<BigDecimal>> timeScores = new HashMap<>();
            for (LocalDateTime time : timePoints) {
                timeScores.put(time, new ArrayList<>());
            }
            dimensionTimeScores.put(name, timeScores);
        }
        return dimensionTimeScores;
    }

    private void processRecords(List<FeedbackRecordsDO> list, List<String> starRatingName,
                                List<FeedbackDimensionsVO> singleChoiceName, List<FeedbackDimensionsVO> multipleChoiceName,
                                long duration, Map<String, Map<LocalDateTime, List<BigDecimal>>> dimensionTimeScores,
                                Map<String, BigDecimal> resultMap, Map<String, Map<String, Integer>> singleChoiceStats,
                                Map<String, Map<String, Integer>> multipleChoiceStats, Map<String, Integer> starRatingCountMap) {
        // 1. 分批处理
        List<List<FeedbackRecordsDO>> batches = Lists.partition(list, 1000);

        // 2. 异步分批解析
        List<CompletableFuture<List<List<OrderFeedbackDimension>>>> batchFutures = batches.stream()
                .map(batch -> CompletableFuture.supplyAsync(
                        () -> parseBatchJson(batch),
                        JSON_PARSE_EXECUTOR
                )).collect(Collectors.toList());

        // 3. 合并结果（保持原有顺序）
        List<List<OrderFeedbackDimension>> batchDimensions = batchFutures.stream()
                .map(future -> {
                    try {
                        return future.get(5, TimeUnit.SECONDS);
                    } catch (Exception e) {
                        log.error("批次解析失败", e);
                        return Collections.<List<OrderFeedbackDimension>>emptyList();
                    }
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());

        for (int i = 0; i < list.size(); i++) {
            FeedbackRecordsDO record = list.get(i);
            List<OrderFeedbackDimension> dimensions = batchDimensions.get(i);
//            log.info("dimensions: {}", dimensions); // 打印评价记录数据

            // 处理总分
            int total = record.getTotal();
            resultMap.put(TOTAL, resultMap.getOrDefault(TOTAL, BigDecimal.ZERO).add(new BigDecimal(total)));

            // 获取调整后的时间点
            LocalDateTime adjustedTime = getAdjustedTime(record.getCreatedTime(), duration);
//            log.info("adjustedTime: {}", adjustedTime); // 打印调整后的时间点

            // 处理总分统计
            dimensionTimeScores.computeIfAbsent(TOTAL, k -> new HashMap<>())
                    .computeIfAbsent(adjustedTime, k -> new ArrayList<>())
                    .add(new BigDecimal(total));

            // 处理星级评分
            processStarRating(dimensions, starRatingName, adjustedTime, dimensionTimeScores, resultMap, starRatingCountMap);

            // 处理单选题
            processSingleChoice(dimensions, singleChoiceName, singleChoiceStats);

            // 处理多选题
            processMultipleChoice(dimensions, multipleChoiceName, multipleChoiceStats);
        }
//        log.info("dimensionTimeScores: {}", dimensionTimeScores); // 打印维度时间评分数据
    }
    private List<List<OrderFeedbackDimension>> parseBatchJson(List<FeedbackRecordsDO> batch) {
        return batch.stream()
                .map(record -> {
                    try {
                        return JSON.parseObject(
                                record.getFeedbackJson(),
                                TYPE_REF_CACHE.get()
                        );
                    } catch (Exception e) {
                        log.error("JSON解析失败 recordId: {}", record.getId(), e);
                        return Collections.<OrderFeedbackDimension>emptyList();
                    } finally {
                        // 确保每次解析后清理
                         TYPE_REF_CACHE.remove();
        }
                })
                .collect(Collectors.toList());
    }
    private LocalDateTime getAdjustedTime(LocalDateTime recordTime, long duration) {
        LocalDateTime adjustedTime;
        if (duration < 1) { // 按小时对齐
            adjustedTime = recordTime.withMinute(0).withSecond(0);
        } else if (duration < 31) { // 按天对齐
            adjustedTime = recordTime.withHour(0).withMinute(0).withSecond(0);
        } else if (duration < 366) { // 按月对齐
            adjustedTime = recordTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        } else { // 按年对齐
            adjustedTime = recordTime.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0);
        }
//        log.info("recordTime: {}, adjustedTime: {}", recordTime, adjustedTime); // 打印原始时间和调整后的时间
        return adjustedTime;
    }


    private void processStarRating(List<OrderFeedbackDimension> dimensions, List<String> starRatingName,
                                   LocalDateTime adjustedTime, Map<String, Map<LocalDateTime, List<BigDecimal>>> dimensionTimeScores,
                                   Map<String, BigDecimal> resultMap, Map<String, Integer> starRatingCountMap) {

        for (OrderFeedbackDimension dimension : dimensions) {
            if (starRatingName.contains(dimension.getName())
                    && FeedDimensionsTypeEnum.STAR_RATING.getCode().equals(dimension.getType())
                    && CollUtil.isNotEmpty(dimension.getResult())
                    && !Integer.valueOf(dimension.getResult().get(0)).equals(0)) {

                BigDecimal score = new BigDecimal(dimension.getResult().get(0));
                resultMap.put(dimension.getName(), resultMap.getOrDefault(dimension.getName(), BigDecimal.ZERO).add(score));
                starRatingCountMap.put(dimension.getName(), starRatingCountMap.getOrDefault(dimension.getName(), 0) + 1);

                Map<LocalDateTime, List<BigDecimal>> timeScores = dimensionTimeScores.get(dimension.getName());
                if (timeScores != null && timeScores.containsKey(adjustedTime)) {
                    timeScores.get(adjustedTime).add(score);
                }
            }
        }
    }

    private void processSingleChoice(List<OrderFeedbackDimension> dimensions,
                                     List<FeedbackDimensionsVO> singleChoiceName,
                                     Map<String, Map<String, Integer>> singleChoiceStats) {

        for (FeedbackDimensionsVO feedbackDimensionsVO : singleChoiceName) {
            for (OrderFeedbackDimension dimension : dimensions) {
                if (dimension.getName().equals(feedbackDimensionsVO.getName())
                        && FeedDimensionsTypeEnum.SINGLE_CHOICE.getCode().equals(dimension.getType())
                        && CollUtil.isNotEmpty(dimension.getResult())) {
                    String selectedOption = dimension.getResult().get(0);
                    singleChoiceStats.computeIfAbsent(feedbackDimensionsVO.getName(), k -> new HashMap<>())
                            .put(selectedOption, singleChoiceStats.getOrDefault(feedbackDimensionsVO.getName(), new HashMap<>())
                                    .getOrDefault(selectedOption, 0) + 1);
                }
            }
        }
    }

    private void processMultipleChoice(List<OrderFeedbackDimension> dimensions,
                                       List<FeedbackDimensionsVO> multipleChoiceName,
                                       Map<String, Map<String, Integer>> multipleChoiceStats) {

        for (FeedbackDimensionsVO feedbackDimensionsVO : multipleChoiceName) {
            for (OrderFeedbackDimension dimension : dimensions) {
                if (dimension.getName().equals(feedbackDimensionsVO.getName())
                        && FeedDimensionsTypeEnum.MULTIPLE_CHOICE.getCode().equals(dimension.getType())
                        && CollUtil.isNotEmpty(dimension.getResult())) {
                    for (String selectedOption : dimension.getResult()) {
                        multipleChoiceStats.computeIfAbsent(feedbackDimensionsVO.getName(), k -> new HashMap<>())
                                .put(selectedOption, multipleChoiceStats.getOrDefault(feedbackDimensionsVO.getName(), new HashMap<>())
                                        .getOrDefault(selectedOption, 0) + 1);
                    }
                }
            }
        }
    }

    private List<ScoreVO> buildScoreResults(int size, Map<String, BigDecimal> resultMap,
                                            List<String> starRatingName, Map<String, Integer> starRatingCountMap) {
        List<ScoreVO> scoreVOList = new ArrayList<>();
        ScoreVO scoreVO = new ScoreVO();
        scoreVO.setTitle(TOTAL);
        if(size >  0) {
            scoreVO.setScore(resultMap.getOrDefault(TOTAL, BigDecimal.ZERO).divide(new BigDecimal(size), 1, RoundingMode.HALF_UP).toString());
        }else {
            scoreVO.setScore("-");
        }
        scoreVOList.add(scoreVO);
        for (String name : starRatingName) {
            ScoreVO starRating = new ScoreVO();
            starRating.setTitle(name);
            starRating.setScore("-");
            int count = starRatingCountMap.getOrDefault(name, 0);
            if (count > 0) {
                BigDecimal avgScore = resultMap.get(name).divide(new BigDecimal(count), 1, RoundingMode.HALF_UP);
                starRating.setScore(avgScore.compareTo(BigDecimal.ZERO) > 0 ? avgScore.toString() : "-");
            }
            scoreVOList.add(starRating);
        }
        return scoreVOList;
    }

    private List<FeedbackPieChartRespVo> buildPieChartData(List<FeedbackDimensionsVO> singleChoiceName,
                                                     Map<String, Map<String, Integer>> singleChoiceStats) {
        List<FeedbackPieChartRespVo> pieChartRespVoList = new ArrayList<>();
        for (FeedbackDimensionsVO feedbackDimensionsVO : singleChoiceName) {
            FeedbackPieChartRespVo pieChartRespVo = new FeedbackPieChartRespVo();
            List<FeedbackPieChartRespVo> data = feedbackDimensionsVO.getOptionList().stream()
                    .map(option -> {
                        FeedbackPieChartRespVo dataVO = new FeedbackPieChartRespVo();
                        dataVO.setName(option.getOption());
                        dataVO.setValue(String.valueOf(singleChoiceStats.getOrDefault(feedbackDimensionsVO.getName(), new HashMap<>())
                                .getOrDefault(option.getOption(), 0)));
                        return dataVO;
                    })
                    .collect(Collectors.toList());
            pieChartRespVo.setData(data);
            pieChartRespVo.setName(feedbackDimensionsVO.getName());
            pieChartRespVoList.add(pieChartRespVo);
        }
        return pieChartRespVoList;
    }

    private List<MultiChoiceBarChartRespVo> buildBarChartData(List<FeedbackDimensionsVO> multipleChoiceName,
                                                        Map<String, Map<String, Integer>> multipleChoiceStats) {
        List<MultiChoiceBarChartRespVo> list = new ArrayList<>();
        for (FeedbackDimensionsVO feedbackDimensionsVO : multipleChoiceName) {
            MultiChoiceBarChartRespVo multiChoiceBarChartRespVo = new MultiChoiceBarChartRespVo();
            multiChoiceBarChartRespVo.setTitle(feedbackDimensionsVO.getName());

            List<String> xData = feedbackDimensionsVO.getOptionList().stream()
                    .map(FeedbackOptionsDTO::getOption)
                    .collect(Collectors.toList());

            List<BigDecimal> seriesData = feedbackDimensionsVO.getOptionList().stream()
                    .map(option -> new BigDecimal(multipleChoiceStats.getOrDefault(feedbackDimensionsVO.getName(), new HashMap<>())
                            .getOrDefault(option.getOption(), 0)))
                    .collect(Collectors.toList());

            multiChoiceBarChartRespVo.setXAxisData(xData);
            multiChoiceBarChartRespVo.setSeriesData(seriesData);
            list.add(multiChoiceBarChartRespVo);
        }
        return list;
    }

    private FeedbackLineChartDataVO buildLineChartData(List<String> starRatingName,
                                                       List<LocalDateTime> timePoints, Map<String, Map<LocalDateTime, List<BigDecimal>>> dimensionTimeScores,
                                                       List<String> xAxisData) {
        FeedbackLineChartDataVO feedbackLineChartDataVO = new FeedbackLineChartDataVO();
        List<LineChartData> lineChartDataList = new ArrayList<>();
        // 处理总分数据
        if (dimensionTimeScores.containsKey(TOTAL)) {
            LineChartData totalLineChartData = new LineChartData();
            List<BigDecimal> totalScores = timePoints.stream()
                    .map(time -> {
                        List<BigDecimal> timeScores = dimensionTimeScores.get(TOTAL).get(time);
                        if (CollUtil.isEmpty(timeScores)) {
                            return BigDecimal.ZERO;
                        }
                        return timeScores.stream()
                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                                .divide(new BigDecimal(timeScores.size()), 1, RoundingMode.HALF_UP);
                    })
                    .collect(Collectors.toList());

            totalLineChartData.setXAxisData(xAxisData);
            totalLineChartData.setTitle(TOTAL);
            totalLineChartData.setSeriesData(totalScores);
            lineChartDataList.add(totalLineChartData);
        }
        // 处理维度评分数据
        for (String dimensionName : starRatingName) {
            LineChartData lineChartData = new LineChartData();
            List<BigDecimal> scores = timePoints.stream()
                    .map(time -> {
                        List<BigDecimal> timeScores = dimensionTimeScores.get(dimensionName).get(time);
                        if (CollUtil.isEmpty(timeScores)) {
                            return BigDecimal.ZERO;
                        }
                        return timeScores.stream()
                                .reduce(BigDecimal.ZERO, BigDecimal::add)
                                .divide(new BigDecimal(timeScores.size()), 1, RoundingMode.HALF_UP);
                    })
                    .collect(Collectors.toList());

            lineChartData.setXAxisData(xAxisData);
            lineChartData.setTitle(dimensionName);
            lineChartData.setSeriesData(scores);
            lineChartDataList.add(lineChartData);
        }



        feedbackLineChartDataVO.setList(lineChartDataList);
        feedbackLineChartDataVO.setXAxisData(xAxisData);
        return feedbackLineChartDataVO;
    }


    private Map<String, Object> buildFinalResult(List<ScoreVO> scoreVOList,
                                                 List<FeedbackPieChartRespVo> pieChartRespVo,
                                                 List<MultiChoiceBarChartRespVo> multiChoiceBarChartRespVo,
                                                 FeedbackLineChartDataVO feedbackLineChartDataVO,
                                                 TableRespVo tableRespVo) {
        Map<String, Object> result = new HashMap<>();
        result.put("scoreResult", scoreVOList);
        result.put("singleChoicePieChart", pieChartRespVo);
        result.put("multiChoiceBarChart", multiChoiceBarChartRespVo);
        //过滤
        FeedbackLineChartDataVO filteredLineChartDataVO = filterLineChartData(feedbackLineChartDataVO);
        result.put("lineChartData", filteredLineChartDataVO);
        result.put("table", tableRespVo);
        return result;
    }

    private FeedbackLineChartDataVO filterLineChartData(FeedbackLineChartDataVO feedbackLineChartDataVO) {
        List<LineChartData> filteredList = feedbackLineChartDataVO.getList().parallelStream()
                .map(lineChartData -> {
                    List<BigDecimal> filteredSeriesData = new ArrayList<>();
                    List<String> filteredXAxisData = new ArrayList<>();

                    for (int i = 0; i < lineChartData.getSeriesData().size(); i++) {
                        if (lineChartData.getSeriesData().get(i).compareTo(BigDecimal.ZERO) > 0) {
                            filteredSeriesData.add(lineChartData.getSeriesData().get(i));
                            filteredXAxisData.add(lineChartData.getXAxisData().get(i));
                        }
                    }

                    if (!filteredSeriesData.isEmpty()) {
                        LineChartData filteredLineChartData = new LineChartData();
                        filteredLineChartData.setTitle(lineChartData.getTitle());
                        filteredLineChartData.setSeriesData(filteredSeriesData);
                        filteredLineChartData.setXAxisData(filteredXAxisData);
                        return filteredLineChartData;
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.info("Original lineChartData: {}", feedbackLineChartDataVO.getList()); // 打印原始数据
        log.info("Filtered lineChartData: {}", filteredList); // 打印过滤后的数据

        FeedbackLineChartDataVO filteredVO = new FeedbackLineChartDataVO();
        filteredVO.setList(filteredList);
        filteredVO.setXAxisData(feedbackLineChartDataVO.getXAxisData());
        return filteredVO;
    }

    public TableRespVo convertLineChartDataToTableRespVo(List<LineChartData> lineChartData, List<String> xAxisData) {
        // 初始化表头
        List<TableRespVo.HeaderItem> headers = new ArrayList<>();
        headers.add(new TableRespVo.HeaderItem("name", "", null));

        // 添加每个时间点作为表头
        for (String time : xAxisData) {
            String prop = generatePropValue(time);
            headers.add(new TableRespVo.HeaderItem(prop, time, null));
        }

        // 初始化表格数据
        List<Map<String, String>> tableData = new ArrayList<>();

        // 遍历每个维度，填充表格数据
        for (LineChartData chartData : lineChartData) {
            Map<String, String> rowData = new HashMap<>();
            rowData.put("name", chartData.getTitle());

            // 遍历每个时间点的数据
            for (int i = 0; i < xAxisData.size(); i++) {
                String time = xAxisData.get(i);
                String prop = generatePropValue(time);

                // 获取当前时间点的评分数据
                if (i < chartData.getSeriesData().size()) {
                    BigDecimal score = chartData.getSeriesData().get(i);
                    rowData.put(prop, score.compareTo(BigDecimal.ZERO) > 0 ? score.toString() : "-");
                } else {
                    rowData.put(prop, "-");
                }
            }

            tableData.add(rowData);
        }

        // 创建并返回 TableRespVo 对象
        return new TableRespVo(headers, tableData);
    }



    private String generatePropValue(String time) {
        // 根据时间格式生成合理的 prop 值
        if (time.contains("/")) { // 年/月格式
            return "month" + time.replace("/", "");
        } else if (time.contains(" ")) { // 小时 a.m. 或 p.m. 格式
            return "hour" + time.replace(" ", "").replace(".", "").toLowerCase();
        } else if (time.length() == 5) { // MMM dd 格式
            return "day" + time.replace(" ", "").toLowerCase();
        } else { // 默认处理其他情况
            return time.toLowerCase().replace(" ", "").replace("/", "");
        }
    }

    private void checkResult(List<OrderFeedbackDimension> dimensionsContent) {
        for (OrderFeedbackDimension orderFeedbackDimension : dimensionsContent) {
            //如果是单选题或者星星 校验result是否大于1个元素
            if (FeedDimensionsTypeEnum.SINGLE_CHOICE.getCode().equals(orderFeedbackDimension.getType())
                ||FeedDimensionsTypeEnum.STAR_RATING.getCode().equals(orderFeedbackDimension.getType())) {
                if(CollUtil.isNotEmpty(orderFeedbackDimension.getResult())
                    && orderFeedbackDimension.getResult().size()>1){
                    throw exception(ErrorCodeConstants.FEEDBACK_RECORDS_SINGLE_CHECK_ERROR);
                }
            }

            //如果是多选题，校验是否有超过10个选项
            if (FeedDimensionsTypeEnum.MULTIPLE_CHOICE.getCode().equals(orderFeedbackDimension.getType())) {
                if(CollUtil.isNotEmpty(orderFeedbackDimension.getResult())
                        && orderFeedbackDimension.getResult().size()>orderFeedbackDimension.getOptionJson().size()){
                    throw exception(ErrorCodeConstants.FEEDBACK_RECORDS_MULTIPLE_CHECK_ERROR);
                }
            }
        }
    }
}

package com.jlr.ecp.order.controller.app.refund;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.order.vo.RefundReasonStatusVO;
import com.jlr.ecp.order.api.refund.dto.BaseOrderRefundApplyDTO;
import com.jlr.ecp.order.controller.app.refund.vo.EcouponRefundItemDetailVO;
import com.jlr.ecp.order.controller.app.refund.vo.RefundDetailVO;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.order.CouponRefundReasonEnum;
import com.jlr.ecp.order.enums.order.OrderTypeEnum;
import com.jlr.ecp.order.enums.order.RefundOrderOperationTypeEnum;
import com.jlr.ecp.order.service.refund.OrderRefundDOService;
import com.jlr.ecp.order.service.refund.ecoupon.EcouponOrderRefundDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Tag(name = "app端 - 退单")
@RestController
@RequestMapping("v1/refund")
@Validated
@Slf4j
public class RefundController {
    @Resource
    private OrderRefundDOService orderRefundService;

    @Resource
    private EcouponOrderRefundDOService ecouponOrderRefundDOService;

    /**
     * 订单详情
     *
     * @param orderCode 订单编号
     * @return RefundDetailRespVO
     */
    @GetMapping("/detail")
    @Operation(summary = "退单详情")
    @Parameter(name = "orderCode", description = "订单编码", required = true)
//    @PermitAll
    CommonResult<RefundDetailVO> getOrderDetail(@RequestParam("orderCode") @NotBlank(message = "订单编码不能为空") String orderCode) {

        RefundDetailVO refundDetailVO = orderRefundService.getOrderRefundDetailByOrderCode(orderCode);
        return CommonResult.success(refundDetailVO);
    }

    @GetMapping("/getRefundDetails")
    @Operation(summary = "电子券退单详情")
    @Parameter(name = "orderItemCode", description = "订单行编码", required = true)
    @Parameter(name = "orderRefundCode", description = "订单退款编码", required = true)
//    @PermitAll
    CommonResult<EcouponRefundItemDetailVO> getRefundDetails(@RequestParam("orderItemCode") @NotBlank(message = "订单行编码不能为空") String orderItemCode
    , @RequestParam("orderRefundCode") @NotBlank(message = "订单退款编码") String orderRefundCode) {
        EcouponRefundItemDetailVO ecouponRefundItemDetailVO = ecouponOrderRefundDOService.getEcouponRefundItemDetail(orderItemCode,orderRefundCode);
        return CommonResult.success(ecouponRefundItemDetailVO);
    }



    /**
     * 取消订单申请
     * @param  baseOrderRefundApplyDTOList
     * @return String
     */
    @PostMapping("/submitRefundApply")
    @Operation(summary = "提交取消订单申请")
    @Parameter(name = "baseOrderRefundApplyDTOList", description = "取消订单申请参数", required = true)
    @PermitAll
    CommonResult<String> applyOrderRefund(@RequestBody @NotEmpty(message = "退款申请对象不能为空")List<BaseOrderRefundApplyDTO> baseOrderRefundApplyDTOList, @RequestHeader(value = "jlrId") String jlrId) {
        //循环设置jlrId到List中
        for (BaseOrderRefundApplyDTO baseOrderRefundApplyDTO : baseOrderRefundApplyDTOList) {
            baseOrderRefundApplyDTO.setJlrId(jlrId);
            baseOrderRefundApplyDTO.setRefundFulfilmentType(OrderTypeEnum.ELECTRONIC_COUPON.getCode());
        }
        String refundOrderCode = ecouponOrderRefundDOService.ecouponOrderRefundApply(baseOrderRefundApplyDTOList, RefundOrderOperationTypeEnum.USER_INITIATED.getCode());
        if (StringUtils.isNotBlank(refundOrderCode)) {
            return CommonResult.success(refundOrderCode);
        }
        return CommonResult.error(ErrorCodeConstants.REFUND_APPLY_FAIL);
    }


    @Deprecated
    @GetMapping("/paymentMock")
    @Operation(summary = "已废弃：支付流转Mock")
    @Parameter(name = "orderCode", description = "订单编码", required = true)
//    @PermitAll
    CommonResult<Integer> paymentMock(
            @RequestParam("orderCode") String orderCode, @RequestParam("parentCode") String parentCode

    ) {
        return CommonResult.success(orderRefundService.paymentMock(orderCode, parentCode));
    }

    @GetMapping("/getRefundReasonList")
    @Operation(summary = "获取所有退款原因选项")
    @PermitAll
    CommonResult<List<RefundReasonStatusVO>> getRefundReasonList() {
        List<RefundReasonStatusVO> orderStatusList= Arrays.stream(CouponRefundReasonEnum.values())
                .map(e -> new RefundReasonStatusVO(e.getCode(), e.getName()))
                .filter(e -> e.getStatus() != CouponRefundReasonEnum.CONSULTATION_AGREED.getCode()
                        && e.getStatus() != CouponRefundReasonEnum.AUTO_REFUND_OVERDUE.getCode() )
                .collect(Collectors.toList());
        return CommonResult.success(orderStatusList);
    }

}

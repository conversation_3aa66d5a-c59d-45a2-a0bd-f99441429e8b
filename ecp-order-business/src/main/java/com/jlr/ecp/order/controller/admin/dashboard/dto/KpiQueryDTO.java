package com.jlr.ecp.order.controller.admin.dashboard.dto;

import com.jlr.ecp.order.enums.dashboard.KpiEnum;
import com.jlr.ecp.order.enums.dashboard.KpiEnumForSales;
import com.jlr.ecp.order.enums.dashboard.KpiEnumForValet;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * KpiQueryDTO
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KpiQueryDTO {
  /**
   * 类型编码
   */
  private Integer code;

  /**
   * 类型名称
   */
  private String desc;

  /**
   * 类型名称英文
   */
  private String descEn;

  /**
   * 类型名称 -for 代客下单
   */
  private String valetDesc;

  /**
   * 单位名称
   */
  private String unit;


  public KpiQueryDTO(KpiEnum kpiEnum) {
    this.code = kpiEnum.getCode();
    this.desc = kpiEnum.getDesc();
    this.descEn = kpiEnum.getDescEn();
    this.valetDesc = kpiEnum.getDescForValet();
    this.unit = kpiEnum.getUnit();
  }

  public KpiQueryDTO(KpiEnumForValet kpiEnum) {
    this.code = kpiEnum.getCode();
    this.desc = kpiEnum.getDesc();
    this.descEn = kpiEnum.getDescEn();
    this.valetDesc = kpiEnum.getDescForValet();
    this.unit = kpiEnum.getUnit();
  }

  public KpiQueryDTO(KpiEnumForSales kpiEnum) {
    this.code = kpiEnum.getCode();
    this.desc = kpiEnum.getDesc();
    this.descEn = kpiEnum.getDescEn();
    this.valetDesc = kpiEnum.getDescForValet();
    this.unit = kpiEnum.getUnit();
  }
}

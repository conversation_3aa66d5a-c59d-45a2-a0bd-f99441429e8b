package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_order_terms
 *
 * <AUTHOR>
 * @TableName t_order_terms
 */
@TableName(value = "t_order_terms")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderTermsDO extends BaseDO {
    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;


    /**
     * 订单号;订单编号
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 订单明细编号
     */
    @TableField(value = "order_item_code")
    private String orderItemCode;

    /**
     * 服务条款编码;服务条款编码，来源于CDT
     */
    @TableField(value = "terms_code")
    private String termsCode;

    /**
     * 签署时间;签署时间
     */
    @TableField(value = "sign_time")
    private LocalDateTime signTime;

    /**
     * 用户编码;用户编码
     */
    @TableField(value = "consumer_code")
    private String consumerCode;

}
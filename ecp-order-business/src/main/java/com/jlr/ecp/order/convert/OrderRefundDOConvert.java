package com.jlr.ecp.order.convert;

import com.jlr.ecp.order.api.refund.dto.OrderRefundDto;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * OrderRefundDOConvert
 *
 * <AUTHOR> <PERSON>
 * @since 2025-03-23 23:26:51
 */
@Mapper(componentModel = "spring")
public interface OrderRefundDOConvert {

    List<OrderRefundDto> toDtoList(List<OrderRefundDO> orderRefundDOS);

    OrderRefundDto toDto(OrderRefundDO orderRefundDO);
}

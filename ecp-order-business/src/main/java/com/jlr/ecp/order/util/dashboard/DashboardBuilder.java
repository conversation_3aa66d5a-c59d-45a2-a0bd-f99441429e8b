package com.jlr.ecp.order.util.dashboard;

import cn.hutool.core.collection.CollUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SummarySqlResultDTO;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ChartRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.TableRespVo;
import com.jlr.ecp.order.enums.order.BrandCodeEnum;
import com.jlr.ecp.order.enums.order.OrderTypeEnum;
import com.jlr.ecp.order.enums.ErrorCodeConstants;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

public class DashboardBuilder {
    private static final String FIRST_COLUMN = "firstColumn";
    private static final String TOTAL = "total";

    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByCount(List<String> tableHeaders, List<SqlResultDTO> resultDTOS, List<String> xAxis,Boolean needTotal) {
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        // 构建图表数据
        ChartRespVo chartRespVo = new ChartRespVo();
        List<OrderTypeEnum> orderTypeEnums = OrderTypeEnum.getDashboardDisplayList();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<Integer, Map<String, Long>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SqlResultDTO::getOrderType,
                        Collectors.toMap(SqlResultDTO::getLabel, SqlResultDTO::getQuantity)
                ));

        for (OrderTypeEnum orderTypeEnum : orderTypeEnums) {
            Map<String, Long> typeDataMap = orderTypeToDataMap.getOrDefault(orderTypeEnum.getCode(), Collections.emptyMap());
            //遍历全部日期，为空则设置默认值0
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, orderTypeEnum.getDashboardDisplay());
            // 计算每一行的总和
            long total = 0;
            for (String date : xAxis) {
                long value = typeDataMap.getOrDefault(date, 0L);
                dataList.add(String.valueOf(value));
                tableItem.put(date, String.valueOf(value));
                total += value;
            }

            // 添加total列
            if(needTotal){
                tableItem.put(TOTAL, String.valueOf(total));
            }
            chartList.add(new ChartRespVo.ChartItemVo(orderTypeEnum.getDashboardDisplay(), dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData,needTotal);
    }


    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByCountForChannel(List<String> tableHeaders, List<SummarySqlResultDTO> resultDTOS, List<String> xAxis) {
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        // 构建图表数据
        ChartRespVo chartRespVo = new ChartRespVo();
        List<BrandCodeEnum> orderChannelEnums = BrandCodeEnum.getDashboardDisplayList();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<String, Map<String, Long>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode,
                        Collectors.toMap(SummarySqlResultDTO::getLabel, SummarySqlResultDTO::getQuantity)
                ));

        for (BrandCodeEnum brandCodeEnum : orderChannelEnums) {
            Map<String, Long> typeDataMap = orderTypeToDataMap.getOrDefault(brandCodeEnum.getBrandCode(), Collections.emptyMap());
            //遍历全部日期，为空则设置默认值0
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, brandCodeEnum.getDisplay());
            // 计算每一行的总和
            long total = 0;
            for (String date : xAxis) {
                long value = typeDataMap.getOrDefault(date, 0L);
                dataList.add(String.valueOf(value));
                tableItem.put(date, String.valueOf(value));
                total += value;
            }

            // 添加total列
            tableItem.put(TOTAL, String.valueOf(total));
            chartList.add(new ChartRespVo.ChartItemVo(brandCodeEnum.getDisplay(), dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData,true);
    }

    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByCountForSeriesCode(List<String> tableHeaders, List<SummarySqlResultDTO> resultDTOS, List<String> xAxis) {
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        // 构建图表数据
        ChartRespVo chartRespVo = new ChartRespVo();
        List<BrandCodeEnum> orderChannelEnums = BrandCodeEnum.getDashboardDisplayList();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<String, Map<String, Long>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode,
                        Collectors.toMap(SummarySqlResultDTO::getLabel, SummarySqlResultDTO::getQuantity)
                ));

        List<String> seriesCodes = resultDTOS.stream().map(SummarySqlResultDTO::getCode).distinct().collect(Collectors.toList());

        for (String seriesCode : seriesCodes) {
            Map<String, Long> typeDataMap = orderTypeToDataMap.getOrDefault(seriesCode, Collections.emptyMap());
            //遍历全部日期，为空则设置默认值0
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, seriesCode);
            // 计算每一行的总和
            long total = 0;
            for (String date : xAxis) {
                long value = typeDataMap.getOrDefault(date, 0L);
                dataList.add(String.valueOf(value));
                tableItem.put(date, String.valueOf(value));
                total += value;
            }

            // 添加total列
            tableItem.put(TOTAL, String.valueOf(total));
            chartList.add(new ChartRespVo.ChartItemVo(seriesCode, dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData,true);
    }

    /**
     * resultDTOS 包含以下数据：
     * [
     *     {"code": "ProductA", "label": "2024", "quantity": 100, "amount": 5000},
     *     {"code": "ProductB", "label": "2024", "quantity": 200, "amount": 10000},
     *     {"code": "ProductA", "label": "2025", "quantity": 50, "amount": 2500}
     * ]
     *
     * 预期输出：
     * 图表数据：
     * [
     *     {"name": "ProductA", "data": ["100", "50"]},
     *     {"name": "ProductB", "data": ["200", "0"]}
     * ]
     *
     * 表格数据：
     * [
     *     {"商品名称": "ProductA", "2024": "100", "2025": "50", "总计": "150"},
     *     {"商品名称": "ProductB", "2024": "200", "2025": "0", "总计": "200"}
     * ]
     */
    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByCountForProductName(
            List<String> tableHeaders,
            List<SummarySqlResultDTO> resultDTOS,
            List<String> xAxis) {

        // 如果查询结果为空，返回错误信息
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }

        // 初始化返回对象
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        ChartRespVo chartRespVo = new ChartRespVo();
        chartRespVo.setXAxis(tableHeaders); // 设置X轴（日期分组）

        // 初始化图表数据和表格数据
        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 按商品名称分组，构建商品名称 -> {日期 -> 数量} 的映射
        Map<String, Map<String, Long>> productNameToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode, // 商品名称
                        Collectors.toMap(SummarySqlResultDTO::getLabel, SummarySqlResultDTO::getQuantity)
                ));

        // 获取所有商品名称
        List<String> productNames = resultDTOS.stream()
                .map(SummarySqlResultDTO::getCode)
                .distinct()
                .collect(Collectors.toList());

        // 遍历每个商品名称，生成图表和表格数据
        for (String productName : productNames) {
            Map<String, Long> typeDataMap = productNameToDataMap.getOrDefault(productName, Collections.emptyMap());

            // 初始化当前商品的图表数据和表格数据
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, productName); // 第一列是商品名称

            // 计算每一行的总和
            long total = 0;

            // 遍历所有日期分组
            for (String date : xAxis) {
                long value = typeDataMap.getOrDefault(date, 0L); // 如果没有数据，默认值为0
                dataList.add(String.valueOf(value)); // 添加到图表数据
                tableItem.put(date, String.valueOf(value)); // 添加到表格数据
                total += value; // 累加总和
            }

            // 添加总计列
            tableItem.put(TOTAL, String.valueOf(total));

            // 将当前商品的数据添加到图表和表格中
            chartList.add(new ChartRespVo.ChartItemVo(productName, dataList));
            tableData.add(tableItem);
        }

        // 构建最终的返回对象
        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData, true);
    }

    public static CommonResult<ProductSalesTrendRespVo> buildProductSalesTrendRespVo(List<String> xAxis, ChartRespVo chartRespVo, List<ChartRespVo.ChartItemVo> chartList, ProductSalesTrendRespVo productSalesTrendRespVo, List<Map<String, String>> tableData,Boolean needTotal) {
        chartRespVo.setDataList(chartList);
        productSalesTrendRespVo.setChart(chartRespVo);

        // 构建表格数据
        TableRespVo tableRespVo = new TableRespVo();

        List<TableRespVo.HeaderItem> headerItems = xAxis.stream()
                .map(date -> {
                    TableRespVo.HeaderItem headerItem = new TableRespVo.HeaderItem();
                    headerItem.setLabel(date);
                    headerItem.setProp(date);
                    return headerItem;
                })
                .collect(Collectors.toList());
        // 表格第一列标题为空
        headerItems.add(0, new TableRespVo.HeaderItem(FIRST_COLUMN, "", null));
        if(needTotal){
            headerItems.add(1, new TableRespVo.HeaderItem(TOTAL, "", null));
        }
        tableRespVo.setHeaders(headerItems);

        tableRespVo.setTableData(tableData);
        productSalesTrendRespVo.setTable(tableRespVo);
        return CommonResult.success(productSalesTrendRespVo);
    }




    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByAmount(List<String> tableHeaders, List<SqlResultDTO> resultDTOS, List<String> xAxis,Boolean needTotal) {
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        // 构建图表数据
        ChartRespVo chartRespVo = new ChartRespVo();
        List<OrderTypeEnum> orderTypeEnums = OrderTypeEnum.getDashboardDisplayList();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<Integer, Map<String, BigDecimal>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SqlResultDTO::getOrderType,
                        Collectors.toMap(
                                SqlResultDTO::getLabel,
                                dto -> new BigDecimal(dto.getAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                        )
                ));

        for (OrderTypeEnum orderTypeEnum : orderTypeEnums) {
            Map<String, BigDecimal> typeDataMap = orderTypeToDataMap.getOrDefault(orderTypeEnum.getCode(), Collections.emptyMap());
            //遍历全部日期，为空则设置默认值0
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, orderTypeEnum.getDashboardDisplay());
            BigDecimal total = BigDecimal.ZERO;
            for (String date : xAxis) {
                BigDecimal value = typeDataMap.getOrDefault(date, BigDecimal.ZERO);
                dataList.add(value.toString());
                tableItem.put(date, value.toString());
                total = total.add(value);
            }

            // 添加total列
            if(needTotal){
                tableItem.put(TOTAL, total.setScale(2, RoundingMode.HALF_UP).toString());
            }
            chartList.add(new ChartRespVo.ChartItemVo(orderTypeEnum.getDashboardDisplay(), dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData,needTotal);
    }


    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByAmountForChannel(List<String> tableHeaders, List<SummarySqlResultDTO> resultDTOS, List<String> xAxis) {
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        // 构建图表数据
        ChartRespVo chartRespVo = new ChartRespVo();
        List<BrandCodeEnum> orderTypeEnums = BrandCodeEnum.getDashboardDisplayList();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<String, Map<String, BigDecimal>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode,
                        Collectors.toMap(
                                SummarySqlResultDTO::getLabel,
                                dto -> new BigDecimal(dto.getAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                        )
                ));

        for (BrandCodeEnum orderTypeEnum : orderTypeEnums) {
            Map<String, BigDecimal> typeDataMap = orderTypeToDataMap.getOrDefault(orderTypeEnum.getBrandCode(), Collections.emptyMap());
            //遍历全部日期，为空则设置默认值0
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, orderTypeEnum.getDisplay());
            // 计算每一行的总和
            BigDecimal total = BigDecimal.ZERO;
            for (String date : xAxis) {
                BigDecimal value = typeDataMap.getOrDefault(date, BigDecimal.ZERO);
                dataList.add(value.toString());
                tableItem.put(date, value.toString());
                total = total.add(value);
            }

            // 添加total列
            tableItem.put(TOTAL, total.setScale(2, RoundingMode.HALF_UP).toString());

            chartList.add(new ChartRespVo.ChartItemVo(orderTypeEnum.getDisplay(), dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData,true);
    }

    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByAmountForSeriesCode(List<String> tableHeaders, List<SummarySqlResultDTO> resultDTOS, List<String> xAxis) {
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        // 构建图表数据
        ChartRespVo chartRespVo = new ChartRespVo();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<String, Map<String, BigDecimal>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode,
                        Collectors.toMap(
                                SummarySqlResultDTO::getLabel,
                                dto -> new BigDecimal(dto.getAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                        )
                ));
        List<String> seriesCodes = resultDTOS.stream().map(SummarySqlResultDTO::getCode).distinct().collect(Collectors.toList());

        for (String seriesCode : seriesCodes) {
            Map<String, BigDecimal> typeDataMap = orderTypeToDataMap.getOrDefault(seriesCode, Collections.emptyMap());
            //遍历全部日期，为空则设置默认值0
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, seriesCode);
            // 计算每一行的总和
            BigDecimal total = BigDecimal.ZERO;
            for (String date : xAxis) {
                BigDecimal value = typeDataMap.getOrDefault(date, BigDecimal.ZERO);
                dataList.add(value.toString());
                tableItem.put(date, value.toString());
                total = total.add(value);
            }

            // 添加total列
            tableItem.put(TOTAL, total.setScale(2, RoundingMode.HALF_UP).toString());

            chartList.add(new ChartRespVo.ChartItemVo(seriesCode, dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData,true);
    }

    /**
     * 测试数据：
     * [
     *     {"code": "ProductA", "label": "2024", "quantity": 100, "amount": 5000},
     *     {"code": "ProductB", "label": "2024", "quantity": 200, "amount": 10000},
     *     {"code": "ProductA", "label": "2025", "quantity": 50, "amount": 2500}
     * ]
     * 预期输出：
     * 图表数据：
     *[
     *     {"name": "ProductA", "data": ["50.00", "25.00"]},
     *     {"name": "ProductB", "data": ["100.00", "0.00"]}
     * ]
     * 表格数据：
     * [
     *     {"商品名称": "ProductA", "2024": "50.00", "2025": "25.00", "总计": "75.00"},
     *     {"商品名称": "ProductB", "2024": "100.00", "2025": "0.00", "总计": "100.00"}
     * ]
     */
    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByAmountForProductName(
            List<String> tableHeaders,
            List<SummarySqlResultDTO> resultDTOS,
            List<String> xAxis) {

        // 如果查询结果为空，返回错误信息
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }

        // 初始化返回对象
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        ChartRespVo chartRespVo = new ChartRespVo();
        chartRespVo.setXAxis(tableHeaders); // 设置X轴（日期分组）

        // 初始化图表数据和表格数据
        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 按商品名称分组，构建商品名称 -> {日期 -> 金额} 的映射
        Map<String, Map<String, BigDecimal>> productNameToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode, // 商品名称
                        Collectors.toMap(
                                SummarySqlResultDTO::getLabel,
                                dto -> new BigDecimal(dto.getAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                        )
                ));

        // 获取所有商品名称
        List<String> productNames = resultDTOS.stream()
                .map(SummarySqlResultDTO::getCode)
                .distinct()
                .collect(Collectors.toList());

        // 遍历每个商品名称，生成图表和表格数据
        for (String productName : productNames) {
            Map<String, BigDecimal> typeDataMap = productNameToDataMap.getOrDefault(productName, Collections.emptyMap());

            // 初始化当前商品的图表数据和表格数据
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, productName); // 第一列是商品名称

            // 计算每一行的总和
            BigDecimal total = BigDecimal.ZERO;

            // 遍历所有日期分组
            for (String date : xAxis) {
                BigDecimal value = typeDataMap.getOrDefault(date, BigDecimal.ZERO); // 如果没有数据，默认值为0
                dataList.add(value.toString()); // 添加到图表数据
                tableItem.put(date, value.toString()); // 添加到表格数据
                total = total.add(value); // 累加总和
            }

            // 添加总计列
            tableItem.put(TOTAL, total.setScale(2, RoundingMode.HALF_UP).toString());

            // 将当前商品的数据添加到图表和表格中
            chartList.add(new ChartRespVo.ChartItemVo(productName, dataList));
            tableData.add(tableItem);
        }

        // 构建最终的返回对象
        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData, true);
    }


    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByPer(List<String> tableHeaders, List<SqlResultDTO> resultDTOS, List<String> xAxis,Boolean needTotal) {
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        // 构建图表数据
        ChartRespVo chartRespVo = new ChartRespVo();
        List<OrderTypeEnum> orderTypeEnums = OrderTypeEnum.getDashboardDisplayList();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<Integer, Map<String, BigDecimal>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SqlResultDTO::getOrderType,
                        Collectors.toMap(SqlResultDTO::getLabel, dto ->
                                BigDecimal.valueOf(dto.getAmount()).divide(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(dto.getQuantity()), RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP))
                ));

        for (OrderTypeEnum orderTypeEnum : orderTypeEnums) {
            Map<String, BigDecimal> typeDataMap = orderTypeToDataMap.getOrDefault(orderTypeEnum.getCode(), Collections.emptyMap());
            //遍历全部日期，为空则设置默认值0
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, orderTypeEnum.getDashboardDisplay());
            // 计算每一行的总和
            BigDecimal total = BigDecimal.ZERO;
            for (String date : xAxis) {
                BigDecimal value = typeDataMap.getOrDefault(date, BigDecimal.ZERO);
                dataList.add(value.toString());
                tableItem.put(date, value.toString());
                total = total.add(value);
            }

            // 添加total列
            if(needTotal){
                tableItem.put(TOTAL, total.setScale(2, RoundingMode.HALF_UP).toString());
            }
            chartList.add(new ChartRespVo.ChartItemVo(orderTypeEnum.getDashboardDisplay(), dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData,needTotal);
    }


    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByPerForChannel(List<String> tableHeaders, List<SummarySqlResultDTO> resultDTOS, List<String> xAxis) {
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        // 构建图表数据
        ChartRespVo chartRespVo = new ChartRespVo();
        List<BrandCodeEnum> orderTypeEnums = BrandCodeEnum.getDashboardDisplayList();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<String, Map<String, BigDecimal>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode,
                        Collectors.toMap(SummarySqlResultDTO::getLabel, dto ->
                                BigDecimal.valueOf(dto.getAmount()).divide(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(dto.getQuantity()), RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP))
                ));

        for (BrandCodeEnum orderTypeEnum : orderTypeEnums) {
            Map<String, BigDecimal> typeDataMap = orderTypeToDataMap.getOrDefault(orderTypeEnum.getBrandCode(), Collections.emptyMap());
            //遍历全部日期，为空则设置默认值0
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, orderTypeEnum.getDisplay());

            // 计算每一行的总和
            BigDecimal total = BigDecimal.ZERO;
            for (String date : xAxis) {
                BigDecimal value = typeDataMap.getOrDefault(date, BigDecimal.ZERO);
                dataList.add(value.toString());
                tableItem.put(date, value.toString());
                total = total.add(value);
            }

            // 添加total列
            tableItem.put(TOTAL, total.setScale(2, RoundingMode.HALF_UP).toString());

            chartList.add(new ChartRespVo.ChartItemVo(orderTypeEnum.getDisplay(), dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData,true);
    }

    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByPerForSeriesCode(List<String> tableHeaders, List<SummarySqlResultDTO> resultDTOS, List<String> xAxis) {
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        // 构建图表数据
        ChartRespVo chartRespVo = new ChartRespVo();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();
        List<String> seriesCodes = resultDTOS.stream().map(SummarySqlResultDTO::getCode).distinct().collect(Collectors.toList());

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<String, Map<String, BigDecimal>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode,
                        Collectors.toMap(SummarySqlResultDTO::getLabel, dto ->
                                BigDecimal.valueOf(dto.getAmount()).divide(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(dto.getQuantity()), RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP))
                ));

        for (String seriesCode : seriesCodes) {
            Map<String, BigDecimal> typeDataMap = orderTypeToDataMap.getOrDefault(seriesCode, Collections.emptyMap());
            //遍历全部日期，为空则设置默认值0
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, seriesCode);
            // 计算每一行的总和
            BigDecimal total = BigDecimal.ZERO;
            for (String date : xAxis) {
                BigDecimal value = typeDataMap.getOrDefault(date, BigDecimal.ZERO);
                dataList.add(value.toString());
                tableItem.put(date, value.toString());
                total = total.add(value);
            }

            // 添加total列
            tableItem.put(TOTAL, total.setScale(2, RoundingMode.HALF_UP).toString());

            chartList.add(new ChartRespVo.ChartItemVo(seriesCode, dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData,true);
    }

    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByIncome(List<String> tableHeaders, List<SqlResultDTO> resultDTOS,
                                                                                            List<String> xAxis, List<SqlResultDTO> refundResultDTOS,Boolean needTotal) {
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        // 构建图表数据
        ChartRespVo chartRespVo = new ChartRespVo();
        List<OrderTypeEnum> orderTypeEnums = OrderTypeEnum.getDashboardDisplayList();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<Integer, Map<String, Long>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SqlResultDTO::getOrderType,
                        Collectors.toMap(SqlResultDTO::getLabel, SqlResultDTO::getAmount)
                ));

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<Integer, Map<String, Long>> refundOrderTypeToDataMap = refundResultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SqlResultDTO::getOrderType,
                        Collectors.toMap(SqlResultDTO::getLabel, SqlResultDTO::getAmount)
                ));

        for (OrderTypeEnum orderTypeEnum : orderTypeEnums) {
            Map<String, Long> typeDataMap = orderTypeToDataMap.getOrDefault(orderTypeEnum.getCode(), Collections.emptyMap());
            Map<String, Long> refundTypeDataMap = refundOrderTypeToDataMap.getOrDefault(orderTypeEnum.getCode(), Collections.emptyMap());
            //遍历全部日期，为空则设置默认值0
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, orderTypeEnum.getDashboardDisplay());
            // 计算每一行的总和
            BigDecimal total = BigDecimal.ZERO;
            for (String date : xAxis) {
                Long amountValue = typeDataMap.getOrDefault(date, 0L);
                Long refundValue = refundTypeDataMap.getOrDefault(date, 0L);
                BigDecimal value = BigDecimal.valueOf(amountValue).subtract(BigDecimal.valueOf(refundValue)).divide(BigDecimal.valueOf(100));
                dataList.add(value.toString());
                tableItem.put(date, value.toString());
                total = total.add(value);
            }

            // 添加total列
            if(needTotal){
                tableItem.put(TOTAL, total.setScale(2, RoundingMode.HALF_UP).toString());
            }
            chartList.add(new ChartRespVo.ChartItemVo(orderTypeEnum.getDashboardDisplay(), dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData,needTotal);
    }

    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByRefund(
            List<String> tableHeaders,
            List<SqlResultDTO> refundResultDTOS,
            List<String> xAxis,
            Boolean needTotal) {

        if (CollUtil.isEmpty(refundResultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }

        // 构建图表数据
        ProductSalesTrendRespVo respVo = new ProductSalesTrendRespVo();
        ChartRespVo chartRespVo = new ChartRespVo();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 按订单类型和日期分组，直接聚合退款金额
        Map<Integer, Map<String, Long>> refundTypeToDataMap = refundResultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SqlResultDTO::getOrderType,
                        Collectors.toMap(SqlResultDTO::getLabel, SqlResultDTO::getAmount)
                ));

        List<OrderTypeEnum> orderTypeEnums = OrderTypeEnum.getDashboardDisplayList();
        for (OrderTypeEnum orderType : orderTypeEnums) {
            Map<String, Long> refundDataMap = refundTypeToDataMap.getOrDefault(orderType.getCode(), Collections.emptyMap());
            List<String> dataList = new ArrayList<>(); //折线图 dataList
            Map<String, String> tableItem = new HashMap<>(); //表格 item
            tableItem.put(FIRST_COLUMN, orderType.getDashboardDisplay());
            BigDecimal total = BigDecimal.ZERO;

            for (String date : xAxis) {
                Long refundValue = refundDataMap.getOrDefault(date, 0L);
                BigDecimal value = BigDecimal.valueOf(refundValue).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                dataList.add(value.toString());
                tableItem.put(date, value.toString());
                total = total.add(value);
            }

            if (needTotal) {
                tableItem.put(TOTAL, total.setScale(2, RoundingMode.HALF_UP).toString());
            }
            chartList.add(new ChartRespVo.ChartItemVo(orderType.getDashboardDisplay(), dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, respVo, tableData, needTotal);
    }

    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByIncomeForChannel(List<String> tableHeaders, List<SummarySqlResultDTO> resultDTOS,
                                                                                           List<String> xAxis, List<SummarySqlResultDTO> refundResultDTOS) {
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        // 构建图表数据
        ChartRespVo chartRespVo = new ChartRespVo();
        List<BrandCodeEnum> orderTypeEnums = BrandCodeEnum.getDashboardDisplayList();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<String, Map<String, Long>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode,
                        Collectors.toMap(SummarySqlResultDTO::getLabel, SummarySqlResultDTO::getAmount)
                ));

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<String, Map<String, Long>> refundOrderTypeToDataMap = refundResultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode,
                        Collectors.toMap(SummarySqlResultDTO::getLabel, SummarySqlResultDTO::getAmount)
                ));

        for (BrandCodeEnum orderTypeEnum : orderTypeEnums) {
            Map<String, Long> typeDataMap = orderTypeToDataMap.getOrDefault(orderTypeEnum.getBrandCode(), Collections.emptyMap());
            Map<String, Long> refundTypeDataMap = refundOrderTypeToDataMap.getOrDefault(orderTypeEnum.getBrandCode(), Collections.emptyMap());
            //遍历全部日期，为空则设置默认值0
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, orderTypeEnum.getDisplay());
            // 计算每一行的总和
            BigDecimal total = BigDecimal.ZERO;
            for (String date : xAxis) {
                Long amountValue = typeDataMap.getOrDefault(date, 0L);
                Long refundValue = refundTypeDataMap.getOrDefault(date, 0L);
                BigDecimal value = BigDecimal.valueOf(amountValue).subtract(BigDecimal.valueOf(refundValue)).divide(BigDecimal.valueOf(100));
                dataList.add(value.toString());
                tableItem.put(date, value.toString());
                total = total.add(value);
            }

            // 添加total列
            tableItem.put(TOTAL, total.setScale(2, RoundingMode.HALF_UP).toString());
            chartList.add(new ChartRespVo.ChartItemVo(orderTypeEnum.getDisplay(), dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData,true);
    }

    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByIncomeForSeriesCode(List<String> tableHeaders, List<SummarySqlResultDTO> resultDTOS,
                                                                                                     List<String> xAxis, List<SummarySqlResultDTO> refundResultDTOS) {
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        // 构建图表数据
        ChartRespVo chartRespVo = new ChartRespVo();
        List<BrandCodeEnum> orderTypeEnums = BrandCodeEnum.getDashboardDisplayList();
        chartRespVo.setXAxis(tableHeaders);

        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<String, Map<String, Long>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode,
                        Collectors.toMap(SummarySqlResultDTO::getLabel, SummarySqlResultDTO::getAmount)
                ));

        // 根据日期分组，再组装成订单类型为key, value为订单数量的map
        Map<String, Map<String, Long>> refundOrderTypeToDataMap = refundResultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode,
                        Collectors.toMap(SummarySqlResultDTO::getLabel, SummarySqlResultDTO::getAmount)
                ));
        List<String> seriesCodes = resultDTOS.stream().map(SummarySqlResultDTO::getCode).distinct().collect(Collectors.toList());

        for (String seriesCode : seriesCodes) {
            Map<String, Long> typeDataMap = orderTypeToDataMap.getOrDefault(seriesCode, Collections.emptyMap());
            Map<String, Long> refundTypeDataMap = refundOrderTypeToDataMap.getOrDefault(seriesCode, Collections.emptyMap());
            //遍历全部日期，为空则设置默认值0
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, seriesCode);
            // 计算每一行的总和
            BigDecimal total = BigDecimal.ZERO;
            for (String date : xAxis) {
                Long amountValue = typeDataMap.getOrDefault(date, 0L);
                Long refundValue = refundTypeDataMap.getOrDefault(date, 0L);
                BigDecimal value = BigDecimal.valueOf(amountValue).subtract(BigDecimal.valueOf(refundValue)).divide(BigDecimal.valueOf(100));
                dataList.add(value.toString());
                tableItem.put(date, value.toString());
                total = total.add(value);
            }

            // 添加total列
            tableItem.put(TOTAL, total.setScale(2, RoundingMode.HALF_UP).toString());
            chartList.add(new ChartRespVo.ChartItemVo(seriesCode, dataList));
            tableData.add(tableItem);
        }

        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData,true);
    }

    public static CommonResult<ProductSalesTrendRespVo> buildLineChartAndTableDataByIncomeForProductName(
            List<String> tableHeaders,
            List<SummarySqlResultDTO> resultDTOS,
            List<String> xAxis,
            List<SummarySqlResultDTO> refundResultDTOS) {

        // 如果查询结果为空，返回错误信息
        if (CollUtil.isEmpty(resultDTOS)) {
            return CommonResult.error(ErrorCodeConstants.NO_ORDERS_AT_THIS_TIME);
        }

        // 初始化返回对象
        ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();
        ChartRespVo chartRespVo = new ChartRespVo();
        chartRespVo.setXAxis(tableHeaders); // 设置X轴（日期分组）

        // 初始化图表数据和表格数据
        List<ChartRespVo.ChartItemVo> chartList = new ArrayList<>();
        List<Map<String, String>> tableData = new ArrayList<>();

        // 按商品名称分组，构建商品名称 -> {日期 -> 订单金额} 的映射
        Map<String, Map<String, Long>> orderTypeToDataMap = resultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode,
                        Collectors.toMap(SummarySqlResultDTO::getLabel, SummarySqlResultDTO::getAmount)
                ));

        // 按商品名称分组，构建商品名称 -> {日期 -> 退款金额} 的映射
        Map<String, Map<String, Long>> refundOrderTypeToDataMap = refundResultDTOS.stream()
                .collect(Collectors.groupingBy(
                        SummarySqlResultDTO::getCode,
                        Collectors.toMap(SummarySqlResultDTO::getLabel, SummarySqlResultDTO::getAmount)
                ));

        // 获取所有商品名称
        List<String> productNames = resultDTOS.stream()
                .map(SummarySqlResultDTO::getCode)
                .distinct()
                .collect(Collectors.toList());

        // 遍历每个商品名称，生成图表和表格数据
        for (String productName : productNames) {
            Map<String, Long> typeDataMap = orderTypeToDataMap.getOrDefault(productName, Collections.emptyMap());
            Map<String, Long> refundTypeDataMap = refundOrderTypeToDataMap.getOrDefault(productName, Collections.emptyMap());

            // 初始化当前商品的图表数据和表格数据
            List<String> dataList = new ArrayList<>();
            Map<String, String> tableItem = new HashMap<>();
            tableItem.put(FIRST_COLUMN, productName); // 第一列是商品名称

            // 计算每一行的总和
            BigDecimal total = BigDecimal.ZERO;

            // 遍历所有日期分组
            for (String date : xAxis) {
                Long amountValue = typeDataMap.getOrDefault(date, 0L); // 如果没有订单金额，默认值为0
                Long refundValue = refundTypeDataMap.getOrDefault(date, 0L); // 如果没有退款金额，默认值为0
                BigDecimal income = BigDecimal.valueOf(amountValue)
                        .subtract(BigDecimal.valueOf(refundValue))
                        .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP); // 计算收入并转换为元
                dataList.add(income.toString()); // 添加到图表数据
                tableItem.put(date, income.toString()); // 添加到表格数据
                total = total.add(income); // 累加总收入
            }

            // 添加总计列
            tableItem.put(TOTAL, total.setScale(2, RoundingMode.HALF_UP).toString());

            // 将当前商品的数据添加到图表和表格中
            chartList.add(new ChartRespVo.ChartItemVo(productName, dataList));
            tableData.add(tableItem);
        }

        // 构建最终的返回对象
        return buildProductSalesTrendRespVo(xAxis, chartRespVo, chartList, productSalesTrendRespVo, tableData, true);
    }


}

package com.jlr.ecp.order.service.order;

import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppTabsVO;
import com.jlr.ecp.order.dal.dataobject.order.VcsOrderStatisticDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_vcs_order_statistic(t_vcs_order_statistic)】的数据库操作Service
* @createDate 2024-01-31 10:33:22
*/
public interface VcsOrderStatisticDOService extends IService<VcsOrderStatisticDO> {

    List<OrderAppTabsVO> getTabs(String consumerCode, String clientId);

}

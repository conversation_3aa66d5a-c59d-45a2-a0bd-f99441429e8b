package com.jlr.ecp.order.enums.order;

import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum OrderFulfillmentTypeEnum {
    /**
     * 0：聚合父订单
     */
    AGGREGATE_PARENT_ORDER(0, "000", 3),

    /**
     * VCS可能有多条业务线，比如：
     * VCS PIVI
     * VCS NGI
     *
     */
    VCS_PIVI(1, "001", 3),

    /**
     * Brand Goods
     */
    BRAND_GOODS(2, "002", 3),

    /**
     * Charging Goods
     */
    CHARGING_GOODS(3, "003", 3),

    /**
     * APB
     */
    APB(4, "004", 3),

    /**
     * CCCM
     */
    CCCM(5, "005", 3),

    /**
     * Accessories
     */
    ACCESSORIES(6, "006", 3),

    /**
     * Spare Parts
     */
    SPARE_PARTS(7, "007", 3);

    /**
     * 履约类型枚举值
     */
    private final Integer fulfilmentType;

    /**
     * 订单履约方式编码
     */
    private final String code;

    /**
     * 数值
     */
    private final Integer value;
}
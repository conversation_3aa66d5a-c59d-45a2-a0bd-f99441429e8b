package com.jlr.ecp.order.util.order;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 订单号生成器
 *
 * <AUTHOR>
 * <p>
 * TODO
 * 1.Consusmer_code = oneID?
 * 2.brandCode前端传还是 通过上下文参数获取？
 * 3.channelCode前端传还是 通过上下文参数获取？
 */
public class OrderCodeGenerator {

    /**
     * 生成父订单号
     * <p>
     * 现在 channelCode 传的是 路虎小程序：MLR 捷豹小程序：MJA，得去掉后两个字母
     * 保留M 原有的生成逻辑
     */
    public static String generateParentOrderCode(String oneID, String brandCode, String channelCode) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String userCodeSuffix = StringUtils.right(oneID, 6);
        String channelCodePrefix = StringUtils.left(channelCode, 1);

        return brandCode + channelCodePrefix + "P" + timestamp + userCodeSuffix;
    }

    /**
     * 生成子订单号
     * <p>
     * 现在 channelCode 传的是 路虎小程序：MLR 捷豹小程序：MJA，得去掉后两个字母
     * 保留M 原有的生成逻辑
     */
    public static String generateChildOrderCode(String oneID, String brandCode, String channelCode, String fulfillmentType, String sequenceNumber) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String userCodeSuffix = StringUtils.right(oneID, 6);
        String channelCodePrefix = StringUtils.left(channelCode, 1);

        return brandCode + channelCodePrefix + fulfillmentType + timestamp + sequenceNumber + userCodeSuffix;
    }

    // 测试主方法
    public static void main(String[] args) {
        String parentOrderCode = generateParentOrderCode("1234567890XXXXXX", "LR", "M");
        String childOrderCode = generateChildOrderCode("1234567890XXXXXX", "LR", "M", "001", "001");

        System.out.println("Parent Order Code: " + parentOrderCode);
        System.out.println("Child Order Code: " + childOrderCode);

        String parentOrderCode2 = generateParentOrderCode("MEMBER:CODE:432", "LR", "MLR");
        String childOrderCode2 = generateChildOrderCode("MEMBER:CODE:432", "LR", "MJA", "001", "001");

        System.out.println("改造后的Parent Order Code: " + parentOrderCode2);
        System.out.println("改造后的Child Order Code: " + childOrderCode2);
    }
}
package com.jlr.ecp.order.enums.order;

import com.jlr.ecp.order.api.order.vo.RefundReasonStatusVO;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 物流退款原因枚举
 * author: lislu
 */
@AllArgsConstructor
public enum LogisticsRefundReasonEnum {

    SEVEN_DAY_NO_REASON(4, 1,"7天无理由退换货"),
    DAMAGED_GOODS_REJECTED(1,1, "货物破损已拒签"),
    PRODUCT_INFO_MISMATCH(2, 1,"商品信息描述不符"),
    PACKAGING_DAMAGED(3, 1,"包装/商品破损/污渍"),
    WRONG_PRODUCT_SHIPPED(5,1, "卖家发错货"),
    QUALITY_ISSUE(6, 1,"质量问题"),
    OTHER_RETURN(7, 1,"其他"), // 退货退款
    WRONG_OR_EXTRA_ORDER(21,2, "拍错/多拍"),
    DISLIKE_OR_UNWANTED(22, 2,"不喜欢/不想要"),
    CONTACT_INFO_ERROR(23, 2,"地址/电话信息填写错误"),
    UNUSED_OR_RARELY_USED(24, 2,"没用/少用优惠"),
    ADDRESS_OUT_OF_SERVICE(25, 2,"地址不在服务范围"),
    OTHER_REFUND(26, 2,"其他"); // 仅退款

    private final Integer code;

    /**
     * type 1-退货退款 2-仅退款
     */
    private final Integer type;
    private final String name;



    public Integer getCode() {
        return code;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static LogisticsRefundReasonEnum getByCode(Integer code) {
        for (LogisticsRefundReasonEnum reason : values()) {
            if (reason.getCode().equals(code)) {
                return reason;
            }
        }
        return null;
    }

    /**
     * 根据类型获取退款原因列表
     * @param type 类型：1-退货退款 2-仅退款 不传退所有
     * @return 退款原因列表
     */
    public static List<RefundReasonStatusVO> getByType(Integer type) {
        List<RefundReasonStatusVO> resultList = new ArrayList<>();
        for (LogisticsRefundReasonEnum reason : values()) {
            if (reason.getType().equals(type)) {
                resultList.add(new RefundReasonStatusVO(reason.getCode(), reason.getName()));
            }
        }
        return resultList;
    }

    public static String getName(Integer code) {
        for (LogisticsRefundReasonEnum reason : values()) {
            if (reason.getCode().equals(code)) {
                return reason.getName();
            }
        }
        return null;
    }

} 
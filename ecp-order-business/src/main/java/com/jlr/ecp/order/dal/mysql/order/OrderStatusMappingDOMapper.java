package com.jlr.ecp.order.dal.mysql.order;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusMappingDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【t_order_status_mapping(t_order_status_mapping)】的数据库操作Mapper
* @createDate 2024-01-21 20:29:33
* @Entity com.jlr.ecp.order.dal.dataobject.order.OrderStatusMappingDO
*/
@Mapper
public interface OrderStatusMappingDOMapper extends BaseMapperX<OrderStatusMappingDO> {

    /**
     *  根据订单状态和退款订单状态查询订单状态映射关系
     * @param orderStatus
     * @param refundOrderStatus
     * @return
     */
    @Select("SELECT order_status,refund_order_status,customer_order_status_view,customer_after_sales_order_status_view,operation_origin_order_status_view,operation_origin_order_cancel_status_view,customer_refund_order_status_view  FROM t_order_status_mapping " +
            "WHERE order_status = #{orderStatus} AND refund_order_status = #{refundOrderStatus} and is_deleted = 0")
    OrderStatusMappingDO getStatusMapping(@Param("orderStatus") Integer orderStatus,@Param("refundOrderStatus") Integer refundOrderStatus);
}





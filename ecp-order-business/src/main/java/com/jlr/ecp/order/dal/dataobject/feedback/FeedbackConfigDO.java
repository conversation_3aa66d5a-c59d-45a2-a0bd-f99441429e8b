package com.jlr.ecp.order.dal.dataobject.feedback;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@TableName("t_feedback_config")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackConfigDO extends BaseDO {
    /**
     * 反馈配置ID
     */
    @TableId
    private Long id;

    /**
     * 业务线
     */
    @TableField("business_code")
    private String businessCode;

    /**
     * 评价维度：PM开头、已支付OR、订单完成、CL、订单整单取消
     */
    @TableField("feedback_dimensions")
    private String feedbackDimensions;

    /**
     * 评价编码：生成规则：feedback_dimensions+递增三位数编号（例如：PM001）
     */
    @TableField("feedback_code")
    private String feedbackCode;

    /**
     * 商品使用状态(启用状态，0=已停用 1=待启用 2=已启用)
     */
    @TableField("enable_status")
    private Integer enableStatus;

    /**
     * 启用时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime enableTime;
    /**
     * 停用时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime downtime;

    /**
     * 定时启用时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime scheduleEnableTime;


    /**
     * 最后修改人;最后修改人编码
     */
    private String lastModifyUser;


    /**
     * 是否允许输入框：0=否；1=是
     */
    @TableField("enable_input")
    private Integer enableInput;

    /**
     * 输入框是否必填：0=否；1=是
     */
    @TableField("must_input")
    private Integer mustInput;

    /**
     * 输入框提示信息
     */
    @TableField("input_text")
    private String inputText;

    /**
     * 备注说明
     */
    @TableField("remark")
    private String remark;


    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Integer tenantId;

}

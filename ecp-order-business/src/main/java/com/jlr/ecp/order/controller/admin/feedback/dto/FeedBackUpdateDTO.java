package com.jlr.ecp.order.controller.admin.feedback.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Data
@Schema(description = "管理后台 -  评价配置创建DTO ")
@ToString(callSuper = true)
public class FeedBackUpdateDTO extends FeedBackCreateDTO{


    @Schema(description = "评价配置编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "评价配置编码不能为空")
    private String feedbackCode;
    /**
     * 版本号
     */
    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer revision;
}

package com.jlr.ecp.order.service.refund;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundAttachmentDO;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundAttachmentDOMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【t_order_refund_attachment(t_order_refund_attachment)】的数据库操作Service实现
* @createDate 2024-01-15 11:28:44
*/
@Service
public class OrderRefundAttachmentDOServiceImpl extends ServiceImpl<OrderRefundAttachmentDOMapper, OrderRefundAttachmentDO>
    implements OrderRefundAttachmentDOService{

}





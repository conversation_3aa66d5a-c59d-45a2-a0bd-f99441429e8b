package com.jlr.ecp.order.util.machine.handler.logistics;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusLogDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.enums.independent.OrderRefundIndependentTypeEnum;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import com.jlr.ecp.order.enums.refund.RefundLogisticsStatusEnum;
import com.jlr.ecp.order.util.machine.handler.LogisticsEventHandler;
import com.jlr.ecp.order.util.order.OrderAssembleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 分账退款处理中
 */
@Component
@Slf4j
public class LogisticsRefundIndependentHandler implements LogisticsEventHandler {

    @Resource
    private OrderRefundDOMapper orderRefundDOMapper;


    @Resource
    private OrderStatusLogDOMapper statusLogMapper;

    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO) {
        //打印各个参数到日志中
        log.info("LogisticsRefundIndependentHandler handleEvent orderInfoDO:{},orderRefundDO:{},orderItemDO:{}",orderInfoDO,orderRefundDO,orderItemDO);
             OrderStatusLogDO logDO = OrderAssembleUtil.assembleOrderStatusLogDO(orderRefundDO.getRefundOrderCode(),orderRefundDO.getCouponRefundStatus(),RefundLogisticsStatusEnum.SPLIT_REFUND_PROCESSING.getCode());
             orderRefundDO.setLogisticsRefundStatus(RefundLogisticsStatusEnum.SPLIT_REFUND_PROCESSING.getCode());
             orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.FULL_REFUND_APPROVE.getCode());
             orderRefundDO.setRefundIndependentType(OrderRefundIndependentTypeEnum.INDEPENDENT_REFUND.getCode());
             orderRefundDOMapper.updateById(orderRefundDO);
             statusLogMapper.insert(logDO);
    }
}

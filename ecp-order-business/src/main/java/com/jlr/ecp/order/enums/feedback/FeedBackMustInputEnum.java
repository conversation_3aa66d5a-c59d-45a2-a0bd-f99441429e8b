package com.jlr.ecp.order.enums.feedback;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum FeedBackMustInputEnum {

    NOT_REQUIRED(0, "否"),

    REQUIRED(1, "是");

    /**
     * 商品启停状态编码
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;


    public static String fromCode(Integer code) {
        for (FeedBackMustInputEnum value : FeedBackMustInputEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

}

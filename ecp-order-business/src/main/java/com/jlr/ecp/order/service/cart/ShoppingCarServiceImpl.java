package com.jlr.ecp.order.service.cart;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.jlr.ecp.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.inventory.api.InventoryOrderApi;
import com.jlr.ecp.inventory.dto.SkuStockRespDTO;
import com.jlr.ecp.inventory.dto.qry.QuerySkuStockDTO;
import com.jlr.ecp.order.api.cart.dto.CarInfoDTO;
import com.jlr.ecp.order.api.cart.dto.CartCreatDTO;
import com.jlr.ecp.order.api.cart.dto.CartItemUpdateDTO;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.app.cart.vo.ShoppingCarItemVO;
import com.jlr.ecp.order.dal.dataobject.cart.ShoppingCarDO;
import com.jlr.ecp.order.dal.dataobject.cart.ShoppingCarItemDO;
import com.jlr.ecp.order.dal.mysql.cart.ShoppingCarItemMapper;
import com.jlr.ecp.order.dal.mysql.cart.ShoppingCarMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.product.api.product.ProductApi;
import com.jlr.ecp.product.api.product.dto.ProductSkuCartDTO;
import com.jlr.ecp.product.api.product.vo.ProductCartViewVO;
import com.jlr.ecp.product.api.product.vo.ProductDetailsVO;
import com.jlr.ecp.product.api.product.vo.ProductSkuRespVO;
import com.jlr.ecp.product.api.product.vo.ProductSpuInfoVO;
import com.jlr.ecp.product.enums.product.ProductStockLimitFlagEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.order.constant.Constants.LIMIT_ONE;
import static com.jlr.ecp.order.enums.ErrorCodeConstants.CHECK_ICR_CONSUMER_ERROR;
import static com.jlr.ecp.order.enums.ErrorCodeConstants.QUANTITY_INVALID;

/**
 * t_product_category(ProductCategory)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-07 20:13:51
 */
@Service("shoppingCarService")
@Validated
@Slf4j
public class ShoppingCarServiceImpl implements ShoppingCarService {

    @Resource
    private Snowflake ecpIdUtil;

    @Resource
    private ShoppingCarItemMapper carItemMapper;

    @Resource
    private ShoppingCarMapper carMapper;

    @Resource
    private ProductApi productApi;

    @Resource
    private RedisService redisService;

    @Resource
    private InventoryOrderApi inventoryOrderApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addCart(CartCreatDTO cartCreatDTO, String brandCode) {
        List<CarInfoDTO> carInfoDTOList = cartCreatDTO.getCarInfoDTOList();
        if(CollUtil.isEmpty(carInfoDTOList)){
            log.info("车辆信息未传入");
            return false;
        }
        // 校验商品数量
        boolean match = carInfoDTOList.stream().anyMatch(item -> item.getQuantity() <= 0);
        if(match){
            log.error("商品数量必须为正整数");
            throw exception(QUANTITY_INVALID);
        }
        String cartCode = cartCreatDTO.getCartCode();
        String incontrolName = cartCreatDTO.getIncontrolName();
        //
//        if(StringUtils.isBlank(incontrolName)){
//            throw exception(CHECK_ICR_CONSUMER_ERROR);
//        }
        //判断购物车主表是否需要生成
        cartCode = getCartCode(cartCreatDTO, cartCode);

        List<ShoppingCarItemDO> insertList = new ArrayList<>();
        List<ShoppingCarItemDO> updateList = new ArrayList<>();
        for (CarInfoDTO carInfoDTO : carInfoDTOList) {
            //入库校验参数：
            //校验sku是否存在 调用productAPI 并组合参数
            CommonResult<ProductSkuCartDTO> skuByCode = productApi.getSkuByCode(carInfoDTO.getProductSkuCode());
            ProductSkuCartDTO data = skuByCode.getData();
            if(data ==null){
                throw exception(ErrorCodeConstants.CART_SKU_ERROR);
            }
            //判断库存和加购数量
            if(data.getSkuQuantity()!=null && carInfoDTO.getQuantity() * carInfoDTOList.size() >data.getSkuQuantity()){
                throw exception(ErrorCodeConstants.CART_SKU_QUANTITY_ERROR);
            }
            //判断现在有的购物车商品是否已经存在
            ShoppingCarItemDO shoppingCarItemDO = carItemMapper.selectOne(new LambdaQueryWrapperX<ShoppingCarItemDO>()
                    .eq(ShoppingCarItemDO::getSeriesCode,carInfoDTO.getSeriesCode())
                    .eq(ShoppingCarItemDO::getCarVin,carInfoDTO.getCarVin())
                    .eq(ShoppingCarItemDO::getProductCode,data.getProductSpuCode())
                    .eq(BaseDO::getIsDeleted,false)
                    .eq(ShoppingCarItemDO::getCartItemType,carInfoDTO.getCartItemType())
                    .eq(ShoppingCarItemDO::getCartCode,cartCode)
                    .eqIfPresent(ShoppingCarItemDO::getIncontrolId, incontrolName)
                    .last(LIMIT_ONE));
            //不存在就新增一条数据
            if(shoppingCarItemDO == null){
                shoppingCarItemDO = new ShoppingCarItemDO();
                BeanUtils.copyProperties(cartCreatDTO,shoppingCarItemDO);
                shoppingCarItemDO.setCartCode(cartCode);
                shoppingCarItemDO.setCartItemType(carInfoDTO.getCartItemType());
                shoppingCarItemDO.setSeriesCode(carInfoDTO.getSeriesCode());
                shoppingCarItemDO.setSeriesName(carInfoDTO.getSeriesName());
                shoppingCarItemDO.setQuantity(carInfoDTO.getQuantity());
                shoppingCarItemDO.setProductSkuCode(carInfoDTO.getProductSkuCode());
                shoppingCarItemDO.setCarVin(carInfoDTO.getCarVin());
                shoppingCarItemDO.setBrandCode(brandCode);
                setShoppingCarItemDO(shoppingCarItemDO,data);
                shoppingCarItemDO.setCartItemCode(ecpIdUtil.nextIdStr());
                shoppingCarItemDO.setIncontrolId(incontrolName);
                insertList.add(shoppingCarItemDO);
            }else {
                setShoppingCarItemDO(shoppingCarItemDO,data);
                updateList.add(shoppingCarItemDO);
            }
        }
        if (CollUtil.isNotEmpty(insertList)){
            carItemMapper.insertBatch(insertList);
        }
        if (CollUtil.isNotEmpty(updateList)){
            carItemMapper.updateBatch(updateList);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean brandGoodsAddCart(CartCreatDTO cartCreatDTO, String brandCode) {
        List<CarInfoDTO> carInfoDTOList = cartCreatDTO.getCarInfoDTOList();
        if(CollUtil.isEmpty(carInfoDTOList)){
            log.info("车辆信息未传入");
            return false;
        }
        // 校验商品数量
        boolean match = carInfoDTOList.stream().anyMatch(item -> item.getQuantity() <= 0);
        if(match){
            log.error("商品数量必须为正整数");
            throw exception(QUANTITY_INVALID);
        }
        String cartCode = cartCreatDTO.getCartCode();
        String incontrolName = cartCreatDTO.getIncontrolName();
        //判断购物车主表是否需要生成
        cartCode = getCartCode(cartCreatDTO, cartCode);

        List<ShoppingCarItemDO> insertList = new ArrayList<>();
        List<ShoppingCarItemDO> updateList = new ArrayList<>();
        for (CarInfoDTO carInfoDTO : carInfoDTOList) {
            //入库校验参数：
            //校验sku是否存在 调用productAPI 并组合参数
            CommonResult<ProductSkuCartDTO> skuByCode = productApi.getSkuByCode(carInfoDTO.getProductSkuCode());
            ProductSkuCartDTO data = skuByCode.getData();
            if(data ==null){
                throw exception(ErrorCodeConstants.CART_SKU_ERROR);
            }
            //判断现在有的购物车商品是否已经存在
            ShoppingCarItemDO shoppingCarItemDO = carItemMapper.selectOne(new LambdaQueryWrapperX<ShoppingCarItemDO>()
                    .eq(ShoppingCarItemDO::getProductCode,data.getProductSpuCode())
                    .eq(ShoppingCarItemDO::getProductSkuCode,data.getProductSkuCode())
                    .eq(BaseDO::getIsDeleted,false)
                    .eq(ShoppingCarItemDO::getCartItemType,carInfoDTO.getCartItemType())
                    .eq(ShoppingCarItemDO::getCartCode,cartCode)
                    .eq(ShoppingCarItemDO::getConsumerCode, cartCreatDTO.getConsumerCode())
                    .last(LIMIT_ONE));
            //不存在就新增一条数据
            if(shoppingCarItemDO == null){
                shoppingCarItemDO = new ShoppingCarItemDO();
                BeanUtils.copyProperties(cartCreatDTO,shoppingCarItemDO);
                shoppingCarItemDO.setCartCode(cartCode);
                shoppingCarItemDO.setCartItemType(carInfoDTO.getCartItemType());
                shoppingCarItemDO.setSeriesCode(carInfoDTO.getSeriesCode());
                shoppingCarItemDO.setSeriesName(carInfoDTO.getSeriesName());
                shoppingCarItemDO.setQuantity(carInfoDTO.getQuantity());
                shoppingCarItemDO.setProductSkuCode(carInfoDTO.getProductSkuCode());
                shoppingCarItemDO.setCarVin(carInfoDTO.getCarVin());
                shoppingCarItemDO.setBrandCode(brandCode);
                setShoppingCarItemDO(shoppingCarItemDO,data);
                shoppingCarItemDO.setCartItemCode(ecpIdUtil.nextIdStr());
                shoppingCarItemDO.setIncontrolId(incontrolName);
                insertList.add(shoppingCarItemDO);
            }else {
                setShoppingCarItemDO(shoppingCarItemDO,data);
                shoppingCarItemDO.setQuantity(shoppingCarItemDO.getQuantity()+carInfoDTO.getQuantity());
                updateList.add(shoppingCarItemDO);
            }
            // 校验库存
            verifyStockLimit(shoppingCarItemDO, data, false);
        }
        if (CollUtil.isNotEmpty(insertList)){
            carItemMapper.insertBatch(insertList);
        }
        if (CollUtil.isNotEmpty(updateList)){
            carItemMapper.updateBatch(updateList);
        }
        return true;
    }

    /** 获取购物车code
     *
     * @param cartCreatDTO 购物车对象
     * @param cartCode 购物车code
     */
    private String getCartCode(CartCreatDTO cartCreatDTO, String cartCode) {
        if(StringUtils.isBlank(cartCode) ){
            //查询客户的购物车主表
            ShoppingCarDO check = checkCartCode(cartCreatDTO.getConsumerCode());
            cartCode = check == null ? ecpIdUtil.nextIdStr() : check.getCartCode();
            if(check == null){
                ShoppingCarDO shoppingCarDO = new ShoppingCarDO();
                BeanUtils.copyProperties(cartCreatDTO,shoppingCarDO);
                shoppingCarDO.setDiscountFeeTotal(0L);
                shoppingCarDO.setOriginalFeeTotal(0L);
                shoppingCarDO.setCartCode(cartCode);
                carMapper.insert(shoppingCarDO);
            }
        }
        return cartCode;
    }

    private void setShoppingCarItemDO(ShoppingCarItemDO shoppingCarItemDO,ProductSkuCartDTO data){
        shoppingCarItemDO.setProductSkuCode(data.getProductSkuCode());
        shoppingCarItemDO.setProductCode(data.getProductSpuCode());
    }


    private ShoppingCarDO checkCartCode(String consumerCode) {
        //判断购物车主表code是否存在
        return carMapper.selectOne(new LambdaQueryWrapperX<ShoppingCarDO>()
                .eq(ShoppingCarDO::getConsumerCode,consumerCode)
                .eq(BaseDO::getIsDeleted, false)
                .last(LIMIT_ONE));
    }

    @Override
    public Boolean chooseSku(CartItemUpdateDTO cartItemUpdateDTO) {
        //CartItemUpdateDTO
        ShoppingCarItemDO shoppingCarItemDO = carItemMapper.selectById(cartItemUpdateDTO.getId());
        if(shoppingCarItemDO == null){
            throw exception(ErrorCodeConstants.CART_UPDATE_FAIL);
        }
        //调用sku查询
        CommonResult<ProductSkuCartDTO> skuByCode = productApi.getSkuByCode(cartItemUpdateDTO.getProductSkuCode());
        ProductSkuCartDTO data = skuByCode.getData();
        if(data ==null){
            throw exception(ErrorCodeConstants.CART_SKU_ERROR);
        }
        setShoppingCarItemDO(shoppingCarItemDO,data);

        int i = carItemMapper.updateById(shoppingCarItemDO);
        return i>0;
    }

    @Override
    public CommonResult<String> brandGoodsChooseSku(CartItemUpdateDTO cartItemUpdateDTO, String jlrId) {
        //CartItemUpdateDTO
        ShoppingCarItemDO shoppingCarItemDO = carItemMapper.selectById(cartItemUpdateDTO.getId());
        if(shoppingCarItemDO == null){
            throw exception(ErrorCodeConstants.CART_UPDATE_FAIL);
        }
        //调用sku查询
        CommonResult<ProductSkuCartDTO> skuByCode = productApi.getSkuByCode(cartItemUpdateDTO.getProductSkuCode());
        ProductSkuCartDTO data = skuByCode.getData();
        if(data ==null){
            throw exception(ErrorCodeConstants.CART_SKU_ERROR);
        }
        setShoppingCarItemDO(shoppingCarItemDO,data);
        List<ShoppingCarItemDO> updateShoppingCarItemDOList = new ArrayList<>();
        updateShoppingCarItemDOList.add(shoppingCarItemDO);
        List<ShoppingCarItemDO> shoppingCarItemDOList = carItemMapper.selectList(new LambdaQueryWrapperX<ShoppingCarItemDO>()
                .eq(ShoppingCarItemDO::getProductCode,data.getProductSpuCode())
                .eq(ShoppingCarItemDO::getProductSkuCode,data.getProductSkuCode())
                .eq(ShoppingCarItemDO::getConsumerCode,jlrId)
                .ne(ShoppingCarItemDO::getId,cartItemUpdateDTO.getId())
                .eq(BaseDO::getIsDeleted,false));
        Integer quantity = cartItemUpdateDTO.getQuantity();
        if (shoppingCarItemDOList != null) {
            for (ShoppingCarItemDO itemDO : shoppingCarItemDOList) {
                quantity += itemDO.getQuantity();
                itemDO.setIsDeleted(true);
                updateShoppingCarItemDOList.add(itemDO);
            }
        }
        shoppingCarItemDO.setQuantity(quantity);

        // 校验库存
        Integer stockQuantity = verifyStockLimit(shoppingCarItemDO, data, true);
        if (stockQuantity != null) {
            if (stockQuantity > 0) {
                shoppingCarItemDO.setQuantity(stockQuantity);
                carItemMapper.updateBatch(updateShoppingCarItemDOList);
                CommonResult<String> result = CommonResult.error(ErrorCodeConstants.CART_SKU_QUANTITY_LIMITED_ERROR);
                result.setData(String.valueOf(stockQuantity));
                return result;
            } else {
                CommonResult<String> result = CommonResult.error(ErrorCodeConstants.CART_SKU_QUANTITY_ZERO_ERROR);
                result.setData(String.valueOf(stockQuantity));
                return result;
            }
        }
        carItemMapper.updateBatch(updateShoppingCarItemDOList);
        return CommonResult.success(Constants.CART_UPDATE_SUCCESS_MESSAGE);
    }

    /**
     * 校验库存
     * @param shoppingCarItemDO
     */
    private Integer verifyStockLimit(ShoppingCarItemDO shoppingCarItemDO,  ProductSkuCartDTO data, boolean updateFlag) {
        ProductSpuInfoVO productSpuInfoVO = getProductSpuInfoVO(shoppingCarItemDO);
        if (Objects.equals(productSpuInfoVO.getStockLimitFlag(), ProductStockLimitFlagEnum.LIMITED.getCode())) {
            //判断库存和加购数量
            QuerySkuStockDTO querySkuStockDTO = new QuerySkuStockDTO();
            querySkuStockDTO.setSkus(Collections.singleton(data.getModelCode()));
            CommonResult<List<SkuStockRespDTO>> result = inventoryOrderApi.querySkuStock(querySkuStockDTO);
            if (result.isSuccess()) {
                Map<String, Integer> skuStockMap = result.getData().stream()
                        .collect(Collectors.toMap(SkuStockRespDTO::getSku, SkuStockRespDTO::getStock));
                Integer stockNum = skuStockMap.getOrDefault(data.getModelCode(), 0);
                if(shoppingCarItemDO.getQuantity() > stockNum){
                    // 如果是修改，并且超过库存，库存>0，就把购物车赋值成最大库存数
                    if (updateFlag) {
                        return stockNum;
                    } else {
                        throw exception(ErrorCodeConstants.CART_SKU_QUANTITY_ERROR);
                    }
                }
            } else {
                throw exception(ErrorCodeConstants.QUERY_SKU_STOCK_FAILED);
            }
        }
        return null;
    }

    private ProductSpuInfoVO getProductSpuInfoVO(ShoppingCarItemDO shoppingCarItemDO) {
        CommonResult<List<ProductSpuInfoVO>> findProductList = productApi.findProductList(Collections.singletonList(shoppingCarItemDO.getProductCode()));
        if(CollUtil.isEmpty(findProductList.getData())){
            throw exception(ErrorCodeConstants.CART_SKU_ERROR);
        }
        return findProductList.getData().get(0);
    }

    public static void supplementShoppingCartItem(ShoppingCarItemVO shoppingCarItemVO, ProductCartViewVO tempProductInfo) {
        shoppingCarItemVO.setSkuList(tempProductInfo.getProductAttr());
        shoppingCarItemVO.setIsDeleted(tempProductInfo.getIsDeleted());
        shoppingCarItemVO.setProductName(tempProductInfo.getProductName());
        shoppingCarItemVO.setProductDetailReqVO(tempProductInfo.getProductDetailReqVO());
        shoppingCarItemVO.setShelfStatus(tempProductInfo.getShelfStatus());
        shoppingCarItemVO.setPolicyInfo(tempProductInfo.getPolicyInfo());
        shoppingCarItemVO.setChildFulfilmentType(tempProductInfo.getChildFulfilmentType());
        shoppingCarItemVO.setBusinessCode(tempProductInfo.getBusinessCode());
        shoppingCarItemVO.setCategoryCodeLevel1Name(tempProductInfo.getCategoryCodeLevel1Name());
        shoppingCarItemVO.setCategoryCodeLevel2Name(tempProductInfo.getCategoryCodeLevel2Name());
        shoppingCarItemVO.setCategoryCodeLevel3Name(tempProductInfo.getCategoryCodeLevel3Name());
    }
}


package com.jlr.ecp.order.enums.dashboard;

import com.jlr.ecp.order.controller.admin.dashboard.dto.QueryReqDTO;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum OrderChannelEnum {

    /**
     * 路虎小程序
     */
    LR_WECHAT("LR", "Land Rover"),

    /**
     * 捷豹小程序
     */
    JA_WECHAT("JA", "Jaguar"),

    /**
     * 全部
     */
    ALL("", "全部");
    private final String code;

    private final String description;

    /**
     * 根据渠道编码获取对应的描述。
     *
     * @param code 渠道编码
     * @return 对应的渠道描述
     */
    public static String getDescriptionByCode(String code) {
        for (OrderChannelEnum channel : OrderChannelEnum.values()) {
            if (channel.getCode().equals(code)) {
                return channel.getDescription();
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    /**
     * 判断参数是否非法。
     *
     */
    public static boolean isInvalidOrderChannel(QueryReqDTO dto) {
        return TypeFilterEnum.CHANNEL.getCode().equals(dto.getType()) &&
                !OrderChannelEnum.LR_WECHAT.getCode().equals(dto.getOrderChannel()) &&
                !OrderChannelEnum.JA_WECHAT.getCode().equals(dto.getOrderChannel());
    }
}
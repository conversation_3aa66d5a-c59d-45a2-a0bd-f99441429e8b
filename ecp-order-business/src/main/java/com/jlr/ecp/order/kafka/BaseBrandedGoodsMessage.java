package com.jlr.ecp.order.kafka;

import lombok.Data;

import java.io.Serializable;

/**
 * 精选好物基础消息类
 */
@Data
public class BaseBrandedGoodsMessage implements Serializable {
    /**
     * 消息id
     */
    private String messageId;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 任务编码
     */
    private String taskCode;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 订单编号
     */
    private String bgorderNumber;

    /**
     * 微信小程序链接
     */
    private String wxUrl;
} 
package com.jlr.ecp.order.enums.order;

public enum RefundMoneyEnum {
    NO_REFUND(0, "否"),
    REFUNDED(1, "是");

    private final int code;
    private final String description;

    RefundMoneyEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static RefundMoneyEnum fromCode(int code) {
        for (RefundMoneyEnum status : RefundMoneyEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown refund status code: " + code);
    }
}

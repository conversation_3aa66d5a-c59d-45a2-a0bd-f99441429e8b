package com.jlr.ecp.order.enums.payment;

import lombok.Getter;

/**
 * PaymentCenter 业务线枚举
 *
 * <AUTHOR> <PERSON>
 * @since 2025-03-17 20:09:52
 */
@Getter
public enum PayCenterBizLineCodeEnum {

    BG("BG", "BrandedGoods"),

    ECP("ECP", "ECP"),

    LRE("LRE",  "LRE"),

    CS("CS",  "CS"),

    SS("SS", "SS"),

    VCS("VCS", "BUSINESS:001"),

    CHARGING("CHARGING",  "CHARGING");

    PayCenterBizLineCodeEnum(String businessLineCode,String businessCode) {
        this.businessLineCode = businessLineCode;
        this.businessCode = businessCode;
    }

    private final String businessLineCode;


    /**
     * 在BusinessIdEnum中的业务线编码 它和PaymentCenter不一样 需要做一次映射
     */
    private final String businessCode;

    //通过businessCode获得业务线枚举
    public static PayCenterBizLineCodeEnum getByBusinessCode(String businessCode) {
        for (PayCenterBizLineCodeEnum value : PayCenterBizLineCodeEnum.values()) {
            if (value.getBusinessCode().equals(businessCode)) {
                return value;
            }
        }
        throw  new RuntimeException("businessLineCode not found");
    }

    //通过businessCode获得业务线枚举
    public static PayCenterBizLineCodeEnum getByBusinessLineCode(String businessLineCode) {
        for (PayCenterBizLineCodeEnum value : PayCenterBizLineCodeEnum.values()) {
            if (value.getBusinessLineCode().equals(businessLineCode)) {
                return value;
            }
        }
        throw  new RuntimeException("businessLineCode: " + businessLineCode + " not found");
    }
}

package com.jlr.ecp.order.service.independent;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentItemDO;
import com.jlr.ecp.order.dal.mysql.independent.OrderIndependentItemDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Hongyi
 */
@Service
@Slf4j
public class OrderIndependentItemDOServiceImpl extends ServiceImpl<OrderIndependentItemDOMapper, OrderIndependentItemDO>
        implements OrderIndependentItemDOService {

}
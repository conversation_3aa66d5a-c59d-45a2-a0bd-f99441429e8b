package com.jlr.ecp.order.dal.mysql.order;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.order.dto.*;
import com.jlr.ecp.order.api.order.vo.ECouponOrderInfoPageVO;
import com.jlr.ecp.order.api.order.vo.BrandGoodsOrderInfoPageVO;
import com.jlr.ecp.order.api.order.vo.ECouponOrderItemVO;
import com.jlr.ecp.order.api.order.vo.BrandGoodsOrderItemVO;
import com.jlr.ecp.order.api.order.vo.OrderInfoPageVO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SummarySqlResultDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.ValetDashboardTempDTO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.mysql.order.po.OrderItemRefundVcsPo;
import com.jlr.ecp.order.enums.order.OrderTypeEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【t_order_info(t_order_info)】的数据库操作Mapper
 * @createDate 2023-12-20 10:41:04
 * @Entity com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO
 */
@Mapper
public interface OrderInfoDOMapper extends BaseMapperX<OrderInfoDO> {


    /**
     * 分页列表
     *
     * @param page 分页
     * @param dto  参数
     * @return 返回
     */
    Page<OrderInfoPageVO> getPage(Page<OrderPageReqDTO> page, @Param("dto") OrderPageReqDTO dto);

    /**
     *  代客下单角色分页列表
     *
     * @param page 分页
     * @param dto  参数
     * @return 返回
     */
    Page<OrderInfoPageVO> getValetPage(Page<OrderPageReqDTO> page, @Param("dto") OrderPageReqDTO dto);

    /**
     * getEcouponOrdersWithPaging
     *
     * @param page 分页
     * @param dto  参数
     * @return 返回
     */
    Page<ECouponOrderInfoPageVO> getEcouponOrdersWithPaging(Page<ECouponOrderPageReqDTO> page, @Param("dto") ECouponOrderPageReqDTO dto);

    /**
     * getEcouponOrderDetailsByCodes
     *
     * @param orderItemCodes 订单商品项编码
     * @return 订单详情列表
     */
    List<ECouponOrderItemVO> getEcouponOrderDetailsByCodes(@Param("orderItemCodes") List<String> orderItemCodes);

    /**
     * getBrandGoodsOrdersWithPaging
     *
     * @param page 分页
     * @param dto  参数
     * @return 返回
     */
    Page<BrandGoodsOrderInfoPageVO> getBrandGoodsOrdersWithPaging(Page<BrandGoodsOrderPageReqDTO> page, @Param("dto") BrandGoodsOrderPageReqDTO dto);
    
    /**
     * getBrandGoodsOrderDetailsByCodes
     *
     * @param orderItemCodes 订单商品项编码
     * @return 订单详情列表
     */
    List<BrandGoodsOrderItemVO> getBrandGoodsOrderDetailsByCodes(@Param("orderItemCodes") List<String> orderItemCodes);

    /**
     * getParentCode
     *
     * @param code 当前订单编码
     */
    default OrderInfoDO getParentCode(String code) {
        return selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, code)
                .eq(BaseDO::getIsDeleted, false)
                .last(Constants.LIMIT_ONE)
                .select(OrderInfoDO::getParentOrderCode, OrderInfoDO::getId));
    }

    /**
     * 获取订单状态
     *
     * @param orderCode
     * @return
     */
    @Select("SELECT order_status FROM t_order_info WHERE order_code = #{orderCode} and is_deleted = 0")
    Integer getOrderStatusByOrderCode(@Param("orderCode") String orderCode);

    /**
     * 通过订单号码查询订单信息
     *
     * @param orderCode 订单编号
     * @return OrderInfoDO
     */
    @Select("SELECT * FROM t_order_info WHERE order_code = #{orderCode} and is_deleted = 0")
    OrderInfoDO queryOrderDoByOrderCode(@Param("orderCode") String orderCode);

    default OrderInfoDO getOrderInfoByOrderCode(String orderCode) {
        return selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(OrderInfoDO::getId)
                .last(Constants.LIMIT_ONE)
        );
    }

    @Select("SELECT * FROM t_order_info WHERE order_code in #{orderCodeList} and is_deleted = 0 order by order_time asc")
    List<OrderInfoDO> queryOrderDoByOrderCodeList(@Param("orderCodeList") List<String> orderCodeList);

    /**
     * 通过订单号码查询订单信息
     *
     * @param parentOrderCode 父订单编号
     * @return OrderInfoDO
     */
    @Select("SELECT * FROM t_order_info WHERE parent_order_code = #{parentOrderCode} and is_deleted = 0 order by order_time desc")
    List<OrderInfoDO> queryOrderDoByParentCode(@Param("parentOrderCode") String parentOrderCode);

    /**
     * 基于 List<car_vin> 的关联查询 t_vcs_order_info 和 t_order_info 表，以获取处于未支付状态的订单信息
     *
     * @param
     * @return OrderCode
     */
    List<OrderUnpaidRelaDTO> selectUnpaidOrAfterSalesOrdersForVCS(@Param("carVinAndServiceTypeList") List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList);

    /**
     * 基于carvin和incontrol账号 查询电话号码
     * @param  carVinList  carVin列表
     * @return List<PhoneByCarVinDTO>
     * */
    List<OrderCarVinDTO> queryPhoneListByCarVin(@Param("carVinList") List<String> carVinList);

    /**
     * 根据itemCode查询商品code和carVin
     * @param orderItemCodes itemCode
     * @return
     */
    List<CarVinAndServiceTypeDTO> queryCarVinAndServiceTypeByItemCodes(@Param("orderItemCodeList") List<String> orderItemCodes);

    /**
     * 根据商品code和carVin查询orderCode
     * @param  dto carvin和productCode
     * @return
     */
    String findOrderCodeByCarVinAndServiceType(@Param("dto") CarVinAndServiceTypeDTO dto);

    /**
     * 根据Vin查询在途订单
     * @param  carVinMd5 carVinMd5
     * @param  serviceType serviceType
     * @param  orderStatusList orderStatusList
     * @return List<OrderInfoDO>
     */
    List<OrderInfoDO> selectOrderInTransit(@Param("carVinMd5") String carVinMd5,
                                           @Param("serviceType") Integer serviceType,
                                           @Param("orderStatusList") List<Integer> orderStatusList);

    /**
     * 根据VinList查询在途订单
     * @param  carVinSet carVinSet
     * @param  serviceType serviceType
     * @param  orderStatusList orderStatusList
     * @return List<OrderInfoDO>
     */
    List<String> selectOrderInTransitByVinSet(@Param("carVinSet") Set<String> carVinSet,
                                           @Param("serviceType") Integer serviceType,
                                           @Param("orderStatusList") List<Integer> orderStatusList);

    default List<OrderInfoDO> queryVCSSubOrderInfoByParentCode(String parentOrderCode,String businessCode){
        return selectList(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getParentOrderCode, parentOrderCode)
                .eq(BaseDO::getIsDeleted, false)
                //orderType >0
                .gt(OrderInfoDO::getOrderType, OrderTypeEnum.PARENT.getCode())
                .eq(OrderInfoDO::getBusinessCode, businessCode));
    }

    /***
     * <AUTHOR>
     * @description 根据orderCodeList查询订单子表，订单项退款信息以及VCS信息
     * @date 2025/3/8 14:39
     * @param orderCodeList:
     * @return: java.util.List<com.jlr.ecp.order.dal.mysql.order.po.OrderItemRefundVcsPo>
    */

    List<OrderItemRefundVcsPo> selectOrderItemInfo(@Param("orderCodeList") List<String> orderCodeList);

    /**
     * Dashboard-Product-产品销售趋势 根据渠道查询订单数量和订单金额
     */
    List<SqlResultDTO> getSalesTrendByChannel(SqlQueryDTO dto);

    /**
     * Dashboard-SaleSummary-销售总览 查询订单数量和订单金额
     */
    List<SummarySqlResultDTO> getSalesSummaryGroupChannel(SqlQueryDTO dto);

    /**
     * Dashboard-Valet-代客下单趋势图 根据渠道查询订单数量和订单金额
     */
    List<SqlResultDTO> getSalesTrendByChannelByValet(SqlQueryDTO sqlQueryDTO);

    /**
     * Dashboard-Product-产品及不同属性销售占比
     */
    List<SqlResultDTO> getSalesProportionByChannel(SqlQueryDTO dto);

    /**
     * Dashboard-Product-产品销售趋势 根据车型查询订单数量和订单金额
     */
    List<SqlResultDTO> getSalesTrendBySeriesCode(SqlQueryDTO dto);

    /**
     * Dashboard-Product-产品销售趋势 根据车型查询订单数量和订单金额
     */
    List<SummarySqlResultDTO> getSalesSummaryGroupSeriesCode(SqlQueryDTO dto);

    /**
     * Dashboard-Product-产品销售趋势 根据商品名称查询订单数量和订单金额
     */
    List<SummarySqlResultDTO> getSalesSummaryGroupProductName(SqlQueryDTO sqlQueryDTO);

    /**
     * Dashboard-Product-产品及不同属性销售占比 根据车型查询订单数量和订单金额
     */
    List<SqlResultDTO> getSalesProportionBySeriesCode(SqlQueryDTO dto);

    /**
     * Dashboard-Product-产品销售趋势 根据渠道查询退单金额
     */
    List<SqlResultDTO> getSalesTrendRefundByChannel(SqlQueryDTO dto);

    /**
     * Dashboard-SaleSummary-销售总览 查询退单金额
     */
    List<SummarySqlResultDTO> getSalesSummaryRefund(SqlQueryDTO dto);

    /**
     * Dashboard-Valet-代客下单趋势图 根据渠道查询退单金额
     */
    List<SqlResultDTO> getSalesTrendRefundByChannelByValet(SqlQueryDTO sqlQueryDTO);

    /**
     * Dashboard-Product-产品及不同属性销售占比 根据渠道查询退单金额
     */
    List<SqlResultDTO> getSalesProportionRefundByChannel(SqlQueryDTO dto);

    /**
     * Dashboard-Product-产品销售趋势 根据车型查询退单金额
     */
    List<SqlResultDTO> getSalesTrendRefundBySeriesCode(SqlQueryDTO dto);

    /**
     * Dashboard-SaleSummary-销售总览 车型查询退单金额
     */
    List<SummarySqlResultDTO> getSalesSummaryRefundGroupSeriesCode(SqlQueryDTO dto);

    /**
     * Dashboard-SaleSummary-销售总览 车型查询退单金额
     */
    List<SummarySqlResultDTO> getSalesSummaryRefundGroupProductName(SqlQueryDTO sqlQueryDTO);

    /**
     * Dashboard-Product-产品及不同属性销售占比 根据车型查询退单金额
     */
    List<SqlResultDTO> getSalesProportionRefundBySeriesCode(SqlQueryDTO dto);

    /**
     * Dashboard-Product-产品销售趋势 根据渠道查询商品数量
     */
    List<SqlResultDTO> getSalesTrendProductCountByChannel(SqlQueryDTO dto);

    List<SummarySqlResultDTO> getSalesSummaryProductCountGroupChannel(SqlQueryDTO dto);

    /**
     * Dashboard-Valet-代客下单趋势图 根据渠道查询商品数量
     */
    List<SqlResultDTO> getSalesTrendProductCountByChannelByValet(SqlQueryDTO sqlQueryDTO);

    /**
     * Dashboard-Product-产品及不同属性销售占比 根据渠道查询商品数量
     */
    List<SqlResultDTO> getSalesProportionProductCountByChannel(SqlQueryDTO dto);

    /**
     * Dashboard-Product-产品销售趋势 根据车型查询商品数量
     */
    List<SqlResultDTO> getSalesTrendProductCountBySeriesCode(SqlQueryDTO dto);

    /**
     * Dashboard-sales-销售总览 车型查询商品数量
     */
    List<SummarySqlResultDTO> getSalesSummaryProductCountGroupSeriesCode(SqlQueryDTO dto);

    /**
     * Dashboard-Product-产品及不同属性销售占比 根据车型查询商品数量
     */
    List<SqlResultDTO> getSalesProportionProductCountBySeriesCode(SqlQueryDTO dto);

    /**
     * 查
     * 1.代客下单订单数 （order_channel为5代客下单）
     * 2.订单总数量
     */
    ValetDashboardTempDTO countOrderByChannel(@Param("orderChannel") String orderChannel, @Param("start") String start, @Param("end") String end);

    /**
     * 查
     * 2.订单总数量
     */
    ValetDashboardTempDTO countTotalOrderCount(@Param("start") String start, @Param("end") String end,@Param("businessCode") String businessCode);

    /**
     * 查
     * 3.代客下单成交数（支付状态为“已支付” payment_status为1：已支付）
     * 4.已支付状态下的订单总数量
     * <p>
     * 5.代客下单交易额（支付状态为“已支付”状态的所有”代客下单实付cost_amount 总金额（含税）“之和）
     * 6.已支付状态下 的 所有订单交易额
     * <p>
     */
    ValetDashboardTempDTO countOrderByPaymentStatus(@Param("orderChannel") String orderChannel, @Param("start") String start, @Param("end") String end);

    /**
     * 查
     * 4.已支付状态下的订单总数量
     * 6.已支付状态下 的 所有订单交易额
     */
    ValetDashboardTempDTO countTotalOrderByPaymentStatus(@Param("start") String start, @Param("end") String end,@Param("businessCode") String businessCode);

    /**
     * 查
     * 9.已支付状态下 代客下单成交商品总数
     * 10.已支付状态下 所有订单成交商品总数
     */
    ValetDashboardTempDTO countOrderItemByChannel(@Param("orderChannel") String orderChannel, @Param("start") String start, @Param("end") String end);

    /**
     * 查
     * 10.已支付状态下 所有订单成交商品总数
     */
    ValetDashboardTempDTO countTotalOrderItemQuantity(@Param("start") String start, @Param("end") String end,@Param("businessCode") String businessCode);

    IPage<OrderInfoDO> selectConsumerOrderPage(@Param("page") Page<OrderInfoDO> page,@Param("consumerCode") String consumerCode,@Param("orderStatus") String orderStatus);

    default List<OrderInfoDO> listChildOrder(String parentOrderCode) {
        return selectList(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getParentOrderCode, parentOrderCode)
                .eq(OrderInfoDO::getIsDeleted, Boolean.FALSE)
                .orderByAsc(OrderInfoDO::getId)
        );
    }
}





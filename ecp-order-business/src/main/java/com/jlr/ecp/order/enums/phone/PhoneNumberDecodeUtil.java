package com.jlr.ecp.order.enums.phone;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.system.api.permission.PermissionApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class PhoneNumberDecodeUtil {

    @Resource
    private PermissionApi permissionApi;

    /**
     * 获取解密的原文
     * @param encodeTxt 加密的密文
     * @return String
     * */
    public String getDecodePhone(String encodeTxt) {
        // log.info("解密手机号入参, encodePhone:{}", encodeTxt);
        // if (PhoneNumberValidator.isValidChinaMobileNumber(encodeTxt)) {
        //     return encodeTxt;
        // }
        // 解密手机号
        String decodeTxt = null;
        CommonResult<String> commonResult = null;
        try {
            JSONObject json = new JSONObject();
            json.put("encryptTex", encodeTxt);
            commonResult = permissionApi.getDecryptText(json);
        } catch (Exception e) {
            log.error("手机号码解密异常：", e);
        }
        if (commonResult != null && commonResult.isSuccess() && commonResult.getData() != null) {
            decodeTxt = commonResult.getData();
        }
        // log.info("解密手机号结果, decodePhone:{}", decodeTxt);
        return decodeTxt;
    }

    /**
     * 获取加密后的的字符串
     * @param originStr 原始字符串
     * @return String  加密字符串
     * */
    public String getEncryptText(String originStr) {
        String resultStr = CharSequenceUtil.EMPTY;
        if (StrUtil.isBlank(originStr)) {
            return resultStr;
        }
        CommonResult<String> commonResult = null;
        try {
            commonResult = permissionApi.getEncryptText(originStr);
        } catch (Exception e) {
            log.error("调用system加密服务异常：", e);
        }
        if (commonResult != null && commonResult.isSuccess() && StrUtil.isNotBlank(commonResult.getData())) {
            resultStr = commonResult.getData();
        }
        return resultStr;
    }

    /**
     * 获取指定icr账号的半隐藏数据  要求只看到@后面的字符 前面的隐藏
     * */
    public String getIncontrolIdMix(String incontrolId) {
        return StrUtil.replace(incontrolId, 0, incontrolId.indexOf("@"), '*');
    }

    /**
     * 获取指定vin号的半隐藏数据  要求只看到前6位 后面的隐藏
     * */
    public String getVinMix(String vin) {
        return StrUtil.replace(vin, 6, vin.length(), '*');
    }
}

package com.jlr.ecp.order.service.refund.ecoupon;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.order.api.refund.dto.*;
import com.jlr.ecp.order.controller.app.refund.vo.EcouponRefundItemDetailVO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;

import java.util.List;

/**
* <AUTHOR>
* @description 虚拟电子券类型退款
* @createDate 20250306
*/
public interface EcouponOrderRefundDOService extends IService<OrderRefundDO> {





    /***
     * 电子券退款
     * @param refundApplyDTOs
     * @param operationType  "操作类型 1-用户发起 2-系统自动 3-运营发起" RefundOrderOperationTypeEnum
     * @return Boolean
     */
    String ecouponOrderRefundApply(List<BaseOrderRefundApplyDTO> refundApplyDTOs , Integer operationType);

    /**
     * 电子券退款详情
     * @param orderItemCode
     * @param orderRefundCode
     * @return
     */
    EcouponRefundItemDetailVO getEcouponRefundItemDetail(String orderItemCode, String orderRefundCode);
}

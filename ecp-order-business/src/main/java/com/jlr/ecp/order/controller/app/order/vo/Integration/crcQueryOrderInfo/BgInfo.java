package com.jlr.ecp.order.controller.app.order.vo.Integration.crcQueryOrderInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * BG 相关信息类
 */

@Schema(description = "BG 相关信息 eg.首商品信息、订单信息、商品列表item信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BgInfo {

    @Schema(description = "第一个商品 的 商品名称")
    private String productName;

    @Schema(description = "第一个商品 的 商品主图URL")
    private String productImageUrl;

    @Schema(description = "商品总数量")
    private String productTotalQuantity;

    @Schema(description = "第一个商品 的 商品属性")
    private String productAttribute;

    @Schema(description = "订单 实付价格")
    private String costAmount;

    @Schema(description = "订单 使用的积分")
    private Integer costPoints;

    @Schema(description = "商品列表")
    private List<BgGood> goodList;

//    @Schema(description = "是否有更多商品")
//    private Boolean moreProduct;
}
package com.jlr.ecp.order.controller.admin.dashboard;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.controller.admin.dashboard.dto.KpiQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.ValetDashboardReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.ValetSalesTrendReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ValetDashboardRespVO;
import com.jlr.ecp.order.enums.dashboard.KpiEnumForValet;
import com.jlr.ecp.order.service.dashboard.ValetDashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Tag(name = "后台管理端 - 代客下单仪表盘")
@RestController
@RequestMapping("/v1/dashboard/valet")
@Validated
@Slf4j
public class ValetOrderController {

    @Resource
    private ValetDashboardService valetDashboardService;

    @PostMapping("/statistics")
    @Operation(summary = "获取dashboard 代客下单统计信息")
    @PreAuthorize("@ss.hasPermission('dashboard:order:index')")
    public CommonResult<ValetDashboardRespVO> getStatistics(@RequestBody @Valid ValetDashboardReqDTO reqDTO) {
        log.info("获取dashboard 代客下单统计信息, 入参reqDTO={}", JSON.toJSONString(reqDTO));

        // 处理时间格式
        String startTime = reqDTO.getStartTime() + " 00:00:00";
        String endTime = reqDTO.getEndTime() + " 23:59:59";
        reqDTO.setStartTime(startTime);
        reqDTO.setEndTime(endTime);

        log.info("获取dashboard 代客下单统计信息, 处理后的 入参reqDTO={}", JSON.toJSONString(reqDTO));
        return CommonResult.success(valetDashboardService.getValetOrderStatistics(reqDTO));
    }

    @Operation(summary = "代客下单销售趋势图")
    @PostMapping("/sales-trend")
    @PreAuthorize("@ss.hasPermission('dashboard:order:index')")
    public CommonResult<ProductSalesTrendRespVo> getSalesTrend(@RequestBody @Valid ValetSalesTrendReqDTO reqDTO) {
        log.info("获取代客下单销售趋势图请求参数: {}", JSON.toJSONString(reqDTO));
        return valetDashboardService.getSalesTrend(reqDTO);
    }

    @Operation(summary = "代客下单 KPI列表")
    @PostMapping("/kpi-list")
    public CommonResult<List<KpiQueryDTO>> getKpiList() {
        return valetDashboardService.getValetKpiList();
    }
}
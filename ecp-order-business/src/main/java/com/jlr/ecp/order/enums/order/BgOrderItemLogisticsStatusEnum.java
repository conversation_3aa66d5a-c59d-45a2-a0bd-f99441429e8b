package com.jlr.ecp.order.enums.order;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.enums.order
 * @className: ItemLogisticsStatus
 * @author: gaoqig
 * @description: t_order_item_logistics表里面的logistics_status字段枚举
 * @date: 2025/4/3 16:43
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum BgOrderItemLogisticsStatusEnum {
    /**
     * 快递状态：1-待发货，2-已发货，3-已签收(已妥投)，4-确认收货
     */
    IN_TRANSIT(1, "待发货"),
    COLLECTED(2, "已发货"),
    PROBLEMATIC(3, "已签收"),
    SIGNED(4, "确认收货"),

    ;

    private final int code;
    private final String name;
}

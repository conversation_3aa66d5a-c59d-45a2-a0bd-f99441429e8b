package com.jlr.ecp.order.kafka.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 取消订单消息
 *
 * <AUTHOR>
 * */

@NoArgsConstructor
@AllArgsConstructor
@Data
public class CancelOrderMessage {
    /**
     * 消息id
     * */
    private String messageId;

    /**
     *  父订单code
     */
    private String parentOrderCode;

    /**
     *  订单code
     */
    private String orderCode;

    /**
     * 租户号
     * */
    private Long tenantId;

    /**
     * 当前的发送时间
     * */
    private LocalDateTime sendTime;

    /**
     * 业务线编码
     */
    private String businessCode;
}

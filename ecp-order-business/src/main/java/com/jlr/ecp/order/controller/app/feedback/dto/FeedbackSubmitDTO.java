package com.jlr.ecp.order.controller.app.feedback.dto;

import com.jlr.ecp.order.api.order.vo.feedback.OrderFeedbackDimension;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@Schema(description = "提交评价的DTO")
public class FeedbackSubmitDTO {


    @Schema(description = "评价编码")
    @NotBlank(message = "feedbackCode不能为空")
    private String feedbackCode;

    @Schema(description ="快照编码：版本号")
    @NotBlank(message = "snapshotCode不能为空")
    private String snapshotCode;

    @NotBlank(message = "orderCode不能为空")
    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderCode;


    /**
     * feedback_dimensions 评价环节：PM开头、已支付OR、订单完成、CL、订单整单取消
     */
    @Schema(description = "评价维度：PM开头：已支付 OR：订单完成、 CL：订单整单取消")
    private String feedbackDimensions;


    /**
     * 单个环节下 评价总分
     */
    @Schema(description = "单个环节下 评价总分")
    private Integer total;


    /**
     * 单个环节下 不同的维度
     *  名称
     *  类型
     *  是否必填
     *  排序
     *  选项
     *  结果
     */
    @Schema(description = "单个环节下 不同的 维度 eg. 购物满意度、产品满意度")
    private List<OrderFeedbackDimension> dimensionsContent;


    /**
     * 输入框额外内容
     */
    @Schema(description = "单个环节下 输入框额外内容")
    private String inputExtra;
}
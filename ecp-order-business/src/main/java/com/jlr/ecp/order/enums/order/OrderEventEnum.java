package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 * 触发事件：
 * 1：发起整单退款申请
 * 2：发起部分退款申请
 * 3：同意整单退款申请
 * 4：同意部分退款申请
 * 5：订单整单退款完成
 * 6：订单部分退款完成
 * 7：拒绝整单退单申请
 * 8：拒绝部分退款申请
 */
@AllArgsConstructor
@Getter
public enum OrderEventEnum {


    /****
     * 触发事件：
     * 1：支付成功回调，回调更新
     * 2：TSDP查询结果，回调更新
     */
    EVENT_PAYMENT_SUCCESS_CALLBACK(100, "支付成功，回调更新"),
    EVENT_PAYMENT(101, "支付回调"),
    EVENT_TSDP_CALLBACK(102, "TSDP查询结果，回调更新");

    /**
     * code
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;


}

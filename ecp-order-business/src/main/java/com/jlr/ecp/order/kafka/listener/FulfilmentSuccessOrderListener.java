package com.jlr.ecp.order.kafka.listener;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.kafka.FufilmentSuccessMessage;
import com.jlr.ecp.order.service.refund.OrderRefundDOService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.errors.TimeoutException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.KafkaException;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;


/**
 * 激活状态同步消费
 *
 * <AUTHOR>
 * */
@Component
@Slf4j
public class FulfilmentSuccessOrderListener {

    @Resource
    private OrderRefundDOService orderRefundService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private static final  String KEY  = "orderComplete:";

    @KafkaListener(topics= KafkaConstants.ORDER_COMPLETE_STATUS_UPDATES_TOPIC, groupId = "order-complete-status-updates-group", properties = "max.poll.records:1")
    @Retryable(value = {KafkaException.class, TimeoutException.class}, exclude = Exception.class, backoff = @Backoff(delay = 5000, multiplier = 2, maxDelay = 30000), maxAttempts = 2)
    public void onMessage(String message) {
        log.info("order fulfilment success 消费消息, message:{}", message);

        try {
            FufilmentSuccessMessage fufilmentSuccessMessage = JSON.parseObject(message, FufilmentSuccessMessage.class);

            boolean checkRes = redisTemplate.opsForValue().setIfAbsent(KEY+fufilmentSuccessMessage.getVcsOrderCode(), "1", 1, TimeUnit.MINUTES);
            if (!checkRes) {
                log.info("order fulfilment success 消费消息，重复消费，cancelMessage:{}", fufilmentSuccessMessage);
                return;
            }
            TenantContextHolder.setTenantId(fufilmentSuccessMessage.getTenantId());
            Integer integer = orderRefundService.tsdpCallBack(fufilmentSuccessMessage.getVcsOrderCode(),fufilmentSuccessMessage.getUpdateStatus());
            if (integer>0){
                log.info("order fulfilment success 状态同步成功");
            }else {
                log.info("order fulfilment success 状态同步失败");
            }
        }  finally {
            TenantContextHolder.clear();
        }
    }

}

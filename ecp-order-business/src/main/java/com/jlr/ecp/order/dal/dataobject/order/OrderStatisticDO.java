package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_order_statistic
 * @TableName t_order_statistic
 */
@TableName(value ="t_order_statistic")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderStatisticDO extends BaseDO {
    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 用户编码;用户编码
     */
    @TableField(value = "consumer_code")
    private String consumerCode;

    /**
     * 订单总数量;订单总数量
     */
    @TableField(value = "order_total_count")
    private Integer orderTotalCount;

    /**
     * vcs订单总数量;vcs订单总数量
     */
    @TableField(value = "vcs_order_total_count")
    private Integer vcsOrderTotalCount;

    /**
     * goods订单总数量;goods订单总数量
     */
    @TableField(value = "brand_goods_total_count")
    private Integer brandGoodsTotalCount;
}
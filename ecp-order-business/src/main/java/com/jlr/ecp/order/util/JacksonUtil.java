package com.jlr.ecp.order.util;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

public class JacksonUtil {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final Logger log = LoggerFactory.getLogger(JacksonUtil.class);
    private static final ObjectMapper mapper = new ObjectMapper();
    private static final ObjectMapper publicMapper = new ObjectMapper();

    public static ObjectMapper getMapper() {
        return publicMapper;
    }

    public static String bean2Json(Object data) {
        try {
            String result = mapper.writeValueAsString(data);
            return result;
        } catch (JsonProcessingException var2) {
            throw new RuntimeException(var2);
        }
    }

    public static <T> T json2Bean(String jsonData, Class<T> beanType) {
        try {
            T result = (T)mapper.readValue(jsonData, beanType);
            return result;
        } catch (Exception var3) {
            log.error("json2Bean error", var3);
            throw new RuntimeException(var3);
        }
    }

    public static <T> List<T> json2List(String jsonData, Class<T> beanType) {
        JavaType javaType = mapper.getTypeFactory().constructParametricType(List.class, new Class[]{beanType});

        try {
            List<T> resultList = (List)mapper.readValue(jsonData, javaType);
            return resultList;
        } catch (Exception var4) {
            log.error("json2List error", var4);
            throw new RuntimeException(var4);
        }
    }

    public static <K, V> Map<K, V> json2Map(String jsonData, Class<K> keyType, Class<V> valueType) {
        JavaType javaType = mapper.getTypeFactory().constructMapType(Map.class, keyType, valueType);

        try {
            Map<K, V> resultMap = (Map)mapper.readValue(jsonData, javaType);
            return resultMap;
        } catch (Exception e) {
            log.error("json2Map error", e);
            return null;
        }
    }

    public static <T> T json2Bean(String jsonData, TypeReference<T> typeReference) {
        try {
            return (T)mapper.readValue(jsonData, typeReference);
        } catch (Exception var3) {
            log.error("json2Bean error", var3);
            throw new RuntimeException(var3);
        }
    }

    public static JsonNode json2Node(String jsonData) {
        try {
            return mapper.readTree(jsonData);
        } catch (Exception var1) {
            log.error("json2Node error", var1);
            throw new RuntimeException(var1);
        }
    }

    static {
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DATE_TIME_FORMATTER));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DATE_FORMATTER));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(TIME_FORMATTER));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DATE_TIME_FORMATTER));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(DATE_FORMATTER));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(TIME_FORMATTER));
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai")).configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.configure(SerializationFeature.WRITE_DURATIONS_AS_TIMESTAMPS, false);
        mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        mapper.registerModule(javaTimeModule);
        publicMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        publicMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        publicMapper.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai")).configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        publicMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        publicMapper.configure(SerializationFeature.WRITE_DURATIONS_AS_TIMESTAMPS, false);
        publicMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        publicMapper.registerModule(javaTimeModule);
    }

}

package com.jlr.ecp.order.controller.admin.feedback.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - FeedBackConfigVO")
public class FeedBackConfigVO {

    @Schema(description = "业务线编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String businessCode;


    @Schema(description = "评价维度编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String feedbackCode;

    /**
     * 评价维度：PM开头、已支付OR、订单完成、CL、订单整单取消
     */
    @Schema(description = "评价适用环节：PM开头、已支付OR、订单完成、CL、订单整单取消", requiredMode = Schema.RequiredMode.REQUIRED)
    private String feedbackDimensions;


    /**
     * 评价维度：PM开头、已支付OR、订单完成、CL、订单整单取消
     */
    @Schema(description = "评价适用环节：PM开头、已支付OR、订单完成、CL、订单整单取消", requiredMode = Schema.RequiredMode.REQUIRED)
    private String feedbackDimensionsText;


    /**
     * 是否设置输入框：0=否；1=是
     */
    @Schema(description = "是否设置输入框：0=否；1=是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer enableInput;

    /**
     * 输入框是否必填：0=否；1=是
     */
    @Schema(description = "输入框是否必填：0=否；1=是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer mustInput;

    /**
     * 输入框提示信息
     */
    @Schema(description = "输入框提示信息是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String inputText;

    /**
     * 备注说明
     */
    @Schema(description = "说明", requiredMode = Schema.RequiredMode.REQUIRED)
    private String remark;


    /**
     * 评价维度配置信息
     */
    @Schema(description = "评价维度配置信息", requiredMode = Schema.RequiredMode.REQUIRED)
    List<FeedbackDimensionsVO> dimensionsList;



    /**
     * 商品使用状态(启用状态，0=待启用 1=已启用 2=已停用)
     */
    @Schema(description = "商品使用状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer enableStatus;

    /**
     * 商品使用状态(启用状态，0=待启用 1=启用 2=停用)
     */
    @Schema(description = "商品使用状态文本", requiredMode = Schema.RequiredMode.REQUIRED)
    private String enableStatusText;

    /**
     * 启用时间
     */
    @Schema(description = "启用时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime enableTime;


    /**
     * 停用时间
     */
    @Schema(description = "停用时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime downtime;

    /**
     * 定时启用时间
     */
    @Schema(description = "定时启用时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime scheduleEnableTime;

    /**
     * 版本号
     */
    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer revision;


    /**
     * 最后修改人;最后修改人编码
     */
    @Schema(description = "最后修改人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String lastModifyUser;

    /**
     * 最新编辑时间
     */
    @Schema(description = "最新编辑时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;

}

package com.jlr.ecp.order.component;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.dal.dataobject.order.OrderDiscountDetailDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderDiscountDetailDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.enums.payment.DiscountTypeEnum;
import com.jlr.ecp.order.enums.payment.PayCenterBizLineCodeEnum;
import com.jlr.ecp.order.enums.payment.PayTypeEnum;
import com.jlr.ecp.payment.api.order.vo.SubmitPayOrderReq;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SubmitPayOrderComponent
 *
 * <AUTHOR> John Gen
 * @since 2025-03-13 14:47:40
 */
@Slf4j
@Component
public class SubmitPayOrderComponent {

    @Resource
    private OrderDiscountDetailDOMapper orderDiscountDetailDOMapper;

    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    /**
     * processOrderItem
     */
    public List<SubmitPayOrderReq.OrderItem> processToOrderItemList(OrderInfoDO orderInfo, List<OrderInfoDO> childOrders, Set<String> payTypeSet) {
        List<SubmitPayOrderReq.OrderItem> orderItems = new ArrayList<>();
        if (CollUtil.isNotEmpty(childOrders)) {
            for (OrderInfoDO childOrder : childOrders) {
                List<SubmitPayOrderReq.OrderItem> orderItemList = processOrderItem(childOrder, payTypeSet);
                if (ObjectUtils.isNotEmpty(orderItemList)) {
                    orderItems.addAll(orderItemList);
                }
            }
        } else {
            List<SubmitPayOrderReq.OrderItem> orderItemList = processOrderItem(orderInfo, payTypeSet);
            orderItems.addAll(orderItemList);
        }

        // 附加运费 到 orderItems
        if (Objects.nonNull(orderInfo.getFreightAmount())
                && orderInfo.getFreightAmount() > 0
                && CollectionUtils.isNotEmpty(orderItems)) {
            //1. 找出BG cash项, 取第一项附加运费
            //2. 如果没有cash项, 可能是零元付, 则新增cash项存储运费, 否则item中的payAmout和总的payAmount不一致
            List<SubmitPayOrderReq.OrderItem> bgItems = orderItems.stream().filter(it -> Objects.equals(PayCenterBizLineCodeEnum.BG, PayCenterBizLineCodeEnum.getByBusinessLineCode(it.getBusinessLine())))
                    .collect(Collectors.toList());
            // 没有BG items, 无需附加运费
            if (CollectionUtils.isEmpty(bgItems)) {
                return orderItems;
            }

            bgItems.stream().filter(it -> StringUtils.equals(it.getItemType(), "CASH"))
                    .findFirst()
                    .ifPresentOrElse(it -> {
                        // BG中有cash项, 取第一个附加运费
                        log.info("processToOrderItemList 附加运费到 productNo: {}", it.getProductNo());
                        it.setItemValue(it.getItemValue() + orderInfo.getFreightAmount());
                        it.setPayAmount(it.getPayAmount() + orderInfo.getFreightAmount());
                        it.setProductName(it.getProductName() + "(含运费)");
                    }, () -> {
                        //手动追加运费 item
                        //追加 payTypeSet --> 外层有数量判断
                        payTypeSet.add(PayTypeEnum.CASH.getCode());
                        SubmitPayOrderReq.OrderItem orderItem = new SubmitPayOrderReq.OrderItem();
                        orderItem.setPayAmount(Long.valueOf(orderInfo.getFreightAmount()));
                        orderItem.setItemValue(Long.valueOf(orderInfo.getFreightAmount()));
                        //取第一个bg item的productNo 作为运费 productNo
                        String productNo = bgItems.stream()
                                .findFirst()
                                .map(SubmitPayOrderReq.OrderItem::getProductNo)
                                .orElse("DEFAULT_PRODUCT_NO");
                        log.info("processToOrderItemList 新增运费item到 productNo: {}", productNo);
                        orderItem.setProductNo(productNo);
                        orderItem.setProductName("运费");
                        orderItem.setItemType(PayTypeEnum.CASH.getCode());
                        orderItem.setBusinessLine(PayCenterBizLineCodeEnum.BG.getBusinessLineCode());
                        orderItems.add(orderItem);
                    });
        }
        return orderItems;
    }

    /**
     * processOrderItem
     *
     * @param childOrderOrderInfoDo orderInfoDO
     * @return SubmitPayOrderReq.OrderItem
     */
    public List<SubmitPayOrderReq.OrderItem> processOrderItem(OrderInfoDO childOrderOrderInfoDo, Set<String> payTypeSet) {

        MultiValueMap<PayTypeEnum, SubmitPayOrderReq.OrderItem> orderItemsMap = new LinkedMultiValueMap<>();

        if (null == childOrderOrderInfoDo) {
            return new ArrayList<>();
        }

        List<OrderItemDO> orderItemDoList = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, childOrderOrderInfoDo.getOrderCode())
                .eq(OrderItemDO::getIsDeleted, false)
        );

        if (ObjectUtils.isNotEmpty(orderItemDoList)) {

            for (OrderItemDO orderItemDo : orderItemDoList) {

                // 现金支付 OrderItem
                if (Objects.nonNull(orderItemDo.getCostAmount()) && orderItemDo.getCostAmount() > 0) {

                    // 如果实付金额大于 0 则记录 PayType 为现金
                    payTypeSet.add(PayTypeEnum.CASH.getCode());

                    SubmitPayOrderReq.OrderItem cashPaymentItem = new SubmitPayOrderReq.OrderItem();

                    cashPaymentItem.setPayAmount(Long.valueOf(orderItemDo.getCostAmount()));
                    cashPaymentItem.setItemValue(Long.valueOf(orderItemDo.getCostAmount()));
                    cashPaymentItem.setProductNo(orderItemDo.getProductCode());
                    cashPaymentItem.setProductName(orderItemDo.getProductName());
                    cashPaymentItem.setItemType(PayTypeEnum.CASH.getCode());

                    String businessLine = getPaymentCenterBusinessCode(childOrderOrderInfoDo.getBusinessCode());
                    cashPaymentItem.setBusinessLine(businessLine);

                    orderItemsMap.add(PayTypeEnum.CASH, cashPaymentItem);
                }

                // 查询折扣详细
                OrderDiscountDetailDO orderDiscountDetailDo =
                        orderDiscountDetailDOMapper.selectOne(new LambdaQueryWrapper<OrderDiscountDetailDO>()
                                .eq(OrderDiscountDetailDO::getOrderItemCode, orderItemDo.getOrderItemCode())
                                .eq(OrderDiscountDetailDO::getIsDeleted, false));

                // 处理折扣详情
                processOrderDiscountDetail(
                        orderDiscountDetailDo, payTypeSet, orderItemDo,
                        childOrderOrderInfoDo, orderItemsMap);
            }
        }

        // 按照 PC-Payment 要求, 根据支付方式 + 业务线组合
        return processPayTypeItemsMap(orderItemsMap);
    }

    /**
     * 处理 Discount Detail
     *
     * @param orderDiscountDetailDo OrderDiscountDetailDO
     * @param payTypeSet            PayTypeSet
     * @param orderItemDo           OrderItemDO
     * @param childOrderOrderInfoDo OrderInfoDO
     * @param orderItemsMap         orderItemsMap
     */
    public void processOrderDiscountDetail(OrderDiscountDetailDO orderDiscountDetailDo, Set<String> payTypeSet,
                                           OrderItemDO orderItemDo, OrderInfoDO childOrderOrderInfoDo,
                                           MultiValueMap<PayTypeEnum, SubmitPayOrderReq.OrderItem> orderItemsMap) {

        if (null != orderDiscountDetailDo) {

            // 如果是优惠券优惠
            if (orderDiscountDetailDo.getDiscountType().equals(DiscountTypeEnum.COUPON_DISCOUNT.getType())) {

                // 添加支付方式为优惠券
                payTypeSet.add(PayTypeEnum.COUPON.getCode());

                SubmitPayOrderReq.OrderItem couponOrderItem = new SubmitPayOrderReq.OrderItem();

                couponOrderItem.setPayAmount(0L);
                couponOrderItem.setItemValue(Long.valueOf(orderDiscountDetailDo.getDiscountAmount()));
                couponOrderItem.setProductNo(orderItemDo.getProductCode());
                couponOrderItem.setProductName(orderItemDo.getProductName());
                couponOrderItem.setItemType(PayTypeEnum.COUPON.getCode());
                couponOrderItem.setItemModelCode(orderDiscountDetailDo.getCouponModelCode());
                couponOrderItem.setItemCode(orderDiscountDetailDo.getCouponCode());

                String businessLine = getPaymentCenterBusinessCode(childOrderOrderInfoDo.getBusinessCode());
                couponOrderItem.setBusinessLine(businessLine);

                orderItemsMap.add(PayTypeEnum.COUPON, couponOrderItem);
            }

            // 如果是积分优惠
            if (orderDiscountDetailDo.getDiscountType().equals(DiscountTypeEnum.POINT_DISCOUNT.getType())) {

                // 添加支付方式为积分
                payTypeSet.add(PayTypeEnum.POINTS.getCode());

                SubmitPayOrderReq.OrderItem pointOrderItem = new SubmitPayOrderReq.OrderItem();

                pointOrderItem.setPayAmount(0L);
                pointOrderItem.setItemValue(Long.valueOf(orderDiscountDetailDo.getCostPoints()));
                pointOrderItem.setProductNo(orderItemDo.getProductCode());
                pointOrderItem.setProductName(orderItemDo.getProductName());
                pointOrderItem.setItemType(PayTypeEnum.POINTS.getCode());

                String businessLine = getPaymentCenterBusinessCode(childOrderOrderInfoDo.getBusinessCode());
                pointOrderItem.setBusinessLine(businessLine);

                orderItemsMap.add(PayTypeEnum.POINTS, pointOrderItem);
            }
        }

    }

    /**
     * 按照 PC-Payment 要求, 根据支付方式 + 业务线组合
     *
     * @param orderItemsMap MultiValueMap of PayType and List SubmitPayOrderReq.OrderItem
     * @return list of SubmitPayOrderReq.OrderItem
     */
    public List<SubmitPayOrderReq.OrderItem> processPayTypeItemsMap(
            MultiValueMap<PayTypeEnum, SubmitPayOrderReq.OrderItem> orderItemsMap) {

        List<SubmitPayOrderReq.OrderItem> orderItems = new ArrayList<>();

        // 遍历支付方式
        for (Map.Entry<PayTypeEnum, List<SubmitPayOrderReq.OrderItem>> payTypeEnumListEntry : orderItemsMap.entrySet()) {
            List<SubmitPayOrderReq.OrderItem> items = payTypeEnumListEntry.getValue();

            // 如果 PayType 下只有一条 Item 记录
            if (items.size() == 1) {

                // 取第一条
                orderItems.add(items.get(0));

            } else
                // 如果 PayType 下有多条 Item 记录
                if (items.size() > 1) {

                    // 对多条记录进行组装
                    SubmitPayOrderReq.OrderItem orderItem = combineMultiItems(items);
                    if (null != orderItem) {
                        orderItems.add(orderItem);
                    }
                }
        }

        return orderItems;
    }

    /**
     * 按照 PayType 合并
     *
     * @param orderItems List of SubmitPayOrderReq.OrderItem
     * @return SubmitPayOrderReq.OrderItem
     */
    public SubmitPayOrderReq.OrderItem combineMultiItems(List<SubmitPayOrderReq.OrderItem> orderItems) {

        if (null == orderItems) {
            return null;
        }

        if (orderItems.size() == 1) {
            return orderItems.get(0);
        }

        if (orderItems.size() > 1) {
            SubmitPayOrderReq.OrderItem orderItem = new SubmitPayOrderReq.OrderItem();

            SubmitPayOrderReq.OrderItem firstOrderItem = orderItems.get(0);

            if (null != firstOrderItem) {

                // Set ItemCode
                orderItem.setItemCode(firstOrderItem.getItemCode());

                // Set ItemModelCode
                orderItem.setItemModelCode(firstOrderItem.getItemModelCode());

                // Set ProductName (有多个时取第一个)
                orderItem.setProductName(firstOrderItem.getProductName());

                // Set ProductNo (有多个时取第一个)
                orderItem.setProductNo(firstOrderItem.getProductNo());

                // Set BusinessLine (BusinessLine 是统一的)
                orderItem.setBusinessLine(firstOrderItem.getBusinessLine());

                // Set ItemType (ItemType 是统一的)
                orderItem.setItemType(firstOrderItem.getItemType());
            }

            // Set ItemValue
            Long itemValueSum = orderItems.stream().mapToLong(SubmitPayOrderReq.OrderItem::getItemValue).sum();
            orderItem.setItemValue(itemValueSum);

            // Set PayAmount
            Long payAmountSum = orderItems.stream().mapToLong(SubmitPayOrderReq.OrderItem::getPayAmount).sum();
            orderItem.setPayAmount(payAmountSum);

            return orderItem;
        }

        return null;
    }

    /**
     * 转换 Business Code
     *
     * @param businessCode BusinessIdEnum
     * @return String
     */
    public String getPaymentCenterBusinessCode(String businessCode) {

        if (null == businessCode) {
            return null;
        }

        if (businessCode.equals(BusinessIdEnum.LRE.getCode())) {
            return PayCenterBizLineCodeEnum.LRE.getBusinessLineCode();
        }

        if (businessCode.equals(BusinessIdEnum.BRAND_GOODS.getCode())) {
            return PayCenterBizLineCodeEnum.BG.getBusinessLineCode();
        }

        return null;
    }
}

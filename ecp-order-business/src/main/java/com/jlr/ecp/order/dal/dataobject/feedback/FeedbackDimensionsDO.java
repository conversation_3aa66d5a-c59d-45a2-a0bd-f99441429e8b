package com.jlr.ecp.order.dal.dataobject.feedback;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
@TableName("t_feedback_dimensions")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackDimensionsDO extends BaseDO {
    /**
     * 维度配置ID
     */
    @TableId
    private Long id;

    /**
     * 维度Code，雪花算法
     */
    @TableField("dimensions_code")
    private String dimensionsCode;

    /**
     * 评价编码：生成规则：feedback_dimensions+递增三位数编号（例如：PM001）
     */
    @TableField("feedback_code")
    private String feedbackCode;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 维度类型：0：星级（5分制）；1：单选题；2：多选题
     */
    @TableField("type")
    private Integer type;

    /**
     * 选项JSON格式："ICR APP","sort":"1"或"广告","sort":"2"
     */
    @TableField("option_json")
    private String optionJson;

    /**
     * 是否必填：0=否；1=是
     */
    @TableField("must_input")
    private Integer mustInput;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 排序字段
     */
    @TableField("sort")
    private Integer sort;


    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Integer tenantId;

}

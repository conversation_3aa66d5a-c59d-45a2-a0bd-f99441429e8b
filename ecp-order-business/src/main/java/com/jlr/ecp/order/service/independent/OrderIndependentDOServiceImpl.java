package com.jlr.ecp.order.service.independent;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.order.api.independent.dto.TriggerIndependentReqDTO;
import com.jlr.ecp.order.api.independent.dto.UpdateStatusToSuccReqDTO;
import com.jlr.ecp.order.api.refund.dto.OrderRefundItemWithStatusDTO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentDO;
import com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentItemDO;
import com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentOrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderCouponDetailDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.mysql.independent.OrderIndependentDOMapper;
import com.jlr.ecp.order.dal.mysql.independent.OrderIndependentItemDOMapper;
import com.jlr.ecp.order.dal.mysql.independent.OrderIndependentOrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderCouponDetailDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderPaymentRecordsMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.independent.OrderIndependentStatusEnum;
import com.jlr.ecp.order.enums.independent.OrderIndependentTypeEnum;
import com.jlr.ecp.order.enums.order.EcouponStatusEnum;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import com.jlr.ecp.order.service.coupon.status.dto.CouponStatusChangedKafkaDto;
import com.jlr.ecp.order.util.JacksonUtil;
import com.jlr.ecp.order.util.OrderTitleUtil;
import com.jlr.ecp.payment.api.independent.IndependentApi;
import com.jlr.ecp.payment.api.independent.dto.CreateIndependentReqDTO;
import com.jlr.ecp.payment.api.independent.dto.CreateIndependentRespDTO;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.order.enums.ErrorCodeConstants.INDEPENDENT_CALLBACK_FAIL;
import static com.jlr.ecp.order.enums.ErrorCodeConstants.INDEPENDENT_FAIL;

/**
 * <AUTHOR> Hongyi
 * TODO LRE要在券核销后就分账。
 */
@Service
@Slf4j
public class OrderIndependentDOServiceImpl extends ServiceImpl<OrderIndependentDOMapper, OrderIndependentDO>
        implements OrderIndependentDOService {

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;
    @Resource
    private OrderItemDOMapper orderItemDOMapper;
    @Resource
    private OrderPaymentRecordsMapper orderPaymentRecordsMapper;
    @Resource
    private OrderIndependentItemDOService orderIndependentItemDOService;
    @Resource
    private OrderIndependentOrderItemDOService orderIndependentOrderItemDOService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private Snowflake ecpIdUtil;
    @Resource
    private IndependentApi independentApi;
    @Resource
    private OrderRefundItemDOMapper orderRefundItemDOMapper;
    @Resource
    private OrderIndependentItemDOMapper orderIndependentItemDOMapper;
    @Resource
    private OrderIndependentOrderItemDOMapper orderIndependentOrderItemDOMapper;
    @Resource
    private OrderCouponDetailDOMapper orderCouponDetailDOMapper;

    @Override
    public Boolean triggerIndependentForCouponUsed(CouponStatusChangedKafkaDto message) {
        OrderInfoDO order = orderInfoDOMapper.queryOrderDoByOrderCode(message.getOrderCode());
        if (!OrderIndependentStatusEnum.TODO.getStatus().equals(order.getIndependentStatus())) {
            log.warn("券核销分账，订单不处于待分账状态，orderCode={}，couponCode={}", message.getOrderCode(), message.getCouponCode());
            return Boolean.TRUE;
        }

        OrderCouponDetailDO coupon = orderCouponDetailDOMapper.getByCouponCode(message.getCouponCode());
        if (coupon == null) {
            log.warn("券核销分账，券不存在，orderCode={}，couponCode={}", message.getOrderCode(), message.getCouponCode());
            return Boolean.TRUE;
        } else if (OrderIndependentStatusEnum.DONE.getStatus().equals(coupon.getIndependentStatus())) {
            log.warn("券核销分账，券已经分账，orderCode={}，couponCode={}", message.getOrderCode(), message.getCouponCode());
            return Boolean.TRUE;
        }

        String orderItemCode = message.getOrderItemCode();

        // 为了获取分账商户号
        OrderItemDO orderItem = orderItemDOMapper.getByOrderItemCode(orderItemCode);

        // 查询订单支付单号
        Map<String, String> payApplyNoMap = orderPaymentRecordsMapper.mapPayApplyNo(List.of(message.getOrderCode()));

        // 初始化分账申请，状态为"待分账"，更新券上的分账状态为"分账中"
        Pair<OrderIndependentDO, List<OrderIndependentItemDO>> initIndependent = initIndependentForCouponUsed(
                order, orderItem, payApplyNoMap, message.getTheLastOrderItemUsed(), coupon);
        if (initIndependent == null) {
            return Boolean.TRUE;
        }

        OrderIndependentDO independent = initIndependent.getKey();
        List<OrderIndependentItemDO> independentItems = initIndependent.getValue();

        // 通过Payment Service调用PC发起分账，更新分账申请的分账状态为"分账中"
        try {
            callPayment(order, independent, independentItems);
            log.info("券核销分账，订单调用分账接口完成，orderCode={}", order.getOrderCode());
        } catch (Exception e) {
            // 此处异常不影响下一笔订单进行分账处理
            log.error("券核销分账，订单调用分账接口异常，orderCode={}", independent.getOrderCode(), e);
            throw e;
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean triggerIndependentForOrderSucc(TriggerIndependentReqDTO req) {
        log.info("执行【订单完成】分账任务开始，req={}", JacksonUtil.bean2Json(req));

        // 没有指定只执行其他业务线的分账任务时，执行LRE【订单完成】分账任务
        if (StringUtils.isEmpty(req.getBusinessCode()) || BusinessIdEnum.LRE.getCode().equals(req.getBusinessCode())) {
            LocalDateTime lreCompletedTimeLimit = req.getLreCompletedTimeLimit()!=null?
                    req.getLreCompletedTimeLimit():LocalDateTime.now().truncatedTo(ChronoUnit.DAYS);
            triggerIndependentForOrderSucc(BusinessIdEnum.LRE.getCode(), lreCompletedTimeLimit, req.getOrderCodes());
        }

        // 没有指定只执行其他业务线的分账任务时，执行BG【订单完成】分账任务
        if (StringUtils.isEmpty(req.getBusinessCode()) || BusinessIdEnum.BRAND_GOODS.getCode().equals(req.getBusinessCode())) {
            LocalDateTime bgCompletedTimeLimit = req.getBgCompletedTimeLimit()!=null?
                    req.getBgCompletedTimeLimit():LocalDateTime.now().truncatedTo(ChronoUnit.DAYS).minusDays(30);
            triggerIndependentForOrderSucc(BusinessIdEnum.BRAND_GOODS.getCode(), bgCompletedTimeLimit, req.getOrderCodes());
        }

        log.info("执行【订单完成】分账任务结束");
        return Boolean.TRUE;
    }

    @Override
    public Boolean triggerIndependentForRetry(TriggerIndependentReqDTO req) {
        LocalDateTime createdTime = LocalDateTime.now().minusMinutes(30);
        log.info("分账任务重试开始，req={}，createdTime={}", JacksonUtil.bean2Json(req), createdTime);

        int limit = 200;
        while (true) {
            // 查询创建时间在30分钟之前且为待分账状态的分账任务
            List<OrderIndependentDO> independents = this.baseMapper.listTodoIndependent(createdTime, limit, req.getOrderCodes());
            for (OrderIndependentDO independent:independents) {
                OrderInfoDO order = orderInfoDOMapper.queryOrderDoByOrderCode(independent.getOrderCode());
                List<OrderIndependentItemDO> independentItems = orderIndependentItemDOMapper.listByIndependentCode(independent.getIndependentCode());
                // 通过Payment Service调用PC发起分账，更新分账申请的分账状态为"分账中"
                try {
                    callPayment(order, independent, independentItems);
                    log.info("分账任务重试，订单调用分账接口完成，independentCode={}", independent.getIndependentCode());
                } catch (Exception e) {
                    // 此处异常不影响下一笔订单进行分账处理
                    log.error("分账任务重试，订单调用分账接口异常，independentCode={}", independent.getIndependentCode(), e);
                }
            }

            if (limit > independents.size()) {
                break;
            }
        }

        log.info("分账任务重试结束");
        return Boolean.TRUE;
    }

    private Boolean triggerIndependentForOrderSucc(String businessCode, LocalDateTime completedTimeLimit, List<String> limitOrderCodes) {
        log.info("执行【订单完成】分账任务开始，businessCode={}，completedTimeLimit={}，limitOrderCodes={}", businessCode, completedTimeLimit, limitOrderCodes);
        while(true) {
            // 查询待分账的订单
            List<OrderInfoDO> orders = listTodoIndependent(businessCode, completedTimeLimit, limitOrderCodes, 200);
            log.info("执行【订单完成】分账任务，查询到待分账订单数量={}", orders.size());
            if (CollUtil.isEmpty(orders)) {
                log.info("执行【订单完成】分账任务完成，businessCode={}，completedTimeLimit={}，limitOrderCodes={}", businessCode, completedTimeLimit, limitOrderCodes);
                return Boolean.TRUE;
            }

            // 查询订单下所有订单行，并按照ID升序
            List<String> orderCodes = orders.stream().map(OrderInfoDO::getOrderCode).collect(Collectors.toList());
            Map<String, List<OrderItemDO>> orderItemMap = orderItemDOMapper.mapByOrderCode(orderCodes);

            // 查询订单相关的所有退单行
            Map<String, List<OrderRefundItemWithStatusDTO>> orderRefundItemMap = mapOrderRefundItem(orderCodes);

            // 查询订单支付单号
            Map<String, String> payApplyNoMap = orderPaymentRecordsMapper.mapPayApplyNo(orderCodes);

            // 查询订单下所有订单行的分账明细
            Map<String, List<OrderIndependentOrderItemDO>> orderItemIndependentMap = orderIndependentOrderItemDOMapper.mapByOrderItemCode(orderCodes);

            // 查询LRE订单下发放的所有券
            Map<String, List<OrderCouponDetailDO>> orderCouponDetailMap = new HashMap<>();
            if (BusinessIdEnum.LRE.getCode().equals(businessCode)) {
                orderCouponDetailMap = orderCouponDetailDOMapper.mapByOrderCode(orderCodes);
            }

            for (OrderInfoDO order:orders) {
                singleOrderProcessIndependent(businessCode, order, orderItemMap, orderRefundItemMap, orderCouponDetailMap, payApplyNoMap, orderItemIndependentMap);
            }
            log.info("执行【订单完成】分账任务，完成一批订单分账处理，继续查询下一批，businessCode={}", businessCode);
        }
    }

    private void singleOrderProcessIndependent(String businessCode, OrderInfoDO order, Map<String, List<OrderItemDO>> orderItemMap, Map<String, List<OrderRefundItemWithStatusDTO>> orderRefundItemMap, Map<String, List<OrderCouponDetailDO>> orderCouponDetailMap, Map<String, String> payApplyNoMap, Map<String, List<OrderIndependentOrderItemDO>> orderItemIndependentMap) {
        log.info("执行【订单完成】分账任务，开始处理订单，businessCode={}，orderCode={}", businessCode, order.getOrderCode());
        List<OrderItemDO> orderItems = orderItemMap.get(order.getOrderCode());

        // 如果订单存在进行中的售后单，则放弃分账，等到售后结束
        if (isRefunding(orderItems, orderRefundItemMap)) {
            log.warn("执行【订单完成】分账任务，订单存在进行中的售后单放弃分账，businessCode={}，orderCode={}", businessCode, order.getOrderCode());
            return;
        }

        // 如果LRE订单下的所有发放的券都是正常核销状态，则不用进行“订单完成”分账，因为已经通过券核销进行分账了
        if (isNoNeedIndependentForLRE(businessCode, order, orderCouponDetailMap)) return;

        // 初始化分账申请，状态为"待分账"，更新订单上的分账状态为"分账中"
        Pair<OrderIndependentDO, List<OrderIndependentItemDO>> initIndependent = initIndependentForOrderSucc(order, orderItems,
                payApplyNoMap, orderRefundItemMap, orderItemIndependentMap);
        if (initIndependent == null) {
            // 更新订单上的分账状态为"无需分账"
            updateToNoNeed(order);
            log.warn("执行【订单完成】分账任务，订单没有有效的订单行，直接设置为无需分账，businessCode={}，orderCode={}", businessCode, order.getOrderCode());
            return;
        }

        OrderIndependentDO independent = initIndependent.getKey();
        List<OrderIndependentItemDO> independentItems = initIndependent.getValue();

        // 通过Payment Service调用PC发起分账，更新分账申请的分账状态为"分账中"
        callPaymentWithCatch(order, independent, independentItems);
    }

    private boolean isNoNeedIndependentForLRE(String businessCode, OrderInfoDO order, Map<String, List<OrderCouponDetailDO>> orderCouponDetailMap) {
        // 如果LRE订单下的所有发放的券都是正常核销状态，则不用进行“订单完成”分账，因为已经通过券核销进行分账了
        if (BusinessIdEnum.LRE.getCode().equals(businessCode)) {
            List<OrderCouponDetailDO> orderCouponDetails = orderCouponDetailMap.get(order.getOrderCode());
            if (isAllVerified(order.getOrderCode(), orderCouponDetails)) {
                // 更新订单上的分账状态为"无需分账"
                updateToNoNeed(order);
                log.warn("执行【订单完成】分账任务，订单下的所有券都是正常核销状态，直接设置为无需分账，businessCode={}，orderCode={}", businessCode, order.getOrderCode());
                return true;
            }
        }
        return false;
    }

    private void callPaymentWithCatch(OrderInfoDO order, OrderIndependentDO independent, List<OrderIndependentItemDO> independentItems) {
        try {
            callPayment(order, independent, independentItems);
            log.info("执行【订单完成】分账任务，订单调用分账接口完成，orderCode={}", order.getOrderCode());
        } catch (Exception e) {
            // 此处异常不影响下一笔订单进行分账处理
            log.error("执行【订单完成】分账任务，订单调用分账接口异常，orderCode={}", independent.getOrderCode(), e);
        }
    }

    /**
     * 是否全部都是正常核销状态
     * @param orderCouponDetails
     * @return
     */
    private boolean isAllVerified(String orderCode, List<OrderCouponDetailDO> orderCouponDetails) {
        if (CollUtil.isEmpty(orderCouponDetails)) {
            log.warn("执行【订单完成】分账任务，订单下没有发放券的记录，orderCode={}", orderCode);
            return true;
        }

        for (OrderCouponDetailDO orderCouponDetail:orderCouponDetails) {
            if (orderCouponDetail.getStatus() == null
                    || !orderCouponDetail.getStatus().equals(EcouponStatusEnum.VERIFIED.getCode())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断订单是否存在进行中的售后单
     * @return
     */
    private boolean isRefunding(List<OrderItemDO> orderItems, Map<String, List<OrderRefundItemWithStatusDTO>> orderRefundItemMap) {
        for (OrderItemDO orderItem:orderItems) {
            List<OrderRefundItemWithStatusDTO> orderRefundItems = orderRefundItemMap.get(orderItem.getOrderItemCode());
            if (CollUtil.isEmpty(orderRefundItems)) {
                continue;
            }

            for (OrderRefundItemWithStatusDTO orderRefundItem:orderRefundItems) {
                if (OrderRefundStatusEnum.isRefunding(orderRefundItem.getRefundOrderStatus())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 计算分账价格
     * 1.非最后一张核销：分账价格（单位：分）=向下取整(实付金额/商品数量）（单位：分），采取舍掉末尾的方式
     * 1.最后一张核销：分账价格（单位：分）=实付金额-向下取整(实付金额/商品数量)*(商品数量-1)，采取舍掉末尾的方式
     * @return
     */
    private Integer calcDivAmt(OrderItemDO orderItem, Boolean theLastUsed) {
        Integer costAmount = orderItem.getCostAmount();//实付金额
        Integer quantity = orderItem.getProductQuantity();//商品数量
        if (quantity.equals(1)) {
            return costAmount;
        }

        Integer singlePrice = costAmount/quantity; // 默认就是向下取整
        if (Boolean.TRUE.equals(theLastUsed)) {
            return costAmount - singlePrice * (quantity - 1);
        } else {
            return singlePrice;
        }
    }

    /**
     * 查询订单行退款的金额（包含售后中、售后完成的），单位分
     */
    private Map<String, List<OrderRefundItemWithStatusDTO>> mapOrderRefundItem(List<String> orderCodes) {
        List<Integer> refundStatusList = List.of(
                OrderRefundStatusEnum.FULL_REFUND_APPLY.getCode(),
                OrderRefundStatusEnum.FULL_REFUND_APPROVE.getCode(),
                OrderRefundStatusEnum.FULL_REFUND_COMPLETED.getCode()
        );
        List<OrderRefundItemWithStatusDTO> orderItems = orderRefundItemDOMapper.listRefundByOrderCode(orderCodes, refundStatusList);
        return orderItems.stream().collect(Collectors.groupingBy(OrderRefundItemWithStatusDTO::getOrderItemCode));
    }

    /**
     * 更新订单上的分账状态为"无需分账"
     */
    private void updateToNoNeed(OrderInfoDO order) {
        OrderInfoDO updateOrder = new OrderInfoDO();
        updateOrder.setId(order.getId());
        updateOrder.setIndependentStatus(OrderIndependentStatusEnum.NO_NEED.getStatus());
        updateOrder.setUpdatedTime(LocalDateTime.now());
        updateOrder.setUpdatedBy(Constants.SYSTEM);
        orderInfoDOMapper.updateById(updateOrder);
    }

    @Override
    public Boolean updateStatusToSucc(UpdateStatusToSuccReqDTO req) {
        log.info("分账结果回调开始，transApplyNo={}，req={}", req.getTransApplyNo(), JacksonUtil.bean2Json(req));
        String transApplyNo = req.getTransApplyNo();
        OrderIndependentDO independent = this.lambdaQuery().eq(OrderIndependentDO::getTransApplyNo, transApplyNo)
                .eq(OrderIndependentDO::getIsDeleted, Boolean.FALSE)
                .one();
        if (OrderIndependentStatusEnum.DONE.getStatus().equals(independent.getIndependentStatus())) {
            return Boolean.TRUE;
        }

        if (OrderIndependentTypeEnum.ORDER_SUCCESS.getType().equals(independent.getIndependentType())) {
            OrderInfoDO orderInfo = orderInfoDOMapper.queryOrderDoByOrderCode(independent.getOrderCode());

            // 更新分账申请的状态为"已分账"、更新订单上的分账状态为"已分账"
            transactionTemplate.executeWithoutResult((status) -> {
                OrderIndependentDO updateIndependent = new OrderIndependentDO();
                updateIndependent.setId(independent.getId());
                updateIndependent.setIndependentStatus(OrderIndependentStatusEnum.DONE.getStatus());
                updateIndependent.setIndependentSuccTime(req.getFinishTime());
                updateIndependent.setUpdatedTime(LocalDateTime.now());
                updateIndependent.setUpdatedBy(Constants.SYSTEM);
                this.updateById(updateIndependent);

                OrderInfoDO updateOrder = new OrderInfoDO();
                updateOrder.setId(orderInfo.getId());
                updateOrder.setIndependentStatus(OrderIndependentStatusEnum.DONE.getStatus());
                updateOrder.setUpdatedTime(LocalDateTime.now());
                updateOrder.setUpdatedBy(Constants.SYSTEM);
                orderInfoDOMapper.updateById(updateOrder);
            });
            log.info("分账结果回调成功【订单完成】，transApplyNo={}", req.getTransApplyNo());
        } else if (OrderIndependentTypeEnum.COUPON_USED.getType().equals(independent.getIndependentType())) {
            OrderCouponDetailDO coupon = orderCouponDetailDOMapper.getByCouponCode(independent.getCouponCode());

            // 更新分账申请的状态为"已分账"、更新券上的分账状态为"已分账"
            transactionTemplate.executeWithoutResult((status) -> {
                OrderIndependentDO updateIndependent = new OrderIndependentDO();
                updateIndependent.setId(independent.getId());
                updateIndependent.setIndependentStatus(OrderIndependentStatusEnum.DONE.getStatus());
                updateIndependent.setIndependentSuccTime(req.getFinishTime());
                updateIndependent.setUpdatedTime(LocalDateTime.now());
                updateIndependent.setUpdatedBy(Constants.SYSTEM);
                this.updateById(updateIndependent);

                OrderCouponDetailDO updateCoupon = new OrderCouponDetailDO();
                updateCoupon.setId(coupon.getId());
                updateCoupon.setIndependentStatus(OrderIndependentStatusEnum.DONE.getStatus());
                updateCoupon.setIndependentSuccTime(req.getFinishTime());
                updateCoupon.setUpdatedTime(LocalDateTime.now());
                updateCoupon.setUpdatedBy(Constants.SYSTEM);
                orderCouponDetailDOMapper.updateById(updateCoupon);
            });
            log.info("分账结果回调成功【券核销】，transApplyNo={}", req.getTransApplyNo());
        } else {
            log.error("分账结果回调失败，非预期的分账类型，transApplyNo={}，independent={}", req.getTransApplyNo(), JacksonUtil.bean2Json(independent));
            throw exception(INDEPENDENT_CALLBACK_FAIL);
        }
        return Boolean.TRUE;
    }

    /**
     * 通过Payment Service调用PC发起分账，更新分账状态为"分账中"
     */
    private void callPayment(OrderInfoDO order, OrderIndependentDO independent, List<OrderIndependentItemDO> independentItems) {
        List<CreateIndependentReqDTO.TransItem> transItems = new ArrayList<>();
        for (OrderIndependentItemDO independentItem:independentItems) {
            CreateIndependentReqDTO.TransItem transItem = new CreateIndependentReqDTO.TransItem();
            transItem.setTransAcctId(independentItem.getMerchantAccountNo());
            transItem.setDivAmt(independentItem.getDivAmt());
            transItems.add(transItem);
        }

        String businessLine = null;
        if (BusinessIdEnum.LRE.getCode().equals(independent.getBusinessCode())) {
            businessLine = "LRE";
        } else if (BusinessIdEnum.BRAND_GOODS.getCode().equals(independent.getBusinessCode())) {
            businessLine = "BG";
        } else {
            throw new ServiceException(INDEPENDENT_FAIL.getCode(), "业务线不符合分账要求");
        }

        CreateIndependentReqDTO req = new CreateIndependentReqDTO();
        if (OrderIndependentTypeEnum.ORDER_SUCCESS.getType().equals(independent.getIndependentType())) {
            req.setReqSeqId("ORDER_SUCCESS-" + independent.getOrderCode());
        } else if (OrderIndependentTypeEnum.COUPON_USED.getType().equals(independent.getIndependentType())) {
            req.setReqSeqId("COUPON_USED-" + independent.getCouponCode());
        }
        req.setPayApplyNo(independent.getPayApplyNo());
        req.setBusinessLine(businessLine);
        req.setTotalDivAmt(independent.getTotalDivAmt());
        req.setRemark(OrderTitleUtil.getOrderTitle(Set.of(order.getBusinessCode())));
        req.setTransItems(transItems);
        log.info("调用Payment分账接口开始，orderCode={}，req={}", independent.getOrderCode(), JacksonUtil.bean2Json(req));
        CommonResult<CreateIndependentRespDTO> pcRes = independentApi.create(req);
        if (!pcRes.isSuccess()) {
            log.error("订单分账调用分账接口失败，orderCode={}，pcRes={}", independent.getOrderCode(), JacksonUtil.bean2Json(pcRes));
            throw exception(INDEPENDENT_FAIL);
        }
        log.info("调用Payment分账接口成功，orderCode={}, transApplyNo={}", independent.getOrderCode(), pcRes.getData().getTransApplyNo());

        // 更新分账状态为"分账中"
        OrderIndependentDO updateParam = new OrderIndependentDO();
        updateParam.setId(independent.getId());
        updateParam.setIndependentStatus(OrderIndependentStatusEnum.ING.getStatus());
        updateParam.setTransApplyNo(pcRes.getData().getTransApplyNo());
        updateParam.setUpdatedTime(LocalDateTime.now());
        updateParam.setUpdatedBy(Constants.SYSTEM);
        baseMapper.updateById(updateParam);
    }

    /**
     * 初始化分账类型为“订单完成”的分账申请，需要更新订单表中的分账状态
     */
    private Pair<OrderIndependentDO, List<OrderIndependentItemDO>> initIndependentForOrderSucc(OrderInfoDO order, List<OrderItemDO> orderItems,
                                                                                               Map<String, String> payApplyNoMap,
                                                                                               Map<String, List<OrderRefundItemWithStatusDTO>> orderRefundItemMap,
                                                                                               Map<String, List<OrderIndependentOrderItemDO>> orderItemIndependentMap) {
        String independentCode = ecpIdUtil.nextIdStr();
        LocalDateTime current = LocalDateTime.now();

        // 组装分账明细（包含商户号维度的明细、订单行维度的明细）
        Pair<List<OrderIndependentItemDO>, List<OrderIndependentOrderItemDO>> independentItemPair = assembleIndependentItem(
                current, independentCode, order, orderItems,
                orderRefundItemMap, orderItemIndependentMap);
        // 无需分账
        if (independentItemPair == null) {
            return null;
        }

        List<OrderIndependentItemDO> independentItems = independentItemPair.getKey();
        List<OrderIndependentOrderItemDO> orderItemIndependents = independentItemPair.getValue();

        // 组装分账申请，分账类型为“订单完成”
        OrderIndependentDO independent = assembleIndependent(independentCode, order, payApplyNoMap,
                independentItems, current, OrderIndependentTypeEnum.ORDER_SUCCESS.getType());

        // 保存到数据库
        transactionTemplate.executeWithoutResult((status) -> {
            // 保存"待分账"状态的分账申请
            this.baseMapper.insert(independent);
            orderIndependentItemDOService.saveBatch(independentItems);
            orderIndependentOrderItemDOService.saveBatch(orderItemIndependents);

            // 更新订单上的分账状态为"分账中"
            OrderInfoDO updateOrder = new OrderInfoDO();
            updateOrder.setId(order.getId());
            updateOrder.setIndependentStatus(OrderIndependentStatusEnum.ING.getStatus());
            updateOrder.setUpdatedTime(LocalDateTime.now());
            orderInfoDOMapper.updateById(updateOrder);
        });
        log.info("执行【订单完成】分账任务，已初始化分账申请，orderCode={}", order.getOrderCode());
        return Pair.of(independent, independentItems);
    }

    /**
     * 组装分账申请
     */
    private OrderIndependentDO assembleIndependent(String independentCode, OrderInfoDO order,
                                                   Map<String, String> payApplyNoMap,
                                                   List<OrderIndependentItemDO> independentItems,
                                                   LocalDateTime current,
                                                   String independentType) {
        OrderIndependentDO independent = new OrderIndependentDO();
        independent.setIndependentType(independentType);
        independent.setIndependentCode(independentCode);
        independent.setOrderCode(order.getOrderCode());
        independent.setBusinessCode(order.getBusinessCode());
        independent.setConsumerCode(order.getConsumerCode());
        independent.setPayApplyNo(payApplyNoMap.get(order.getOrderCode()));
        independent.setTotalDivAmt(independentItems.stream().mapToInt(OrderIndependentItemDO::getDivAmt).sum());
        independent.setIndependentStatus(OrderIndependentStatusEnum.TODO.getStatus());
        independent.setTenantId(1);
        setCommonCol(independent, current);
        return independent;
    }

    /**
     * 初始化分账类型为“券核销”的分账申请，无需更新订单表中的分账状态
     */
    private Pair<OrderIndependentDO, List<OrderIndependentItemDO>> initIndependentForCouponUsed(OrderInfoDO order, OrderItemDO orderItem,
                                                                                                Map<String, String> payApplyNoMap,
                                                                                                Boolean theLastUsed,
                                                                                                OrderCouponDetailDO coupon) {
        String independentCode = ecpIdUtil.nextIdStr();
        LocalDateTime current = LocalDateTime.now();

        // 计算分账价格（单位：分）
        Integer divAmt = calcDivAmt(orderItem, theLastUsed);

        // 组装分账明细
        Pair<List<OrderIndependentItemDO>, List<OrderIndependentOrderItemDO>> independentItemPair = assembleIndependentItem(
                current, independentCode, orderItem, divAmt, coupon);
        // 无需分账
        if (independentItemPair == null) {
            return null;
        }

        List<OrderIndependentItemDO> independentItems = independentItemPair.getKey();
        List<OrderIndependentOrderItemDO> orderItemIndependents = independentItemPair.getValue();

        // 组装分账申请，分账类型为“券核销”
        OrderIndependentDO independent = assembleIndependent(independentCode, order, payApplyNoMap,
                independentItems, current, OrderIndependentTypeEnum.COUPON_USED.getType());
        independent.setCouponCode(coupon.getCouponCode());

        // 保存到数据库
        transactionTemplate.executeWithoutResult((status) -> {
            // 保存"待分账"状态的分账申请
            this.baseMapper.insert(independent);
            orderIndependentItemDOService.saveBatch(independentItems);
            orderIndependentOrderItemDOService.saveBatch(orderItemIndependents);

            // 更新券上的分账状态为"分账中"
            OrderCouponDetailDO updateCoupon = new OrderCouponDetailDO();
            updateCoupon.setId(coupon.getId());
            updateCoupon.setIndependentStatus(OrderIndependentStatusEnum.ING.getStatus());
            updateCoupon.setUpdatedTime(LocalDateTime.now());
            orderCouponDetailDOMapper.updateById(updateCoupon);
        });
        return Pair.of(independent, independentItems);
    }

    /**
     * 组装分账明细For“券核销”场景
     */
    private Pair<List<OrderIndependentItemDO>, List<OrderIndependentOrderItemDO>> assembleIndependentItem(
            LocalDateTime current, String independentCode,
            OrderItemDO orderItem, Integer divAmt, OrderCouponDetailDO coupon) {
        // 无需分账
        if (divAmt <= 0) {
            log.warn("执行【券核销】分账任务，券可分账金额,小于等于0，orderItemCode={}，costAmount={}，productQuantity={}",
                    orderItem.getOrderItemCode(), orderItem.getCostAmount(), orderItem.getProductQuantity());
            return null;
        }

        String independentType = OrderIndependentTypeEnum.COUPON_USED.getType();

        // 构建正向订单行维度的分账明细
        List<OrderIndependentOrderItemDO> newOrderItemIndependents = new ArrayList<>();
        OrderIndependentOrderItemDO orderItemIndependent = buildOrderIndependentOrderItemDO(orderItem, independentCode
                , independentType, divAmt, current);
        newOrderItemIndependents.add(orderItemIndependent);

        // 构建商户号维度的分账明细
        List<OrderIndependentItemDO> independentItems = new ArrayList<>();
        OrderIndependentItemDO independentItem = buildOrderIndependentItemDO(orderItem, independentCode
                , independentType, divAmt, current);
        independentItems.add(independentItem);
        return Pair.of(independentItems, newOrderItemIndependents);
    }

    /**
     * 组装分账明细For“订单完成”场景
     * @param current 当前时间
     * @param independentCode 分账申请单号
     * @param order 要分账的订单
     * @param orderItems 要分账的订单下所有订单行，按照ID升序
     * @param orderRefundItemMap 同一批次要分账的订单关联的所有退单行
     * @param orderRefundItemMap 同一批次要分账的订单关联的已发生的分账记录（订单行维度）
     */
    private Pair<List<OrderIndependentItemDO>, List<OrderIndependentOrderItemDO>> assembleIndependentItem(
                                                                    LocalDateTime current, String independentCode,
                                                                    OrderInfoDO order, List<OrderItemDO> orderItems,
                                                                    Map<String, List<OrderRefundItemWithStatusDTO>> orderRefundItemMap,
                                                                    Map<String, List<OrderIndependentOrderItemDO>> orderItemIndependentMap) {
        String independentType = OrderIndependentTypeEnum.ORDER_SUCCESS.getType();

        // 商户号维度的分账明细
        Map<String, OrderIndependentItemDO> independentItemMap = new HashMap<>();
        // 正向订单行维度的分账明细
        List<OrderIndependentOrderItemDO> newOrderItemIndependents = new ArrayList<>();

        // 将商品实付金额算进分账明细里
        for (OrderItemDO orderItem:orderItems) {
            String orderItemCode = orderItem.getOrderItemCode();
            List<OrderRefundItemWithStatusDTO> orderRefundItems = orderRefundItemMap.get(orderItemCode);
            List<OrderIndependentOrderItemDO> existsOrderItemIndependents = orderItemIndependentMap.get(orderItemCode);

            // 订单行可分账金额（单位分） = 订单行实付金额 - 退款金额 - 已分账金额（电子兑换券在订单完成分账前已经进行了一部分券核销分账）
            // 可分账金额小于等于0，则此订单行无需进行分账
            Integer divAmt = calcDivAmt(orderItem, orderRefundItems, existsOrderItemIndependents);
            if (divAmt <= 0) {
                continue;
            }

            // 构建正向订单行维度的分账明细
            OrderIndependentOrderItemDO orderItemIndependent = buildOrderIndependentOrderItemDO(orderItem, independentCode
                    , independentType, divAmt, current);
            newOrderItemIndependents.add(orderItemIndependent);

            String merchantAccountNo = orderItem.getMerchantAccountNo();
            OrderIndependentItemDO independentItem = independentItemMap.get(merchantAccountNo);
            if (independentItem != null) {
                independentItem.setDivAmt(independentItem.getDivAmt() + divAmt);
            } else if (independentItem == null) {
                // 构建商户号维度的分账明细
                independentItem = buildOrderIndependentItemDO(orderItem, independentCode
                        , independentType, divAmt, current);
                independentItemMap.put(merchantAccountNo, independentItem);
            }
        }

        // 将运费算进分账明细里，运费使用第一个订单行里的商户号
        Integer freightAmount = calcIndependentFreight(order, orderItems, orderRefundItemMap);
        if (freightAmount > 0) {
            String merchantAccountNo = orderItems.get(0).getMerchantAccountNo();
            OrderIndependentItemDO independentItem = independentItemMap.get(merchantAccountNo);
            if (independentItem != null) {
                independentItem.setDivAmt(independentItem.getDivAmt() + freightAmount);
            } else if (independentItem == null) {
                // 构建商户号维度的分账明细
                independentItem = buildOrderIndependentItemDO(orderItems.get(0).getOrderCode(), merchantAccountNo
                        , independentCode, independentType, freightAmount, current);
                independentItemMap.put(merchantAccountNo, independentItem);
            }
        }

        // 没有需要分账的订单行
        if (independentItemMap.isEmpty()) {
            return null;
        }
        return Pair.of(new ArrayList<>(independentItemMap.values()), newOrderItemIndependents);
    }

    /**
     * 计算可分账运费
     * @return
     */
    private Integer calcIndependentFreight(OrderInfoDO order, List<OrderItemDO> orderItems, Map<String, List<OrderRefundItemWithStatusDTO>> orderRefundItemMap) {
        if (order.getFreightAmount() == null || order.getFreightAmount() == 0) {
            return 0;
        }

        Integer freightAmount = order.getFreightAmount();
        for (OrderItemDO orderItem:orderItems) {
            List<OrderRefundItemWithStatusDTO> orderRefundItems = orderRefundItemMap.get(orderItem.getOrderItemCode());
            if (orderRefundItems == null) {
                continue;
            }
            for (OrderRefundItemWithStatusDTO orderRefundItem:orderRefundItems) {
                if (OrderRefundStatusEnum.isRefunding(orderRefundItem.getRefundOrderStatus())
                        || OrderRefundStatusEnum.isRefunded(orderRefundItem.getRefundOrderStatus())) {
                    freightAmount = freightAmount - ObjectUtil.defaultIfNull(orderRefundItem.getRefundFreight(), 0);
                }
            }
        }
        return freightAmount;
    }

    private OrderIndependentOrderItemDO buildOrderIndependentOrderItemDO(OrderItemDO orderItem, String independentCode
            , String independentType, Integer divAmt, LocalDateTime current) {
        OrderIndependentOrderItemDO orderItemIndependent = new OrderIndependentOrderItemDO();
        orderItemIndependent.setIndependentCode(independentCode);
        orderItemIndependent.setIndependentType(independentType);
        orderItemIndependent.setOrderCode(orderItem.getOrderCode());
        orderItemIndependent.setOrderItemCode(orderItem.getOrderItemCode());
        orderItemIndependent.setDivAmt(divAmt);
        orderItemIndependent.setMerchantAccountNo(orderItem.getMerchantAccountNo());
        orderItemIndependent.setTenantId(1);
        setCommonCol(orderItemIndependent, current);
        return orderItemIndependent;
    }

    private OrderIndependentItemDO buildOrderIndependentItemDO(OrderItemDO orderItem, String independentCode
            , String independentType, Integer divAmt, LocalDateTime current) {
        return buildOrderIndependentItemDO(orderItem.getOrderCode(), orderItem.getMerchantAccountNo(), independentCode
                , independentType, divAmt, current);
    }

    private OrderIndependentItemDO buildOrderIndependentItemDO(String orderCode, String merchantAccountNo, String independentCode
            , String independentType, Integer divAmt, LocalDateTime current) {
        OrderIndependentItemDO independentItem = new OrderIndependentItemDO();
        independentItem.setIndependentCode(independentCode);
        independentItem.setIndependentType(independentType);
        independentItem.setOrderCode(orderCode);
        independentItem.setDivAmt(divAmt);
        independentItem.setMerchantAccountNo(merchantAccountNo);
        independentItem.setTenantId(1);
        setCommonCol(independentItem, current);
        return independentItem;
    }

    /**
     * 计算订单行的可分账金额（单位分）
     * 订单行可分账金额（单位分） = 订单行实付金额 - 退款金额 - 已分账金额
     */
    private Integer calcDivAmt(OrderItemDO orderItem,
                               List<OrderRefundItemWithStatusDTO> orderRefundItems,
                               List<OrderIndependentOrderItemDO> orderItemIndependents) {
        Integer costAmount = orderItem.getCostAmount();

        int refundAmt = 0; // 退款金额（包含退款中（理论上不会出现，因为前面已经卡了，如果订单存在售后中的单子，则暂时放弃分账）、已退款）
        if (CollUtil.isNotEmpty(orderRefundItems)) {
            for (OrderRefundItemWithStatusDTO orderRefundItem:orderRefundItems) {
                if (OrderRefundStatusEnum.isRefunding(orderRefundItem.getRefundOrderStatus())
                        || OrderRefundStatusEnum.isRefunded(orderRefundItem.getRefundOrderStatus())) {
                    refundAmt += orderRefundItem.getRefundMoney();
                }
            }
        }

        int independentedAmt = 0; // 已分账金额
        if (CollUtil.isNotEmpty(orderItemIndependents)) {
            for (OrderIndependentOrderItemDO orderItemIndependent:orderItemIndependents) {
                independentedAmt += orderItemIndependent.getDivAmt();
            }
        }
        Integer divAmt = costAmount - refundAmt - independentedAmt;
        if (divAmt <= 0) {
            log.warn("执行【订单完成】分账任务，订单行可分账金额,小于等于0，orderItemCode={}，costAmount={}，refundAmt={}，independentedAmt={}",
                    orderItem.getOrderItemCode(), costAmount, refundAmt, independentedAmt);
        }
        return divAmt;
    }

    /**
     * 查询待分账的订单
     */
    private List<OrderInfoDO> listTodoIndependent(String businessCode, LocalDateTime completedTimeLimit, List<String> orderCodes, int pageSize) {
        LambdaQueryWrapper<OrderInfoDO> wrapper = Wrappers.lambdaQuery(OrderInfoDO.class)
                .eq(OrderInfoDO::getBusinessCode, businessCode)
                .in(CollUtil.isNotEmpty(orderCodes), OrderInfoDO::getOrderCode, orderCodes)
                .eq(OrderInfoDO::getOrderStatus, OrderStatusEnum.COMPLETED.getCode())
                .eq(OrderInfoDO::getIndependentStatus, OrderIndependentStatusEnum.TODO.getStatus())
                .eq(OrderInfoDO::getIsDeleted, Boolean.FALSE)
                .lt(OrderInfoDO::getCompletedTime, completedTimeLimit)
                .last("limit " + pageSize);
        return orderInfoDOMapper.selectList(wrapper);
    }

    private void setCommonCol(BaseDO base, LocalDateTime current) {
        base.setCreatedTime(current);
        base.setUpdatedTime(current);
        base.setCreatedBy(Constants.SYSTEM);
        base.setUpdatedBy(Constants.SYSTEM);
        base.setIsDeleted(Boolean.FALSE);
        base.setRevision(1);
    }

}
package com.jlr.ecp.order.service.feedback;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedbackPageReqDTO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedbackModifyLogVO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedbackVserionListVO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackRecordsDO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackSnapshotDO;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackRecordsDOMapper;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackSnapshotDOMapper;
import com.jlr.ecp.order.enums.feedback.ModifyOperationTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 评价设置快照表ServiceImpl
 */
@Service
public class FeedbackSnapshotDOServiceImpl extends ServiceImpl<FeedbackSnapshotDOMapper, FeedbackSnapshotDO> implements FeedbackSnapshotDOService {

    @Resource
    private FeedbackRecordsDOMapper recordsDOMapper;

    @Override
    public PageResult<FeedbackVserionListVO> selectHistoryPage(FeedbackPageReqDTO dto) {

        PageResult<FeedbackSnapshotDO> pageResult = baseMapper.selectPage(dto,new LambdaQueryWrapperX<FeedbackSnapshotDO>()
                .eq(FeedbackSnapshotDO::getFeedbackCode, dto.getFeedbackCode())
                .eq(FeedbackSnapshotDO::getIsDeleted, false)
                .orderByDesc(FeedbackSnapshotDO::getId));

        List<FeedbackVserionListVO> list = pageResult.getList().stream().map(item -> {
            FeedbackVserionListVO vo = new FeedbackVserionListVO();
            BeanUtils.copyProperties(item, vo);

            if(item.getSubmitNum() == null){
                Long aLong = recordsDOMapper.selectCount(new LambdaQueryWrapperX<FeedbackRecordsDO>()
                        .eq(BaseDO::getIsDeleted, false)
                        .eq(FeedbackRecordsDO::getSnapshotCode, item.getSnapshotCode()));
                vo.setSubmitNum(aLong.intValue());
            }
            return vo;
        }).collect(Collectors.toList());
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public List<FeedbackVserionListVO> getSnapshotCodeByFeedbackCode(String feedbackCode) {

        List<FeedbackSnapshotDO> feedbackSnapshotDOS = baseMapper.getSnapshotCodeByFeedbackCode(feedbackCode);
        //过滤掉feedbackSnapshotDOS中统计评价数submitNum为0的数据
        feedbackSnapshotDOS = new ArrayList<>(feedbackSnapshotDOS);
        return feedbackSnapshotDOS.stream()
                .map(feedbackSnapshotDO -> {
                    FeedbackVserionListVO feedbackVserionListVO = new FeedbackVserionListVO();
                    BeanUtils.copyProperties(feedbackSnapshotDO, feedbackVserionListVO);
                    feedbackVserionListVO.setDowntime(feedbackSnapshotDO.getDowntime());
                    return feedbackVserionListVO;
                })
                .collect(Collectors.toList());

    }
}

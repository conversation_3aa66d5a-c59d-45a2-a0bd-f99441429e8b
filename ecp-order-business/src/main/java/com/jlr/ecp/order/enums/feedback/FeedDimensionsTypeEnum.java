package com.jlr.ecp.order.enums.feedback;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * k
 */
@AllArgsConstructor
@Getter
public enum FeedDimensionsTypeEnum {


    STAR_RATING(0, "星级（5分制）"),

    SINGLE_CHOICE(1, "单选题"),

    MULTIPLE_CHOICE(2, "多选题");


    private final Integer code;

    private final String name;



    // 根据code获取对应的枚举实例
    public static String fromCode(Integer code) {
        for (FeedDimensionsTypeEnum type : FeedDimensionsTypeEnum.values()) {
            if (Objects.equals(type.getCode(), code)) {
                return type.getName();
            }
        }
        return null;
    }
}

package com.jlr.ecp.order.dal.dataobject.refund;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_order_refund_attachment
 *
 * <AUTHOR>
 * @TableName t_order_refund_attachment
 */
@TableName(value = "t_order_refund_attachment")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderRefundAttachmentDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 退款订单号;退款订单号
     */
    @TableField(value = "refund_order_code")
    private String refundOrderCode;

    /**
     * 退款附件链接URL;退款附件链接URL
     */
    @TableField(value = "attachment_url")
    private String attachmentUrl;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;


}
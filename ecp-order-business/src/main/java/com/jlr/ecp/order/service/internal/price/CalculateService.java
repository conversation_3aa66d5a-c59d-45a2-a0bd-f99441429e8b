package com.jlr.ecp.order.service.internal.price;

import com.jlr.ecp.order.api.cart.dto.CalculateAmountDTO;
import com.jlr.ecp.order.controller.app.cart.vo.CalculateAmountVO;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.internal.price
 * @className: CalculateService
 * @author: gaoqig
 * @description: 计算服务接口
 * @date: 2025/3/27 10:50
 * @version: 1.0
 */
public interface CalculateService {
    /***
     * <AUTHOR>
     * @description 购物车结算
     * 目前确认的是VCS商品和 (LRE/BG)商品不会放在一个订单，也不允许
     * @date 2025/3/6 09:40
     * @param calculateAmountDTO 计算优惠价
     * @param customerCode 客户编码
     * @return: com.jlr.ecp.order.controller.app.cart.vo.CalculateAmountVO
     */

    CalculateAmountVO calculateAmount(CalculateAmountDTO calculateAmountDTO, String customerCode);
}

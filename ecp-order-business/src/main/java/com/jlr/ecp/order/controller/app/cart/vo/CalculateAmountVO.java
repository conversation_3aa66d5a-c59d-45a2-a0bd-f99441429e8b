package com.jlr.ecp.order.controller.app.cart.vo;

import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * t_shopping_car(ShoppingCar)表实体类
 *
 * <AUTHOR>
 */
@Data
@Schema(description = " 购物车计算优惠价- VO")
@ToString(callSuper = true)
public class CalculateAmountVO {

    /**
     * CarPaymentTypeEnum
     */
    @Schema(description = "1:现金 ，2：积分，3:优惠券")
    private Integer paymentType;

    /**
     * 单位：元
     */
    @Schema(description = "折扣金额")
    private String discountTotalAmount;

    @Schema(description = "积分")
    private Integer costPoints;

    @Schema(description = "优惠券")
    private PromotionDto chooseCoupon;

    @Schema(description = "是否使用优惠券")
    private boolean useCouponCode;

    /**
     * 实际需要支付金额（优惠后的）
     */
    @Schema(description = "预估应付金额（不包含运费）")
    private String costAmount;

    /**
     * 应付总金额（优惠前的）
     */
    @Schema(description = "应付总价金额")
    private String totalAmount;

    @Schema(description = "购物车商品")
    private List<ShoppingCarItemVO> itemList;

    @Schema(description = "购物车SKU商品")
    private List<CartProductSkuInfo> cartSkuProductList;

    @Schema(description = "用户可用积分")
    private Integer availablePoints;

    @Schema(description = "用户可用优惠券")
    private List<PromotionDto> availableCoupons;
}


package com.jlr.ecp.order.controller.app.cart.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 */
@Data
@Schema(description = " 优惠券- VO")
@ToString(callSuper = true)
public class CouponVO {

    @Schema(description = "优惠券编码")
    private String couponCode;

    @Schema(description = "优惠券名称")
    private String couponName;

    @Schema(description = "优惠券类型")
    private String couponType;


}


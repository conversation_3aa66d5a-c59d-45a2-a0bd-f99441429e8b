package com.jlr.ecp.order.enums.payment;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.enums.payment
 * @enumName: AppInvoiceStatrusEnum
 * @author: gaoqig
 * @description: TODO
 * @date: 2025/4/16 19:00
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum AppInvoiceStatrusEnum {
    NONE(0, "去开票"),
    APPLY(1, "已提交开票申请"),
    SUCCESS(2, "开票完成"),
    ;

    private final Integer status;
    private final String name;

    public static AppInvoiceStatrusEnum getByCode(Integer code) {
        for (AppInvoiceStatrusEnum value : values()) {
            if (value.getStatus().equals(code)) {
                return value;
            }
        }
        return null;
    }
}

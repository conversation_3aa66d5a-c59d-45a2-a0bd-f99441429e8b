package com.jlr.ecp.order.controller.admin.dashboard.dto;

import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Schema(description = "仪表盘 - SQL查询参数")
@Data
@ToString(callSuper = true)
public class SqlQueryDTO {

    @Schema(description = "开始日期")
    private LocalDate startDate;

    @Schema(description = "结束日期")
    private LocalDate endDate;

    @Schema(description = "日期格式化 eg." +
            "BY_HOUR: %l %p" +
            "BY_DAY: %b %d" +
            "BY_MONTH: %Y/%m" +
            "BY_YEAR: %Y")
    private String dateFormat;

    @Schema(description = "指标")
    private Integer kpi;

    @Schema(description = "下单渠道, LR或JA")
    private String orderChannel;

    @Schema(description = "车型")
    private String vehicleModel;

    @Schema(description = "业务线")
    private String businessCode = BusinessIdEnum.VCS.getCode();
}

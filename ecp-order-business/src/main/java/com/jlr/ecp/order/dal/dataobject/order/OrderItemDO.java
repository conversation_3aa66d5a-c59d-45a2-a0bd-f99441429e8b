package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.math.BigDecimal;

/**
 * t_order_item
 *
 * <AUTHOR>
 * @TableName t_order_item
 */
@TableName(value = "t_order_item")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderItemDO extends BaseDO {
    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 订单item编码;订单item编码，雪花算法ID
     */
    @TableField(value = "order_item_code")
    private String orderItemCode;

    /**
     * 订单编码;订单编码
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 商品快照编码;商品快照编码
     */
    @TableField(value = "product_version_code")
    private String productVersionCode;

    /**
     * 商品编码;商品编码
     */
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 商品SKU编码;商品SKU编码
     */
    @TableField(value = "product_sku_code")
    private String productSkuCode;

    /**
     * 商品名称;商品名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 商品主图URL;商品主图URL
     */
    @TableField(value = "product_image_url")
    private String productImageUrl;

    /**
     * stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI
     */
    @TableField(value = "product_attribute")
    private String productAttribute;

    /**
     * 商品市场价格
     */
    @TableField(value = "product_market_price")
    private Integer productMarketPrice;

    /**
     * 商品销售单价;商品销售单价，单位分
     */
    @TableField(value = "product_sale_price")
    private Integer productSalePrice;

    /**
     * 商品数量;商品数量
     */
    @TableField(value = "product_quantity")
    private Integer productQuantity;

    /**
     * 应付总金额;应付总金额，单位分
     */
    @TableField(value = "total_amount")
    private Integer totalAmount;

    /**
     * 实付金额;实付金额，单位分
     */
    @TableField(value = "cost_amount")
    private Integer costAmount;

    /**
     * 实付积分;实付积分，单位分
     */
    @TableField(value = "point_amount")
    private Integer pointAmount;

    /**
     * 折扣金额;折扣金额，单位分
     */
    @TableField(value = "discount_fee_amount")
    private Integer discountFeeAmount;

    /**
     * 不含税总金额
     */
    @TableField(value = "exclude_tax_total_amount")
    private Integer excludeTaxTotalAmount;

    /**
     * 税费总金额
     */
    @TableField(value = "tax_amount")
    private Integer taxAmount;

    /**
     * 税率
     */
    @TableField(value = "tax_rate")
    private BigDecimal taxRate;

    /**
     * 税务编码
     */
    @TableField(value = "tax_code")
    private String taxCode;

    /**
     * 订单item商品的类型，1普通商品 2组合商品
     */
    @TableField(value = "order_item_spu_type")
    private Integer orderItemSpuType;

    /**
     * 售后处理状态;售后处理中 1：售后处理中 2：售后完成 3：售后关闭
     */
    @TableField(value = "aftersales_status")
    private Integer aftersalesStatus;

    /**
     * 商户号
     */
    @TableField(value = "merchant_account_no")
    private String merchantAccountNo;

    /**
     * 商品备注信息
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * item履约类型;1：远程车控REMOTE SERVICE； 2：PIVI Subscription   3: 实物商品；4：组合商品 5：优惠券商品
     */
    @TableField(value = "item_fufilement_type")
    private Integer itemFulfillmentType;
    
    /**
     * 卡券模版 CODE
     */
    @TableField(value = "coupon_model_code")
    private String couponModelCode;

    /**
     * 金蝶sku编码
     */
    @TableField(value = "kingdee_sku_code")
    private String kingdeeSkuCode;

    /**
     * item状态;VCS: 1:待激活  2:激活中  3：已激活 4：激活失败
     * 物流履约订单状态：1：待发货，2：已发货，3：已妥投，4: 已收货，5: 已关闭，6：已取消
     * 优惠券履约订单状态：1：待发放，2：待核销  3：已核销  4：已回收，6：已取消
     */
    @TableField(value = "item_status")
    private Integer itemStatus;

}
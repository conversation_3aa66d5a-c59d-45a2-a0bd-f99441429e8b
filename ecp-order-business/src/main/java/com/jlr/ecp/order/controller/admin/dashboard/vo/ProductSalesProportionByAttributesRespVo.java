package com.jlr.ecp.order.controller.admin.dashboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "dashboard-产品及不同属性销售占比")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSalesProportionByAttributesRespVo {
  @Schema(description = "图表数据")
  private List<PieChartRespVo> pieChart;

  @Schema(description = "表格数据")
  private TableRespVo table;

  @Schema(description = "报错信息")
  private String errorMsg;
}

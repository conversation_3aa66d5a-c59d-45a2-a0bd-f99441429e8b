package com.jlr.ecp.order.controller.admin.refund;

import cn.hutool.core.lang.Snowflake;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.order.api.refund.dto.*;
import com.jlr.ecp.order.enums.order.OrderTypeEnum;
import com.jlr.ecp.order.enums.order.RefundOrderOperationTypeEnum;
import com.jlr.ecp.order.service.refund.ecoupon.EcouponOrderRefundDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 订单
 *
 * <AUTHOR>
 */
@Tag(name = "后台管理端 - 退单管理")
@RestController
@RequestMapping("v1/refund/ecoupon")
@Validated
public class EcouponOrderRefundController {
    @Resource
    private EcouponOrderRefundDOService ecouponOrderRefundDOService;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private ProducerTool producerTool;

    /**
     * 发起退款申请
     */
    @PostMapping("/apply")
    @Operation(summary = "运营发起退款申请")
    @PreAuthorize("@ss.hasPermission('trade:lreorder:refund')")
    CommonResult<String> refund(@RequestBody List<BaseOrderRefundApplyDTO> baseOrderRefundApplyDTOList) {
        if(CollectionUtils.isNotEmpty(baseOrderRefundApplyDTOList)){
            baseOrderRefundApplyDTOList.forEach(baseOrderRefundApplyDTO ->
                baseOrderRefundApplyDTO.setRefundFulfilmentType(OrderTypeEnum.ELECTRONIC_COUPON.getCode())
            );
        }
        return CommonResult.success(ecouponOrderRefundDOService.ecouponOrderRefundApply(baseOrderRefundApplyDTOList, RefundOrderOperationTypeEnum.OPERATION_INITIATED.getCode()));
    }
}

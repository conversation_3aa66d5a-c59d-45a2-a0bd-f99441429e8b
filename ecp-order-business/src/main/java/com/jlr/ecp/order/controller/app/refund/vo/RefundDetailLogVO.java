package com.jlr.ecp.order.controller.app.refund.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "App端 - order refund detail VO")
public class RefundDetailLogVO {


    @Schema(description = "退单状态")
    private Integer refundOrderStatus;

    @Schema(description = "文本")
    private String text ;

    @Schema(description = "详细文本")
    private String detail ;


    @Schema(description = "申请时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime changeTime;


}

package com.jlr.ecp.order.service.internal.promotion;


import com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.internal.promotion
 * @className: PromotionalFactory
 * @author: gaoqig
 * @description: 优惠券算法工厂
 * @date: 2025/3/6 19:09
 * @version: 1.0
 */
public class PromotionalFactory {
    public static PromotionalStrategy getPromotional(CouponTypeEnum couponType) {
        PromotionalStrategy promotionalStrategy;
        switch (couponType) {
            case CASH_BACK:
                promotionalStrategy = new CashBackCouponPromotion();
                break;
            case DISCOUNT:
                promotionalStrategy = new DiscountCouponPromotion();
                break;
            case POINTS:
                promotionalStrategy = new PointsPromotion();
                break;
            case VOUCHER:
                promotionalStrategy = new VoucherPromotion();
                break;
            default:
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.NOT_SUPPORT_COUPON);
        }
        return promotionalStrategy;
    }
}

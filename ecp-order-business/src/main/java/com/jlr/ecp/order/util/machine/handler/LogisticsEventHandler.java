package com.jlr.ecp.order.util.machine.handler;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import org.springframework.stereotype.Component;

/**
 * 实物订单退单事件处理
 * <AUTHOR>
 */
@Component
public interface LogisticsEventHandler {
    /**
     * 处理退单状态
     * @param orderInfoDO 订单
     * @param orderRefundDO 退单
     * @param orderItemDO 退单项
     */
    void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO);
}

package com.jlr.ecp.order.controller.admin.order;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.order.dto.OrderCreateDTO;
import com.jlr.ecp.order.api.order.dto.OrderShopCarItemDTO;
import com.jlr.ecp.order.api.order.dto.customer.GuestOrderCreateDTO;
import com.jlr.ecp.order.api.order.dto.customer.GuestOrderPreValidationDTO;
import com.jlr.ecp.order.api.order.dto.customer.GuestOrderShopCarItemDTO;
import com.jlr.ecp.order.api.order.dto.customer.ResendMessageDTO;
import com.jlr.ecp.order.api.order.vo.OrderCreateRespVO;
import com.jlr.ecp.order.api.order.vo.ResendMessageRespVO;
import com.jlr.ecp.order.service.order.OrderInfoDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

import static com.jlr.ecp.order.constant.Constants.CUSTOMER_SERVICE_ORDER;

/**
 * 代客下单 - 订单
 *
 * <AUTHOR>
 */
@Tag(name = "后台管理端 - 代客下单")
@RestController
@RequestMapping("v1/guest/order")
@Validated
@Slf4j
public class GuestOrderController {

    @Resource
    private OrderInfoDOService orderInfoDOService;

    @PostMapping("/onBehalfOf/preValidate")
    @Operation(summary = "代客下单-选购商品时 前置校验接口 sprint47请勿重复下单的情况提前")
    @PreAuthorize("@ss.hasPermission('trade:order:create')")
    public CommonResult<OrderCreateRespVO> preValidateGuestOrderInfo(@Valid @RequestBody GuestOrderPreValidationDTO guestOrderPreValidationDTO) {
        log.info("controller层代客下单选购商品前置校验接口 入参，guestOrderPreValidationDTO：{}", JSON.toJSONString(guestOrderPreValidationDTO));

        // 构建orderCreateDTO对象
        OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        BeanUtils.copyProperties(guestOrderPreValidationDTO, orderCreateDTO);
        // 复制 shopCarItemList 并设置实付金额
        List<OrderShopCarItemDTO> shopCarItemDTOList = guestOrderPreValidationDTO.getShopCarItemList().stream()
                .map(guestItem -> {
                    OrderShopCarItemDTO dtoItem = new OrderShopCarItemDTO();
                    BeanUtils.copyProperties(guestItem, dtoItem);
                    return dtoItem;
                })
                .collect(Collectors.toList());
        orderCreateDTO.setShopCarItemList(shopCarItemDTOList);

        // 调用服务层进行前置校验
        OrderCreateRespVO result = orderInfoDOService.preValidateOrderInfo(orderCreateDTO);
        return CommonResult.success(result);
    }

    /**
     * 创建代客订单API接口
     *
     * @param guestOrderCreateDTO
     * @return
     */
    @PostMapping("/onBehalfOf/create")
    @Operation(summary = "代客创建订单")
    @PreAuthorize("@ss.hasPermission('trade:order:create')")
    public CommonResult<OrderCreateRespVO> createGuestOrderInfo(@Valid @RequestBody GuestOrderCreateDTO guestOrderCreateDTO)
            throws JsonProcessingException {
        log.info("controller层代客下单接口 入参，guestOrderCreateDTO：{}", JSON.toJSONString(guestOrderCreateDTO));

        // 验证是否是代客下单请求 通过GlobalInfoDTO.channelCode
        if (!guestOrderCreateDTO.getGlobalInfoDTO().isGuestOrderRequest()) {
            throw new IllegalArgumentException("此接口仅接受代客下单请求， 渠道编码channelCode需为CS（CustomerService）");
        }

        // 验证品牌编码一致性 通过GlobalInfoDTO.globalBrandCode 和 ShopCarItemDTO.itemBrandCode
        guestOrderCreateDTO.validateBrandCodes();

        // 设置消费者编码为"CUSTOMER_SERVICE_ORDER"
        guestOrderCreateDTO.getGlobalInfoDTO().setConsumerCode(CUSTOMER_SERVICE_ORDER);

        // 遍历购物车列表调用validatePayPrice方法进行实付金额校验
        guestOrderCreateDTO.getShopCarItemList().forEach(GuestOrderShopCarItemDTO::validatePayPrice);

        // 构建orderCreateDTO对象
        OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        BeanUtils.copyProperties(guestOrderCreateDTO, orderCreateDTO);
        // 复制 shopCarItemList 并设置实付金额
        List<OrderShopCarItemDTO> shopCarItemDTOList = guestOrderCreateDTO.getShopCarItemList().stream()
                .map(guestItem -> {
                    OrderShopCarItemDTO dtoItem = new OrderShopCarItemDTO();
                    BeanUtils.copyProperties(guestItem, dtoItem);
                    return dtoItem;
                })
                .collect(Collectors.toList());
        orderCreateDTO.setShopCarItemList(shopCarItemDTOList);

        // 调用服务层创建订单
        OrderCreateRespVO result = orderInfoDOService.createOrderInfo(orderCreateDTO, null);
        return CommonResult.success(result);
    }

    /**
     * 客服订单短信重发API接口 针对子单
     *
     * @param resendMessageDTO 短信重发请求参数
     * @return 操作结果
     */
    @PostMapping("/resend/sms")
    @Operation(summary = "客服订单短信重发")
    @PreAuthorize("@ss.hasPermission('trade:order:resend-sms')")
    public CommonResult<ResendMessageRespVO> resendCustomerServiceOrderMessage(@Valid @RequestBody ResendMessageDTO resendMessageDTO) {
        log.info("controller层客服订单短信重发接口 入参，resendMessageDTO：{}", JSON.toJSONString(resendMessageDTO));
        ResendMessageRespVO response = orderInfoDOService.resendMessage(resendMessageDTO);
        return CommonResult.success(response);
    }
}
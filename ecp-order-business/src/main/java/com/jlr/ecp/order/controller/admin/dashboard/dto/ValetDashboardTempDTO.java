package com.jlr.ecp.order.controller.admin.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "仪表盘统计 temp DTO")
@Data
public class ValetDashboardTempDTO {
    // 订单数量统计
    /**
     * 1.代客下单订单数 （order_channel为5代客下单）
     * 2.订单总数量
     * 代客下单订单数 占全部订单的 百分比
     */
    @Schema(description = "代客下单订单数")
    private BigDecimal valetOrderCount = BigDecimal.ZERO;
    @Schema(description = "订单总数量")
    private BigDecimal totalOrderCount = BigDecimal.ZERO;
    @Schema(description = "代客下单订单数占全部订单的百分比")
    private String valetOrderPercentage;

    // 成交数统计
    /**
     * 3.代客下单成交数（支付状态为“已支付” payment_status为1：已支付）
     * 4.已支付状态下的订单总数量
     * 代客下单成交数 占 订单成交数的 百分比
     */
    @Schema(description = "代客下单成交数")
    private BigDecimal valetPaidCount = BigDecimal.ZERO;
    @Schema(description = "已支付状态下的订单总数量")
    private BigDecimal totalPaidCount = BigDecimal.ZERO;
    @Schema(description = "代客下单成交数占 订单成交数的 百分比")
    private String paidPercentage;

    // 交易额统计
    /**
     * 5.代客下单交易额（支付状态为“已支付”状态的所有”代客下单实付cost_amount 总金额（含税）“之和）
     * 6.已支付状态下 的 所有订单交易额
     * 代客下单交易额 占 订单交易额的百分比
     */
    @Schema(description = "代客下单交易额")
    private BigDecimal valetGmv = BigDecimal.ZERO;
    @Schema(description = "已支付状态下 的 所有订单交易额")
    private BigDecimal totalGmv = BigDecimal.ZERO;
    @Schema(description = "代客下单交易额占 订单交易额的百分比")
    private String gmvPercentage;

    // 收入统计
    /**
     * 代客下单总收入 = 5- 7.代客下单总退款额
     * 所有订单总收入 = 6- 8.已支付状态下 订单 的总退款额
     * 代客下单总收入 占 所有订单总收入 的百分比
     */
    @Schema(description = "7.代客下单总退款额")
    private BigDecimal valetRefundAmount = BigDecimal.ZERO;
    @Schema(description = "8.已支付状态下 订单 的总退款额")
    private BigDecimal totalRefundAmount = BigDecimal.ZERO;
    @Schema(description = "代客下单总退款额 占 所有订单退款额 的百分比")
    private String refundPercentage;

    @Schema(description = "代客下单总收入")
    private BigDecimal valetIncome = BigDecimal.ZERO;
    @Schema(description = "所有订单总收入")
    private BigDecimal totalIncome = BigDecimal.ZERO;
    @Schema(description = "代客下单总收入占 所有订单总收入 的百分比")
    private String incomePercentage;

    // 客单价统计
    /**
     * 代客下单成交客单价 = 5 / 3
     * 订单成交客单价=6 / 4
     * 展示“比小程序下单”多的百分比，计算方式：代客下单成交客单价/所有订单成交客单价-1
     */
    @Schema(description = "代客下单成交客单价")
    private BigDecimal valetCustomerPrice = BigDecimal.ZERO;
    @Schema(description = "订单成交客单价")
    private BigDecimal totalCustomerPrice = BigDecimal.ZERO;
    @Schema(description = "展示“比小程序下单”多的百分比，计算方式：代客下单成交客单价/所有订单成交客单价-1")
    private String customerPriceDiff;

    // 商品总数统计
    /**
     * 9.已支付状态下 代客下单成交商品总数
     * 10.已支付状态下 所有订单成交商品总数
     * 代客下单成交商品总数 占 所有订单成交商品总数 的百分比
     */
    @Schema(description = "代客下单成交商品总数")
    private BigDecimal valetProductCount = BigDecimal.ZERO;
    @Schema(description = "已支付状态下 所有订单成交商品总数")
    private BigDecimal totalProductCount = BigDecimal.ZERO;
    @Schema(description = "代客下单成交商品总数占 所有订单成交商品总数 的百分比")
    private String productCountPercentage;

    // Getters and Setters
}
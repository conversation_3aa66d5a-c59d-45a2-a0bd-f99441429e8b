package com.jlr.ecp.order.util.common;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class AttributeUtil {

    /**
     * 将属性字符串转换为格式化的属性值字符串。
     *
     * @param attributeStr 属性字符串，例如 "service_year_code:一年,color_code:red"
     * @return 格式化的属性值字符串，例如 "一年,red"
     */
    public static String formatProductAttributes(String attributeStr) {
        if (attributeStr == null || attributeStr.trim().isEmpty()) {
            return "";
        }

        try {
            return Arrays.stream(attributeStr.split(","))
                    // 获取每个属性的值部分
                         .map(attr -> attr.split(":")[1])
                    // 用逗号连接
                         .collect(Collectors.joining(","));
        } catch (Exception e) {
            // 日志记录或处理异常
            System.err.println("Error parsing attribute string: " + e.getMessage());
            return "";
        }
    }

    public static void main(String[] args) {
        // 测试数据
        String testAttributeString = "service_year_code:一年,color_code:red";

        String testAttributeString2 = "service_year_code:一年";

        // 调用工具类方法
        String formattedAttributes = AttributeUtil.formatProductAttributes(testAttributeString);
        String formattedAttributes2 = AttributeUtil.formatProductAttributes(testAttributeString2);

        // 输出结果
        System.out.println("Formatted Attributes: " + formattedAttributes);
        System.out.println("Formatted Attributes: " + formattedAttributes2);
    }
}

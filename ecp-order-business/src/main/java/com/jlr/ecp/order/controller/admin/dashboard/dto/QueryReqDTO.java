package com.jlr.ecp.order.controller.admin.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(description = "仪表盘 - 查询参数")
@Data
@ToString(callSuper = true)
public class QueryReqDTO {

    @Schema(description = "开始日期")
    @NotBlank(message="开始日期不能为空")
    private String startTime;

    @Schema(description = "结束日期")
    @NotBlank(message="结束日期不能为空")
    private String endTime;

    @Schema(description = "类型筛选, TypeFilterEnum")
    @NotNull(message="类型不能为空")
    private Integer type;

    @Schema(description = "下单渠道, OrderChannelEnum")
    private String orderChannel;

    @Schema(description = "车型")
    private String vehicleModel;

    @Schema(description = "指标, KpiEnum")
    private Integer kpi;

    @Schema(description = "右侧指标, 虚拟产品偏好仪表盘查询全部时传入")
    private Integer rightKpi;
}

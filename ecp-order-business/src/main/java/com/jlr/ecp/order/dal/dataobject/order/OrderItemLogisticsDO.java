package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_order_item_logistics
 * 履约方式为实物时，记录物流信息
 * @TableName t_order_item_logistics
 */
@TableName(value ="t_order_item_logistics")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderItemLogisticsDO extends BaseDO {
    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 订单编码
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 订单明细编码
     */
    @TableField(value = "order_item_code")
    private String orderItemCode;

    /**
     * 发货时间
     */
    @TableField(value = "send_time")
    private LocalDateTime sendTime;
    /**
     * 物流状态;快递物流状态 1-待发货，2-已发货，3-已签收，4-确认收货
     */
    @TableField(value = "logistics_status")
    private Integer logisticsStatus;

    /**
     * 物流公司名称;物流公司
     */
    @TableField(value = "logistics_company")
    private String logisticsCompany;

    /**
     * 物流单号;物流单号
     */
    @TableField(value = "logistics_no")
    private String logisticsNo;

    /**
     * 签收时间
     */
    @TableField(value = "sign_time")
    private LocalDateTime signTime;

    /**
     * 确认收货时间
     */
    @TableField(value = "confirm_package_time")
    private LocalDateTime confirmPackageTime;
    /**
     * 收件人;收件人
     */
    @TableField(value = "recipient")
    private String recipient;

    /**
     * 收件人半隐藏
     */
    @TableField(value = "recipient_mix")
    private String recipientMix;


    /**
     * 收件人联系手机;
     */
    @TableField(value = "recipient_phone")
    private String recipientPhone;

    /**
     * 半隐藏收件人联系手机
     */
    @TableField(value = "recipient_phone_mix")
    private String recipientPhoneMix;

    /**
     * 收件人联系手机MD5
     */
    @TableField(value = "recipient_phone_md5")
    private String recipientPhoneMd5;

    /**
     * 收件省;收件省
     */
    @TableField(value = "province_code")
    private String provinceCode;

    /**
     * 收件市;收件市
     */
    @TableField(value = "city_code")
    private String cityCode;

    /**
     * 收件区;收件区
     */
    @TableField(value = "area_code")
    private String areaCode;

    /**
     * 收件详细地址;收件详细地址
     */
    @TableField(value = "detail_address")
    private String detailAddress;

    /**
     * 收件详细地址半隐藏
     */
    @TableField(value = "detail_address_mix")
    private String detailAddressMix;

    /**
     * 完整收件地址
     */
    @TableField(value = "full_detail_address")
    private String fullDetailAddress;

    /**
     * 邮编地址;邮编地址
     */
    @TableField(value = "post_code")
    private String postCode;

    /**
     * 无理由退回时间（截止时间点）
     */
    @TableField(value = "no_reason_returns_time")
    private LocalDateTime noReasonReturnsTime;

    /**
     * 签收任务是否执行 0、1
     */
    @TableField(value = "is_sign_task")
    private Integer isSignTask;
} 
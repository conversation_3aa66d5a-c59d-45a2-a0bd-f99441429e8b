package com.jlr.ecp.order.service.order.handler;

import com.jlr.ecp.order.api.order.dto.OrderCreateDTO;
import com.jlr.ecp.order.api.order.dto.OrderShopCarItemDTO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;

import java.util.List;

///**
// * 职责链模式
// * 一个处理完 交给下一个处理
// */
//public interface OrderSplitHandler {
//    OrderSplitHandler setNext(OrderSplitHandler handler);
//    OrderSplitHandler getNext();
//
//    //判断产品项是否是当前类型拆单需要处理的项 里面的条件商品履约类型
//    boolean canItemMatch();
//
//    // 这里存的是 履约类型下的商品item
//    List<OrderShopCarItemDTO> matchItems();
//
//    //生成内存订单
//    List<OrderInfoDO> handleRequest(OrderCreateDTO orderCreateDTO,List<OrderInfoDO> lastOrderInfoList);
//}

package com.jlr.ecp.order.enums.order;

/**
 * 支付接口订单项类型枚举
 */
public enum PayOrderItemTypeEnum {

    CASH(1, "现金"),
    POINT(2, "积分"),
    COUPON(3,"卡券");

    private final int code;
    private final String name;

    PayOrderItemTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code获取枚举实例
     * @param code 状态编码
     * @return 对应的枚举实例，未找到返回null
     */
    public static PayOrderItemTypeEnum getByCode(int code) {
        for (PayOrderItemTypeEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

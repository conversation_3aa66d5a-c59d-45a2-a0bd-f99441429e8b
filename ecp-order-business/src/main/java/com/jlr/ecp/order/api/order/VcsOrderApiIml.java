package com.jlr.ecp.order.api.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.order.dto.VCSOrderInfoDTO;
import com.jlr.ecp.order.dal.dataobject.order.VCSOrderInfoDO;
import com.jlr.ecp.order.dal.mysql.order.VcsOrderInfoDOMapper;
import com.jlr.ecp.order.enums.order.OrderServiceTypeEnum;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
@Validated
@Slf4j
public class VcsOrderApiIml implements VcsOrderApi{

    @Resource
    private VcsOrderInfoDOMapper vcsOrderInfoDOMapper;


    /**
     * 根据车辆VIN码查询VCS订单信息。
     *
     * @param carVin 车辆的VIN码。
     * @return 返回查询到的VCS订单信息列表，如果未查询到相关订单，则返回空列表。
     */
    @Override
    public CommonResult<List<VCSOrderInfoDTO>> queryPIVIVcsOrderByCarVin(String carVin) {
        List<VCSOrderInfoDO> vcsOrderInfoDOS = selectPIVIVcsOrderByCarVin(carVin);
        log.info("根据车辆VIN码查询VCS订单信息, carVin:{}, vcsOrderInfoDOS:{}", carVin, vcsOrderInfoDOS);
        if (CollUtil.isEmpty(vcsOrderInfoDOS)) {
            return CommonResult.success(new ArrayList<>());
        }
        List<VCSOrderInfoDTO> resp = new ArrayList<>();
        for (VCSOrderInfoDO vcsOrderInfoDO : vcsOrderInfoDOS) {
            resp.add(BeanUtil.copyProperties(vcsOrderInfoDO, VCSOrderInfoDTO.class));
        }
        log.info("根据车辆VIN码查询VCS订单信息, vcsOrderInfoDTOResp:{}", resp);
        return CommonResult.success(resp);
    }

    /**
     * 根据车辆vin码查询PIVI类型的VCS订单信息。
     *
     * @param carVin 车辆的vin码。
     * @return 返回匹配的VCS订单信息列表。如果vin码为空或查询出现异常，则返回空列表。
     */
    private List<VCSOrderInfoDO> selectPIVIVcsOrderByCarVin(String carVin) {
        String md5CarVin = SecureUtil.md5(carVin);
        log.info("根据车辆vin码查询PIVI类型的VCS订单信息, carVin:{}, md5CarVin:{}", carVin, md5CarVin);
        if (StringUtil.isBlank(carVin)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<VCSOrderInfoDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VCSOrderInfoDO::getCarVinMd5, md5CarVin)
                .eq(VCSOrderInfoDO::getServiceType, OrderServiceTypeEnum.PIVI.getType())
                .eq(VCSOrderInfoDO::getIsDeleted, false);
        try {
            return vcsOrderInfoDOMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.info("根据车辆vin码查询PIVI类型的VCS订单信息，异常:", e);
        }
        return new ArrayList<>();
    }
}

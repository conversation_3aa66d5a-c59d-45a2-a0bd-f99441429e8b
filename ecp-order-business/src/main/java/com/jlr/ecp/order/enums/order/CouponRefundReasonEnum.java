package com.jlr.ecp.order.enums.order;

/**
 * 退款原因枚举
 * author: maojie
 */
public enum CouponRefundReasonEnum {
    SEVEN_DAY_NO_REASON(4, "7天无理由退换货"),
    DISLIKE_OR_UNWANTED(1, "不喜欢/不想要"),
    WRONG_OR_EXTRA_ORDER(2, "拍错/多拍"),
    CONTACT_INFO_ERROR(3, "电话信息填写错误"),
    UNUSED_OR_RARELY_USED(5, "没用/少用优惠"),
    SCHEDULE_CHANGED(6, "计划有变没时间消费"),
    SAFETY_CONCERNS(7, "担心安全问题（天气等）"),
    APPOINTMENT_FAILURE(8, "预约不上/排队太久"),
    CANNOT_CONTACT_STORE(9, "联系不上使用门店"),
    CONSULTATION_AGREED(10, "协商一致退款"),
    AUTO_REFUND_OVERDUE(11, "超期自动退款"),
    OTHER(0, "其他")
    ;

    private final Integer code;
    private final String name;

    CouponRefundReasonEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static CouponRefundReasonEnum getByCode(Integer code) {
        for (CouponRefundReasonEnum reason : values()) {
            if (reason.getCode().equals(code)) {
                return reason;
            }
        }
        return null;
    }

    public static String getName(Integer code) {
        for (CouponRefundReasonEnum reason : values()) {
            if (reason.getCode().equals(code)) {
                return reason.getName();
            }
        }
        return null;
    }

}

package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.assertj.core.util.Lists;

import java.util.List;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum OrderModifyLogEnum {

    /**
     * 编辑订单备注
     * 编辑商品备注
     * 申请部分取消订单
     * 申请整单取消订单
     * 同意部分取消订单
     * 同意整单取消订单
     * 拒绝部分取消订单
     * 拒绝整单取消订单
     */
    FULL_REFUND_APPLY(0, "申请整单取消"),
    PARTIAL_REFUND_APPLY(1, "申请部分取消"),

    FULL_REFUND_APPROVE(3, "同意整单取消"),
    PARTIAL_REFUND_APPROVE(4, "同意部分取消"),
    EDIT_ORDER_REMARK(5, "编辑订单备注"),
    EDIT_PRODUCT_REMARK(6, "编辑商品备注"),
    FULL_REFUND_REFUSE(7, "拒绝整单取消"),
    PARTIAL_REFUND_REFUSE(8, "拒绝部分取消"),
    EDIT_INVOICE_INFO(9, "修改纸质专票信息"),
    EDIT_ORDER_REFUND_REMARK(10, "编辑退单信息"),
    EDIT_ESPECIAL_INVOICE_INFO(11, "修改电子专票信息"),
    REFUND_APPLY(12, "发起退款申请"),
    EDIT_ORDER_INFO(13, "编辑订单信息"),
    CUSTOMER_INFO(14, "客户信息"),
    ORDER_REFUND_REMARK(15, "退单信息"),
    MANUAL_FINISH_ORDER(16, "手动完成订单");



    private final Integer code;

    private final String description;
    
    public static String getDescriptionByCode(Integer code) {
        for (OrderModifyLogEnum status : OrderModifyLogEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        throw new IllegalArgumentException("Invalid order status code: " + code);
    }

    public static boolean contains(String description) {
        // 只有list里的内容，才会展示查看详情和修改字段
        List<String> list = Lists.list(
            EDIT_ORDER_REMARK.getDescription(),
            EDIT_PRODUCT_REMARK.getDescription(), 
            EDIT_INVOICE_INFO.getDescription(), 
            EDIT_ESPECIAL_INVOICE_INFO.getDescription(),
            EDIT_ORDER_INFO.getDescription(), 
            REFUND_APPLY.getDescription()
        );
        return list.contains(description);
    }
}
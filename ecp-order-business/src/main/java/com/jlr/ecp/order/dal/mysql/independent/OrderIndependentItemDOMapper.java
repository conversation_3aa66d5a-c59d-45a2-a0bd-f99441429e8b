package com.jlr.ecp.order.dal.mysql.independent;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentItemDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR> Hongyi
* @description 针对表【t_order_independent_item】的数据库操作Mapper
* @createDate 2025-03-13 01:08:44
* @Entity com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentItemDO
*/
@Mapper
public interface OrderIndependentItemDOMapper extends BaseMapper<OrderIndependentItemDO> {

    default List<OrderIndependentItemDO> listByIndependentCode(String independentCode) {
        LambdaQueryWrapper<OrderIndependentItemDO> wrapper = Wrappers.lambdaQuery(OrderIndependentItemDO.class)
                .in(OrderIndependentItemDO::getIndependentCode, independentCode)
                .eq(OrderIndependentItemDO::getIsDeleted, Boolean.FALSE);
        return selectList(wrapper);
    }

}
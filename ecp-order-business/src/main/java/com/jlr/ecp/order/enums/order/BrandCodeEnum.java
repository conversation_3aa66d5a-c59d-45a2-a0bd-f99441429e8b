package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@AllArgsConstructor
@Getter
public enum BrandCodeEnum {

    /**
     * 路虎品牌
     */
    LAND_ROVER("LR", 1, "路虎","Land Rover"),

    /**
     * 捷豹品牌
     */
    JAGUAR("JA", 2, "捷豹","Jaguar");

    /**
     * 品牌编码，用于标识品牌
     */
    private final String brandCode;

    /**
     * 数据库中的品牌ID
     */
    private final Integer brandId;

    /**
     * 品牌名称
     */
    private final String brandName;


    /**
     * 显示
     */
    private final String display;



    /**
     * 根据品牌编码获取品牌名称
     *
     * @param brandCode 品牌编码
     * @return 对应的品牌名称
     */
    public static String getBrandNameByBrandCode(String brandCode) {
        for (BrandCodeEnum brand : BrandCodeEnum.values()) {
            if (brand.getBrandCode().equals(brandCode)) {
                return brand.getBrandName();
            }
        }
        throw new IllegalArgumentException("Invalid brand code: " + brandCode);
    }

    /**
     * 根据数据库中的品牌ID获取品牌名称
     *
     * @param brandId 品牌ID
     * @return 对应的品牌名称
     */
    public static String getBrandNameById(Integer brandId) {
        for (BrandCodeEnum brand : BrandCodeEnum.values()) {
            if (brand.getBrandId().equals(brandId)) {
                return brand.getBrandName();
            }
        }
        throw new IllegalArgumentException("Invalid brand ID: " + brandId);
    }

    /**
     * 根据品牌编码获取品牌ID
     */
    public static Integer getBrandIdByBrandCode(String brandCode) {
        for (BrandCodeEnum type : BrandCodeEnum.values()) {
            if (type.getBrandCode().equals(brandCode)) {
                return type.getBrandId();
            }
        }
        throw new IllegalArgumentException("Invalid brand code: " + brandCode);
    }

    /**
     * Test method to validate getBrandNameByBrandCode()
     */
    public static void testGetBrandNameByBrandCode() {
        // 验证路虎品牌的名称是否正确
        assertEquals("路虎", getBrandNameByBrandCode("LR"));

        // 验证捷豹品牌的名称是否正确
        assertEquals("捷豹", getBrandNameByBrandCode("JA"));

        // 测试非法品牌编码时抛出IllegalArgumentException
        try {
            getBrandNameByBrandCode("INVALID");
        } catch (IllegalArgumentException e) {
            assertEquals("Invalid brand code: INVALID", e.getMessage());
        }
    }

    /**
     * Test method to validate getBrandNameById()
     */
    public static void testGetBrandNameById() {
        // 验证品牌ID为1对应的名称是否为路虎
        assertEquals("路虎", getBrandNameById(1));

        // 验证品牌ID为2对应的名称是否为捷豹
        assertEquals("捷豹", getBrandNameById(2));

        // 测试非法品牌ID时抛出IllegalArgumentException
        try {
            getBrandNameById(99);
        } catch (IllegalArgumentException e) {
            assertEquals("Invalid brand ID: 99", e.getMessage());
        }
    }

    /**
     * Test method to validate getBrandIdByBrandCode()
     */
    public static void testGetBrandIdByBrandCode() {
        // 验证路虎品牌的ID转换是否正确
        assertEquals(1, getBrandIdByBrandCode("LR").intValue());

        // 验证捷豹品牌的ID转换是否正确
        assertEquals(2, getBrandIdByBrandCode("JA").intValue());

        // 测试非法品牌编码时抛出IllegalArgumentException
        try {
            getBrandIdByBrandCode("INVALID");
        } catch (IllegalArgumentException e) {
            assertEquals("Invalid brand code: INVALID", e.getMessage());
        }
    }

    public static List<BrandCodeEnum> getDashboardDisplayList() {
        return List.of(LAND_ROVER, JAGUAR);
    }

    public static void main(String[] args) {
        testGetBrandNameByBrandCode();
        testGetBrandNameById();
        testGetBrandIdByBrandCode();
    }
}
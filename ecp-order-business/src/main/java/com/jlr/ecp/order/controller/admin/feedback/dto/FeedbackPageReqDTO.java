package com.jlr.ecp.order.controller.admin.feedback.dto;

import com.jlr.ecp.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "详情 - 分页req参数")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FeedbackPageReqDTO extends PageParam {

    @Schema(description = "评价配置编码")
    @NotNull(message = "评价配置编码不能为空")
    private String feedbackCode;
}

package com.jlr.ecp.order.enums.redis;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * RedisDelayQueueEnum
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum RedisDelayQueueEnum {

    /**
     * 添加。
     */
    ECP_ORDER_SCHEDULED_LAUNCH("ECP-ORDER-SCHEDULED-LAUNCH", "ECP-ORDER定时任务", "scheduledLaunchBean"),
    ECP_ORDER_FEEDBACK_SCHEDULED_LAUNCH("ECP-ORDER-FEEDBACK-SCHEDULED-LAUNCH", "ECP-ORDER-FEEDBACK定时任务", "feedbackScheduledLaunchBean");



    /**
     * 延迟队列 Redis Key
     */
    private final String code;

    /**
     * 中文描述
     */
    private final String name;

    /**
     * 延迟队列具体业务实现的 Bean
     * 可通过 Spring 的上下文获取
     */
    private String beanId;

}

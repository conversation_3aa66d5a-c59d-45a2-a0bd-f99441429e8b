package com.jlr.ecp.order.kafka;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderPaymentSuccessMessage implements Serializable {
    /**
     * 车辆vin码
     */
    private String carVin;

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 任务编码
     */
    private String taskCode;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 订单编号
     */
    private String orderNumber;


    /**
     * 微信短连接
     */
    private String wxUrl;

    /**
     * 品牌编码
     * 	1路虎 ，2捷豹
     */
    private Integer brandCode;
}

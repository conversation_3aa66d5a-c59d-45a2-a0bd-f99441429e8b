package com.jlr.ecp.order.dal.dataobject.customer.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

/**
 * t_customer_service_order
 *
 * <AUTHOR>
 */
@TableName(value = "t_customer_service_order")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CustomerServiceOrderDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 下单ID;客服下单ID，雪花算法ID
     */
    @TableField(value = "order_id")
    private String orderId;

    /**
     * 关联订单号;关联订单号
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 接收付款短信号;接收付款短信号
     */
    @TableField(value = "recieve_phone")
    private String recievePhone;

    /**
     * 短信模板编码;短信模板编码
     */
    @TableField(value = "message_template_code")
    private String messageTemplateCode;

    /**
     * 创建人;创建人
     */
    @TableField(value = "create_operator")
    private String createOperator;

    /**
     * 是否绑定客户;是否绑定客户，0否 1是
     * 客户绑定订单前，t_order_info的consumer_code为固定值：CUSTOMER_SERVICE_ORDER
     */
    @TableField(value = "bind_customer")
    private Integer bindCustomer;
}
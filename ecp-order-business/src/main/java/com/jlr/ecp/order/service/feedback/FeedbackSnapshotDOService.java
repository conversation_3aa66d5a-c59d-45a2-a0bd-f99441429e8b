package com.jlr.ecp.order.service.feedback;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedbackPageReqDTO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedbackVserionListVO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackSnapshotDO;

import java.util.List;

/**
 * 评价设置快照表Service
 */
public interface FeedbackSnapshotDOService extends IService<FeedbackSnapshotDO> {
    /**
     * 查询历史版本分页列表
     * @param dto 入参
     * @return 列表
     */
    PageResult<FeedbackVserionListVO> selectHistoryPage(FeedbackPageReqDTO dto);



    List<FeedbackVserionListVO> getSnapshotCodeByFeedbackCode(String feedbackCode);
}

package com.jlr.ecp.order.dal.dataobject.refund;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 订单退款支付记录 DO
 */
@TableName("t_order_refund_payment_records")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class OrderRefundPaymentRecordsDO extends BaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 原单号
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 退单号
     */
    @TableField(value = "refund_order_code")
    private String refundOrderCode;

    /**
     * 交易单号; 支付中心交易单号
     */
    @TableField(value = "pay_apply_no")
    private String payApplyNo;

    /**
     * 退款申请单号; 支付中心退款申请单号
     */
    @TableField(value = "refund_apply_no")
    private String refundApplyNo;

    /**
     * 退单状态; 交易处理中： PENDING，交易成功 ：SUCCESS，交易失败 ：FAIL
     */
    @TableField(value = "trade_status")
    private String tradeStatus;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

} 
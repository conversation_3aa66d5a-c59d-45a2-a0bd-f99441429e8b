package com.jlr.ecp.order.controller.admin.dashboard.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 图表查询DTO
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChartQueryDTO {
  /**
   * 订单类型
   */
  private Integer orderType;
  /**
   * 日期列表
   */
  private List<String> dateList;
  /**
   * 日期映射
   */
  private Map<String, String> dateNameMap;
}

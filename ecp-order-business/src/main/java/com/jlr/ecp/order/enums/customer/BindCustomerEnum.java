package com.jlr.ecp.order.enums.customer;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 是否绑定客户的枚举类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum BindCustomerEnum {

    /**
     * 否
     */
    NO(0, "否"),

    /**
     * 是
     */
    YES(1, "是");

    private final Integer code;

    private final String description;

    // 根据code获取描述
    public static String getDescription(Integer code) {
        if (Objects.isNull(code)) {
            return "-";
        }
        for (BindCustomerEnum status : BindCustomerEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getDescription();
            }
        }
        return "-";
    }

    // 根据code获取枚举实例
    public static BindCustomerEnum fromCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (BindCustomerEnum status : BindCustomerEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
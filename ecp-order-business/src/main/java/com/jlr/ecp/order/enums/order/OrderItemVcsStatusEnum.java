package com.jlr.ecp.order.enums.order;

/**
 * 订单项VCS状态枚举
 */
public enum OrderItemVcsStatusEnum {

    /**
     * 待激活
     */
    PENDING_ACTIVATION(1, "待激活"),

    /**
     * 激活中
     */
    ACTIVATING(2, "激活中"),

    /**
     * 已激活
     */
    ACTIVATED(3, "已激活"),

    /**
     * 激活失败
     */
    ACTIVATION_FAILED(4, "激活失败");

    private final int code;
    private final String name;

    OrderItemVcsStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据状态码获取枚举实例
     * @param code 状态编码
     * @return 匹配的枚举实例，未找到返回null
     */
    public static OrderItemVcsStatusEnum getByCode(int code) {
        for (OrderItemVcsStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 严格校验模式（可选）
     * @throws IllegalArgumentException 当code无效时抛出异常
     */
    public static OrderItemVcsStatusEnum getByCodeStrict(int code) {
        OrderItemVcsStatusEnum status = getByCode(code);
        if (status == null) {
            throw new IllegalArgumentException("无效的VCS状态码: " + code);
        }
        return status;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
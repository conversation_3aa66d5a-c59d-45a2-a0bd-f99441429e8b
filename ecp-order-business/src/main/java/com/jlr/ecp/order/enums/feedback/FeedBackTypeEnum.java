package com.jlr.ecp.order.enums.feedback;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum FeedBackTypeEnum {
    PAID("PM", "2", "已支付"),
    COMPLETED("OR", "3", "订单完成"),
    FULLY_CANCELLED("CL", "7", "订单整单取消");

    private final String code;

    // 订单状态
    private final String orderStatus;

    private final String desc;


    // 根据code获取对应的枚举实例
    public static String fromCode(String code) {
        for (FeedBackTypeEnum type : FeedBackTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return code;
    }

    // 根据订单状态获取评价维度
    public static String getFeedbackDimensionsByOrderStatus(int orderStatus) {
        for (FeedBackTypeEnum type : FeedBackTypeEnum.values()) {
            if (Integer.parseInt(type.getOrderStatus()) == orderStatus) {
                return type.getCode();
            }
        }
        return null;
    }
}

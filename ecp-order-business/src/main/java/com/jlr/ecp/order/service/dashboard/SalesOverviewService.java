package com.jlr.ecp.order.service.dashboard;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.controller.admin.dashboard.dto.KpiQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SalesSummaryReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SummaryQueryReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.SalesSummaryRespVo;

import java.util.List;

public interface SalesOverviewService {
    SalesSummaryRespVo getSalesSummary(SalesSummaryReqDTO reqDTO);

    CommonResult<ProductSalesTrendRespVo> querySalesSummaryCharts(SummaryQueryReqDTO dto);

    CommonResult<List<KpiQueryDTO>> salesKpiList();
}
package com.jlr.ecp.order.delayqueue.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jetbrains.annotations.Nullable;

/**
 * 错误类型枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ErrorTypeEnum {
    
    /**
     * 网络超时错误
     */
    NETWORK_TIMEOUT("NETWORK_TIMEOUT", "网络超时错误", true),
    
    /**
     * 连接错误
     */
    CONNECTION_ERROR("CONNECTION_ERROR", "连接错误", true),
    
    /**
     * 数据库错误
     */
    DATABASE_ERROR("DATABASE_ERROR", "数据库错误", true),
    
    /**
     * Redis错误
     */
    REDIS_ERROR("REDIS_ERROR", "Redis错误", true),
    
    /**
     * 业务错误
     */
    BUSINESS_ERROR("BUSINESS_ERROR", "业务错误", false),
    
    /**
     * 验证错误
     */
    VALIDATION_ERROR("VALIDATION_ERROR", "验证错误", false),
    
    /**
     * 系统错误
     */
    SYSTEM_ERROR("SYSTEM_ERROR", "系统错误", true),
    
    /**
     * 未知错误
     */
    UNKNOWN("UNKNOWN", "未知错误", true);
    
    /**
     * 错误代码
     */
    private final String code;
    
    /**
     * 错误描述
     */
    private final String description;
    
    /**
     * 是否可重试
     */
    private final boolean retryable;
    
    /**
     * 根据异常获取错误类型
     * 
     * @param exception 异常
     * @return 错误类型枚举
     */
    public static ErrorTypeEnum fromException(Exception exception) {
        if (exception == null) {
            return UNKNOWN;
        }
        
        String exceptionName = exception.getClass().getSimpleName();
        String message = exception.getMessage();

        ErrorTypeEnum networkTimeout = getErrorTypeEnum(exceptionName);
        if (networkTimeout != null) return networkTimeout;

        // 根据异常消息进一步判断
        if (message != null) {
            String lowerMessage = message.toLowerCase();
            if (lowerMessage.contains("timeout")) {
                return NETWORK_TIMEOUT;
            } else if (lowerMessage.contains("connection")) {
                return CONNECTION_ERROR;
            } else if (lowerMessage.contains("validation") || lowerMessage.contains("invalid")) {
                return VALIDATION_ERROR;
            }
        }
        
        // 默认为系统错误
        return SYSTEM_ERROR;
    }

    @Nullable
    private static ErrorTypeEnum getErrorTypeEnum(String exceptionName) {
        // 网络相关异常
        if (exceptionName.contains("Timeout")) {
            return NETWORK_TIMEOUT;
        }

        // 连接相关异常
        if (exceptionName.contains("Connection") || exceptionName.contains("Socket")) {
            return CONNECTION_ERROR;
        }

        // 数据库相关异常
        if (exceptionName.contains("SQL") || exceptionName.contains("Database") ||
            exceptionName.contains("DataAccess")) {
            return DATABASE_ERROR;
        }

        // Redis相关异常
        if (exceptionName.contains("Redis") || exceptionName.contains("Jedis")) {
            return REDIS_ERROR;
        }

        // 业务相关异常
        if (exceptionName.contains("Business")) {
            return BUSINESS_ERROR;
        }

        // 验证相关异常
        if (exceptionName.contains("Validation") || exceptionName.contains("IllegalArgument")) {
            return VALIDATION_ERROR;
        }
        return null;
    }

    /**
     * 根据错误代码获取枚举
     * 
     * @param code 错误代码
     * @return 错误类型枚举
     */
    public static ErrorTypeEnum fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return UNKNOWN;
        }
        
        for (ErrorTypeEnum errorType : values()) {
            if (errorType.getCode().equals(code)) {
                return errorType;
            }
        }
        
        return UNKNOWN;
    }
}

package com.jlr.ecp.order.kafka;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivateMessage {
    /**
     * 消息Id
     * */
    private String messageId;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     *  任务code
     * */
    private String taskCode;

    /**
     * 租户id
     * */
    private Long tenantId;

    /**
     *  服务名称
     * */
    private String serviceName;

    /**
     * 订单编号
     * */
    private String orderNumber;

    /**
     * 父订单编号
     * */
    private String parentOrderCode;

    /**
     * 服务到期时间
     * */
    private String validityDate;

    /**
     * 解密后的carVin
     */
    private String carVin;

    /**
     * 1路虎, 2捷豹
     * */
    private Integer brandCode;

    /**
     * 完成状态
     */
    private Boolean completed;
}


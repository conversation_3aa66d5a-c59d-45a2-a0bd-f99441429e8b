package com.jlr.ecp.order.controller.admin.feedback.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "管理后台 - 评价修改详情VO")
public class FeedbackModifyDetailVO {

    @Schema(description = "修改时间", required = true)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;

    @Schema(description = "修改人账号", required = true)
    private String operateUser;

    @Schema(description = "修改前数据（JSON格式）", required = true)
    private String modifyFieldOldValue;

    @Schema(description = "修改后数据（JSON格式）", required = true)
    private String modifyFieldNewValue;

    @Schema(description = "字段变更详情")
    private List<FieldChange> changes;

    @Data
    @AllArgsConstructor
    public static class FieldChange {
        @Schema(description = "字段名称")
        private String fieldName;

        @Schema(description = "旧值")
        private String oldValue;

        @Schema(description = "新值")
        private String newValue;
    }
}
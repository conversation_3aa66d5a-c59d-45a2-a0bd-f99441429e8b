package com.jlr.ecp.order.enums.cart;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ProductShelfStatusEnum
 *
 * <AUTHOR> <PERSON>
 * @since 2025-03-22 14:21:07
 */
@AllArgsConstructor
@Getter
public enum ProductShelfStatusEnum {

    /**
     * 草稿
     */
    DRAFT(0,"草稿"),
    /**
     * 未上架
     */
    NOT_UP(1,"未上架"),
    /**
     * 已上架
     */
    UP(2,"已上架"),
    /**
     * 已下架
     */
    DOWN(3,"已下架");

    /**
     * 商品上下架状态编码
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

}

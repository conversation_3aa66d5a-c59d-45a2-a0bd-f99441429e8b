package com.jlr.ecp.order.service.order;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.logistics.api.packageinfo.PackageInfoApi;
import com.jlr.ecp.logistics.api.packageinfo.dto.PackageInfoQueryDTO;
import com.jlr.ecp.logistics.api.packageinfo.dto.PackageInfoQueryRespDTO;
import com.jlr.ecp.logistics.api.packageinfo.dto.QueryTrackData;
import com.jlr.ecp.logistics.enums.company.LogisticsCompanyEnum;
import com.jlr.ecp.order.controller.app.order.dto.OrderLogisticsQryDTO;
import com.jlr.ecp.order.controller.app.order.vo.LogisticsDetailItemVO;
import com.jlr.ecp.order.controller.app.order.vo.LogisticsDetailVO;
import com.jlr.ecp.order.controller.app.order.vo.OrderLogisticsQryRespVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemLogisticsDO;
import com.jlr.ecp.order.dal.mysql.order.OrderItemLogisticsDOMapper;
import com.jlr.ecp.order.util.PIPLDataUtil;
import com.jlr.ecp.system.api.address.AddressConfigApi;
import com.jlr.ecp.system.api.address.dto.AddressNameDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * OrderItemLogisticsDOServiceImpl
 *
 * <AUTHOR> John Gen
 * @since 2025-04-03 15:21
 */
@Slf4j
@Component
public class OrderItemLogisticsDOServiceImpl
        extends ServiceImpl<OrderItemLogisticsDOMapper, OrderItemLogisticsDO>
        implements OrderItemLogisticsDOService {

    @Resource
    private PackageInfoApi packageInfoApi;

    @Resource
    private AddressConfigApi addressConfigApi;

    @Resource
    private PIPLDataUtil piplDataUtil;

    @Override
    public OrderLogisticsQryRespVO queryOrderLogisticsInfo(OrderLogisticsQryDTO orderLogisticsQryDTO) {

        // 根据 Order Code 查询 OrderItemLogisticsDO
        String orderCode = orderLogisticsQryDTO.getOrderCode();

        OrderLogisticsQryRespVO orderLogisticsQryRespVO = new OrderLogisticsQryRespVO();

        // 设置 Order Code
        orderLogisticsQryRespVO.setOrderCode(orderCode);

        QueryWrapper<OrderItemLogisticsDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtils.isNotBlank(orderLogisticsQryDTO.getOrderCode()), OrderItemLogisticsDO::getOrderCode, orderCode)
                .eq(StringUtils.isNotBlank(orderLogisticsQryDTO.getNumber()), OrderItemLogisticsDO::getLogisticsNo, orderLogisticsQryDTO.getNumber())
                .eq(OrderItemLogisticsDO::getIsDeleted, false);
        List<OrderItemLogisticsDO> orderItemLogisticsDOList = baseMapper.selectList(queryWrapper);

        log.info("在数据库中查询到的快递信息:{}", JSON.toJSONString(orderItemLogisticsDOList));

        if (ObjectUtil.isNotEmpty(orderItemLogisticsDOList)) {

            // 取第一个 OrderItemLogistics 的手收件人信息
            OrderItemLogisticsDO orderItemLogisticsDO = orderItemLogisticsDOList.get(0);
            log.info("获取到的第一个快递信息:{}", JSON.toJSONString(orderItemLogisticsDO));
            if (null != orderItemLogisticsDO) {

                // 设置收件人
                orderLogisticsQryRespVO.setRecipient(piplDataUtil.getDecodeText(orderItemLogisticsDO.getRecipient()));

                // 设置收件人手机号
                orderLogisticsQryRespVO.setRecipientPhone(piplDataUtil.getDecodeText(orderItemLogisticsDO.getRecipientPhone()));

                // 调用 Address API 查询省市区信息
                AddressNameDTO addressName = getAddressName(orderItemLogisticsDO);
                log.info("获取到的省市区信息:{}", JSON.toJSONString(addressName));

                if (null != addressName) {
                    // 设置省信息
                    orderLogisticsQryRespVO.setProvince(addressName.getProvince());

                    // 设置市信息
                    orderLogisticsQryRespVO.setCity(addressName.getCity());

                    // 设置区信息
                    orderLogisticsQryRespVO.setAre(addressName.getArea());
                }

                // 设置详细地址信息
                orderLogisticsQryRespVO.setDetailAddress(piplDataUtil.getDecodeText(orderItemLogisticsDO.getDetailAddress()));

                // 设置物流公司名称
                orderLogisticsQryRespVO.setCompany(orderItemLogisticsDO.getLogisticsCompany());
                orderLogisticsQryRespVO.setCompanyName(LogisticsCompanyEnum.getName(orderItemLogisticsDO.getLogisticsCompany()));

                // 设置快递单号
                orderLogisticsQryRespVO.setNumber(orderLogisticsQryDTO.getNumber());

                // 调用 Kuaidi100 查询物流信息
                PackageInfoQueryRespDTO logisticsInfo = getLogisticsInfo(
                        orderLogisticsQryDTO.getCompany(),
                        orderLogisticsQryDTO.getNumber(),
                        piplDataUtil.getDecodeText(orderItemLogisticsDO.getRecipientPhone())
                );
                log.info("调用快递 100 查询到的物流信息:{}", JSON.toJSONString(logisticsInfo));

                // 组装物流信息
                List<LogisticsDetailItemVO> detailItemList = assembleLogisticsInfo(logisticsInfo);
                log.info("组装完成的快递信息:{}", JSON.toJSONString(detailItemList));
                orderLogisticsQryRespVO.setDetailItemList(detailItemList);
            }

        }

        return orderLogisticsQryRespVO;
    }

    /**
     * 组装物流信息
     *
     * @param logisticsInfo PackageInfoQueryRespDTO
     * @return List of LogisticsDetailItemVO
     */
    private List<LogisticsDetailItemVO> assembleLogisticsInfo(PackageInfoQueryRespDTO logisticsInfo) {

        List<LogisticsDetailItemVO> itemVOList = new ArrayList<>();

        List<QueryTrackData> queryTrackData = logisticsInfo.getData();
        if (null != queryTrackData) {

            // 按照物流状态 Grouping
            LinkedHashMap<String, List<QueryTrackData>> statusTrackDataMap =
                    queryTrackData.stream()
                            .collect(Collectors.groupingBy(
                                    QueryTrackData::getStatus, LinkedHashMap::new, Collectors.toList()
                            ));

            for (Map.Entry<String, List<QueryTrackData>> trackDataEntry : statusTrackDataMap.entrySet()) {

                LogisticsDetailItemVO logisticsDetailItemVO = new LogisticsDetailItemVO();

                String packageStatus = trackDataEntry.getKey();
                List<QueryTrackData> trackDataList = trackDataEntry.getValue();

                // 设置物流状态
                logisticsDetailItemVO.setStatus(packageStatus);

                // 取第一条 Track Data 时间为状态变更时间
                if (ObjectUtil.isNotEmpty(trackDataList)) {
                    QueryTrackData firstTrackData = trackDataList.get(0);
                    if (null != firstTrackData) {
                        // 设置状态变更时间
                        logisticsDetailItemVO.setStatusDate(parseDateString(firstTrackData.getFtime(), "yyyy-MM-dd HH:mm"));
                    }

                    // 组装物流信息
                    List<LogisticsDetailVO> logisticsDetailVOList = assembleToLogisticsDetialVoList(trackDataList);
                    if (ObjectUtil.isNotEmpty(logisticsDetailVOList)) {
                        logisticsDetailItemVO.setDetailList(logisticsDetailVOList);
                    }
                }

                itemVOList.add(logisticsDetailItemVO);
            }

        }

        return itemVOList;
    }

    /**
     * 将 List of QueryTrackData 转换为 List of LogisticsDetailVO
     *
     * @param trackDataList List of QueryTrackData
     * @return List of LogisticsDetailVO
     */
    private List<LogisticsDetailVO> assembleToLogisticsDetialVoList(List<QueryTrackData> trackDataList) {

        List<LogisticsDetailVO> logisticsDetailVOList = new ArrayList<>();

        if (ObjectUtil.isEmpty(trackDataList)) {
            return logisticsDetailVOList;
        }

        for (QueryTrackData queryTrackData : trackDataList) {
            LogisticsDetailVO logisticsDetailVO = assembleToLogisticsDetailVo(queryTrackData);
            if (null != logisticsDetailVO) {
                logisticsDetailVOList.add(logisticsDetailVO);
            }
        }

        return logisticsDetailVOList;
    }

    /**
     * 将 QueryTrackData 转换为 LogisticsDetailVO
     *
     * @param queryTrackData QueryTrackData
     * @return LogisticsDetailVO
     */
    private LogisticsDetailVO assembleToLogisticsDetailVo(QueryTrackData queryTrackData) {

        if (null == queryTrackData) {
            return null;
        }

        LogisticsDetailVO logisticsDetailVO = new LogisticsDetailVO();

        // Set Date
        logisticsDetailVO.setDate(parseDateString(queryTrackData.getFtime(), "yyyy/MM/dd HH:mm:ss"));

        // Set Context
        logisticsDetailVO.setContext(queryTrackData.getContext());

        return logisticsDetailVO;
    }

    /**
     * 获取省市区名称信息
     *
     * @param orderItemLogisticsDO OrderItemLogisticsDO
     * @return AddressNameDTO
     */
    public AddressNameDTO getAddressName(OrderItemLogisticsDO orderItemLogisticsDO) {

        // 调用 Address API 查询省市区信息
        AddressNameDTO addressNameDTO = null;
        try {
            CommonResult<AddressNameDTO> addressNameResult = addressConfigApi.getNameByCode(
                    orderItemLogisticsDO.getProvinceCode(),
                    orderItemLogisticsDO.getCityCode(),
                    orderItemLogisticsDO.getAreaCode()
            );

            if (addressNameResult.isSuccess()) {
                addressNameDTO = addressNameResult.getData();
            }

        } catch (Exception e) {
            log.error("OrderItemLogisticsDOServiceImpl:getAddressName:", e);
        }

        return addressNameDTO;
    }


    /**
     * 调用 Logistics 服务查询快递信息
     *
     * @param companyName 快递公司名称 -- code
     * @param number      快递单号
     * @param phone       快递关联手机号
     * @return PackageInfoQueryRespDTO
     */
    @Override
    public PackageInfoQueryRespDTO getLogisticsInfo(String companyName, String number, String phone) {

        PackageInfoQueryDTO packageInfoQueryDTO = new PackageInfoQueryDTO();

        packageInfoQueryDTO.setCompany(companyName);
        packageInfoQueryDTO.setNumber(number);
        packageInfoQueryDTO.setPhone(phone);

        PackageInfoQueryRespDTO packageInfoQueryRespDTO = null;

        try {
            log.info("调用logistics服务, queryPackageInfo 请求参数: {}", JSON.toJSONString(packageInfoQueryDTO));
            CommonResult<PackageInfoQueryRespDTO> packageInfoQueryRespDTOCommonResult =
                    packageInfoApi.queryPackageInfo(packageInfoQueryDTO);
            if (packageInfoQueryRespDTOCommonResult.isSuccess()) {
                packageInfoQueryRespDTO = packageInfoQueryRespDTOCommonResult.getData();
            }
        } catch (Exception e) {
            log.error("OrderItemLogisticsDOServiceImpl:getLogisticsInfo:", e);
        }

        return packageInfoQueryRespDTO;
    }

    /**
     * 转 yyyy-MM-dd HH:mm:ss 为 target 格式
     *
     * @param dateString DateString yyyy-MM-dd HH:mm:ss
     * @param format formatString yyyy/MM/dd HH:mm:ss
     * @return Long
     */
    public String parseDateString(String dateString, String format) {

        if (StringUtils.isEmpty(dateString)) {
            return null;
        }

        String result = null;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime localDateTime = null;
        try {
            localDateTime = LocalDateTime.parse(dateString, formatter);
            result = localDateTime.format(DateTimeFormatter.ofPattern(format));
        } catch (Exception e) {
            log.error("OrderItemLogisticsDOServiceImpl:parseDateString:", e);
        }
        return result;
    }
}

package com.jlr.ecp.order.controller.app.feedback;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.controller.app.feedback.dto.FeedbackQueryDTO;
import com.jlr.ecp.order.controller.app.feedback.dto.FeedbackSubmitDTO;
import com.jlr.ecp.order.controller.app.feedback.vo.FeedbackSettingVO;
import com.jlr.ecp.order.service.feedback.FeedbackConfigDOService;
import com.jlr.ecp.order.service.feedback.FeedbackRecordsDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 订单评论控制器
 *
 * <AUTHOR>
 */
@Tag(name = "app端 - 订单评论")
@RestController
@RequestMapping("v1/feedback")
@Validated
@Slf4j
public class FeedBackAppController {

    // 注入所需的依赖服务
    @Resource
    private FeedbackConfigDOService feedbackConfigDOService;

    @Resource
    private FeedbackRecordsDOService feedbackRecordsDOService;

    /**
     * 1.评价记录根据当前 订单所处环节（已支付、订单完成、订单整单取消）查询评价设置（维度+输入框）
     */
    @PostMapping("/queryFeedbackSettings")
    @Operation(summary = "根据订单环节查询评价设置")
    public CommonResult<FeedbackSettingVO> queryFeedbackSettings(@Valid @RequestBody FeedbackQueryDTO dto) {
        log.info("根据订单环节查询评价设置, 入参dto={}", dto);

        // 调用服务层获取评价设置信息
        FeedbackSettingVO feedbackSettingVO = feedbackConfigDOService.getFeedBackByDimensions(dto);

        log.info("查询评价设置成功, feedbackSettingVO={}", feedbackSettingVO);
        return CommonResult.success(feedbackSettingVO);
    }

    /**
     * 提交评价（新增评价记录）
     */
    @PostMapping("/submitFeedbackRecord")
    @Operation(summary = "提交评价（新增评价记录）")
    public CommonResult<Boolean> submitFeedback(@Valid @RequestBody FeedbackSubmitDTO dto) {
        log.info("提交评价, 入参dto={}", dto);

        // 调用服务层处理提交评价逻辑
        Boolean result = feedbackRecordsDOService.submitFeedback(dto);

        log.info("提交评价成功, resultMessage={}", result);
        return CommonResult.success(result);
    }


}

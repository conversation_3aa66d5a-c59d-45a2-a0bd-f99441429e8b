package com.jlr.ecp.order.service.order;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.order.dto.*;
import com.jlr.ecp.order.api.order.vo.*;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;

/**
 * <AUTHOR>
 * @description 针对表【t_order_info(t_order_info)】的数据库操作Service
 * @createDate 2023-12-20 10:41:04
 */
public interface BrandGoodsOrderInfoDOService extends IService<OrderInfoDO> {

    /**
     * 创建订单
     *
     * @param orderCreateDTO
     * @return
     */
    OrderCreateRespVO createOrderInfo(BrandGoodsOrderCreateDTO orderCreateDTO) throws JsonProcessingException;

    /**
     * 订单分页列表
     *
     * @param dto 分页入参
     * @return PageResult<OrderInfoPageVO>
     */
    PageResult<BrandGoodsOrderInfoPageVO> getPage(BrandGoodsOrderPageReqDTO dto);

    /**
     * 确认收货
     * @param reqDto
     * @return
     */
    Void confirmReceipt(BrandGoodsOrderConfirmReceiptDTO reqDto);

    /**
     * 确认收货定时任务
     * @param reqDTO
     * @return
     */
    Boolean confirmReceiptList(ConfirmReceiptJobReqDTO reqDTO);
}

package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 订单支付记录 DO
 */
@TableName("t_order_payment_records")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class OrderPaymentRecordsDO extends BaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 发起用户JLRID
     */
    @TableField(value = "consumer_code")
    private String consumerCode;

    /**
     * 订单编号
     */
    @TableField(value = "order_code")
    private String orderCode;

    /**
     * 交易流水号; 支付中心交易流水号
     */
    @TableField(value = "pay_apply_no")
    private String payApplyNo;

    /**
     * 发起交易时间
     */
    @TableField(value = "submit_time")
    private LocalDateTime submitTime;

    /**
     * 支付状态; 1待支付 2已支付 3.支付失败
     */
    @TableField(value = "pay_status")
    private Integer payStatus;

    /**
     * 支付完成时间
     */
    @TableField(value = "pay_finish_time")
    private LocalDateTime payFinishTime;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Integer tenantId;

} 
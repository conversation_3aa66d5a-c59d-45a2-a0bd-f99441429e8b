package com.jlr.ecp.order.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * t_order_status_mapping
 * <AUTHOR>
 * @TableName t_order_status_mapping
 */
@TableName(value ="t_order_status_mapping")
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderStatusMappingDO extends BaseDO {
    /**
     * 租户号
     */
    private Integer tenantId;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 订单状态;订单状态
     */
    @TableField(value = "order_status")
    private Integer orderStatus;

    /**
     * 退单状态;退单状态
     */
    @TableField(value = "refund_order_status")
    private Integer refundOrderStatus;

    /**
     * 小程序订单状态;小程序订单状态
     */
    @TableField(value = "customer_order_status_view")
    private String customerOrderStatusView;

    /**
     * 小程序售后状态;小程序售后状态
     */
    @TableField(value = "customer_after_sales_order_status_view")
    private String customerAfterSalesOrderStatusView;

    /**
     * 原子订单运营状态;原子订单运营状态
     */
    @TableField(value = "operation_origin_order_status_view")
    private String operationOriginOrderStatusView;

    /**
     * 原单服务取消状态;原单服务取消状态
     */
    @TableField(value = "operation_origin_order_cancel_status_view")
    private String operationOriginOrderCancelStatusView;



    /**
     * 小程序退单状态
     */
    @TableField(value = "customer_refund_order_status_view")
    private String customerRefundOrderStatusView;
}
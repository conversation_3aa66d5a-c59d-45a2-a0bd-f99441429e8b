package com.jlr.ecp.order.delayqueue.consumer;

import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.delayqueue.dto.CancelOrderTask;
import com.jlr.ecp.order.delayqueue.service.DelayQueueMonitorService;
import com.jlr.ecp.order.delayqueue.service.RedissonOrderCancelDelayService;
import com.jlr.ecp.order.delayqueue.service.RedissonRetryQueueService;
import com.jlr.ecp.order.kafka.message.CancelOrderMessage;
import com.jlr.ecp.order.service.order.CancelOrderService;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collections;

/**
 * Redisson延迟队列消费者
 * 使用Redisson的监听器机制，无需手动创建线程
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class RedissonCancelOrderConsumer {
    
    @Resource
    private RedissonOrderCancelDelayService delayService;

    @Resource
    private RedissonRetryQueueService retryQueueService;

    @Resource
    private DelayQueueMonitorService monitorService;

    @Resource
    private CancelOrderService cancelOrderService;
    
    @PostConstruct
    public void init() {
        // 启动BG+LRE队列监听器
        startBgLreQueueListener();

        // 启动VCS队列监听器
        startVcsQueueListener();

        // 启动重试队列监听器
        startRetryQueueListener();

        log.info("Redisson延迟队列和重试队列监听器已启动");

        // 记录初始队列状态
        monitorService.logQueueStatus();
    }

    /**
     * 启动BG+LRE队列监听器（15分钟延迟）
     */
    private void startBgLreQueueListener() {
        RBlockingQueue<CancelOrderTask> bgLreQueue = delayService.getBgLreQueue();
        String bgLreBusinessLine = BusinessIdEnum.BRAND_GOODS.getName() + "/" + BusinessIdEnum.LRE.getName();

        // 使用Redisson的监听器，自动处理任务
        bgLreQueue.subscribeOnElements(task -> {
            try {
                processTask(task, bgLreBusinessLine);
            } catch (Exception e) {
                log.error("{}队列任务处理失败，taskId={}, orderCode={}",
                        bgLreBusinessLine, task.getTaskId(), task.getOrderCode(), e);
            }
        });

        log.info("{}业务线延迟队列监听器已启动", bgLreBusinessLine);
    }
    
    /**
     * 启动VCS队列监听器（2小时延迟）
     */
    private void startVcsQueueListener() {
        RBlockingQueue<CancelOrderTask> vcsQueue = delayService.getVcsQueue();
        String vcsBusinessLine = BusinessIdEnum.VCS.getName();

        // 使用Redisson的监听器，自动处理任务
        vcsQueue.subscribeOnElements(task -> {
            try {
                processTask(task, vcsBusinessLine);
            } catch (Exception e) {
                log.error("{}队列任务处理失败，taskId={}, orderCode={}",
                        vcsBusinessLine, task.getTaskId(), task.getOrderCode(), e);
            }
        });

        log.info("{}业务线延迟队列监听器已启动", vcsBusinessLine);
    }
    
    /**
     * 处理延迟任务
     */
    private void processTask(CancelOrderTask task, String businessLine) {
        log.info("开始处理{}业务线延迟取消任务，taskId={}, orderCode={}", 
                businessLine, task.getTaskId(), task.getOrderCode());
        
        // 设置租户上下文
        TenantContextHolder.setTenantId(task.getTenantId());
        
        try {
            // 构建取消订单消息
            CancelOrderMessage message = buildCancelOrderMessage(task);
            
            // 执行取消订单逻辑
            cancelOrderService.processMessage(Collections.singletonList(message));
            
            log.info("{}业务线延迟取消订单成功，taskId={}, orderCode={}", 
                    businessLine, task.getTaskId(), task.getOrderCode());
                    
        } catch (Exception e) {
            log.error("{}业务线延迟取消订单失败，taskId={}, orderCode={}",
                    businessLine, task.getTaskId(), task.getOrderCode(), e);

            // 使用重试机制处理失败任务
            retryQueueService.handleTaskFailure(task, e, businessLine);

        } finally {
            // 清理租户上下文
            TenantContextHolder.clear();
        }
    }
    
    /**
     * 构建取消订单消息
     */
    private CancelOrderMessage buildCancelOrderMessage(CancelOrderTask task) {
        CancelOrderMessage message = new CancelOrderMessage();
        message.setOrderCode(task.getOrderCode());
        message.setParentOrderCode(task.getParentOrderCode());
        message.setTenantId(task.getTenantId());
        message.setSendTime(task.getSendTime());
        message.setBusinessCode(task.getBusinessCode());
        return message;
    }

    /**
     * 启动重试队列监听器
     */
    private void startRetryQueueListener() {
        RBlockingQueue<CancelOrderTask> retryQueue = retryQueueService.getRetryQueue();

        retryQueue.subscribeOnElements(task -> {
            try {
                // 根据任务的业务编码确定业务线名称
                String businessLineName = getBusinessLineName(task.getBusinessCode());
                processTask(task, "重试队列-" + businessLineName);
            } catch (Exception e) {
                String businessLineName = getBusinessLineName(task.getBusinessCode());
                log.error("重试队列任务处理失败，taskId={}, orderCode={}, businessLine={}",
                        task.getTaskId(), task.getOrderCode(), businessLineName, e);
                // 重试失败，再次进入重试逻辑
                retryQueueService.handleTaskFailure(task, e, businessLineName);
            }
        });

        log.info("重试队列监听器已启动");
    }

    /**
     * 根据业务编码获取业务线名称
     */
    private String getBusinessLineName(String businessCode) {
        if (BusinessIdEnum.VCS.getCode().equals(businessCode)) {
            return BusinessIdEnum.VCS.getName();
        } else if (BusinessIdEnum.BRAND_GOODS.getCode().equals(businessCode)) {
            return BusinessIdEnum.BRAND_GOODS.getName();
        } else if (BusinessIdEnum.LRE.getCode().equals(businessCode)) {
            return BusinessIdEnum.LRE.getName();
        } else {
            return "未知业务线";
        }
    }

}

package com.jlr.ecp.order.util.freight;


import cn.smallbun.screw.core.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.util.freight
 * @className: FreightUtil
 * @author: gaoqig
 * @description: 运费转换工具
 * @date: 2025/4/8 17:41
 * @version: 1.0
 */
public class FreightUtil {
    public static BigDecimal transferWeight(String weight) {
        BigDecimal weightDecimal = null;
        // 价格传入乘以1000处理成分入库
        if (StringUtils.isNotBlank(weight)) {
            weightDecimal = new BigDecimal(weight).multiply(new BigDecimal(1000));
        }
        return weightDecimal;
    }

    public static String convertWeight(Integer weight) {
        if (weight == null) {
            return null;
        }
        try {
            BigDecimal weightDecimal = BigDecimal.valueOf(weight).divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);        // 去掉小数部分的多余零
            weightDecimal = weightDecimal.stripTrailingZeros();
            return weightDecimal.toString();
        } catch (NumberFormatException e) {        // 处理非法的算术运算
            return null;
        }
    }
}
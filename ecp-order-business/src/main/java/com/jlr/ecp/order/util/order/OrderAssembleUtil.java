package com.jlr.ecp.order.util.order;

import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;

import java.time.LocalDateTime;

/**
 * 组装对象工具类
 * author lislu
 */
public class OrderAssembleUtil {

    /**
     *
     * @param code  order code / order refund code
     * @param beforeStatus
     * @param afterStatus
     * @return
     */
    public static OrderStatusLogDO assembleOrderStatusLogDO(String code,Integer beforeStatus,Integer afterStatus){
        OrderStatusLogDO statusLogDO = new OrderStatusLogDO();
        statusLogDO.setOrderCode(code);
        statusLogDO.setBeforeStatus(beforeStatus==null?afterStatus:beforeStatus);
        statusLogDO.setAfterStatus(afterStatus);
        statusLogDO.setChangeTime(LocalDateTime.now());
        return statusLogDO;
    }

}

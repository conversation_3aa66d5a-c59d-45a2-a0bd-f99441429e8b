package com.jlr.ecp.order.service.internal.price.impl;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.cart.dto.CalculateAmountDTO;
import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.ProductCouponRelDto;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.controller.app.cart.vo.CalculateAmountVO;
import com.jlr.ecp.order.controller.app.cart.vo.ShoppingCarItemVO;
import com.jlr.ecp.order.dal.dataobject.cart.ShoppingCarItemDO;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.cart.CarPaymentTypeEnum;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.service.internal.price.CalculateService;
import com.jlr.ecp.order.service.internal.price.ShopCalculateAmountService;
import com.jlr.ecp.order.service.internal.promotion.dto.PromotionRespDto;
import com.jlr.ecp.order.util.money.MoneyUtil;
import com.jlr.ecp.product.api.couponrule.ProductCouponUsageRuleApi;
import com.jlr.ecp.product.api.couponrule.dto.ListCanUsedCouponModelRespDTO;
import com.jlr.ecp.product.api.product.vo.ProductAttributeRespVO;
import com.jlr.ecp.product.api.product.vo.ProductCodeListVO;
import com.jlr.ecp.product.api.product.vo.ProductDetailsVO;
import com.jlr.ecp.product.api.product.vo.ProductSkuRespVO;
import com.jlr.ecp.product.api.sku.ProductSkuApi;
import com.jlr.ecp.product.enums.product.ProductStockLimitFlagEnum;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import com.jlr.ecp.virtual.api.coupon.CouponApi;
import com.jlr.ecp.virtual.api.point.PointApi;
import com.jlr.ecp.virtual.dto.coupon.query.AvailableCouponQry;
import com.jlr.ecp.virtual.dto.coupon.response.AvailableCouponResp;
import com.jlr.ecp.virtual.dto.coupon.response.AvailablePointResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jlr.ecp.order.enums.ErrorCodeConstants.NOT_CLEAR_COUPON_TYPE;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.internal.price.impl
 * @className: CalculateServiceImpl
 * @author: gaoqig
 * @description: 计算服务
 * @date: 2025/3/27 10:51
 * @version: 1.0
 */
@Service
@Slf4j
public class CalculateServiceImpl implements CalculateService {
    @Value("${couponFlag}")
    private Boolean couponFlag;

    @Resource
    private ProductSkuApi productSkuApi;

    @Resource
    private ShopCalculateAmountService shopCalculateAmountService;

    @Resource
    private ProductCouponUsageRuleApi productCouponUsageRuleApi;

    @Resource
    private PointApi pointApi;

    @Resource
    private CouponApi couponApi;

    @Value("${useMockCoupons: false}")
    private Boolean useMockCoupons;

    @Override
    public CalculateAmountVO calculateAmount(CalculateAmountDTO calculateAmountDTO, String consumerCode) {
        CalculateAmountVO result = new CalculateAmountVO();
        List<ShoppingCarItemDO> shoppingCarItemDOS;
        if (calculateAmountDTO.getCartItemCodeList() != null) {
            shoppingCarItemDOS = shopCalculateAmountService.checkCartItem(calculateAmountDTO.getCartItemCodeList(), consumerCode);
            shoppingCarItemDOS = shoppingCarItemDOS.stream().sorted(Comparator.comparing(ShoppingCarItemDO::getId)).collect(Collectors.toList());
        } else {//目前之后有非VCS商品，所以信息不用那么全，如果后续要支持，可以让前端传更多的信息（PDP跳转场景）
            shoppingCarItemDOS = calculateAmountDTO.getProductItems().stream().map(x -> {
                ShoppingCarItemDO shoppingCarItemDO = new ShoppingCarItemDO();
                shoppingCarItemDO.setConsumerCode(consumerCode);
                shoppingCarItemDO.setQuantity(x.getQuantity());
                shoppingCarItemDO.setProductSkuCode(x.getProductSkuCode());
                shoppingCarItemDO.setProductCode(x.getProductSpuCode());

                return shoppingCarItemDO;
            }).collect(Collectors.toList());
        }

        //构建chooseFlag的count map，后面根据这个count map来标识哪些做了选择
        Map<String, Integer> orderSkuItemDetailFlag = new HashMap<>();
        boolean choosePointWay = false; //是否有选择积分模式
        choosePointWay = isChoosePointWay(calculateAmountDTO, choosePointWay, orderSkuItemDetailFlag);

        //获取sku list,并转成map
        List<String> sdkCodeList = shoppingCarItemDOS.stream().map(ShoppingCarItemDO::getProductSkuCode).collect(Collectors.toList());
        Map<String, ProductSkuRespVO> productSpuInfoVOMap = getProductSkuMap(sdkCodeList);

        //构建获取商品模版关系的请求参数
        ProductCodeListVO productCodeListVO = new ProductCodeListVO();
        productCodeListVO.setProductCodeList(shoppingCarItemDOS.stream().map(ShoppingCarItemDO::getProductCode).collect(Collectors.toList()));

        //获取商品与优惠券的绑定关系
        Map<String, ProductCouponRelDto> productCoiponModuleMap = getSpuCouponRelation(productCodeListVO.getProductCodeList());

        Map<String, String> skuToKingdeeSkuMap = productSpuInfoVOMap.values().stream().collect(Collectors.toMap(
                ProductSkuRespVO::getProductSkuCode,
                ProductSkuRespVO::getModelCode
        ));

        Map<String, Integer> modeCodeToQuantityMap = getModeCodeToQuantity(shoppingCarItemDOS, skuToKingdeeSkuMap);
        //CartProductSkuInfo
        List<CartProductSkuInfo> productSkuInfos = new ArrayList<>();
        for (ShoppingCarItemDO x : shoppingCarItemDOS) {
            ProductSkuRespVO tmpProductSku = productSpuInfoVOMap.get(x.getProductSkuCode());
            ProductCouponRelDto productCouponRelDto = productCoiponModuleMap.get(x.getProductCode());
            String modeCode = skuToKingdeeSkuMap.get(x.getProductSkuCode());
            // modeCode对应的商品数量
            Integer needQuantity = modeCodeToQuantityMap.get(modeCode);
            for (int i = 0; i < x.getQuantity(); i++) {
                CartProductSkuInfo tmp = new CartProductSkuInfo();
                tmp.setCartItemId(getCartItemId(x, i));//如果有购物车ID就赋值ID，没有就用这个遍历的index，主要为了排序
                tmp.setSalePrice(MoneyUtil.convertToYuanFromStr(tmpProductSku.getSalePrice()));
                tmp.setSalePoints(tmpProductSku.getSalePoints());
                tmp.setSalePointsPrice(MoneyUtil.convertToYuanFromStr(tmpProductSku.getSalePointsPrice()));
                tmp.setProductSkuCode(tmpProductSku.getProductSkuCode());
                tmp.setProductCode(tmpProductSku.getProductSpuCode());
                tmp.setProductName(tmpProductSku.getProductDetailsVO().getProductName());
                tmp.setSupportCashAndPoints(tmpProductSku.getProductDetailsVO().getSupportCashAndPoints());
                tmp.setCouponModuleCodeList(getCouponModuleCodeList(productCouponRelDto));
                tmp.setBusinessCode(tmpProductSku.getProductDetailsVO().getBusinessCode());
                setChosseCouponCode(calculateAmountDTO, productCouponRelDto, tmp);

                //如果是购物车页面来计算优惠，则不会管库存。否则需要管库存是否足够，库存会决定是否参与后面的计算
                setJoinCalculateFlag(tmpProductSku, choosePointWay, tmp, calculateAmountDTO.isFromShopCartPage(), needQuantity);



                //对冗余字段进行赋值(productSkuInfos里面的优惠券信息)
                Integer count = orderSkuItemDetailFlag.get(tmp.getProductSkuCode());

                extracted(calculateAmountDTO, count, tmp, orderSkuItemDetailFlag, choosePointWay);

                productSkuInfos.add(tmp);
            }
        }

        BigDecimal totalAmount = shopCalculateAmountService.calculateCartItemAmount(productSkuInfos, calculateAmountDTO.isFromShopCartPage());

        List<PromotionDto> promotions;
        PromotionDto point;
        //获取优惠券和积分
        if (Boolean.TRUE.equals(useMockCoupons)) {
            //下面两行mock 代码
            promotions = getCouponsFromJsonFile();
            point = getPointsFromJsonFile();
        } else {
            promotions = getAvailableCouponList(consumerCode, calculateAmountDTO.getVinList());
            point = getAvailablePoints(consumerCode);
        }

        promotions = fiterAvailableCouponList(promotions, productSkuInfos, calculateAmountDTO.isFromShopCartPage());


        //将用户可用优惠券和积分封装进结果中
        result.setAvailableCoupons(promotions);
        result.setAvailablePoints(getAvailablePoints(point));
        promotions.add(point);

        PromotionDto userChoosePromotion = null;

        userChoosePromotion = getPromotionDto(calculateAmountDTO, choosePointWay, userChoosePromotion, promotions);

        if (ignoreCalculateDiscount(calculateAmountDTO)) {//不计算优惠
            result.setPaymentType(CarPaymentTypeEnum.CASH.getCode());
            // 直接汇总计算逻辑
            result.setTotalAmount(MoneyUtil.convertFromCents(totalAmount));
            result.setCostAmount(MoneyUtil.convertFromCents(totalAmount));
            result.setDiscountTotalAmount(BigDecimal.ZERO.toString());
            result.setCartSkuProductList(productSkuInfos);
            result.setUseCouponCode(false);
            result.setCostPoints(null);
            setItemList(result, shoppingCarItemDOS, productSpuInfoVOMap);
            result.setAvailableCoupons(result.getAvailableCoupons().stream()
                    .filter(x -> !CouponTypeEnum.POINTS.getType().equals(x.getCouponModelClassify()))//把积分优惠方式过滤
                    .collect(Collectors.toList()));
            result.setCartSkuProductList(result.getCartSkuProductList().stream().sorted(Comparator.comparing(CartProductSkuInfo::getCartItemId)).collect(Collectors.toList()));
            return result;
        }

        PromotionRespDto promotionRespDto = shopCalculateAmountService.compareCouponAmount(productSkuInfos, promotions, userChoosePromotion, calculateAmountDTO.getPaymentType());
        if (promotionRespDto != null) {
            result.setPaymentType(promotionRespDto.getCouponTypeEnum().coverToCarpaymentTypeEnum().getCode());
            result.setTotalAmount(MoneyUtil.convertFromCents(totalAmount));
            result.setCostAmount(MoneyUtil.convertFromCents(totalAmount.subtract(MoneyUtil.convertToCents(promotionRespDto.getDiscountTotalAmount()))));
            result.setDiscountTotalAmount(promotionRespDto.getDiscountTotalAmount());
            result.setCartSkuProductList(promotionRespDto.getCartSkuProductList());
            if (CouponTypeEnum.POINTS.equals(promotionRespDto.getCouponTypeEnum())) {
                result.setUseCouponCode(false);//如果不是积分模式，则是命中了优惠券
                result.setCostPoints(promotionRespDto.getCostPoints());
            } else {
                result.setUseCouponCode(true);//如果不是积分模式，则是命中了优惠券
                result.setChooseCoupon(promotionRespDto.getChooseCoupon());
                result.setCostPoints(null);
            }

            setItemList(result, shoppingCarItemDOS, productSpuInfoVOMap);

            //对优惠券排序，优惠金额越大越前面
            result.setAvailableCoupons(result.getAvailableCoupons().stream()
                    .filter(x -> !CouponTypeEnum.POINTS.getType().equals(x.getCouponModelClassify()))//把积分优惠方式过滤
                    .sorted(CalculateServiceImpl::compareSortResult).collect(Collectors.toList()));
        } else {
            //没有优惠券的形式，直接和关闭一样返回
            result.setPaymentType(CarPaymentTypeEnum.CASH.getCode());
            // 直接汇总计算逻辑
            result.setTotalAmount(MoneyUtil.convertFromCents(totalAmount));
            result.setCostAmount(MoneyUtil.convertFromCents(totalAmount));
            result.setDiscountTotalAmount(BigDecimal.ZERO.toString());
            result.setCartSkuProductList(productSkuInfos);
            result.setUseCouponCode(false);
            result.setCostPoints(null);
            result.setAvailableCoupons(result.getAvailableCoupons().stream()
                    .filter(x -> !CouponTypeEnum.POINTS.getType().equals(x.getCouponModelClassify()))//把积分优惠方式过滤
                    .collect(Collectors.toList()));
            setItemList(result, shoppingCarItemDOS, productSpuInfoVOMap);
            result.setCartSkuProductList(result.getCartSkuProductList().stream().sorted(Comparator.comparing(CartProductSkuInfo::getCartItemId)).collect(Collectors.toList()));
        }
        return result;
    }

    private static void setChosseCouponCode(CalculateAmountDTO calculateAmountDTO, ProductCouponRelDto productCouponRelDto, CartProductSkuInfo tmp) {
        if (productCouponRelDto != null
                && productCouponRelDto.getCouponModuleCodeList() !=null
                && productCouponRelDto.getCouponModuleCodeList().contains(calculateAmountDTO.getCouponCode())){
            tmp.setChooseCouponCode(calculateAmountDTO.getCouponCode());
        }
    }

    @NotNull
    private static Map<String, Integer> getModeCodeToQuantity(List<ShoppingCarItemDO> shoppingCarItemDOS, Map<String, String> skuToKingdeeSkuMap) {
        Map<String, Integer> modeCodeToQuantityMap = new HashMap<>();
        for (ShoppingCarItemDO shoppingCarItemDO : shoppingCarItemDOS) {
            String modeCode = skuToKingdeeSkuMap.get(shoppingCarItemDO.getProductSkuCode());
            Integer quantity = modeCodeToQuantityMap.get(modeCode);
            if (quantity == null) {
                quantity = 0;
            }
            quantity += shoppingCarItemDO.getQuantity();
            modeCodeToQuantityMap.put(modeCode, quantity);
        }
        return modeCodeToQuantityMap;
    }

    private static int compareSortResult(PromotionDto a, PromotionDto b) {
        Integer costCompare = compareResult(a, b);
        if (costCompare != null && costCompare != 0) {
            return costCompare;
        }

        // 最后比较优惠券类型
        return Integer.compare(
                CouponTypeEnum.getByType(b.getCouponModelClassify()).getPriority(),
                CouponTypeEnum.getByType(a.getCouponModelClassify()).getPriority()
        );
    }

    private static void setJoinCalculateFlag(ProductSkuRespVO tmpProductSku, boolean choosePointWay, CartProductSkuInfo tmp, boolean fromShopCartPage, Integer needQuantity) {
        if (fromShopCartPage){//如果是来自购物车，用户时无法选择积分模式的
            tmp.setJoinCalculateFlag(true);
            tmp.setFinalJoinCalculateFlag(true);
            //如果来自购物车页面，则忽略库存是否足够，都参与计算（其他条件依然需要自行判断）
            return;
        }
        if (tmpProductSku.getSkuQuantity() == null){//如果库存为空，则说明是LRE商品，
            tmp.setJoinCalculateFlag(true);
            tmp.setFinalJoinCalculateFlag(true);
            if (choosePointWay){ //如果当前是选择的积分模式
                //如果当前商品不支持积分模式，则不参与计算
                tmp.setFinalJoinCalculateFlag(tmpProductSku.getProductDetailsVO().getSupportCashAndPoints());
            }
            return;
        }

        setJoinCalculateFlagForBG(tmpProductSku, choosePointWay, tmp, tmpProductSku.getSkuQuantity(), needQuantity);
    }

    private static void setJoinCalculateFlagForBG(ProductSkuRespVO tmpProductSku, boolean choosePointWay, CartProductSkuInfo tmp, Integer stockNumber, Integer needQuantity) {
        //对于有库存限制的商品
        boolean isStockLimited = Objects.equals(
                tmpProductSku.getProductDetailsVO().getStockLimitFlag(),
                ProductStockLimitFlagEnum.LIMITED.getCode()
        );
        boolean isStockSufficient = stockNumber >= needQuantity;
        boolean supportsCashAndPoints = tmpProductSku.getProductDetailsVO().getSupportCashAndPoints();

        if (isStockLimited) {
            // 库存限制逻辑
            tmp.setJoinCalculateFlag(isStockSufficient);
            tmp.setFinalJoinCalculateFlag(isStockSufficient);
        } else {
            // 无库存限制逻辑
            tmp.setJoinCalculateFlag(true);
            tmp.setFinalJoinCalculateFlag(!choosePointWay || supportsCashAndPoints);
        }

        if (!tmp.isJoinCalculateFlag()){
            //如果当前商品因库存问题不能参与计算，将积分价格也置空，这样就表示这个商品不能选择积分优惠了
            tmp.setSalePoints(null);
            tmp.setSalePointsPrice(null);
        }
    }

    private boolean ignoreCalculateDiscount(CalculateAmountDTO calculateAmountDTO) {
        return Boolean.FALSE.equals(couponFlag) || (calculateAmountDTO.getIgnoreDiscount() != null && calculateAmountDTO.getIgnoreDiscount());
    }

    private static List<String> getCouponModuleCodeList(ProductCouponRelDto productCouponRelDto) {
        return productCouponRelDto != null ? productCouponRelDto.getCouponModuleCodeList() : new ArrayList<>();
    }

    private static long getCartItemId(ShoppingCarItemDO x, int i) {
        return x.getId() == null ? i : x.getId();
    }

    private static int getAvailablePoints(PromotionDto point) {
        return (point == null || point.getPoints() == null) ? 0 : point.getPoints();
    }

    @Nullable
    private static Integer compareResult(PromotionDto a, PromotionDto b) {
        // 首先比较 costAmount（花费越少越靠前）
        int costCompare = new BigDecimal(a.getCostAmount() == null ? "0" : a.getCostAmount()).compareTo(new BigDecimal(b.getCostAmount() == null ? "0" : b.getCostAmount()));
        if (costCompare != 0) return costCompare;

        // 然后比较过期时间（越早过期越靠前）
        if (a.getValidEndTime() == null && b.getValidEndTime() == null) return 0;
        if (a.getValidEndTime() == null) return 1;
        if (b.getValidEndTime() == null) return -1;
        int timeCompare = a.getValidEndTime().compareTo(b.getValidEndTime());
        if (timeCompare != 0) return timeCompare;
        return null;
    }

    private static PromotionDto getPromotionDto(CalculateAmountDTO calculateAmountDTO, boolean choosePointWay, PromotionDto userChoosePromotion, List<PromotionDto> promotions) {
        if (choosePointWay) {//如果选择了积分就优先使用积分
            userChoosePromotion = promotions.stream().
                    filter(x -> CouponTypeEnum.POINTS.getType().equals(x.getCouponModelClassify())).//注意要在封装point进来之前判断
                            findFirst().orElse(null);
            if (userChoosePromotion == null) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.GET_POINTS_FAILED);
            }
        } else if (!StringUtils.isEmpty(calculateAmountDTO.getCouponCode())) {
            userChoosePromotion = promotions.stream().
                    filter(x -> !CouponTypeEnum.POINTS.getType().equals(x.getCouponModelClassify()) && x.getCouponCode().equals(calculateAmountDTO.getCouponCode())).//注意要在封装point进来之前判断
                            findFirst().orElse(null);
            if (userChoosePromotion == null) {
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.NOT_FOUND_COUPON);
            }
        }
        return userChoosePromotion;
    }

    private static void extracted(CalculateAmountDTO calculateAmountDTO, Integer count, CartProductSkuInfo tmp, Map<String, Integer> orderSkuItemDetailFlag, boolean choosePointWay) {
        if (count != null && count > 0) {
            tmp.setChooseFlag(true);
            count--;
            orderSkuItemDetailFlag.put(tmp.getProductSkuCode(), count);
        } else {
            tmp.setChooseFlag(false);
            //choosePointWay-true: 用户选择积分模式
            //choosePointWay-false: 用户选择没有选择积分模式，可能选择优惠券模式、或者执行最优模式计算
            if (choosePointWay) {
                tmp.setFinalJoinCalculateFlag(false);
            }
        }
        if (calculateAmountDTO.getCouponCode() != null) {
            tmp.setChooseCouponCode(calculateAmountDTO.getCouponCode());
            if (calculateAmountDTO.getCouponType() == null) {
                log.info("选择了优惠券，但是没有选择优惠类型");
                throw ServiceExceptionUtil.exception(NOT_CLEAR_COUPON_TYPE);
            }
        }
    }

    private static boolean isChoosePointWay(CalculateAmountDTO calculateAmountDTO, boolean choosePointWay, Map<String, Integer> orderSkuItemDetailFlag) {
        if (CollUtil.isNotEmpty(calculateAmountDTO.getProductItemList())) {
            for (CartProductSkuInfo cartProductSkuInfo : calculateAmountDTO.getProductItemList()) {
                if (Boolean.FALSE.equals(cartProductSkuInfo.getChooseFlag())) {
                    continue;
                }
                choosePointWay = true;
                if (orderSkuItemDetailFlag.containsKey(cartProductSkuInfo.getProductSkuCode())) {
                    orderSkuItemDetailFlag.put(cartProductSkuInfo.getProductSkuCode(), orderSkuItemDetailFlag.get(cartProductSkuInfo.getProductSkuCode()) + 1);
                } else {
                    orderSkuItemDetailFlag.put(cartProductSkuInfo.getProductSkuCode(), 1);
                }
            }
        }
        return choosePointWay;
    }


    /***
     * <AUTHOR>
     * @description 获取商品SKU信息，并转成Map
     * 这里会校验有没有VCS的商品，如果有，会直接报错
     * @date 2025/3/9 11:38
     * @param skuCodeList: 需要获取的商品SKU Code 列表
     * @return: java.util.Map<java.lang.String, com.jlr.ecp.product.api.product.vo.ProductSkuRespVO>
     * 里面的价格会转成分（默认请求API得到的单位是元）
     */

    private Map<String, ProductSkuRespVO> getProductSkuMap(List<String> skuCodeList) {
        CommonResult<List<ProductSkuRespVO>> skuRespVOList = productSkuApi.getSkuList(skuCodeList);
        if (!GlobalErrorCodeConstants.SUCCESS.getCode().equals(skuRespVOList.getCode()) && CollUtil.isEmpty(skuRespVOList.getData())) {
            log.info("获取sku信息失败,skuCodeList:{}", skuCodeList);
            throw ServiceExceptionUtil.
                    exception(ErrorCodeConstants.NOT_FOUND_SKU);
        }

        if (skuRespVOList.getData().stream().filter(x -> x.getProductDetailsVO().getBusinessCode().equals(BusinessIdEnum.VCS.getCode())).findAny().orElse(null) != null) {
            log.info("检测到SKU商品中存在VCS商品");
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.BUSINESS_INVALID);
        }


        return skuRespVOList.getData().stream().peek(item -> {
            //把单位全部转成分
            item.setMarketPrice(MoneyUtil.convertToCentsFromStr(item.getMarketPrice()));
            item.setSalePrice(MoneyUtil.convertToCentsFromStr(item.getSalePrice()));
            item.setSalePointsPrice(MoneyUtil.convertToCentsFromStr(item.getSalePointsPrice()));
            item.getProductDetailsVO().getSupportCashAndPoints();
        }).collect(Collectors.toMap(ProductSkuRespVO::getProductSkuCode, Function.identity()));
    }

    /***
     * <AUTHOR>
     * @description 获取商品与优惠券的关系, 并转成Map关系
     * @date 2025/3/9 11:01
     * @param spuList: 商品code list
     * @return: java.util.List<com.jlr.ecp.order.api.cart.dto.productCouponRelDto>
     */

    private Map<String, ProductCouponRelDto> getSpuCouponRelation(List<String> spuList) {
        CommonResult<List<ListCanUsedCouponModelRespDTO>> listCommonResult = productCouponUsageRuleApi.listCanUsedCouponModel(spuList);
        if (!CollUtil.isEmpty(listCommonResult.getData())) {
            return listCommonResult.getData().stream().map(x -> {
                ProductCouponRelDto productCouponRelDto = new ProductCouponRelDto();
                productCouponRelDto.setProductCode(x.getProductCode());
                productCouponRelDto.setCouponModuleCodeList(x.getCouponCodeList().stream().map(ListCanUsedCouponModelRespDTO.CouponTemplateItemRespDTO::getCouponModelCode).collect(Collectors.toList()));
                if (productCouponRelDto.getCouponModuleCodeList() == null) {
                    productCouponRelDto.setCouponModuleCodeList(new ArrayList<>());
                }
                return productCouponRelDto;

            }).collect(Collectors.toMap(ProductCouponRelDto::getProductCode, Function.identity()));
        }
        return new HashMap<>();
    }

    /***
     * <AUTHOR>
     * @description 获取用户的优惠券
     * 这里为移除当前不支持的优惠券：满减优惠券（满量），兑换券
     * @date 2025/3/9 10:06
     * @param customerCode: 用户编码
     * @return: java.util.List<com.jlr.ecp.order.api.cart.dto.PromotionDto>
     */

    private List<PromotionDto> getAvailableCouponList(String customerCode, List<String> carVinList) {
        AvailableCouponQry getAvailableCouponParam = new AvailableCouponQry();
        List<PromotionDto> result = new ArrayList<>();
        getAvailableCouponParam.setJlrId(customerCode);
        getAvailableCouponParam.setPageNo(0);//这里虽然设置为0，但是拿之前需要设置为1
        getAvailableCouponParam.setPageSize(100);//设置大一点，尽量调一次接口拿到
        getAvailableCouponParam.setVinList(carVinList);
        CommonResult<PageResult<AvailableCouponResp>> getAvailableCouponResp;
        do {
            getAvailableCouponParam.setPageNo(getAvailableCouponParam.getPageNo() + 1);
            log.info("请求virtual参数：getAvailableCouponParam：{}，customerCode：{}", JSON.toJSONString(getAvailableCouponParam), customerCode);
            getAvailableCouponResp = couponApi.obtainAvailableCouponList(getAvailableCouponParam, customerCode);
            log.info("请求virtual拿到的响应：getAvailableCouponParam：{}", JSON.toJSONString(getAvailableCouponResp));
            if (getAvailableCouponResp.isSuccess() && getAvailableCouponResp.getData().getList() != null) {
                List<PromotionDto> tmpResult = getAvailableCouponResp.getData().getList().stream().filter(CalculateServiceImpl::filter).map(item -> {
                    PromotionDto promotionDto = new PromotionDto();
                    promotionDto.setCouponModelClassify(item.getCouponModelClassify());
                    promotionDto.setCouponCode(item.getCouponCode());
                    promotionDto.setCouponModelCode(item.getCouponModelCode());
                    promotionDto.setCouponModelName(item.getCouponModelName());
                    promotionDto.setUseExplain(item.getUseExplain());
                    promotionDto.setCouponImgLink(item.getCouponImgLink());
                    promotionDto.setRuleType(item.getRuleType());
                    promotionDto.setUseRule(item.getUseRule());
                    promotionDto.setValidEndTime(item.getValidEndTime());
                    promotionDto.setValidStartTime(item.getValidStartTime());
                    promotionDto.setCouponScope(item.getCouponScope());
                    setAboutMoney(item, promotionDto);
                    return promotionDto;
                }).collect(Collectors.toList());
                result.addAll(tmpResult);
            }
        }while (getAvailableCouponResp.getData() != null && getAvailableCouponResp.getData().getTotal() != null && (long) getAvailableCouponParam.getPageNo() * getAvailableCouponParam.getPageSize() < getAvailableCouponResp.getData().getTotal() );//保证拿完所有数据
        return result;
    }

    private static void setAboutMoney(AvailableCouponResp item, PromotionDto promotionDto) {
        if (item.getTriggerMoney() != null){
            promotionDto.setTriggerMoney(String.valueOf(item.getTriggerMoney()));
        }
        if (item.getTriggerAmount() != null){
            promotionDto.setTriggerAmount(String.valueOf(item.getTriggerAmount()));
        }
        if (item.getMoney() != null){
            promotionDto.setMoney(String.valueOf(item.getMoney()));
        }
        if (item.getTriggerNumber() != null){
            promotionDto.setTriggerNumber(item.getTriggerNumber());
        }
        if (item.getDiscountPercent() != null){
            promotionDto.setDiscountPercent(Double.valueOf(item.getDiscountPercent()));
        }
    }

    private static boolean filter(AvailableCouponResp item) {
        if (item.getCouponModelClassify() == 1) {
            return false;
        }
        if (item.getCouponModelClassify() == 4 && item.getRuleType().equals(2)) {//满数量减不要
            return false;
        }
        //作废的不要
        return item.getCouponStatus() == null || item.getCouponStatus() != 4;
    }

    /***
     * <AUTHOR>
     * @description 获取可用积分
     * @date 2025/3/9 12:03
     * @param customerCode:
     * @return: java.util.List<com.jlr.ecp.order.api.cart.dto.PromotionDto>
     */

    private PromotionDto getAvailablePoints(String customerCode) {
        log.info("获取积分请求：getAvailablePoints, customerCode:{}", customerCode);
        CommonResult<AvailablePointResp> getAvailablePointsResp = pointApi.obtainAvailablePoint(customerCode);
        log.info("获取积分响应：getAvailablePointsResp, :{}", getAvailablePointsResp);
        if (!getAvailablePointsResp.isSuccess()){
            log.info("获取积分失败:{}", getAvailablePointsResp);
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.GET_POINTS_FAILED);
        }
        PromotionDto promotionDto = new PromotionDto();
        promotionDto.setCouponModelClassify(CouponTypeEnum.POINTS.getType());
        if (getAvailablePointsResp.getData() == null || getAvailablePointsResp.getData().getPoint() == null){
            promotionDto.setPoints(0);
        }else{
            promotionDto.setPoints(getAvailablePointsResp.getData().getPoint());
        }
        return promotionDto;
    }


    private List<PromotionDto> fiterAvailableCouponList(List<PromotionDto> promotions, List<CartProductSkuInfo> cartProductSkuInfoList, boolean isFromShopCartPage) {

        //所有商品配置的优惠模板编码
        List<String> availableCouponModelList = cartProductSkuInfoList.stream()
                .filter(item-> isFromShopCartPage || item.isJoinCalculateFlag())//如果当前来自购物车页面，不过滤。否则需要把不参与计算的过滤掉
                .flatMap(cartProductSkuInfo -> cartProductSkuInfo.getCouponModuleCodeList().stream())
                .distinct()
                .collect(Collectors.toList());

        // 满减券同模板优惠券金额累加Map
        Map<String, BigDecimal> couponAmountMap = new HashMap<>();
        for (PromotionDto promotionDto: promotions){
            if (CouponTypeEnum.CASH_BACK.getType().equals(promotionDto.getCouponModelClassify())){
                BigDecimal discountTotalMoney = cartProductSkuInfoList.stream().filter(item->item.getCouponModuleCodeList() != null
                        && item.getCouponModuleCodeList().contains(promotionDto.getCouponModelCode())
                        && item.isJoinCalculateFlag()
                        ).map(x-> MoneyUtil.getBigDecimal(x.getSalePrice())).
                        reduce(BigDecimal.ZERO, BigDecimal::add);
                couponAmountMap.put(promotionDto.getCouponModelCode(), discountTotalMoney);
            }
        }

        return promotions.stream().filter(item -> {
            // 优惠券模版code不在可用优惠券列表中
            if (!availableCouponModelList.contains(item.getCouponModelCode())) {
                return false;
            }
            // 满减券，判断triggerAmount是否大于等于商品同模板的金额之和
            if (Objects.equals(item.getCouponModelClassify(), CouponTypeEnum.CASH_BACK.getType())) {
                BigDecimal amount = couponAmountMap.get(item.getCouponModelCode());
                if (amount == null) {
                    return false;
                }
                return item.getTriggerMoney() != null && new BigDecimal(item.getTriggerMoney()).compareTo(amount) <= 0;
            }

            return true;
        }).collect(Collectors.toList());
    }

    private void setItemList(CalculateAmountVO result, List<ShoppingCarItemDO> shoppingCarItemDOS, Map<String, ProductSkuRespVO> productSpuInfoVOMap) {
        List<CartProductSkuInfo> productSkuInfoList = result.getCartSkuProductList();
        //将返回的订单行信息
        List<ShoppingCarItemVO> itemVOList = new ArrayList<>();
        for (ShoppingCarItemDO item : shoppingCarItemDOS){
            setItemInfo(result, productSpuInfoVOMap, item, itemVOList, productSkuInfoList);
        }

        result.setItemList(itemVOList);
    }

    private void setItemInfo(CalculateAmountVO result, Map<String, ProductSkuRespVO> productSpuInfoVOMap, ShoppingCarItemDO item, List<ShoppingCarItemVO> itemVOList, List<CartProductSkuInfo> productSkuInfoList) {
        /* ************************************基础信息组装****开始******************************************************************************/
        ShoppingCarItemVO shoppingCarItemVO = item.coverToVO();
        ProductSkuRespVO tempProductSkuView = getProductSkuRespVO(productSpuInfoVOMap, shoppingCarItemVO);
        //目前理论上来说，所有的商品的joinCalculateFlag都是一致的，要么全部为true，要么全部false;unPurchasableFlag表示不能参与购买，和joinFlag是相反的
        if (CollUtil.isNotEmpty(productSkuInfoList)){
            CartProductSkuInfo cartProductSkuInfo = productSkuInfoList.stream().filter(x -> x.getProductSkuCode().equals(tempProductSkuView.getProductSkuCode())).findAny().orElse(null);
            if (cartProductSkuInfo != null){
                shoppingCarItemVO.setUnPurchasableFlag(!cartProductSkuInfo.isJoinCalculateFlag());
            }
        }
        /* ************************************基础信息组装*****结束*****************************************************************************/
        //如果整体的支付方式是现金，说明没有任何商品参与优惠，直接计算赋值即可
        if (CarPaymentTypeEnum.CASH.getCode().equals(result.getPaymentType())){
            //如果是现金支付的方式，直接赋值即可，不用计算
            //costAmount
            BigDecimal costAmount = new BigDecimal(tempProductSkuView.getSalePrice()).multiply(BigDecimal.valueOf(shoppingCarItemVO.getQuantity()));//这个单位已经转过了
            shoppingCarItemVO.setCostAmount(MoneyUtil.convertFromCents(costAmount));
            //discountAmount
            shoppingCarItemVO.setDiscountTotalAmount("0");
            itemVOList.add(shoppingCarItemVO);
            return;
        }
        /* ************************************优惠信息组装*****开始*****************************************************************************/
        //订单行价格，等于每个商品商品的售价合
        //商品行的金额: 用商品总售价-优惠金额
        BigDecimal tmpCostAmount = BigDecimal.ZERO;  //汇总实际花费价格
        int tmpTotalCostPoint = 0;   //汇总实际花费积分
        BigDecimal tmpDiscountTotalAmount = BigDecimal.ZERO;  //汇总实际抵扣金额

        for (CartProductSkuInfo cartProductSkuInfo : productSkuInfoList) {//能进来，说明一定至少有一个SKU是参与了优惠的，但是只能说明是至少一个，而不是全部
            if (cartProductSkuInfo.getProductSkuCode().equals(shoppingCarItemVO.getProductSkuCode())) {
                if (isCashPayment(cartProductSkuInfo)) {//如果现金支付，则价格直接是销售价,空也是做现金支付
                    tmpCostAmount = tmpCostAmount.add(MoneyUtil.convertToCents(cartProductSkuInfo.getSalePrice()));
                } else {
                    shoppingCarItemVO.setPaymentType(CouponTypeEnum.coverToCarpaymentTypeEnum(cartProductSkuInfo.getChooseCouponType()).getCode());

                    //如果商品不支持任何优惠券或者积分，则花费金额就是销售价格。虽然这里理论上会有券后券，但还是做这个空检测
                    String realCostAmount = getRealCostAmount(cartProductSkuInfo);
                    tmpCostAmount = tmpCostAmount.add(MoneyUtil.convertToCents(realCostAmount));
                    tmpDiscountTotalAmount = tmpDiscountTotalAmount.add(getDiscountAmount(cartProductSkuInfo));

                    if (CarPaymentTypeEnum.POINT.getCode().equals(shoppingCarItemVO.getPaymentType())) {//只有选择了积分优惠，才需要计算积分相关的
                        tmpTotalCostPoint += getSafeSalePoints(cartProductSkuInfo);
                    }
                }
            }
        }

        shoppingCarItemVO.setDiscountTotalAmount(MoneyUtil.convertFromCents(tmpDiscountTotalAmount));
        shoppingCarItemVO.setCostAmount(MoneyUtil.convertFromCents(tmpCostAmount));
        setItemValue(shoppingCarItemVO, tmpTotalCostPoint);

        itemVOList.add(shoppingCarItemVO);
    }

    private static boolean isCashPayment(CartProductSkuInfo cartProductSkuInfo) {
        return cartProductSkuInfo.getChooseCouponType() == null || Objects.equals(CarPaymentTypeEnum.CASH.getCode(), cartProductSkuInfo.getChooseCouponType());
    }

    private static void setItemValue(ShoppingCarItemVO shoppingCarItemVO, Integer tmpTotalCostPoint) {
        if (!CarPaymentTypeEnum.POINT.getCode().equals(shoppingCarItemVO.getPaymentType())){
            shoppingCarItemVO.setCostPoints(null);
            if (!CarPaymentTypeEnum.CASH.getCode().equals(shoppingCarItemVO.getPaymentType())){//如果使用了优惠券则计算券后价（单个）
                //展示优惠单价，取该订单行下商品的最低券后价格
                BigDecimal unitCouponPrice = new BigDecimal(shoppingCarItemVO.getCostAmount()).divide(BigDecimal.valueOf(shoppingCarItemVO.getQuantity()),2, RoundingMode.DOWN);//用订单行的总花费/数量，得到单个券后价
                shoppingCarItemVO.setUnitCouponPrice(String.valueOf(unitCouponPrice));
            }
        } else {
            shoppingCarItemVO.setCostPoints(tmpTotalCostPoint);
            shoppingCarItemVO.setChoosePointFlag(true);
        }
    }

    @NotNull
    private ProductSkuRespVO getProductSkuRespVO(Map<String, ProductSkuRespVO> productSpuInfoVOMap, ShoppingCarItemVO shoppingCarItemVO) {
        ProductSkuRespVO tempProductSkuView = productSpuInfoVOMap.get(shoppingCarItemVO.getProductSkuCode());
        supplementShoppingCartItem(shoppingCarItemVO, tempProductSkuView.getProductDetailsVO());
        //拼接SKU属性值
        if (productSpuInfoVOMap.get(shoppingCarItemVO.getProductSkuCode()).getAttributeArr() != null) {
            shoppingCarItemVO.setAttributeValues(String.join("", productSpuInfoVOMap.get(shoppingCarItemVO.getProductSkuCode()).getAttributeArr()
                    .stream()
                    .map(ProductAttributeRespVO::getAttributeValue)
                    .collect(Collectors.joining())));
        }
        shoppingCarItemVO.setBusinessCode(tempProductSkuView.getProductDetailsVO().getBusinessCode());
        if (tempProductSkuView.getProductDetailsVO().getSupportCashAndPoints() != null && tempProductSkuView.getProductDetailsVO().getSupportCashAndPoints()) {
            shoppingCarItemVO.setSalePoints(tempProductSkuView.getSalePoints());
            shoppingCarItemVO.setSalePointsPrice(MoneyUtil.convertToYuanFromStr(tempProductSkuView.getSalePointsPrice()));
            shoppingCarItemVO.setSupportPointDiscount(true);
        }
        //salePrice(str)
        shoppingCarItemVO.setSalePrice(new BigDecimal(tempProductSkuView.getSalePrice()).longValue());
        shoppingCarItemVO.setSalePriceYuanStr(MoneyUtil.convertToYuanFromStr(tempProductSkuView.getSalePrice()));
        //marketPrice(str)
        if (tempProductSkuView.getMarketPrice() != null){
            shoppingCarItemVO.setMarketPrice(new BigDecimal(tempProductSkuView.getMarketPrice()).longValue());
            shoppingCarItemVO.setMarketPriceYuanStr(MoneyUtil.convertToYuanFromStr(tempProductSkuView.getMarketPrice()));
        }

        //赋值运费相关信息
        shoppingCarItemVO.setFreightTemplateCode(tempProductSkuView.getProductDetailsVO().getFeightTemplateCode());
        shoppingCarItemVO.setWeight(tempProductSkuView.getWeight());

        //履约类型
        shoppingCarItemVO.setFulfilmentType(tempProductSkuView.getProductDetailsVO().getFulfilmentType());

        //分类信息
        shoppingCarItemVO.setCategoryCodeLevel1Name(tempProductSkuView.getProductDetailsVO().getCategoryCodeLevel1Name());
        shoppingCarItemVO.setCategoryCodeLevel2Name(tempProductSkuView.getProductDetailsVO().getCategoryCodeLevel2Name());
        shoppingCarItemVO.setCategoryCodeLevel3Name(tempProductSkuView.getProductDetailsVO().getCategoryCodeLevel3Name());

        //先默认当前订单行是现金支付，在后续sku如果至少命中一个优惠，则这个订单行也认为是命中了这个优惠
        shoppingCarItemVO.setPaymentType(CarPaymentTypeEnum.CASH.getCode());
        shoppingCarItemVO.setChoosePointFlag(false);//默认没有选择积分模式，如果下面遍历时发现至少有一个用了积分，则认为该订单行采用了积分模式
        return tempProductSkuView;
    }

    private void supplementShoppingCartItem(ShoppingCarItemVO shoppingCarItemVO, ProductDetailsVO tempProductInfo) {
        shoppingCarItemVO.setSkuList(tempProductInfo.getProductAttr());
        shoppingCarItemVO.setIsDeleted(false);
        shoppingCarItemVO.setProductName(tempProductInfo.getProductName());
        //shoppingCarItemVO.setProductDetailReqVO(tempProductInfo.getProductDetailReqVO());
        shoppingCarItemVO.setShelfStatus(tempProductInfo.getShelfStatus());
        //shoppingCarItemVO.setPolicyInfo(tempProductInfo.getPolicyInfo());
        //shoppingCarItemVO.setCartItemType(tempProductInfo.getFulfilmentType());  todo BG时候放开
        shoppingCarItemVO.setChildFulfilmentType(tempProductInfo.getChildFulfilmentType());
    }
    // 使用辅助方法 降低认知复杂度
    private String getRealCostAmount(CartProductSkuInfo cartProductSkuInfo) {
        return cartProductSkuInfo.getCouponPrice() == null ?
                cartProductSkuInfo.getSalePrice() :
                cartProductSkuInfo.getCouponPrice();
    }

    // 使用辅助方法 降低认知复杂度
    private BigDecimal getDiscountAmount(CartProductSkuInfo cartProductSkuInfo) {
        return MoneyUtil.convertToCents(
                cartProductSkuInfo.getDiscountAmount() == null ?
                        "0" :
                        cartProductSkuInfo.getDiscountAmount()
        );
    }

    // 使用辅助方法 降低认知复杂度
    private int getSafeSalePoints(CartProductSkuInfo cartProductSkuInfo) {
        return cartProductSkuInfo.getSalePoints() == null ?
                0 :
                cartProductSkuInfo.getSalePoints();
    }

    private List<PromotionDto> getCouponsFromJsonFile() {
        try (InputStream inputStream = getClass().getResourceAsStream("/file/coupons.json")) {
            if (inputStream == null) {
                throw new RuntimeException("coupons.json not found in resources");
            }
            String json = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            return JSON.parseArray(json, PromotionDto.class);
        } catch (Exception e) {
            log.error("Failed to read coupons from JSON file", e);
            throw new RuntimeException("Failed to read coupons from JSON file", e);
        }
    }

    private PromotionDto getPointsFromJsonFile() {
        try (InputStream inputStream = getClass().getResourceAsStream("/file/points.json")) {
            if (inputStream == null) {
                throw new RuntimeException("points.json not found in resources");
            }
            String json = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            return JSON.parseObject(json, PromotionDto.class);
        } catch (Exception e) {
            log.error("Failed to read points from JSON file", e);
            throw new RuntimeException("Failed to read points from JSON file", e);
        }
    }
}

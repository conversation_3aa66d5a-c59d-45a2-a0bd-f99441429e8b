package com.jlr.ecp.order.kafka;


import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class RollbackSuccessFulfilmentMessage implements Serializable {

    /**
     * 消息ID，具有唯一性
     */
    private String messageId;

    /**
     * VCS订单号
     */
    private String vcsOrderCode;

    /**
     * 退款订单号;执行退款操作后的退单订单号
     */
    private String refundOrderCode;

    /**
     * 服务请求结束时间;服务回滚的请求结束时间
     */
    private LocalDateTime serviceEndDate;

    /**
     * 租户号
     */
    private Long tenantId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 服务履约类型：1：远程车控Remote Service； 2：PIVI Subscription Service；
     * @link CartItemTypeEnum
     * */
    private Integer serviceType;
}
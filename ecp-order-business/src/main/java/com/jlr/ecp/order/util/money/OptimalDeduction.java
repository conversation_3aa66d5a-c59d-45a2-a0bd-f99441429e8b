package com.jlr.ecp.order.util.money;

import cn.hutool.core.collection.CollUtil;
import lombok.Getter;

import java.util.List;


/**
 *  A商品：`600售价 240积分价 需要积分600 每个积分抵扣效率360/600=0.6000
 * `B商品：`420售价 400积分价 需要积分180 每个积分抵扣效率20/180=0.11000
 * `C商品：`1000售价 500积分价 需要积分1000 每个积分抵扣效率500/1000=0.5`
 *
 * 有1000积分，按最优积分抵扣效率去命中，只会命中A商品和B商品 优惠360+30元
 * 但命中C商品优惠 500元，算法是否有漏洞？
 */
public class OptimalDeduction {
    @Getter
    static class Product {
        int requiredPoints; // 所需积分
        int deduction; // 抵扣金额（售价 - 积分价）

        Product(int requiredPoints, int deduction) {
            this.requiredPoints = requiredPoints;
            this.deduction = deduction;
        }
    }

    public static void main(String[] args) {
        List<Product> products = List.of(
            new Product(600, 360), // A
            new Product(180, 20),  // B
            new Product(1000, 500) // C
        );
        int availablePoints = 1000;
        System.out.println("最大抵扣金额：" + maxDeduction(availablePoints, products)); // 输出 500

        //方法二
        int maxDeduction = calculateMaxDeduction(availablePoints, products);
        System.out.println("最大抵扣金额：" + maxDeduction); // 输出 500
    }

    public static int maxDeduction(int points, List<Product> products) {
        int n = products.size();
        int[][] dp = new int[n + 1][points + 1];

        for (int i = 1; i <= n; i++) {
            Product p = products.get(i - 1);
            for (int j = 0; j <= points; j++) {
                dp[i][j] = dp[i - 1][j];
                if (j >= p.requiredPoints) {
                    dp[i][j] = Math.max(dp[i][j], dp[i - 1][j - p.requiredPoints] + p.deduction);
                }
            }
        }
        return dp[n][points];
    }

    /**
     * 计算最大积分抵扣金额（一维动态规划实现）
     *
     * @param availablePoints 可用积分
     * @param products        商品列表（需包含 requiredPoints 和 deduction）
     * @return 最大抵扣金额
     */
    public static int calculateMaxDeduction(int availablePoints, List<Product> products) {
        if (availablePoints <= 0 || CollUtil.isEmpty(products)) {
            return 0;
        }

        int[] dp = new int[availablePoints + 1];
        for (Product product : products) {
            for (int j = availablePoints; j >= product.getRequiredPoints(); j--) {
                dp[j] = Math.max(dp[j], dp[j - product.getRequiredPoints()] + product.getDeduction());
            }
        }
        return dp[availablePoints];
    }
}
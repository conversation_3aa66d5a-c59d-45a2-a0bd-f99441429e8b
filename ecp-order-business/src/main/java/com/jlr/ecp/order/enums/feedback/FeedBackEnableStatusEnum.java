package com.jlr.ecp.order.enums.feedback;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum FeedBackEnableStatusEnum {

    /**
     * 停用
     */
    DISABLE(0, "已停用"),
    /**
     * 待启用
     */
    WAIT(1, "待启用"),

    /**
     * 启用
     */
    ENABLE(2, "已启用");



    /**
     * 商品启停状态编码
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;


    public static String fromCode(Integer code) {
        for (FeedBackEnableStatusEnum value : FeedBackEnableStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

}

package com.jlr.ecp.order.controller.admin.refund;

import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.api.order.vo.OrderStatusVO;
import com.jlr.ecp.order.api.order.vo.RefundReasonStatusVO;
import com.jlr.ecp.order.api.refund.dto.*;
import com.jlr.ecp.order.api.refund.vo.OrderRefundDetailVO;
import com.jlr.ecp.order.api.refund.vo.OrderRefundPageRespNewVO;
import com.jlr.ecp.order.api.refund.vo.OrderRefundPageVO;
import com.jlr.ecp.order.api.refund.vo.RefundDetailRespVO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.enums.ApiConstants;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.order.CouponRefundReasonEnum;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import com.jlr.ecp.order.kafka.OrderRefundSuccessMessage;
import com.jlr.ecp.order.service.refund.OrderRefundDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.constraints.NotBlank;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * 订单
 *
 * <AUTHOR>
 */
@Tag(name = "后台管理端 - 退单管理")
@RestController
@RequestMapping("v1/refund")
@Validated
public class OrderRefundController {
    @Resource
    private OrderRefundDOService orderRefundService;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private ProducerTool producerTool;

    /**
     * 退单列表
     *
     * @param dto 分页参数
     * @return OrderRefundPageVO
     */
    @GetMapping("/page")
    @Operation(summary = "订单列表列表、分页")
    @PreAuthorize("@ss.hasPermission('trade:chargeback:list')")
    CommonResult<PageResult<OrderRefundPageVO>> page(@SpringQueryMap @Validated OrderRefundPageReqDTO dto) {
        // 为了兼容前端传入的大小写，这里统一转换为小写
        dto.validateParameters();
        if (dto.getCreatedTimeSort() != null) {
            dto.setCreatedTimeSort(dto.getCreatedTimeSort().toLowerCase(Locale.ROOT));
        }

        PageResult<OrderRefundPageVO> pageResult = orderRefundService.getPage(dto);
        return CommonResult.success(pageResult);
    }

    /**
     * 订单详情
     *
     * @param orderRefundCode 订单编号
     * @return RefundDetailRespVO
     */
    @GetMapping("/detail")
    @Operation(summary = "退单详情")
    @Parameter(name = "orderRefundCode", description = "退单编码", required = true)
    @PreAuthorize("@ss.hasPermission('trade:chargeback:detail')")
    CommonResult<RefundDetailRespVO> getOrderDetail(@RequestParam("orderRefundCode") @NotBlank(message = "退单编码不能为空") String orderRefundCode) {

        RefundDetailRespVO refundDetailRespVO = orderRefundService.getOrderRefundDetail(orderRefundCode);
        return CommonResult.success(refundDetailRespVO);
    }

    /**
     * 取消订单申请
     * @param  refundApplyDTO refundApplyDTO
     * @return String
     */
    @PostMapping("/apply")
    @Operation(summary = "提交取消订单申请")
    @PreAuthorize("@ss.hasPermission('trade:order:detail')")
    CommonResult<String> applyOrderRefund(@Validated @RequestBody OrderRefundApplyDTO refundApplyDTO) {
        Boolean success = orderRefundService.applyOrderRefund(refundApplyDTO);
        if (success) {
            return CommonResult.success(Constants.REFUND_APPLY_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.REFUND_APPLY_FAIL);
    }



    /**
     * 取消订单申请校验接口
     * @param  refundCheckDTO
     * @return String
     */
    @PostMapping("/checkVcsOrderCancel")
    @Operation(summary = "取消订单申请校验接口")
    @PreAuthorize("@ss.hasPermission('trade:order:detail')")
    CommonResult<String> checkVcsOrderCancel(@Validated @RequestBody OrderRefundCheckDTO refundCheckDTO) {
        Boolean success = orderRefundService.checkOrderRefund(refundCheckDTO);
        if (success) {
            return CommonResult.success(Constants.REFUND_APPLY_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.REFUND_APPLY_FAIL);
    }


    @PostMapping("/approve")
    @Operation(summary = "取消订单审批")
    @PreAuthorize("@ss.hasPermission('trade:chargeback:cancel-approval')")
    CommonResult<String> orderRefundApprove(@Validated @RequestBody OrderRefundApproveDTO refundApproveDTO) {
        Boolean success = orderRefundService.orderRefundApprove(refundApproveDTO);
        if (success) {
            return CommonResult.success(Constants.REFUND_APPROVE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.REFUND_APPROVE_FAIL);
    }

    @GetMapping("/getRefundStatusList")
    @Operation(summary = "退单状态下拉列表接口")
    @PermitAll
    CommonResult<List<OrderStatusVO>> getOrderStatusList() {
        List<OrderStatusVO> orderStatusList = Arrays.stream(OrderRefundStatusEnum.values())
                .map(status -> {
                    OrderStatusVO vo = new OrderStatusVO();
                    vo.setStatus(status.getCode());
                    vo.setText(status.getDescription());
                    return vo;
                })
                .collect(Collectors.toList());
        return CommonResult.success(orderStatusList);
    }


    @GetMapping("/getCount")
    @Operation(summary = "待办数量")
    @PreAuthorize("@ss.hasPermission('trade:chargeback:cancel-approval')")
    CommonResult<Integer> getCount() {
        Integer success = orderRefundService.getCount();
        return CommonResult.success(success);
    }

    @GetMapping("/refundPaymentCallback")
    @Operation(summary = "退单支付回调")
    @PermitAll
    CommonResult<Integer> refundPaymentCallback(@RequestParam("orderRefundCode") @NotBlank(message = "退单编码不能为空") String orderRefundCode) {
        Integer success = orderRefundService.paymentCallbackProcess(orderRefundCode);
        return CommonResult.success(success);
    }

    @GetMapping("/refundTransPaymentCallback")
    @Operation(summary = "分账退单支付回调")
    @PermitAll
    CommonResult<Integer> refundTransPaymentCallback(@RequestParam("orderRefundCode") @NotBlank(message = "退单编码不能为空") String orderRefundCode) {
        Integer success = orderRefundService.transPaymentCallbackProcess(orderRefundCode);
        return CommonResult.success(success);
    }


    @GetMapping("/tsdpMock")
    @Operation(summary = "tsdp模拟回调接口")
    @PermitAll
    @Parameter(name = "orderRefundCode", description = "退单编码", required = true)
    CommonResult<Integer> tsdpMock(@RequestParam("orderRefundCode") @NotBlank(message = "退单编码不能为空") String orderRefundCode) {
        Integer success = orderRefundService.tsdpRefundCallBack(orderRefundCode,true);
        return CommonResult.success(success);
    }


    /**
     * 订单详情
     */
    @GetMapping("/sendTest")
    @Operation(summary = "订单详情")
    @PermitAll
    CommonResult<Boolean> getOrderDetail() {
        OrderRefundSuccessMessage orderRefundSuccessMessage = new OrderRefundSuccessMessage();
        orderRefundSuccessMessage.setOrderNumber("RLRM00120240119140925001XXXXXX001");
        orderRefundSuccessMessage.setServiceName(ApiConstants.NAME);
        orderRefundSuccessMessage.setPhoneNumber("13983218357");
        orderRefundSuccessMessage.setTaskCode("order-cancel-code");
        orderRefundSuccessMessage.setMessageId(ecpIdUtil.nextIdStr());
        orderRefundSuccessMessage.setTenantId(TenantContextHolder.getTenantId());
        producerTool.sendMsg(KafkaConstants.ORDER_CANCEL_TOPIC,"", JSON.toJSONString(orderRefundSuccessMessage));
        return CommonResult.success(true);
    }

    @GetMapping("/getRefundReasonList")
    @Operation(summary = "获取所有退款原因选项")
    @PermitAll
    CommonResult<List<RefundReasonStatusVO>> getRefundReasonList() {
        List<RefundReasonStatusVO>  orderStatusList= Arrays.stream(CouponRefundReasonEnum.values())
                .map(e -> new RefundReasonStatusVO(e.getCode(), e.getName()))
                .filter(e -> e.getStatus() != CouponRefundReasonEnum.CONSULTATION_AGREED.getCode()
                        && e.getStatus() != CouponRefundReasonEnum.AUTO_REFUND_OVERDUE.getCode() )
                .collect(Collectors.toList());
        return CommonResult.success(orderStatusList);
    }


//    @GetMapping("/paymentMock")
//    @Operation(summary = "支付流转Mock")
//    @Parameter(name = "orderCode", description = "订单编码", required = true)
//    @PermitAll
//    CommonResult<Integer> paymentMock(@RequestParam("orderCode") @NotBlank(message = "订单编码不能为空") String orderCode) {
//
//        return CommonResult.success(orderRefundService.paymentMock(orderCode));
//    }

    @GetMapping("/orderRefundPageNew")
    @Operation(summary = "退单列表、分页")
    @PreAuthorize("@ss.hasAnyPermissions('trade:lrechargeback:list','trade:bgchargeback:list')")
    CommonResult<PageResult<OrderRefundPageRespNewVO>> orderRefundPageNew(@SpringQueryMap @Validated OrderRefundPageReqNewDTO dto) {
        // 为了兼容前端传入的大小写，这里统一转换为小写
        dto.validateParameters();
        if (dto.getCreatedTimeSort() != null) {
            dto.setCreatedTimeSort(dto.getCreatedTimeSort().toLowerCase(Locale.ROOT));
        }

        PageResult<OrderRefundPageRespNewVO> pageResult = orderRefundService.orderRefundPageNew(dto);
        return CommonResult.success(pageResult);
    }

    /**
     * 编辑退单信息
     */
    @PostMapping("/editOrderRefund")
    @Operation(summary = "编辑退单信息")
    @PreAuthorize("@ss.hasAnyPermissions('trade:lrechargeback:edit', 'trade:bgchargeback:edit')")
    CommonResult<Boolean> editOrderRefund(@Validated @RequestBody OrderRefundEditDTO orderRefundEditDTO) {
        return CommonResult.success(orderRefundService.editOrderRefund(orderRefundEditDTO));
    }

    /**
     * 退单详情
     */
    @GetMapping("/getOrderRefundDetail")
    @Operation(summary = "退单详情")
    @Parameter(name = "orderRefundCode", description = "退单编码", required = true)
    @PreAuthorize("@ss.hasAnyPermissions('trade:lrechargeback:detail', 'trade:bgchargeback:detail')")
    CommonResult<OrderRefundDetailVO> getOrderRefundDetailNew(@RequestParam("orderRefundCode") @NotBlank(message = "退单编码不能为空") String orderRefundCode) {
        return CommonResult.success(orderRefundService.getOrderRefundDetailNew(orderRefundCode));
    }


}

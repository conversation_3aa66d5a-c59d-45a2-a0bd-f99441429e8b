package com.jlr.ecp.order.enums.payment;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Optional;

/**
 * 支付类型枚举
 *
 * <AUTHOR>
 * @date 2024/12/02
 */
@Getter
@RequiredArgsConstructor
@Schema(description = "明细类型枚举类")
public enum PayTypeEnum {

    @Schema(description = "现金")
    CASH("CASH", "现金"),

    @Schema(description = "积分")
    POINTS("POINTS", "积分"),

    @Schema(description = "优惠券")
    COUPON("COUPON", "优惠券"),

    @Schema(description = "组合支付")
    COMBINE("COMBINE", "组合支付");

    @EnumValue
    private final String code;

    private final String desc;

    /**
     * 根据代码获取枚举实例
     *
     * @param code 代码
     * @return 枚举实例
     * @throws IllegalArgumentException 如果代码不存在
     */
    public static PayTypeEnum fromCode(String code) {
        return Optional.ofNullable(code)
                .filter(StrUtil::isNotBlank)
                .map(object -> Arrays.stream(PayTypeEnum.values())
                        .filter(item -> item.getCode()
                                .equals(object)).findAny()
                        .orElseThrow(() -> new IllegalArgumentException("Invalid DetailType code: " + code)))
                .orElse(null);
    }
}

package com.jlr.ecp.order.kafka;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 支付成功后券发放消息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderECouponSuccessfulMessage {
    /**
     * 消息id，具有唯一性
     */
    private String messageId;

    /**
     * 租户号
     */
    private Long tenantId;

    /**
     * 用户编码
     */
    private String consumerCode;

    /**
     * 订单编号
     * requestId
     * 请求卡券发放的唯一业务单据号
     */
    private String orderCode;

    /**
     * 订单明细列表
     */
    private List<OrderItem> orderItems;

    @Data
    public static class OrderItem {
        private String orderItemCode;
        private String couponModelCode;
        private Integer productQuantity;
    }

}

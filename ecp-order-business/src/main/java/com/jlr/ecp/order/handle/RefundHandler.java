package com.jlr.ecp.order.handle;

import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.order.api.order.vo.detail.ProductItemInfoAppVO;
import com.jlr.ecp.order.api.refund.dto.BaseOrderRefundApplyDTO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.controller.app.refund.vo.BaseRefundItemDetailVO;
import com.jlr.ecp.order.controller.app.refund.vo.RefundDetailLogVO;
import com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentDO;
import com.jlr.ecp.order.dal.dataobject.order.*;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundAttachmentDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundPaymentRecordsDO;
import com.jlr.ecp.order.dal.mysql.independent.OrderIndependentDOMapper;
import com.jlr.ecp.order.dal.mysql.order.*;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundAttachmentDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundPaymentRecordsMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.independent.OrderIndependentStatusEnum;
import com.jlr.ecp.order.enums.order.*;
import com.jlr.ecp.order.enums.payment.PayCenterBizLineCodeEnum;
import com.jlr.ecp.order.enums.payment.PayTypeEnum;
import com.jlr.ecp.order.enums.payment.RefundTradeStatus;
import com.jlr.ecp.order.enums.phone.PhoneNumberDecodeUtil;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import com.jlr.ecp.order.enums.refund.RefundLogisticsStatusEnum;
import com.jlr.ecp.order.enums.refund.RefundOrderTypeEnum;
import com.jlr.ecp.order.enums.sms.ShortLinkPathEnum;
import com.jlr.ecp.order.kafka.BaseOrderRefundSuccessMessage;
import com.jlr.ecp.order.kafka.producer.BrandedGoodsNotificationProducer;
import com.jlr.ecp.order.service.order.handler.ShortLinkHandler;
import com.jlr.ecp.order.util.OrderTitleUtil;
import com.jlr.ecp.order.util.RedisReentrantLockUtil;
import com.jlr.ecp.order.util.common.AttributeUtil;
import com.jlr.ecp.order.util.machine.EcouponOrderRefundStatusMachine;
import com.jlr.ecp.order.util.machine.LogisticsOrderRefundStatusMachine;
import com.jlr.ecp.order.util.money.MoneyUtil;
import com.jlr.ecp.order.util.refund.CodeGenerator;
import com.jlr.ecp.payment.api.invoice.InvoiceApiV2;
import com.jlr.ecp.payment.api.invoice.vo.InvoiceDetailVO;
import com.jlr.ecp.payment.api.order.PayCenterOrderApi;
import com.jlr.ecp.payment.api.order.dto.PayTransRefundReqDTO;
import com.jlr.ecp.payment.api.order.vo.OrderItemsVo;
import com.jlr.ecp.payment.api.order.vo.SubmitRefundOrderResp;
import com.jlr.ecp.payment.api.order.vo.SubmitTransRefundOrderResp;
import com.jlr.ecp.payment.api.order.vo.TransRefundItemVO;
import com.jlr.ecp.payment.api.refund.dto.PayRefundCreateReqDTO;
import com.jlr.ecp.product.api.product.ProductApi;
import com.jlr.ecp.virtual.api.coupon.CouponApi;
import com.jlr.ecp.virtual.dto.coupon.command.CouponCancelCmd;
import com.jlr.ecp.virtual.dto.coupon.response.CouponCancelResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 退款业务处理类
 * create by Maojie on 2025/03/08
 */
@Component
@Slf4j
public class RefundHandler{

    @Resource
    private OrderInfoDOMapper orderInfoMapper;
    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Resource
    private InvoiceApiV2 invoiceApiV2;
    @Resource
    private CouponApi couponApi;
    @Resource
    private OrderRefundDOMapper orderRefundMapper;
    @Resource
    private OrderModifyDetailLogDOMapper modifyDetailLogMapper;
    @Resource
    private OrderPaymentRecordsMapper orderPaymentRecordsMapper;
    @Resource
    private OrderRefundPaymentRecordsMapper orderRefundPaymentRecordsMapper;
    @Resource
    private PayCenterOrderApi payOrderApi;
    @Resource
    private EcouponOrderRefundStatusMachine ecouponOrderRefundStatusMachine;
    @Resource
    private OrderRefundAttachmentDOMapper attachmentMapper;
    @Resource
    private OrderRefundItemDOMapper orderRefundItemDOMapper;
    @Resource
    private OrderDiscountDetailDOMapper orderDiscountDetailDOMapper;
    @Resource
    private PhoneNumberDecodeUtil phoneNumberDecodeUtil;
    @Value("${taskCode.order.ecoupon.refund:order-ecoupon-refund-code}")
    private String taskCode;
    @Resource
    private ShortLinkHandler shortLinkHandler;
    @Resource
    Snowflake ecpIdUtil;
    @Resource
    private ProducerTool producerTool;
    @Resource
    private OrderCouponDetailDOMapper orderCouponDetailDOMapper;
    @Resource
    private OrderIndependentDOMapper orderIndependentDOMapper;

    @Resource
    private RedisReentrantLockUtil redisReentrantLockUtil;

    @Resource
    private Redisson redisson;

    @Resource
    private OrderStatusLogDOMapper statusLogMapper;

    @Resource
    private ProductApi productApi;

    @Resource
    private OrderRefundAttachmentDOMapper orderRefundAttachmentDOMapper;

    @Resource
    private LogisticsOrderRefundStatusMachine logisticsOrderRefundStatusMachine;

    @Resource
    private BrandedGoodsNotificationProducer notificationProducer;

    @Resource
    private OrderItemLogisticsDOMapper orderItemLogisticsDOMapper;

    /**
     * 电子券退款
     * @param refundApplyDTOs
     * @param operationType  "操作类型 1-用户发起 2-系统自动 3-运营发起" RefundOrderOperationTypeEnum
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    public String refundProcess(List<BaseOrderRefundApplyDTO> refundApplyDTOs, Integer operationType) {
        if(CollectionUtils.isEmpty(refundApplyDTOs)){
            log.warn("退款申请  refundProcess - 退款申请列表为空");
            return "";
        }
        log.info("退款申请  refundProcess - 开始处理电子券退款请求, 操作类型: {}, 退款申请数量: {}",
                operationType,refundApplyDTOs.size());
        if(RefundOrderOperationTypeEnum.getByCode(operationType) ==null){
            log.error("退款申请  refundProcess - 操作类型为空或无效: {}", operationType);
            throw exception(ErrorCodeConstants.OPERATION_TYPE_CAN_NOT_NULL);
        }
        String refundOrderCode = "";
        RefundProcessResult result = new RefundProcessResult();
        for (BaseOrderRefundApplyDTO refundApplyDTO : refundApplyDTOs){
            log.info("退款申请  refundProcess - 处理单个退款申请, 原始订单号: {}, 订单项编号: {}",
                    refundApplyDTO.getOriginOrderCode(), refundApplyDTO.getOrderItemCode());
            //1.1 加锁 防止重复提交
            String idempotentKey = getIdempotentKey(operationType, refundApplyDTO);
            RLock rLock = redisson.getLock(idempotentKey);
            try {
                log.info("退款申请  refundProcess -加锁 防止重复提交, RLock: {}", rLock);
                if (!redisReentrantLockUtil.tryLock(rLock, 10, 60, TimeUnit.SECONDS)) {
                    log.warn("退款申请  refundProcess - 重复提交, RLock: {}", rLock);
                    throw exception(ErrorCodeConstants.REPEAT_SUBMISSION);
                }
                // 创建退款单及相关数据（数据库操作部分）
                result = createRefundEntity(refundApplyDTO,operationType);
                // 外部服务调用
                processExternalServices(result);
                if(StringUtils.isBlank(refundOrderCode)){
                    refundOrderCode = result.getOrderRefundDO().getRefundOrderCode();
                }
            } catch (Exception e) {
                log.error("退款申请  refundProcess - 处理退款异常: {}", e.getMessage(), e);
                throw e; // 重新抛出异常以确保事务回滚
            }finally {
                //释放redis锁
                redisReentrantLockUtil.unlock(rLock, 3);
            }
        }
        sendMessage(refundApplyDTOs, result);
        log.info("退款申请  refundProcess - 退款处理完成");
        return refundOrderCode;
    }

    /**
     * 发送退款申请成功消息
     * @param refundApplyDTOs
     * @param result
     */
    private void sendMessage(List<BaseOrderRefundApplyDTO> refundApplyDTOs, RefundProcessResult result) {
        //这里catch异常 确保事务不会回滚
        try {
            //发送短信通知
            Integer fulfilmentType = refundApplyDTOs.get(0).getRefundFulfilmentType();
            BaseOrderRefundSuccessMessage baseOrderRefundSuccessMessage = buildBaseOrderRefundSuccessMessage(result.getOrderInfoDO(), result.getOrderRefundDO(), result.getOrderItemDO());
            if(fulfilmentType.equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())){
                sendRefundSuccessMessageByKafka(baseOrderRefundSuccessMessage);
            }else if(fulfilmentType.equals(OrderTypeEnum.BRAND_GOOD.getCode())){
                notificationProducer.sendRefundOnlyNotification(baseOrderRefundSuccessMessage.getBgorderNumber(),baseOrderRefundSuccessMessage.getPhoneNumber(), baseOrderRefundSuccessMessage.getWxUrl());
            }
            log.info("退款申请  refundProcess - 发送短信通知成功");
        }catch (Exception e){
            log.info("退款申请  refundProcess - 发送短信通知异常: {}", e.getMessage(), e);
        }
    }

    private  String getIdempotentKey(Integer operationType, BaseOrderRefundApplyDTO refundApplyDTO) {
        String idempotentKey = "";
        Integer fulfilmentType = refundApplyDTO.getRefundFulfilmentType();
        if(fulfilmentType.equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())){
            idempotentKey =Constants.LRE_REFUND_IDEMPOTENT_KEY + operationType +":"+ refundApplyDTO.getOrderItemCode();
        }else if(fulfilmentType.equals(OrderTypeEnum.BRAND_GOOD.getCode())){
            idempotentKey =Constants.BG_REFUND_IDEMPOTENT_KEY + operationType +":"+ refundApplyDTO.getOrderItemCode();
        }
        log.info("退款申请  refundProcess - 获取幂等key: {}", idempotentKey);
        return idempotentKey;
    }


    /**
     * 发起bg退货退款申请
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    public String returnProcess(List<BaseOrderRefundApplyDTO> refundApplyDTOs, Integer operationType) {
        log.info("退货退款申请  returnProcess - 开始处理退货退款请求, 操作类型: {}, 退货退款申请数量: {}",
                operationType, refundApplyDTOs != null ? refundApplyDTOs.size() : 0);
        if(CollectionUtils.isEmpty(refundApplyDTOs)){
            log.warn("退货退款申请  returnProcess - 退货退款申请列表为空");
            return "";
        }
        if(operationType==null || RefundOrderOperationTypeEnum.getByCode(operationType) ==null){
            log.error("退货退款申请  returnProcess - 操作类型为空或无效: {}", operationType);
            throw exception(ErrorCodeConstants.OPERATION_TYPE_CAN_NOT_NULL);
        }
        String refundOrderCode = "";
        RefundProcessResult result = new RefundProcessResult();
        for (BaseOrderRefundApplyDTO refundApplyDTO : refundApplyDTOs){
            log.info("退货退款申请  returnProcess - 处理单个退货退款申请, 原始订单号: {}, 订单项编号: {}",
                    refundApplyDTO.getOriginOrderCode(), refundApplyDTO.getOrderItemCode());
            //1.1 加锁 防止重复提交
            String idempotentKey = Constants.BG_REFUND_IDEMPOTENT_KEY +operationType+":"+refundApplyDTO.getOrderItemCode();
            RLock rLock = redisson.getLock(idempotentKey);
            try {
                log.info("退货退款申请  returnProcess -加锁 防止重复提交, RLock: {}", rLock);
                if (!redisReentrantLockUtil.tryLock(rLock, 10, 60, TimeUnit.SECONDS)) {
                    log.warn("退货退款申请  returnProcess - 重复提交, RLock: {}", rLock);
                    throw exception(ErrorCodeConstants.REPEAT_SUBMISSION);
                }
                // 创建退款单及相关数据（数据库操作部分）
                result = createRefundEntity(refundApplyDTO,operationType);
                if(StringUtils.isBlank(refundOrderCode)){
                    refundOrderCode = result.getOrderRefundDO().getRefundOrderCode();
                }
                int event = LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_APPLY.getCode();
                logisticsOrderRefundStatusMachine.changeOrderStatus(event,result.getOrderInfoDO(), result.getOrderRefundDO(), result.getOrderItemDO());
            } catch (Exception e) {
                log.error("退货退款申请  returnProcess - 处理退款异常: {}", e.getMessage(), e);
                throw e; // 重新抛出异常以确保事务回滚
            }finally {
                //释放redis锁
                redisReentrantLockUtil.unlock(rLock, 3);
            }
        }
        log.info("退货退款申请  refundProcess - 退货退款处理完成");
        return refundOrderCode;
    }

    /**
     * 校验及创建退款实体
     * @param refundApplyDTO
     * @param operationType
     * @return RefundProcessResult
     */
    private  RefundProcessResult createRefundEntity(BaseOrderRefundApplyDTO refundApplyDTO, Integer operationType){
        //1.校验订单相关信息并计算出退款金额与退款积分
        OrderInfoResult orderInfoResult = validOrderInfo(refundApplyDTO,operationType);
        //2.创建退货单：(多张表修改，需要开启事务)
        RefundOrderInfoResult refundOrderInfoResult = createRefundOrderInfoResult(orderInfoResult,refundApplyDTO,operationType);
        RefundProcessResult result = new RefundProcessResult(orderInfoResult.getOrderInfoDO(),refundOrderInfoResult.getOrderRefundDO(),orderInfoResult.getOrderItemDO(),refundOrderInfoResult.getRefundItemDO(),refundOrderInfoResult.getRefundCoupons(),orderInfoResult.getOrderPaymentRecordsDO());
        //3.记录订单操作日志 t_order_modify_log
        log.info("退款申请  refundProcess - 创建订单修改日志, 订单号: {}", orderInfoResult.getOrderInfoDO().getOrderCode());
        modifyDetailLogMapper.createModifyLog(orderInfoResult.getOrderInfoDO().getOrderCode(), OrderModifyLogEnum.REFUND_APPLY.getDescription(),null, null);

        return result;
    }


    private OrderInfoResult validOrderInfo(BaseOrderRefundApplyDTO refundApplyDTO, Integer operationType){
        OrderInfoResult orderInfoResult = new OrderInfoResult();
        //1.2数据真实性校验：订单是否存在
        OrderInfoDO orderInfoDO = getOrderInfoDO(refundApplyDTO.getOriginOrderCode());
        OrderItemDO orderItemDO = getOrderItemDo(refundApplyDTO.getOriginOrderCode(),refundApplyDTO.getOrderItemCode());
        //支付记录检查
        OrderPaymentRecordsDO orderPaymentRecordsDO = orderPaymentRecordsMapper.queryByOrderCode(orderInfoDO.getOrderCode(),orderInfoDO.getParentOrderCode());
        if (orderPaymentRecordsDO == null) {
            log.error("退款申请  refundProcess - 关联的支付记录不存在, 订单号: {}", orderInfoDO.getOrderCode());
            throw exception(ErrorCodeConstants.ORDER_PAYMENT_NOT_EXIST);
        }

        //如果没有客服填写的退款金额则通过计算得出真实退款金额
        //查询出已退款金额,计算还能退款的余额 如果refundMoney为null 则是全退的情况,要计算出剩余可退金额
        Integer fulfilmentType = refundApplyDTO.getRefundFulfilmentType();
        int remainRefundMoney = 0;
        if(fulfilmentType.equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())){
            //1.3 订单相关状态检查
            orderInfoStatusCheck(orderInfoDO,orderItemDO,operationType);
            remainRefundMoney =calculateMaxRefundMoney(orderItemDO,operationType);
        }else if(fulfilmentType.equals(OrderTypeEnum.BRAND_GOOD.getCode())){
            bgOrderInfoStatusCheck(orderInfoDO,orderItemDO,operationType,refundApplyDTO.getRefundOrderType());
            remainRefundMoney = calculateLogisticsMaxRefundMoney(orderItemDO,null);
        }
        //没有传入退款金额则使用计算出来的金额
        int realRefundMoney;
        if(RefundOrderOperationTypeEnum.OPERATION_INITIATED.getCode()==operationType){//运营发起则直接取订单项里面的金额
            if(refundApplyDTO.getRefundMoneyAmount() ==null ||MoneyUtil.convertToCents(refundApplyDTO.getRefundMoneyAmount()).setScale(0, RoundingMode.FLOOR).intValueExact() < 0){
                throw exception(ErrorCodeConstants.INVALID_REFUND_AMOUNT);
            }
            realRefundMoney =MoneyUtil.convertToCents(refundApplyDTO.getRefundMoneyAmount()).setScale(0, RoundingMode.FLOOR).intValueExact();
        }else{//其他情况是全退 直接取剩余金额
            realRefundMoney=remainRefundMoney;
        }
        log.info("退款申请  refundProcess - 计算可退款金额, 申请退款金额: {}, 计算可退款金额: {}",
                realRefundMoney, remainRefundMoney);

        int remainRefundPoint = 0;
        if(fulfilmentType.equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())){
            remainRefundPoint = calculateRefundPoints(orderItemDO,remainRefundMoney,realRefundMoney);
        }else if(fulfilmentType.equals(OrderTypeEnum.BRAND_GOOD.getCode())){
            remainRefundPoint = calculateLogisticsRefundPoints(orderItemDO,remainRefundMoney,realRefundMoney);
        }
        log.info("退款申请  refundProcess - 计算可退积分, 可退积分: {}", remainRefundPoint);

        //1.4 最大可退金额校验：校验计算。
        checkRefundAmount(realRefundMoney,orderItemDO,remainRefundMoney);

        //1.5 检查专票类型发票的状态(如果发票未开或者发票状态为已红冲才可以申请退款)
        validInoviceStatus(refundApplyDTO.getOriginOrderCode());
        orderInfoResult.setOrderInfoDO(orderInfoDO);
        orderInfoResult.setOrderItemDO(orderItemDO);
        orderInfoResult.setRealRefundMoney(realRefundMoney);
        orderInfoResult.setRefundPoints(remainRefundPoint);
        orderInfoResult.setOrderPaymentRecordsDO(orderPaymentRecordsDO);
        return orderInfoResult;
    }

    /**
     * 检查专票的状态
     * @param orderCode
     */
    public void validInoviceStatus(String orderCode) {
        log.info("退款申请  refundProcess - 检查发票状态, 订单号: {}", orderCode);
        CommonResult<InvoiceDetailVO> detailVOCommonResult = invoiceApiV2.getInvoiceDetail(orderCode);
        Boolean invoiceCheck = checkInvoiceStatus(detailVOCommonResult);
        log.info("退款申请  refundProcess - 检查发票状态, 结果: {}", invoiceCheck);
        if(!invoiceCheck){
            log.error("退款申请  refundProcess - 发票状态检查失败, 订单号: {}", orderCode);
            throw exception(ErrorCodeConstants.INVOICE_CHECK_FAILED);
        }
    }

    private void bgOrderInfoStatusCheck(OrderInfoDO orderInfoDO,OrderItemDO orderItemDO,Integer operationType, Integer orderRefundType){
        Boolean isOperationFlag = operationType.equals(RefundOrderOperationTypeEnum.OPERATION_INITIATED.getCode());
        if (orderInfoDO == null || orderItemDO == null) {
            log.error("退货退款申请  returnProcess - 订单不存在, 订单号: {}", orderInfoDO.getOrderCode());
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        if(OrderIndependentStatusEnum.ING.getStatus().equals(orderInfoDO.getIndependentStatus())){
            log.error("退货退款申请  returnProcess - 订单正在分账中, 订单号: {}", orderInfoDO.getOrderCode());
            throw exception(ErrorCodeConstants.ORDER_INDEPENDENT_PROCESSING);
        }
        if(OrderItemAftersalesStatusEnum.PROCESSING.getCode().equals(orderItemDO.getAftersalesStatus())){
            log.info("退货退款申请  returnProcess -该订单行:{}已经在售后中",orderItemDO.getOrderItemCode());
            throw exception(ErrorCodeConstants.ORDER_ITEM_REFUND_RPOCESSING);
        }
        //订单行 用户发起仅退款 orderItemDo的状态必须为待发货
        log.info("退货退款申请  returnProcess - 检查订单项状态, 当前状态: {}", orderItemDO.getItemStatus());
        if(OrderItemLogisticsStatusEnum.PENDING_SHIPMENT.getCode()!=orderItemDO.getItemStatus()
                &&!isOperationFlag&&orderRefundType.equals(RefundOrderTypeEnum.REFUND_ONLY.getCode())){
            log.error("退货退款申请  returnProcess - 仅退款订单行状态不是待发货, 当前状态: {}", orderItemDO.getItemStatus());
            throw exception(ErrorCodeConstants.ORDER_STATUS_IS_PENDING_SHIPMENT);
        }
        //用户发起退货退款 状态不能为待发货
        if(OrderItemLogisticsStatusEnum.PENDING_SHIPMENT.getCode().equals(orderItemDO.getItemStatus())
        &&!isOperationFlag && orderRefundType.equals(RefundOrderTypeEnum.REFUND_RETURN_GOODS.getCode())){
            log.error("退货退款申请  returnProcess - 用户发起退货退款时的订单行状态为待发货, 当前状态: {}", RefundOrderTypeEnum.getByCode(orderRefundType).getName());
            throw exception(ErrorCodeConstants.ORDER_STATUS_IS_PENDING_SHIPMENT);
        }
        //如果是运营人员发起退款订单行状态 必须是待发货(运营)
        if(isOperationFlag
                && !orderRefundType.equals(RefundOrderTypeEnum.REFUND_ONLY.getCode())){
            log.info("退货退款申请  returnProcess - 运营人员发起退款 只能是仅退款 :{}", RefundOrderTypeEnum.getByCode(orderRefundType).getName());
            throw exception(ErrorCodeConstants.OPERATION_SUBMIT_NOT_REFUND_ONLY);
        }
        //订单状态校验：订单当前状态能否能够申请退款（订单状态:订单完成或者已支付或者已关闭或者部分取消的订单 ，支付状态：已支付）
        log.info("退货退款申请  returnProcess - 检查订单状态, 订单状态: {}, 支付状态: {}",
                orderInfoDO.getOrderStatus(), orderInfoDO.getPaymentStatus());
        if(!orderInfoDO.getPaymentStatus().equals(PaymentStatusEnum.PAID.getCode())){
            log.error("退货退款申请  returnProcess - 订单支付状态不允许退款, 订单支付状态: {}, 支付状态: {}",
                    orderInfoDO.getCouponStatus(), orderInfoDO.getPaymentStatus());
            throw exception(ErrorCodeConstants.REFUND_ORDER_STATUS_ERROR);
        }
    }

    private void orderInfoStatusCheck(OrderInfoDO orderInfoDO,OrderItemDO orderItemDO,Integer operationType){
        Boolean isOperationFlag = operationType.equals(RefundOrderOperationTypeEnum.OPERATION_INITIATED.getCode());
        if (orderInfoDO == null || orderItemDO == null) {
            log.error("退款申请  refundProcess - 订单不存在, 订单号: {}", orderInfoDO.getOrderCode());
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        if(OrderIndependentStatusEnum.ING.getStatus().equals(orderInfoDO.getIndependentStatus())){
            log.error("退款申请  refundProcess - 订单正在分账中, 订单号: {}", orderInfoDO.getOrderCode());
            throw exception(ErrorCodeConstants.ORDER_INDEPENDENT_PROCESSING);
        }
        if(OrderItemAftersalesStatusEnum.PROCESSING.getCode()==orderItemDO.getAftersalesStatus()){
            log.info("退款申请  refundProcess -该订单行:{}已经在售后中",orderItemDO.getOrderItemCode());
            throw exception(ErrorCodeConstants.ORDER_ITEM_REFUND_RPOCESSING);
        }
        //订单行 orderItemDo的状态必须为待核销且不是运营发起才能退款
        log.info("退款申请  refundProcess - 检查订单项状态, 当前状态: {}", orderItemDO.getItemStatus());
        if(OrderItemCouponStatusEnum.PENDING_VERIFICATION.getCode()!=orderItemDO.getItemStatus()
                &&!isOperationFlag){
            log.error("退款申请  refundProcess - 订单项状态不是待核销, 当前状态: {}", orderItemDO.getItemStatus());
            throw exception(ErrorCodeConstants.ORDER_ITEM_STATUS_NOT_PENDING_VERIFY);
        }
        //如果是运营人员发起退款订单行状态 必须是待核销 已核销或者已回收才能发起退款
        if(isOperationFlag
                && !(OrderItemCouponStatusEnum.PENDING_VERIFICATION.getCode()==orderItemDO.getItemStatus()
                || OrderItemCouponStatusEnum.RECLAIMED.getCode()==orderItemDO.getItemStatus()
                || OrderItemCouponStatusEnum.VERIFIED.getCode()==orderItemDO.getItemStatus())){
            log.info("退款申请  refundProcess - 运营人员发起退款 订单项状态不是待核销或已回收, 当前状态:{}",orderItemDO.getItemStatus());
            throw exception(ErrorCodeConstants.ORDER_ITEM_STATUS_NOT_PENDING_VERIFY_OR_NOT_RECALL);
        }
        //订单状态校验：订单当前状态能否能够申请退款（订单状态:订单完成或者已支付或者已关闭或者部分取消的订单 ，支付状态：已支付）
        log.info("退款申请  refundProcess - 检查订单状态, 订单状态: {}, 支付状态: {}",
                orderInfoDO.getOrderStatus(), orderInfoDO.getPaymentStatus());
        if(!checkOrderStatus(orderInfoDO,operationType)){
            log.error("退款申请  refundProcess - 订单状态不允许退款, 订单状态: {}, 支付状态: {}",
                    orderInfoDO.getCouponStatus(), orderInfoDO.getPaymentStatus());
            throw exception(ErrorCodeConstants.REFUND_ORDER_STATUS_ERROR);
        }
    }

    private RefundOrderInfoResult createRefundOrderInfoResult(OrderInfoResult orderInfoResult, BaseOrderRefundApplyDTO refundApplyDTO, Integer operationType) {
        OrderItemDO orderItemDO = orderInfoResult.getOrderItemDO();
        Integer realRefundMoney  = orderInfoResult.getRealRefundMoney();
        Integer remainRefundPoint = orderInfoResult.getRefundPoints();
        RefundOrderInfoResult result = new RefundOrderInfoResult();
        Integer fulfilmentType = refundApplyDTO.getRefundFulfilmentType();;
        //查询可退的券数量
        List<String> coupons = new ArrayList<>();
        log.info("退款申请  refundProcess - 开始创建退款单");
        /**
         * 2.1 创建退款单：t_order_refund
         */
        OrderRefundDO orderRefundDO = assembleOrderRefundDO(refundApplyDTO,realRefundMoney,operationType,fulfilmentType);
        if(fulfilmentType.equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())){
            setRefundStatusSup(orderRefundDO,orderItemDO,realRefundMoney,operationType);
            coupons = getRefundCoupons(orderItemDO.getOrderItemCode(),operationType);
        }
        log.info("退款申请  refundProcess - 创建退款单, 退款单号: {}", orderRefundDO.getRefundOrderCode());
        orderRefundMapper.insert(orderRefundDO);
        /**
         * 2.2 创建退款单附件：t_order_refund_attachment
         */
        List<OrderRefundAttachmentDO> refundAttachmentDOS = getOrderRefundAttachmentList(refundApplyDTO.getAttachmentUrls(),orderRefundDO);
        log.info("退款申请  refundProcess - 创建退款单附件, 数量: {}",
                refundAttachmentDOS != null ? refundAttachmentDOS.size() : 0);

        /**
         * 2.3 创建退款单item：t_order_refund_item
         */
        int refundQuantity = 0;
        //退单数量
        refundQuantity = getRefundQuantity(fulfilmentType, refundQuantity, coupons, orderItemDO);
        OrderRefundItemDO refundItemDO = getOrderRefundItemDO(refundApplyDTO.getOrderItemCode(),orderRefundDO,realRefundMoney,remainRefundPoint,refundQuantity);
        log.info("退款申请  refundProcess - 创建退款单项, 退款单号: {}, 订单项编号: {}",
                orderRefundDO.getRefundOrderCode(), refundApplyDTO.getOrderItemCode());

        if (CollectionUtils.isNotEmpty(refundAttachmentDOS)) {
            log.info("退款申请  refundProcess - 插入退款单附件");
            attachmentMapper.insertBatch(refundAttachmentDOS);
        }

        //2.3.批量插入t_order_refund_item  对应该退单号对应的订单细则item_code
        if (refundItemDO!=null) {
            log.info("退款申请  refundProcess - 插入退款单项");
            orderRefundItemDOMapper.insert(refundItemDO);
        }

        result.setOrderRefundDO(orderRefundDO);
        result.setRefundItemDO(refundItemDO);
        result.setRefundCoupons(coupons);
        return result;
    }

    private int getRefundQuantity(Integer fulfilmentType, int refundQuantity, List<String> coupons, OrderItemDO orderItemDO) {
        if(fulfilmentType.equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())){
            refundQuantity = coupons.size();
        } else if (fulfilmentType.equals(OrderTypeEnum.BRAND_GOOD.getCode())) {
            Integer alreadyRefundMoney = getLogisticsAlreadyRefundMoney(orderItemDO.getOrderItemCode(),null);
            refundQuantity = alreadyRefundMoney==null || alreadyRefundMoney == 0? orderItemDO.getProductQuantity():0;//判断是否重复退款
        }
        return refundQuantity;
    }

    /**
     * 设置退款状态补充说明
     * @param orderRefundDO
     * @param operationType
     */
    private void setRefundStatusSup(OrderRefundDO orderRefundDO,OrderItemDO orderItemDO,Integer realRefundMoney ,Integer operationType){
        log.info("退款申请  setRefundStatusSup - 设置退款状态补充说明, 退款单号: {}, 券数量: {}, 剩余券数量: {},操作类型:{}",orderRefundDO.getRefundOrderCode(),RefundOrderOperationTypeEnum.getByCode(operationType).getName());
        Integer costAmount = orderItemDO.getCostAmount();
        //打印各个金额的log
        log.info("退款申请  setRefundStatusSup - 设置退款状态补充说明, 订单项成本金额: {}, 退款金额: {},退款单号: {}",costAmount,realRefundMoney,orderRefundDO.getRefundOrderCode());
        if(costAmount - realRefundMoney ==0){//一次性退完才能是全退,其他情况是部分退
            if(RefundOrderOperationTypeEnum.SYSTEM_AUTO.getCode()==operationType){
                orderRefundDO.setRefundStatusSup(RefundStatusSupEnum.COUPON_EXPIRED.getCode());
            }else {
                orderRefundDO.setRefundStatusSup(RefundStatusSupEnum.ACTIVE_REFUND.getCode());
            }
        }else{//部分退
            if(RefundOrderOperationTypeEnum.SYSTEM_AUTO.getCode()==operationType){
                orderRefundDO.setRefundStatusSup(RefundStatusSupEnum.COUPON_EXPIRED_PARTIAL.getCode());
            }else {
                orderRefundDO.setRefundStatusSup(RefundStatusSupEnum.ACTIVE_REFUND_PARTIAL.getCode());
            }
        }



    }

    /**
     * 处理外部服务调用（无事务或新事务）
     */
    public void processExternalServices(RefundProcessResult result) {
        List<String> coupons = result.getRefundCoupons();
        OrderInfoDO orderInfoDO = result.getOrderInfoDO();
        OrderRefundDO orderRefundDO =result.getOrderRefundDO();
        OrderItemDO orderItemDO= result.getOrderItemDO();
        OrderRefundItemDO refundItemDO = result.getRefundItemDO();
        OrderPaymentRecordsDO orderPaymentRecordsDO = result.getOrderPaymentRecordsDO();
        //4.进行退款相关操作 退款是异步操作的,回调接口在com.jlr.ecp.order.api.refund.RefundApiImpl#refundPaymentCallback
        //执行退款
        log.info("退款申请  processExternalServices - 开始执行退款流程, 订单号: {}, 退款单号: {}",
                orderInfoDO.getOrderCode(), orderRefundDO.getRefundOrderCode());
        refundMoneyProcess(refundItemDO,orderItemDO,orderRefundDO,orderInfoDO,orderPaymentRecordsDO);
        //5 履约方式是电子券则通知作废LRE券
        if(orderInfoDO.getOrderType().equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())&&CollectionUtils.isNotEmpty(coupons)){//同一单多次退的场景下 coupons可能为0
            log.info("退款申请  processExternalServices - 开始作废卡券, 数量: {}", coupons.size());
            cancelCoupon(coupons,orderRefundDO.getRefundOrderCode(),orderRefundDO.getOriginOrderCode());
        }
    }


    /**
     * 计算可退积分
     * 如果金额没退完则 不退还积分 其他情况按比例退积分
     */
    public Integer calculateRefundPoints(OrderItemDO orderItemDO,Integer remainRefundMoney,Integer refundMoneyAmount){
        log.info("退款申请  calculateRefundPoints - 开始计算可退积分, 可退金额: {}, 申请退款金额: {}",
                remainRefundMoney, refundMoneyAmount);

        //查询出itemCode下面的所有卡券数量
        Long couponCount = orderCouponDetailDOMapper.getAllCouponCountByItemCode(orderItemDO.getOrderItemCode());
        log.info("退款申请  refundProcess - 获取订单项下所有卡券, 数量: {}", couponCount);

        Integer refundPoints;
        Integer pointAmount = orderItemDO.getPointAmount();
        if(pointAmount == null){
            return 0;
        }

        //如果退款金额和最大可退金额不一致，则不退积分否则按比例退积分
        if(refundMoneyAmount !=null && !(refundMoneyAmount.compareTo(remainRefundMoney) ==0)){
            refundPoints = 0;
            log.info("退款申请  calculateRefundPoints - 运营发起退款且金额不一致, 不退回积分: {}", refundPoints);
            return refundPoints;
        }else{
            Integer verifiedCoupons = orderCouponDetailDOMapper.getCouponCountByStatus(orderItemDO.getOrderItemCode(),List.of(EcouponStatusEnum.VERIFIED.getCode())).intValue();
            if(verifiedCoupons != null && verifiedCoupons >0){//需要扣减掉核销的积分
               Integer verifiedPoints = calculateByRatio(pointAmount,verifiedCoupons,couponCount.intValue());
               refundPoints = pointAmount - verifiedPoints;
               log.info("退款申请  calculateRefundPoints - 按比例可退积分: {} 核销的券数量:{}", refundPoints,verifiedPoints);
            }else{
                refundPoints = pointAmount;
            }


        }
        log.info("退款申请  calculateRefundPoints - 最终可退积分: {}", refundPoints);
        return refundPoints;
    }


    /**
     * 计算可退积分
     * 如果金额没退完则 不退还积分 否则全退
     */
    public Integer calculateLogisticsRefundPoints(OrderItemDO orderItemDO,Integer remainRefundMoney,Integer refundMoneyAmount){
        log.info("退款申请  calculateLogisticsRefundPoints - 开始计算可退积分, 可退金额: {}, 申请退款金额: {}",
                remainRefundMoney, refundMoneyAmount);

        Integer refundPoints;
        Integer pointAmount = orderItemDO.getPointAmount();
        if(pointAmount == null){
            return 0;
        }

        //如果退款金额和最大可退金额不一致，则不退积分否则按比例退积分
        if(refundMoneyAmount !=null && !(refundMoneyAmount.compareTo(remainRefundMoney) ==0)){
            refundPoints = 0;
            log.info("退款申请  calculateLogisticsRefundPoints - 退款金额和最大可退金额不一致, 不退回积分: {}", refundPoints);
            return refundPoints;
        }else{
            refundPoints = pointAmount;
        }
        log.info("退款申请  calculateLogisticsRefundPoints - 最终可退积分: {}", refundPoints);
        return refundPoints;
    }


    /**
     * 按比例计算数值
     * 因为这里的单位是分  所以只保留整数
     */
    private Integer calculateByRatio(Integer amount,Integer size,Integer totalSize){
        if(size == null || size == 0){
            return  0;
        }
        //这里必须先算出券单价再*数量 与其他地方保持算法一致
        BigDecimal unitPrice = BigDecimal.valueOf(amount)
                .divide(BigDecimal.valueOf(totalSize), 0, RoundingMode.FLOOR); // 保留 2 位小数
        // 总价
        BigDecimal totalPrice = unitPrice.multiply(BigDecimal.valueOf(size))
                .setScale(0, RoundingMode.FLOOR);
        log.info("退款申请  calculateByRatio -unitPrice:{} totalPrice: {}",unitPrice,totalPrice);
        // 3.转换为整数（根据业务需求选择取舍方式）
        Integer finalAmount = totalPrice.setScale(0, RoundingMode.FLOOR).intValueExact();
        log.info("退款申请  calculateByRatio - 按比例可退值: {}", finalAmount);
        return finalAmount;
    }



    public BaseOrderRefundSuccessMessage buildBaseOrderRefundSuccessMessage(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO) {
        BaseOrderRefundSuccessMessage message = new BaseOrderRefundSuccessMessage();
        message.setMessageId(ecpIdUtil.nextIdStr());
        String phone = phoneNumberDecodeUtil.getDecodePhone(StringUtils.isNotBlank(orderInfoDO.getWxPhone())?orderInfoDO.getWxPhone():orderInfoDO.getContactPhone());
        message.setPhoneNumber(phone);
        message.setTaskCode(taskCode);
        message.setTenantId(orderInfoDO.getTenantId().longValue());
        message.setServiceName(orderItemDO.getProductName());
        message.setBgorderNumber(orderInfoDO.getOrderCode());
        String wxUrl = shortLinkHandler.getShortLink(orderItemDO.getOrderItemCode(),orderRefundDO.getRefundOrderCode(), ShortLinkPathEnum.COUPON_REFUND_SUCCESS_DETAIL.getPath(),orderInfoDO.getBusinessCode());
        log.info("退款申请  buildBaseOrderRefundSuccessMessage wxUrl: {}",wxUrl);
        message.setWxUrl(wxUrl);
        return message;
    }


    private void sendRefundSuccessMessageByKafka(BaseOrderRefundSuccessMessage orderPaymentSuccessMessage){
        log.info("sendRefundSuccessMessageByKafka orderPaymentSuccessMessage:{}", orderPaymentSuccessMessage);
        producerTool.sendMsg(KafkaConstants.ORDER_ECOUPON_REFUND_TOPIC, "", JSON.toJSONString(orderPaymentSuccessMessage));

    }

    /**
     * 查询待退款的coupons
     * @param orderItemCode
     * @param operationType
     * @return
     */
    private List<String> getRefundCoupons(String orderItemCode,Integer operationType){
        log.info("退款申请  getRefundCoupons - 开始查询待退款券码, 订单项编号: {}", orderItemCode);

        if(StringUtils.isEmpty(orderItemCode)){
            log.error("退款申请  getRefundCoupons - 订单项编号为空");
            throw exception(ErrorCodeConstants.ORDER_ITEMS_CAN_NOT_EMPTY);
        }

        //根据itemCode查询出对应的券code
        List<Integer> status;
        if(RefundOrderOperationTypeEnum.SYSTEM_AUTO.getCode()==operationType){//系统自动退款则只找过期的券
            status= Arrays.asList(
                    EcouponStatusEnum.EXPIRED.getCode()
            );
        }else{
            status = Arrays.asList(
                    EcouponStatusEnum.WAREHOUSING.getCode(),EcouponStatusEnum.NOT_ACTIVE.getCode(),EcouponStatusEnum.PENDING_USE.getCode()
            );
        }
        List<OrderCouponDetailDO> infoDOS =  orderCouponDetailDOMapper.selectList(new LambdaQueryWrapperX<OrderCouponDetailDO>()
                .eq(OrderCouponDetailDO::getOrderItemCode, orderItemCode)
                .eq(OrderCouponDetailDO::getIsDeleted, Boolean.FALSE)
                .in(OrderCouponDetailDO::getStatus,
                        status
                ));

        if(CollectionUtils.isEmpty(infoDOS)){
            if(RefundOrderOperationTypeEnum.OPERATION_INITIATED.getCode()==operationType){//运营人员可能多次退款
                log.info("退款申请  getRefundCoupons - 运营人员可能多次退款, 订单项编号: {}", orderItemCode);
                return new ArrayList<>();
            }
            log.error("退款申请  getRefundCoupons - 未找到可退款的券码, 订单项编号: {}", orderItemCode);
            throw exception(ErrorCodeConstants.COUPON_CODE_NOT_EXISTS);
        }

        //获取券code
        List<String> couponCodes = infoDOS.stream()
                .filter(Objects::nonNull)
                .map(OrderCouponDetailDO::getCouponCode)
                .filter(code -> code != null && !code.isEmpty())
                .collect(Collectors.toList());

        log.info("退款申请  getRefundCoupons - 查询到待退款券码 couponCodes: {}", couponCodes);
        return couponCodes;
    }
    

    /**
     * 计算电子券最大可退款金额
     * 若非第一次退款
     *   最大可退款金额=订单行实付金额-已退款金额-已核销金额
     * 若是第一次退款
     *   最大可退款金额=订单行实付金额*剩余可退券数量/券总数量
     *  若是第一次退款且卡券全退
     *   最大可退金额 = 实付金额
     * @return
     */
    private Integer calculateMaxRefundMoney(OrderItemDO orderItemDO,Integer operationType){

        //查询出已核销的券数量
        Integer verifiedCoupons = orderCouponDetailDOMapper.getCouponCountByStatus(orderItemDO.getOrderItemCode(),List.of(EcouponStatusEnum.VERIFIED.getCode())).intValue();
        log.info("退款申请  calculateMaxRefundMoney - 获取订单项已核销的券码, 数量: {}", verifiedCoupons);
        //查询出可退的的卡券
        Integer refundCoupons = 0;
        if(RefundOrderOperationTypeEnum.SYSTEM_AUTO.getCode() == operationType){//如果是系统自动退要计算过期券
            refundCoupons  = orderCouponDetailDOMapper.getCouponCountByStatus(orderItemDO.getOrderItemCode(),List.of(EcouponStatusEnum.WAREHOUSING.getCode(),EcouponStatusEnum.NOT_ACTIVE.getCode(),EcouponStatusEnum.PENDING_USE.getCode(),EcouponStatusEnum.EXPIRED.getCode())).intValue();
        }else{//非系统自动退不计算过期的券
            refundCoupons  = orderCouponDetailDOMapper.getCouponCountByStatus(orderItemDO.getOrderItemCode(),List.of(EcouponStatusEnum.WAREHOUSING.getCode(),EcouponStatusEnum.NOT_ACTIVE.getCode(),EcouponStatusEnum.PENDING_USE.getCode())).intValue();
        }
        log.info("退款申请  calculateMaxRefundMoney - 获取订单项待核销的券码, 数量: {}", refundCoupons);


        //查询出itemCode下面的所有卡券数量
        Long couponCount = orderCouponDetailDOMapper.getAllCouponCountByItemCode(orderItemDO.getOrderItemCode());
        log.info("退款申请  calculateMaxRefundMoney - 获取订单项下所有卡券, 数量: {}", couponCount);

        // 这一行的花费金额
        int costAmount = orderItemDO.getCostAmount();
        Integer finalAmount;
        //计算出可退款的金额
        if(refundCoupons == couponCount.intValue()){//全退直接取实付金额
            finalAmount = costAmount;
        } else if(refundCoupons == 0){//说明是重复退款
            //计算出已核销的金额
            Integer verifiedMoney = calculateByRatio(costAmount,verifiedCoupons,couponCount.intValue());
            //计算已退款金额
            Integer alreadyRefundMoney = getAlreadyRefundMoney(orderItemDO.getOrderItemCode(),null);
            //可退款金额=订单行实付金额-已退款金额-已核销金额
            finalAmount = costAmount - alreadyRefundMoney - verifiedMoney;
        }else{//说明是第一次退款
            //可退款金额=订单行实付金额*剩余可退券数量/券总数量
            finalAmount = calculateByRatio(costAmount,refundCoupons,couponCount.intValue());
        }
        log.info("退款申请  calculateMaxRefundMoney - 获取订单项可退款金额, 数量: {}", finalAmount);
        return  finalAmount;
    }



    /**
     * 计算实物商品最大可退款金额
     * 最大可退款金额=订单行实付金额-已退款金额
     * @return
     */
    private Integer calculateLogisticsMaxRefundMoney(OrderItemDO orderItemDO,Long orderRefundItemId){
        //计算已退款金额
        Integer alreadyRefundMoney = getLogisticsAlreadyRefundMoney(orderItemDO.getOrderItemCode(),orderRefundItemId);
        log.info("退款申请  calculateLogisticsMaxRefundMoney - 获取订单项已退款金额, 数量: {}", alreadyRefundMoney);
        Integer finalAmount= orderItemDO.getCostAmount() - alreadyRefundMoney;
        log.info("退款申请  calculateLogisticsMaxRefundMoney - 获取订单项可退款金额, 数量: {}", finalAmount);
        return  finalAmount;
    }

    /**
     *
     * 获取已退款金额
     * @return
     */
    private Integer getAlreadyRefundMoney(String orderItemDO,Long orderRefundItemId){
        List<OrderRefundItemDO> orderRefundItemDOList;
        if(orderRefundItemId !=null){//已经插入了数据的情况下需要传入自身ID排除掉
            orderRefundItemDOList = orderRefundItemDOMapper.selectList(
                    new LambdaQueryWrapper<OrderRefundItemDO>()
                            .eq(OrderRefundItemDO::getOrderItemCode, orderItemDO)
                            .ne(OrderRefundItemDO::getId, orderRefundItemId)//排除掉自身
                            .eq(OrderRefundItemDO::getIsDeleted,Boolean.FALSE));
        }else{
            orderRefundItemDOList = orderRefundItemDOMapper.selectList(
                    new LambdaQueryWrapper<OrderRefundItemDO>()
                            .eq(OrderRefundItemDO::getOrderItemCode, orderItemDO)
                            .eq(OrderRefundItemDO::getIsDeleted,Boolean.FALSE));
        }
        if(CollectionUtils.isNotEmpty(orderRefundItemDOList)){
            log.info("getAlreadyRefundMoney - orderRefundItemDOList:{}",orderRefundItemDOList);
            //计算orderRefundItemDOList中refundMoney的总和
            Integer refundMoney = orderRefundItemDOList.stream().mapToInt(OrderRefundItemDO::getRefundMoney).sum();
            return refundMoney;
        }
        return  0;
    }


    /**
     *
     * 获取实物商品已退款金额 需要核减掉撤销与拒绝退款申请的金额
     * @return
     */
    private Integer getLogisticsAlreadyRefundMoney(String orderItemCode,Long orderRefundItemId){
        return  orderRefundItemDOMapper.getLogisticsAlreadyRefundMoney(orderItemCode,orderRefundItemId,RefundLogisticsStatusEnum.REFUND_COMPLETED.getCode());
    }

    /**
     * 进行分账退款
     * @param refundItemDOS
     * @param orderRefundDO
     * @param orderItemDO
     * @return
     */
    private boolean transRefundMoneyProcess(OrderInfoDO orderInfoDO, OrderRefundItemDO refundItemDOS,OrderRefundDO orderRefundDO,OrderItemDO orderItemDO,OrderPaymentRecordsDO orderPaymentRecordsDO){
        PayTransRefundReqDTO payTransRefundReqDTO = getPayTransRefundReqDTO(orderInfoDO, refundItemDOS, orderRefundDO, orderItemDO, orderPaymentRecordsDO);
        log.info("退款申请  分账退款申请 - 请求对象PayTransRefundReqDTO:{}",payTransRefundReqDTO);
        CommonResult<SubmitTransRefundOrderResp> commonResult = payOrderApi.submitTransRefundOrder(payTransRefundReqDTO);
        if(commonResult!=null && commonResult.isSuccess() && Objects.nonNull(commonResult.getData())){
            log.info("退款申请  分账退款申请 - 退款成功, 订单号: {}, 返回结果: {}",orderInfoDO.getOrderCode(),commonResult);
            Integer fulfilmentType = orderInfoDO.getOrderType();
            if(fulfilmentType.equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())){
                int event = EcouponOrderRefundEventEnum.ECOUPON_REFUND_INDEPENDENT.getCode();
                ecouponOrderRefundStatusMachine.changeOrderStatus(event,orderInfoDO,orderRefundDO,orderItemDO);
            } else if (fulfilmentType.equals(OrderTypeEnum.BRAND_GOOD.getCode())) {
                int event = LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_INDEPENDENT.getCode();
                logisticsOrderRefundStatusMachine.changeOrderStatus(event,orderInfoDO,orderRefundDO,orderItemDO);
            }
            return true;
        } else {
            log.error("退款申请  分账退款申请 - 发起调用退款接口异常, 订单号: {}, 返回结果: {}",
                    orderRefundDO.getRefundOrderCode(), commonResult);
            throw exception(ErrorCodeConstants.REFUND_APPROVE_SEND_REFUND);
        }
    }


    private PayTransRefundReqDTO getPayTransRefundReqDTO(OrderInfoDO orderInfoDO, OrderRefundItemDO refundItemDOS, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO, OrderPaymentRecordsDO orderPaymentRecordsDO) {
        OrderIndependentDO independent = orderIndependentDOMapper.getOrderFinishByOrderCode(orderInfoDO.getOrderCode());
        PayTransRefundReqDTO payTransRefundReqDTO = new PayTransRefundReqDTO();//退款请求对象
        payTransRefundReqDTO.setPayApplyNo(orderPaymentRecordsDO.getPayApplyNo());
        payTransRefundReqDTO.setTransApplyNo(independent.getTransApplyNo());
        payTransRefundReqDTO.setOrderRefundCode(orderRefundDO.getRefundOrderCode());
        payTransRefundReqDTO.setOrderCode(orderRefundDO.getOriginOrderCode());
        payTransRefundReqDTO.setJlrId(orderPaymentRecordsDO.getConsumerCode());
        payTransRefundReqDTO.setRemark(OrderTitleUtil.getOrderTitle(Set.of(orderInfoDO.getBusinessCode())));
        Integer refundFreight = refundItemDOS.getRefundFreight()==null?0:refundItemDOS.getRefundFreight();
        payTransRefundReqDTO.setTotalDivRefundAmt(orderRefundDO.getRefundMoneyAmount()+refundFreight);

        TransRefundItemVO refundItemVO = new TransRefundItemVO();
        refundItemVO.setDivRefundAmt(refundItemDOS.getRefundMoney());
        refundItemVO.setTransAcctId(orderItemDO.getMerchantAccountNo());
        List<TransRefundItemVO> transRefundItems = new ArrayList<>();
        if(refundFreight !=0){//需要退还运费
            //用labmda表达式根据orderCode获取正序第一个订单行OrderItem
            OrderItemDO firstOrderItemDO = orderItemDOMapper.selectOne(new LambdaQueryWrapper<OrderItemDO>()
                    .eq(OrderItemDO::getOrderCode, orderRefundDO.getOriginOrderCode())
                    .orderByAsc(OrderItemDO::getId)
                    .last(Constants.LIMIT_ONE));
            log.info("getAlreadyRefundMoney - firstOrderItemDO:{}",firstOrderItemDO);
            //判断运费所在订单行所在商户号是否等于当前退款行,等于则合并退款 否则创建单独的退款项
            if(StringUtils.isNotBlank(firstOrderItemDO.getMerchantAccountNo())
                    && firstOrderItemDO.getMerchantAccountNo().equals(orderItemDO.getMerchantAccountNo())){
                refundItemVO.setDivRefundAmt(refundItemDOS.getRefundMoney()+refundFreight);
            }else{
                TransRefundItemVO refundFreightItemVO = new TransRefundItemVO();
                refundFreightItemVO.setTransAcctId(firstOrderItemDO.getMerchantAccountNo());
                refundFreightItemVO.setDivRefundAmt(refundFreight);
                transRefundItems.add(refundFreightItemVO);
            }
        }
        transRefundItems.add(refundItemVO);
        payTransRefundReqDTO.setTransRefundItems(transRefundItems);
        log.info("退款申请  分账退款申请 - 请求对象PayTransRefundReqDTO:{}",payTransRefundReqDTO);
        return payTransRefundReqDTO;
    }

    /**
     * 进行退款
     * @param refundItemDOS
     * @param orderRefundDO
     * @param orderInfoDO
     * @param orderPaymentRecordsDO
     * @return
     */
   /* public boolean refundMoneyProcess(OrderRefundItemDO refundItemDOS,OrderItemDO orderItemDO,OrderRefundDO orderRefundDO,OrderInfoDO orderInfoDO,OrderPaymentRecordsDO orderPaymentRecordsDO){
        log.info("退款申请  refundMoneyProcess - 开始处理退款, 退款单号: {}, 原订单号: {}",
                orderRefundDO.getRefundOrderCode(), orderRefundDO.getOriginOrderCode());
        int refundProcessing = EcouponOrderRefundEventEnum.ECOUPON_REFUND_PROCESSING.getCode();
        ecouponOrderRefundStatusMachine.changeOrderStatus(refundProcessing,orderInfoDO,orderRefundDO,orderItemDO);
        if (orderRefundDO.getRefundMoney().equals(RefundMoneyEnum.REFUNDED.getCode())) {
            //有退款就发起退款api
            PayRefundCreateReqDTO payRefundCreateReqDTO = new PayRefundCreateReqDTO();
            payRefundCreateReqDTO.setOrderCode(orderRefundDO.getOriginOrderCode());
            payRefundCreateReqDTO.setApplyNo(orderRefundDO.getRefundOrderCode());
            payRefundCreateReqDTO.setParentCode(orderInfoDO.getParentOrderCode());
            payRefundCreateReqDTO.setRefundAmount(refundItemDOS.getRefundMoney());
            payRefundCreateReqDTO.setPayApplyNo(orderPaymentRecordsDO.getPayApplyNo());

            //组装退款的具体数据
            List<OrderItemsVo> orderItems= new ArrayList<>();
            if(refundItemDOS.getRefundMoney() != null && refundItemDOS.getRefundMoney() !=0){
                OrderItemsVo moneyItems = getOrderItemsVo(refundItemDOS, orderItemDO,PayTypeEnum.CASH.getCode());
                orderItems.add(moneyItems);
            }

            if(refundItemDOS.getRefundPoint() !=null && refundItemDOS.getRefundPoint() !=0){
                OrderItemsVo pointItems = getOrderItemsVo(refundItemDOS, orderItemDO,PayTypeEnum.POINTS.getCode());
                orderItems.add(pointItems);
            }

            //对积分和金额为0的且多订单行的特殊情况进行处理
            Boolean isCompletedRefund =isCompletedRefundIfNoMoneyAndPoint(refundItemDOS,orderItemDO);
            if(isCompletedRefund){//提前完成此单
                int event = EcouponOrderRefundEventEnum.ECOUPON_REFUND_COMPLETED.getCode();
                ecouponOrderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, orderRefundDO, orderItemDO);
                return true;
            }

            *//**
             *
             * 检查是否需要退回优惠券 若该主订单使用了优惠券且所有分摊了优惠券的子订单都已经退款,
             * 且退款金额=实付金额则需要退回优惠券,记录优惠券code到t_refund_order表中
             *//*
            OrderDiscountDetailDO orderDiscountDetailDO = calculateIfReturnCouponCode(orderItemDO,refundItemDOS);
            if(orderDiscountDetailDO !=null && StringUtils.isNotBlank(orderDiscountDetailDO.getCouponCode())){
                OrderItemsVo pointItems = getOrderItemsVo(orderItemDO, orderDiscountDetailDO);
                orderItems.add(pointItems);
            }
            if(CollectionUtils.isEmpty(orderItems)){
                   //如果什么都不退的情况下则不调用payment center的接口直接返回成功(目的是退卡券)
                    log.info("退款申请  refundMoneyProcess 退款成功- 没有退回现金 积分或优惠券, 退款单号: {}, 原订单号: {}",orderRefundDO.getRefundOrderCode(),orderRefundDO.getOriginOrderCode());
                    //提前完成此单
                    int event = EcouponOrderRefundEventEnum.ECOUPON_REFUND_COMPLETED.getCode();
                    ecouponOrderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, orderRefundDO, orderItemDO);
                    return true;
            }
            payRefundCreateReqDTO.setOrderItems(orderItems);

            //调用退款接口
            log.info("退款申请  refundMoneyProcess - 发起调用退款接口, 入参: {}", JSON.toJSONString(payRefundCreateReqDTO));
            CommonResult<SubmitRefundOrderResp> commonResult = payOrderApi.submitRefundOrder(payRefundCreateReqDTO);

            if(commonResult!=null && commonResult.isSuccess() && Objects.nonNull(commonResult.getData()) && StringUtils.isNotBlank(commonResult.getData().getRefundApplyNo())){
                SubmitRefundOrderResp resp = commonResult.getData();
                log.info("退款申请  refundMoneyProcess - 退款申请成功, 退款申请号: {}", resp.getRefundApplyNo());
                OrderRefundPaymentRecordsDO recordsDO = new OrderRefundPaymentRecordsDO();
                recordsDO.setOrderCode(orderRefundDO.getOriginOrderCode());
                recordsDO.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
                recordsDO.setPayApplyNo(payRefundCreateReqDTO.getPayApplyNo());
                recordsDO.setRefundApplyNo(resp.getRefundApplyNo());
                recordsDO.setTradeStatus(RefundTradeStatus.PENDING.getCode());
                orderRefundPaymentRecordsMapper.insert(recordsDO);
                log.info("退款申请  refundMoneyProcess - 创建退款支付记录成功");
                //退回的优惠券记录到order_refund中
                if(orderDiscountDetailDO !=null){
                    orderRefundDO.setRefundCouponCode(orderDiscountDetailDO.getCouponCode());
                    log.info("退款申请  refundMoneyProcess - 退款成功, 退款单号: {}, 优惠券code: {}",orderRefundDO.getRefundOrderCode(),orderDiscountDetailDO.getCouponCode());
                    orderRefundMapper.updateById(orderRefundDO);
                }

            } else {
                log.error("退款申请  refundMoneyProcess - 发起调用退款接口异常, 订单号: {}, 返回结果: {}",
                        orderInfoDO.getOrderCode(), commonResult);
                throw exception(ErrorCodeConstants.REFUND_APPROVE_SEND_REFUND);
            }
        }

        log.info("退款申请  refundMoneyProcess - 退款处理完成, 退款单号: {}", orderRefundDO.getRefundOrderCode());
        return true;
    }
*/


    /**
     * 进行退款
     * @param refundItemDOS
     * @param orderRefundDO
     * @param orderInfoDO
     * @param orderPaymentRecordsDO
     * @return
     */
    public boolean refundMoneyProcess(OrderRefundItemDO refundItemDOS, OrderItemDO orderItemDO,
                                      OrderRefundDO orderRefundDO, OrderInfoDO orderInfoDO, OrderPaymentRecordsDO orderPaymentRecordsDO) {
        log.info("退款申请  refundMoneyProcess - 开始处理退款, 退款单号: {}, 原订单号: {}",
                orderRefundDO.getRefundOrderCode(), orderRefundDO.getOriginOrderCode());

        // 更新退款状态为处理中
        updateRefundStatus(orderInfoDO, orderRefundDO, orderItemDO);
        if (!orderRefundDO.getRefundMoney().equals(RefundMoneyEnum.REFUNDED.getCode())) {
            log.info("退款申请  refundMoneyProcess - 退款处理完成, 退款单号: {}", orderRefundDO.getRefundOrderCode());
            return true;
        }
        // 处理退款逻辑
        return processRefund(refundItemDOS, orderItemDO, orderRefundDO, orderInfoDO, orderPaymentRecordsDO);
    }


    private void updateRefundStatus(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO) {
        Integer fulfillmentType = orderRefundDO.getRefundFulfilmentType();
        if(fulfillmentType.equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())){
            int refundProcessing = EcouponOrderRefundEventEnum.ECOUPON_REFUND_PROCESSING.getCode();
            ecouponOrderRefundStatusMachine.changeOrderStatus(refundProcessing, orderInfoDO, orderRefundDO, orderItemDO);
        } else if (fulfillmentType.equals(OrderTypeEnum.BRAND_GOOD.getCode())) {
            int refundProcessing = LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_PROCESSING.getCode();
            logisticsOrderRefundStatusMachine.changeOrderStatus(refundProcessing, orderInfoDO, orderRefundDO, orderItemDO);
        }
    }

    private PayRefundCreateReqDTO createRefundRequestDTO(OrderRefundItemDO refundItemDOS,
                                                         OrderRefundDO orderRefundDO, OrderInfoDO orderInfoDO, OrderPaymentRecordsDO orderPaymentRecordsDO) {
        PayRefundCreateReqDTO dto = new PayRefundCreateReqDTO();
        dto.setOrderCode(orderRefundDO.getOriginOrderCode());
        dto.setApplyNo(orderRefundDO.getRefundOrderCode());
        dto.setParentCode(orderInfoDO.getParentOrderCode());
        dto.setRefundAmount((refundItemDOS.getRefundMoney()==null?0:refundItemDOS.getRefundMoney())+(refundItemDOS.getRefundFreight()==null?0:refundItemDOS.getRefundFreight()));
        dto.setPayApplyNo(orderPaymentRecordsDO.getPayApplyNo());
        return dto;
    }


    private List<OrderItemsVo> assembleRefundItems(OrderRefundItemDO refundItemDOS,
                                                   OrderItemDO orderItemDO, OrderRefundDO orderRefundDO,String businessCode) {
        List<OrderItemsVo> orderItems = new ArrayList<>();
        Integer fulfilmentType = orderRefundDO.getRefundFulfilmentType();
        // 添加现金退款项
        //这里涉及到运费 所以要先计算后判断
        OrderItemsVo orderItemsCashVo = getOrderItemsVo(refundItemDOS, orderItemDO, PayTypeEnum.CASH.getCode(),businessCode);
        if(orderItemsCashVo.getRefundAmount()!=null && orderItemsCashVo.getRefundAmount() !=0){
            log.info("退款申请  refundMoneyProcess - 添加现金退款项, 退款金额: {}, 退款单号: {}",orderItemsCashVo.getRefundAmount(),orderRefundDO.getRefundOrderCode());
            orderItems.add(orderItemsCashVo);
            if(refundItemDOS.getRefundFreight()!=null && refundItemDOS.getRefundFreight() !=0){
                //如果运费存在则写入退单主表 方便报表查询和统计
                log.info("退款申请  refundMoneyProcess - 运费存在, 运费: {}, 退款单号: {}",refundItemDOS.getRefundFreight(),orderRefundDO.getRefundOrderCode());
                orderRefundDO.setRefundFreight(refundItemDOS.getRefundFreight());
                orderRefundMapper.updateById(orderRefundDO);
            }
        }

        // 添加积分退款项
        if (refundItemDOS.getRefundPoint() != null && refundItemDOS.getRefundPoint() != 0) {
            orderItems.add(getOrderItemsVo(refundItemDOS, orderItemDO, PayTypeEnum.POINTS.getCode(),businessCode));
        }

        // 添加优惠券退款项
        OrderDiscountDetailDO discountDetailDO = null;
        if(fulfilmentType.equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())){
            discountDetailDO = calculateIfReturnCouponCode(orderItemDO);
        } else if (fulfilmentType.equals(OrderTypeEnum.BRAND_GOOD.getCode())) {
            discountDetailDO = calculateIfReturnLogisticsCouponCode(orderItemDO,refundItemDOS);
        }
        if (discountDetailDO != null && StringUtils.isNotBlank(discountDetailDO.getCouponCode())) {
            orderItems.add(getOrderItemsVo(orderItemDO, discountDetailDO,businessCode));
        }

        return orderItems;
    }


    private boolean processRefund(OrderRefundItemDO refundItemDOS, OrderItemDO orderItemDO,
                                  OrderRefundDO orderRefundDO, OrderInfoDO orderInfoDO, OrderPaymentRecordsDO orderPaymentRecordsDO) {


        // 组装退款项
        List<OrderItemsVo> orderItems = assembleRefundItems(refundItemDOS, orderItemDO, orderRefundDO,orderInfoDO.getBusinessCode());

        // 创建退款请求DTO
        PayRefundCreateReqDTO payRefundCreateReqDTO = createRefundRequestDTO(refundItemDOS, orderRefundDO,
                orderInfoDO, orderPaymentRecordsDO);

        // 检查是否可以提前完成退款
        if (canCompleteRefundEarly( refundItemDOS, orderItemDO, orderInfoDO, orderRefundDO,orderItems)) {
            return true;
        }

        //如果存在分账退款则组装分账退款对象
        if(OrderIndependentStatusEnum.isIndependentRefund(orderInfoDO.getIndependentStatus())){//组装分账退款对象
            PayTransRefundReqDTO refundReqDTO = getPayTransRefundReqDTO(orderInfoDO,refundItemDOS,orderRefundDO,orderItemDO,orderPaymentRecordsDO);
            List<PayTransRefundReqDTO> transRefundsReq = List.of(refundReqDTO);
            payRefundCreateReqDTO.setTransRefundsReq(transRefundsReq);
            log.info("退款申请  processRefund -  分账退款入参: {}", JSON.toJSONString(transRefundsReq));
        }

        // 提交退款申请
        return submitRefundRequest(payRefundCreateReqDTO, orderItems, orderRefundDO, orderInfoDO);
    }


    private boolean submitRefundRequest(PayRefundCreateReqDTO payRefundCreateReqDTO,
                                        List<OrderItemsVo> orderItems, OrderRefundDO orderRefundDO, OrderInfoDO orderInfoDO) {
        payRefundCreateReqDTO.setOrderItems(orderItems);

        log.info("退款申请  submitRefundRequest - 发起调用退款接口, 入参: {}", JSON.toJSONString(payRefundCreateReqDTO));
        CommonResult<SubmitRefundOrderResp> commonResult = payOrderApi.submitRefundOrder(payRefundCreateReqDTO);

        if (!isRefundRequestSuccessful(commonResult)) {
            log.error("退款申请  submitRefundRequest - 发起调用退款接口异常, 订单号: {}, 返回结果: {}",
                    orderInfoDO.getOrderCode(), commonResult);
            throw exception(ErrorCodeConstants.REFUND_APPROVE_SEND_REFUND);
        }

        saveRefundRecord(commonResult.getData(), payRefundCreateReqDTO, orderRefundDO);
        //退回的优惠券记录到order_refund中
        for(OrderItemsVo orderItemVo:orderItems){
            if(orderItemVo.getItemType().equals(PayTypeEnum.COUPON.getCode())){
                orderRefundDO.setRefundCouponCode(orderItemVo.getItemCode());
                log.info("退款申请  submitRefundRequest - 退款成功, 退款单号: {}, 退还优惠券code: {}",orderRefundDO.getRefundOrderCode(),orderItemVo.getItemCode());
                orderRefundMapper.updateById(orderRefundDO);
            }
        }
        return true;
    }


    private boolean isRefundRequestSuccessful(CommonResult<SubmitRefundOrderResp> commonResult) {
        return commonResult != null && commonResult.isSuccess() && Objects.nonNull(commonResult.getData()) && StringUtils.isNotBlank(commonResult.getData().getRefundApplyNo());
    }

    private void saveRefundRecord(SubmitRefundOrderResp resp, PayRefundCreateReqDTO payRefundCreateReqDTO, OrderRefundDO orderRefundDO) {
        OrderRefundPaymentRecordsDO recordsDO = new OrderRefundPaymentRecordsDO();
        recordsDO.setOrderCode(orderRefundDO.getOriginOrderCode());
        recordsDO.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
        recordsDO.setPayApplyNo(payRefundCreateReqDTO.getPayApplyNo());
        recordsDO.setRefundApplyNo(resp.getRefundApplyNo());
        recordsDO.setTradeStatus(RefundTradeStatus.PENDING.getCode());
        orderRefundPaymentRecordsMapper.insert(recordsDO);
        log.info("退款申请  refundMoneyProcess - 创建退款支付记录成功");
    }

    private boolean canCompleteRefundEarly(OrderRefundItemDO refundItemDOS,
                                           OrderItemDO orderItemDO, OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO,List<OrderItemsVo> orderItems) {
        Boolean isCompletedRefund = isCompletedRefundIfNoMoneyAndPoint(refundItemDOS, orderItemDO,orderItems);
        if (isCompletedRefund) {
            log.info("退款申请  canCompleteRefundEarly 退款成功- 没有退回现金 积分或优惠券, 退款单号: {}, 原订单号: {}",orderRefundDO.getRefundOrderCode(),orderRefundDO.getOriginOrderCode());
            completeRefund(orderInfoDO, orderRefundDO, orderItemDO);
            return true;
        }
        return false;
    }

        private void completeRefund (OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO){
            if (orderInfoDO.getOrderType().equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())){
                int event = EcouponOrderRefundEventEnum.ECOUPON_REFUND_COMPLETED.getCode();
                ecouponOrderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, orderRefundDO, orderItemDO);
            } else if (orderInfoDO.getOrderType().equals(OrderTypeEnum.BRAND_GOOD.getCode())) {
                int event = LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_COMPLETED.getCode();
                logisticsOrderRefundStatusMachine.changeOrderStatus(event, orderInfoDO, orderRefundDO, orderItemDO);
            }
        }

    private  OrderItemsVo getOrderItemsVo(OrderRefundItemDO refundItemDOS, OrderItemDO orderItemDO,String itemType,String businessCode) {
        OrderItemsVo itemsVo = new OrderItemsVo();
        itemsVo.setBusinessLine(PayCenterBizLineCodeEnum.getByBusinessCode(businessCode).getBusinessLineCode());
        if(PayTypeEnum.CASH.getCode().equals(itemType)){
            Integer fulfillmentType = orderItemDO.getItemFulfillmentType();
            Integer refundMoney = refundItemDOS.getRefundMoney();
            if(fulfillmentType .equals( OrderTypeEnum.BRAND_GOOD.getCode())){//实物订单需要判断是否退回运费
                Integer refundFreight = calculateReturnFreight(refundItemDOS,orderItemDO);
                refundMoney += refundFreight;
            }
            itemsVo.setRefundAmount(Long.valueOf(refundMoney));
            itemsVo.setItemValue(Long.valueOf(refundMoney));
        } else if (PayTypeEnum.POINTS.getCode().equals(itemType)) {
            itemsVo.setItemValue(Long.valueOf(refundItemDOS.getRefundPoint()));
            itemsVo.setRefundAmount(0L);
        }
        itemsVo.setItemType(itemType);
        itemsVo.setProductName(orderItemDO.getProductName());
        itemsVo.setProductNo(orderItemDO.getProductCode());
        return itemsVo;
    }

    /**
     * 计算是否需要退回运费
     * 检查是否为0元购(总订单金额为0)
     * 如果是0元购 检查所有订单行是否都已经退款且退款类型为仅退款 如果是则退回运费,反之不退回
     * 如果是非0元购 检查所有订单行是否为仅退款且所有金额都已经退回且不存在回填的物流信息(有物流信息代表实际是退货退款)
     * 且0元实付金额和积分的的订单行都已退回 如果是则退回运费,反之不退回
     * @param refundItemDOS
     * @param orderItemDO
     * @return
     */
    private Integer calculateReturnFreight(OrderRefundItemDO refundItemDOS, OrderItemDO orderItemDO) {
        log.info("退回运费计算 calculateReturnFreight orderItemDO:{}",orderItemDO);
        //根据orderRefundCode查询orderRefund对象
        OrderRefundDO orderRefundDO = orderRefundMapper.selectOne(new LambdaQueryWrapperX<OrderRefundDO>()
                        .eq(OrderRefundDO::getRefundOrderCode, refundItemDOS.getRefundOrderCode())
                        .eq(OrderRefundDO::getIsDeleted, Boolean.FALSE)
                    .last(Constants.LIMIT_ONE)
        );
        //退货退款不退回运费
        if(orderRefundDO!=null && orderRefundDO.getRefundOrderType() ==RefundOrderTypeEnum.REFUND_RETURN_GOODS.getCode()){
            log.info("退回运费计算 calculateReturnFreight orderRefundDO:{}",orderRefundDO);
            return 0;
        }
        //根据orderItemCode在orderItemLogisticsDOMapper中查询物流信息中查询物流信息
        OrderItemLogisticsDO orderItemLogisticsDO = orderItemLogisticsDOMapper.selectOne(new LambdaQueryWrapperX<OrderItemLogisticsDO>()
                .eq(OrderItemLogisticsDO::getOrderItemCode, orderItemDO.getOrderItemCode())
                .eq(OrderItemLogisticsDO::getIsDeleted, Boolean.FALSE)
                .last(Constants.LIMIT_ONE)
        );
        //如果有物流信息 说明发生过退货退款 不退回运费
        log.info("退回运费计算 calculateReturnFreight orderItemLogisticsDO:{}",orderItemLogisticsDO);
        if (ObjectUtils.isNotEmpty(orderItemLogisticsDO)&&StringUtils.isNotBlank(orderItemLogisticsDO.getLogisticsNo())) {
            log.info("退回运费计算 calculateReturnFreight 有物流信息,不退回运费");
            return 0;
        }

        //根据orderCode查询orderInfo中的总订单金额,若订单金额为0则是0元购场景
        OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderItemDO.getOrderCode())
                .eq(OrderInfoDO::getIsDeleted, Boolean.FALSE));

        //根据orderCode查询出所有的仅退款的退单总金额
        Integer refundFreight = 0;
        Integer alreadyRefundMoney = orderRefundItemDOMapper.getOnlyRefundTotalMoneyByOrderCode(orderItemDO.getOrderCode(),RefundLogisticsStatusEnum.REFUND_COMPLETED.getCode());
        Integer refundTotalAmount = alreadyRefundMoney+(refundItemDOS.getRefundMoney()==null?0:refundItemDOS.getRefundMoney());
        log.info("退回运费计算 calculateReturnFreight refundTotalAmount:{}",refundTotalAmount);
        Integer costAmount = orderInfoDO.getCostAmount();
        refundFreight = getRefundFreight(orderItemDO, costAmount, orderInfoDO, refundFreight, refundTotalAmount);
        if(refundFreight!=0){//记录退单运费到orderItemRefund中
            refundItemDOS.setRefundFreight(refundFreight);
            orderRefundItemDOMapper.updateById(refundItemDOS);
        }
        return refundFreight;
    }

    private Integer getRefundFreight(OrderItemDO orderItemDO, Integer costAmount, OrderInfoDO orderInfoDO, Integer refundFreight, Integer refundTotalAmount) {
        if(costAmount ==null || costAmount ==0){//0元购
            log.info("退回运费计算 calculateReturnFreight 0元购");
            //根据orderCode统计订单行的行数
            Long orderItemCount = orderItemDOMapper.selectCount(new LambdaQueryWrapperX<OrderItemDO>()
                    .eq(OrderItemDO::getOrderCode, orderItemDO.getOrderCode())
                    .eq(OrderItemDO::getIsDeleted, Boolean.FALSE)
            );
            //根据orderCode 统计t_order_refund的有效行数
            //(因为orderItemRefund和orderRefund在0元购+仅退款情况下是一对一的关系且不存在重复退款的情况)
            Long orderRefundCount = orderRefundMapper.selectCount(new LambdaQueryWrapperX<OrderRefundDO>()
                    .eq(OrderRefundDO::getOriginOrderCode, orderInfoDO.getOrderCode())
                    .in(OrderRefundDO::getLogisticsRefundStatus,List.of(RefundLogisticsStatusEnum.REFUND_COMPLETED.getCode(),RefundLogisticsStatusEnum.REFUND_PROCESSING.getCode(),RefundLogisticsStatusEnum.SPLIT_REFUND_PROCESSING.getCode()))
                    .eq(OrderRefundDO::getIsDeleted, Boolean.FALSE)
            );
            log.info("退回运费计算 calculateReturnFreight orderItemCount:{},orderRefundCount:{}",orderItemCount,orderRefundCount);
            if(orderItemCount.compareTo(orderRefundCount) ==0){//需要退回运费
                log.info("退回运费计算 calculateReturnFreight 0元购需要退回运费");
                refundFreight = orderInfoDO.getFreightAmount() ==null?0: orderInfoDO.getFreightAmount();
            }

        }else {
            //非0元购
            refundFreight = calculateFreightWithNoZeroPuchase(orderItemDO, costAmount, orderInfoDO, refundFreight, refundTotalAmount);
        }
        return refundFreight;
    }

    /**
     * 计算非0元购下的退单运费
     * @param orderItemDO
     * @param costAmount
     * @param orderInfoDO
     * @param refundFreight
     * @param refundTotalAmount
     * @return
     */
    private Integer calculateFreightWithNoZeroPuchase(OrderItemDO orderItemDO, Integer costAmount, OrderInfoDO orderInfoDO, Integer refundFreight, Integer refundTotalAmount) {
        String orderCode = orderItemDO.getOrderCode();
        List<String> zeroPurchaseItemCodes = getZeroPurchaseItemCodes(orderCode);
        Long aftersalesUnCompletedItemsCount = 0L;
        if(CollectionUtils.isNotEmpty(zeroPurchaseItemCodes)){
            aftersalesUnCompletedItemsCount = getAftersalesUnCompletedCount(zeroPurchaseItemCodes, orderItemDO.getOrderItemCode());
        }
        if(costAmount.compareTo(refundTotalAmount)==0 && aftersalesUnCompletedItemsCount.compareTo(0L)==0 ){//需要退回运费
            log.info("退回运费计算 calculateReturnFreight 非0元购需要退回运费");
            refundFreight = orderInfoDO.getFreightAmount() ==null?0: orderInfoDO.getFreightAmount();
        }
        return refundFreight;
    }


    private  OrderItemsVo getOrderItemsVo(OrderItemDO orderItemDO, OrderDiscountDetailDO orderDiscountDetailDO,String businessCode) {
        OrderItemsVo pointItems = new OrderItemsVo();
        pointItems.setBusinessLine(PayCenterBizLineCodeEnum.getByBusinessCode(businessCode).getBusinessLineCode());
        pointItems.setItemCode(orderDiscountDetailDO.getCouponCode());
        pointItems.setItemModelCode(orderDiscountDetailDO.getCouponModelCode());
        pointItems.setRefundAmount(0L);
        pointItems.setItemValue(Long.valueOf(sumDiscountAmountByCouponCode(orderDiscountDetailDO.getCouponCode())));
        pointItems.setItemType(PayTypeEnum.COUPON.getCode());
        pointItems.setProductName(orderItemDO.getProductName());
        pointItems.setProductNo(orderItemDO.getProductCode());
        return pointItems;
    }

    private Integer sumDiscountAmountByCouponCode(String couponCode){
        //根据orderCode和couponCode查询orderDiscountDetail表中的discountAmount
        List<OrderDiscountDetailDO> discountDetailDOS = orderDiscountDetailDOMapper.selectList(new LambdaQueryWrapperX<OrderDiscountDetailDO>()
                .eq(OrderDiscountDetailDO::getCouponCode, couponCode)
                .eq(OrderDiscountDetailDO::getIsDeleted, Boolean.FALSE)
        );
        if(CollectionUtils.isNotEmpty(discountDetailDOS)){
            //计算优惠券的总金额
            return discountDetailDOS.stream().mapToInt(item -> item.getDiscountAmount()==null?0:item.getDiscountAmount()).sum();
        }
        return  0;
    }

    /**
     * 特殊场景处理
     * 查询0实付金额和积分的订单行号
     * @param orderCode
     * @param orderItemCode
     * @return
     */
    private List<String> getZeroPurchaseItemCodes(String orderCode){
    //根据orderCode查询orderItem表中costamount和pointamount都为0的记录
        List<OrderItemDO> orderItemDOList = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, orderCode)
                .eq(OrderItemDO::getIsDeleted, Boolean.FALSE)
                .eq(OrderItemDO::getCostAmount, 0)
                .and(wrapper -> wrapper.eq(OrderItemDO::getPointAmount, 0).or().isNull(OrderItemDO::getPointAmount))
        );
        log.info("查询0实付金额和积分的订单行号:{}",orderItemDOList);
        if(CollectionUtils.isNotEmpty(orderItemDOList)){
            //获取所有的ItemCode并返回
            return orderItemDOList.stream().map(OrderItemDO::getOrderItemCode).collect(Collectors.toList());

        }
        return new ArrayList<>();
    }


    /**
     * 对于金额和积分为0的多订单场景特殊处理
     * 如果生成了待退款的优惠券则返回true 提前完成此订单 如果有可退的券 因为积分和金额是0 一定是优惠券则返回false退还优惠券
     * @param refundItemDOS
     * @param orderItemDO
     * @return
     */
    private boolean isCompletedRefundIfNoMoneyAndPoint(OrderRefundItemDO refundItemDOS, OrderItemDO orderItemDO,List<OrderItemsVo> orderItems) {
        if((refundItemDOS.getRefundMoney() == null || refundItemDOS.getRefundMoney() ==0)
                && (refundItemDOS.getRefundPoint() ==null || refundItemDOS.getRefundPoint() ==0)){
            log.info("退款申请  refundMoneyProcess - 退款金额和积分为0 start,订单号: {}, 订单行: {}",orderItemDO.getOrderCode(),orderItemDO.getOrderItemCode());
            //没有可退的券则直接返回true 提前完成此订单 如果有可退的券 因为积分和金额是0 List一定包含优惠券 返回false退还优惠券
            if(CollectionUtils.isEmpty(orderItems)){
                return true;
            }
        }
        return false;
    }

    /**
     * 计算是否需要退回满减的优惠券
     * 若订单行有任意一张卡券已核销则不退回
     * 若退款金额+已退金额!=实付金额则不退回
     * 则返回该优惠券的CouponCode
     * @return
     */
    private OrderDiscountDetailDO calculateIfReturnCouponCode(OrderItemDO orderItemDO){
        //根据orderItemCode查询orderCouponDetail中已核销的券的数量
        Long alreadyVerifiedCount = orderCouponDetailDOMapper.getCouponCountByStatus(orderItemDO.getOrderItemCode(),List.of(EcouponStatusEnum.VERIFIED.getCode()));
        if(alreadyVerifiedCount !=null && alreadyVerifiedCount >0 ){//有已核销的券则不退回卡券
            log.info("退款申请  calculateIfReturnCouponCode - 有已核销的券不退回卡券,订单编号: {},订单行号:{},核销数量:{}",orderItemDO.getOrderCode(),orderItemDO.getOrderItemCode(),alreadyVerifiedCount);
            return null;
        }
        OrderDiscountDetailDO orderDiscountDetailDO =  orderDiscountDetailDOMapper.selectOne(new LambdaQueryWrapper<OrderDiscountDetailDO>()
                .eq(OrderDiscountDetailDO::getOrderItemCode,orderItemDO.getOrderItemCode())
                .eq(OrderDiscountDetailDO::getIsDeleted, Boolean.FALSE)
                .eq(OrderDiscountDetailDO::getDiscountType, OrderDiscountTypeEnum.COUPON.getCode())
                .last(Constants.LIMIT_ONE));
        if(ObjectUtils.isEmpty(orderDiscountDetailDO)){
            return null;
        }
        log.info("退款申请  calculateIfReturnCouponCode - 根据CouponCode查询到优惠券信息: {}", JSON.toJSONString(orderDiscountDetailDO));
        //寻找该CouponCode被多少订单行使用
        List<OrderDiscountDetailDO> orderDiscountDetailDOList = orderDiscountDetailDOMapper.selectList(new LambdaQueryWrapper<OrderDiscountDetailDO>()
                .eq(OrderDiscountDetailDO::getCouponCode,orderDiscountDetailDO.getCouponCode())
                .eq(OrderDiscountDetailDO::getOrderCode,orderItemDO.getOrderCode())
                .eq(OrderDiscountDetailDO::getDiscountType, OrderDiscountTypeEnum.COUPON.getCode())
                .eq(OrderDiscountDetailDO::getIsDeleted, Boolean.FALSE));
        log.info("退款申请  calculateIfReturnCouponCode - orderDiscountDetailDOList: {}", JSON.toJSONString(orderDiscountDetailDOList));
        if(CollectionUtils.isEmpty(orderDiscountDetailDOList)){
            return  null;
        }

        //取出orderDiscountDetailDOList中的itemCode
        List<String> itemCodeList = orderDiscountDetailDOList.stream().map(OrderDiscountDetailDO::getOrderItemCode).collect(Collectors.toList());
        log.info("退款申请  calculateIfReturnCouponCode - itemCodeList: {}", JSON.toJSONString(itemCodeList));
        Boolean isReturnedCoupon = checkAllUsedCouponOrderItemIsRefunded(itemCodeList);
        if(isReturnedCoupon){
            return orderDiscountDetailDO;
        }else{
            return null;
        }
    }


    /**
     * bg计算是否需要退回满减的优惠券
     * 若存在订单行不为售后完成则不退回卡券
     * 若退款金额+已退金额!=实付金额则不退回
     * 则返回该优惠券的CouponCode
     * @return
     */
    private OrderDiscountDetailDO calculateIfReturnLogisticsCouponCode(OrderItemDO orderItemDO,OrderRefundItemDO refundItemDOS){
        OrderDiscountDetailDO orderDiscountDetailDO =  orderDiscountDetailDOMapper.selectOne(new LambdaQueryWrapper<OrderDiscountDetailDO>()
                .eq(OrderDiscountDetailDO::getOrderItemCode,orderItemDO.getOrderItemCode())
                .eq(OrderDiscountDetailDO::getIsDeleted, Boolean.FALSE)
                .eq(OrderDiscountDetailDO::getDiscountType, OrderDiscountTypeEnum.COUPON.getCode())
                .last(Constants.LIMIT_ONE));
        if(ObjectUtils.isEmpty(orderDiscountDetailDO)){
            return null;
        }
        log.info("退款申请  calculateIfReturnLogisticsCouponCode - 根据orderItemCode查询到优惠券信息: {}", JSON.toJSONString(orderDiscountDetailDO));
        //寻找该CouponCode被多少订单行使用
        List<OrderDiscountDetailDO> orderDiscountDetailDOList = orderDiscountDetailDOMapper.selectList(new LambdaQueryWrapper<OrderDiscountDetailDO>()
                .eq(OrderDiscountDetailDO::getCouponCode,orderDiscountDetailDO.getCouponCode())
                .eq(OrderDiscountDetailDO::getOrderCode,orderItemDO.getOrderCode())
                .eq(OrderDiscountDetailDO::getDiscountType, OrderDiscountTypeEnum.COUPON.getCode())
                .eq(OrderDiscountDetailDO::getIsDeleted, Boolean.FALSE));
        log.info("退款申请  calculateIfReturnLogisticsCouponCode - orderDiscountDetailDOList: {}", JSON.toJSONString(orderDiscountDetailDOList));
        if(CollectionUtils.isEmpty(orderDiscountDetailDOList)){
            return  null;
        }

        //取出orderDiscountDetailDOList中的itemCode
        List<String> itemCodeList = orderDiscountDetailDOList.stream().map(OrderDiscountDetailDO::getOrderItemCode).collect(Collectors.toList());
        log.info("退款申请  calculateIfReturnLogisticsCouponCode - itemCodeList: {}", JSON.toJSONString(itemCodeList));
        Boolean isReturnedCoupon = checkLogisticsAllUsedCouponOrderItemIsRefunded(itemCodeList,refundItemDOS);
        if(isReturnedCoupon){
            return orderDiscountDetailDO;
        }else{
            return null;
        }
    }

    /**
     * 检查所有使用了优惠券的订单行 是否要退回优惠券
     * 若是0元购则判断是否有卡券已核销 有的话不退回 没有则退回
     * 非0元购场景判断实付金额是否等于退款金额 若等于则退回 反之不退回
     * @param orderItemCodes
     */
    private boolean checkAllUsedCouponOrderItemIsRefunded(List<String> orderItemCodes){
        log.info("退款申请  checkAllUsedCouponOrderItemIsRefunded - orderItemDOList: {}", JSON.toJSONString(orderItemCodes));
        if(CollectionUtils.isEmpty(orderItemCodes)){
            log.info("退款申请  checkAllUsedCouponOrderItemIsRefunded - orderItemDOList为空");
            return true;
        }
        //计算orderItemDOList总的实付金额
        List<OrderItemDO> orderItemDOList = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                .in(OrderItemDO::getOrderItemCode,orderItemCodes)
                .eq(OrderItemDO::getIsDeleted,Boolean.FALSE)
        );
        Integer paymentMoney = orderItemDOList.stream().mapToInt(OrderItemDO::getCostAmount).sum();
        List<String> itemCodeList = orderItemDOList.stream().map(OrderItemDO::getOrderItemCode).collect(Collectors.toList());
        List<OrderRefundItemDO> orderRefundItemDOList = orderRefundItemDOMapper.selectList(new LambdaQueryWrapper<OrderRefundItemDO>()
                .in(OrderRefundItemDO::getOrderItemCode,itemCodeList)
                .eq(OrderRefundItemDO::getIsDeleted,Boolean.FALSE)
        );
        log.info("退款申请  checkAllUsedCouponOrderItemIsRefunded - orderRefundItemDOList: {}, paymentMoney:{}", JSON.toJSONString(orderRefundItemDOList),paymentMoney);
        if(paymentMoney == null ||paymentMoney.equals(0)){//说明是0元购的场景,这里特殊处理
            //检查orderItemDOList下的所有电子券是否有已核销的券,有任意一张则不退回优惠券,反之退回
            Long verifiedCount = orderCouponDetailDOMapper.getCouponCountByStatus(itemCodeList,List.of(EcouponStatusEnum.VERIFIED.getCode()));
            log.info("退款申请  checkAllUsedCouponOrderItemIsRefunded - verifiedCount: {}",verifiedCount);
            if(verifiedCount == 0){
                return true;
            }else{
                return false;
            }
        }else{//非0元购通过判断金额来决定是否要退优惠券
            //若orderRefundItemDOList不为空 计算orderRefundItemDOList中退款金额汇总
            if(CollectionUtils.isNotEmpty(orderRefundItemDOList)){
                Integer refundMoneyFromItem = orderRefundItemDOList.stream().mapToInt(OrderRefundItemDO::getRefundMoney).sum();
                log.info("退款申请  checkAllUsedCouponOrderItemIsRefunded - paymentMoney: {} ,refundMoneyFromItem: {}",paymentMoney,refundMoneyFromItem);
                if(paymentMoney.equals(refundMoneyFromItem)){
                    return true;
                }
            }
        }
        return  false;
    }


    /**
     * 检查所有使用了优惠券的订单行 是否要退回优惠券
     * 若是0元购则判断是否有订单行不是售后完成的状态 有的话不退回 没有则退回
     * 非0元购场景判断实付金额是否等于退款金额 若等于则退回 反之不退回
     * @param orderItemCodes
     */
    private boolean checkLogisticsAllUsedCouponOrderItemIsRefunded(List<String> orderItemCodes,OrderRefundItemDO refundItemDO){
        String orderItemCode = refundItemDO.getOrderItemCode();
        log.info("退款申请  checkLogisticsAllUsedCouponOrderItemIsRefunded - orderItemDOList: {}", JSON.toJSONString(orderItemCodes));
        if(CollectionUtils.isEmpty(orderItemCodes)){
            log.info("退款申请  checkLogisticsAllUsedCouponOrderItemIsRefunded - orderItemDOList为空");
            return true;
        }
        //计算orderItemDOList总的实付金额
        List<OrderItemDO> orderItemDOList = orderItemDOMapper.selectList(new LambdaQueryWrapper<OrderItemDO>()
                .in(OrderItemDO::getOrderItemCode,orderItemCodes)
                .eq(OrderItemDO::getIsDeleted,Boolean.FALSE)
        );
        Integer paymentMoney = orderItemDOList.stream().mapToInt(OrderItemDO::getCostAmount).sum();
        String orderCode = orderItemDOList.get(0).getOrderCode();
        List<OrderRefundItemDO> orderRefundItemDOList = orderRefundItemDOMapper.selectList(new LambdaQueryWrapper<OrderRefundItemDO>()
                .in(OrderRefundItemDO::getOrderItemCode,orderItemCodes)
                .eq(OrderRefundItemDO::getIsDeleted,Boolean.FALSE)
        );
        log.info("退款申请  checkLogisticsAllUsedCouponOrderItemIsRefunded - orderRefundItemDOList: {}, paymentMoney:{}", JSON.toJSONString(orderRefundItemDOList),paymentMoney);
        if(paymentMoney == null ||paymentMoney.equals(0)){//说明是0元购的场景,这里特殊处理
            //检查orderItemDOList下的是否存在不是订单售后已完成的订单行(需要排除自身),有任意一张则不退回优惠券,反之退回
            Long verifiedCount = getAftersalesUnCompletedCount(orderItemCodes, orderItemCode);
            log.info("退款申请  checkLogisticsAllUsedCouponOrderItemIsRefunded - verifiedCount: {}",verifiedCount);
            if(verifiedCount == 0){
                return true;
            }else{
                return false;
            }
        }else{//非0元购通过判断金额来决定是否要退优惠券
            //若orderRefundItemDOList不为空 计算orderRefundItemDOList中退款金额汇总
            if (isReturnCouponWhenNotZeroPurchase(orderItemCodes, refundItemDO, orderRefundItemDOList, paymentMoney, orderCode, orderItemCode))
                return true;
        }
        return  false;
    }

    private boolean isReturnCouponWhenNotZeroPurchase(List<String> orderItemCodes, OrderRefundItemDO refundItemDO, List<OrderRefundItemDO> orderRefundItemDOList, Integer paymentMoney, String orderCode, String orderItemCode) {
        if(CollectionUtils.isNotEmpty(orderRefundItemDOList)){
            Integer alreadyRefundMoney = orderRefundItemDOMapper.getAllLogisticsAlreadyRefundMoney(orderItemCodes,RefundLogisticsStatusEnum.REFUND_COMPLETED.getCode());
            log.info("退款申请  checkLogisticsAllUsedCouponOrderItemIsRefunded - paymentMoney: {} ,refundMoneyFromItem: {}", paymentMoney,alreadyRefundMoney);
            //统计总退款金额
            List<String> zeroPurchaseItemCodes = getZeroPurchaseItemCodes(orderCode);
            Long aftersalesUnCompletedItemsCount = 0L;
            if(CollectionUtils.isNotEmpty(zeroPurchaseItemCodes)){
                aftersalesUnCompletedItemsCount = getAftersalesUnCompletedCount(zeroPurchaseItemCodes, orderItemCode);
            }
            Integer refundTotalAmount = alreadyRefundMoney+ refundItemDO.getRefundMoney();
            //金额退完且0元购的订单行都退完则退优惠券
            if(paymentMoney.equals(refundTotalAmount) && aftersalesUnCompletedItemsCount.compareTo(0L) ==0){
                return true;
            }
        }
        return false;
    }

    private Long getAftersalesUnCompletedCount(List<String> orderItemCodes, String orderItemCode) {
        Long verifiedCount = orderItemDOMapper.selectCount(new LambdaQueryWrapperX<OrderItemDO>()
                .in(OrderItemDO::getOrderItemCode, orderItemCodes)
                .eq(OrderItemDO::getIsDeleted,Boolean.FALSE)
                .ne(OrderItemDO::getAftersalesStatus,OrderItemAftersalesStatusEnum.COMPLETED.getCode())
                .ne(OrderItemDO::getOrderItemCode, orderItemCode)
        );
        log.info("退款申请  getAftersalesUnCompletedCount - getAftersalesUnCompletedCount: {}",verifiedCount);
        return verifiedCount;
    }


    /**
     * 创建退款行
     * @param itemCode
     * @param orderRefundDO
     * @param realRefundMoney
     * @param realRefundPoints
     * @param refundQuantity
     * @return
     */
    private OrderRefundItemDO getOrderRefundItemDO(String itemCode, OrderRefundDO orderRefundDO,Integer realRefundMoney,Integer realRefundPoints,Integer refundQuantity) {
        OrderRefundItemDO refundItemDO = new OrderRefundItemDO();
        refundItemDO.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
        refundItemDO.setOrderRefundItemCode("R"+itemCode);
        refundItemDO.setRefundMoney(realRefundMoney);
        refundItemDO.setRefundPoint(realRefundPoints);
        refundItemDO.setOrderItemCode(itemCode);
        refundItemDO.setRefundQuantity(refundQuantity);//退单数量
        return refundItemDO;
    }

    private  OrderRefundDO assembleOrderRefundDO(BaseOrderRefundApplyDTO refundApplyDTO, int remainRefundMoney, int operationType,int fulfillmentType){
        OrderRefundDO orderRefundDO = new OrderRefundDO();
        orderRefundDO.setRefundOrderCode(setMaxRefundOrderCode(refundApplyDTO.getOriginOrderCode()));
        orderRefundDO.setRefundMoney(RefundMoneyEnum.REFUNDED.getCode());//退款
        if(StringUtils.isBlank(refundApplyDTO.getRefundMoneyAmount())){
            orderRefundDO.setRefundMoneyAmount(remainRefundMoney);
        }else{
            orderRefundDO.setRefundMoneyAmount(MoneyUtil.convertToCents(refundApplyDTO.getRefundMoneyAmount()).intValue());
        }
        orderRefundDO.setOriginOrderCode(refundApplyDTO.getOriginOrderCode());
        orderRefundDO.setRefundRemark(refundApplyDTO.getRemark());
        String submitUser = StringUtils.isNotBlank(refundApplyDTO.getJlrId())?refundApplyDTO.getJlrId():WebFrameworkUtils.getLoginUserName();
        orderRefundDO.setSubmitUser(StringUtils.isNotBlank(submitUser)?submitUser:Constants.SYSTEM);
        orderRefundDO.setRefundReason(refundApplyDTO.getRefundReason());
        orderRefundDO.setRefundOrderType(refundApplyDTO.getRefundOrderType());
        orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.FULL_REFUND_APPROVE.getCode());
        orderRefundDO.setRefundFulfilmentType(fulfillmentType);
        orderRefundDO.setSupDesc(refundApplyDTO.getSupDesc());
        orderRefundDO.setRefundSource(operationType);
        orderRefundDO.setCreatedBy(StringUtils.isNotBlank(refundApplyDTO.getJlrId())?refundApplyDTO.getJlrId():WebFrameworkUtils.getLoginUserName());
        return orderRefundDO;
    }


    private List<OrderRefundAttachmentDO> getOrderRefundAttachmentList(List<String> attachmentUrls, OrderRefundDO orderRefundDO) {
        if(CollectionUtils.isEmpty(attachmentUrls)){
            return new ArrayList<>();
        }
        List<OrderRefundAttachmentDO> refundAttachmentList = new ArrayList<>();
        for (String attachmentUrl : attachmentUrls) {
            OrderRefundAttachmentDO refundAttachmentDO = new OrderRefundAttachmentDO();
            refundAttachmentDO.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
            refundAttachmentDO.setAttachmentUrl(attachmentUrl);
            refundAttachmentList.add(refundAttachmentDO);
        }
        return refundAttachmentList;
    }

    private String setMaxRefundOrderCode(String originOrderCode) {
        //查询最大RefundOrderCode
        OrderRefundDO maxDO
                = orderRefundMapper.selectOne(new LambdaQueryWrapperX<OrderRefundDO>()
                .eq(OrderRefundDO::getOriginOrderCode, originOrderCode)
                .orderByDesc(OrderRefundDO::getId)
                .last(Constants.LIMIT_ONE));
        String prefix = "RE" + originOrderCode;
        if (maxDO != null && StringUtils.isNotBlank(maxDO.getRefundOrderCode())
                && maxDO.getRefundOrderCode().startsWith(prefix)) {
            prefix = maxDO.getRefundOrderCode();
            return CodeGenerator.nextCode(prefix);
        } else {
            return CodeGenerator.initCodeWithPrefix(prefix);
        }
    }

    /**
     * 作废卡券
     */
    private void cancelCoupon(List<String> couponCodes,String orderRefundCode,String orderCode){
        log.info("退款申请  cancelCoupon - 开始作废卡券, 退款单号: {}, 原订单号: {}, 券码数量: {}",
                orderRefundCode, orderCode, couponCodes.size());
        //调用作废接口
        CouponCancelCmd couponCancelCmd = new CouponCancelCmd();
        couponCancelCmd.setOriginalRequestId(orderCode);
        couponCancelCmd.setRequestId(orderRefundCode);
        couponCancelCmd.setCouponCodes(couponCodes);

        log.info("退款申请  cancelCoupon - 调用卡券作废接口, 参数: {}", JSON.toJSONString(couponCancelCmd));
        CommonResult<List<CouponCancelResp>> result = couponApi.couponCancel(couponCancelCmd);//作废卡券
        log.info("退款申请  cancelCoupon - 卡券作废接口返回结果: {}", result);

        if(result!=null&& result.isSuccess()){
            log.info("退款申请  cancelCoupon - 卡券作废成功, 退款单号: {}", orderRefundCode);
        }else{
            log.error("退款申请  cancelCoupon - 卡券作废失败, 退款单号: {}", orderRefundCode);
            throw exception(ErrorCodeConstants.REFUND_APPLY_FULFILMENT_ERROR);
        }

    }

    public OrderItemDO getOrderItemDo(String orderCode,String itemCode){
        OrderItemDO orderItemDO = orderItemDOMapper.selectOne(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, orderCode)
                .eq(OrderItemDO::getOrderItemCode, itemCode)
                .eq(BaseDO::getIsDeleted, false));
        if(orderItemDO == null){
            throw exception(ErrorCodeConstants.REFUND_APPLY_FULFILMENT_ERROR);
        }
        return orderItemDO;
    }

    public OrderInfoDO getOrderInfoDO(String orderCode) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(new LambdaQueryWrapperX<OrderInfoDO>()
                .eq(OrderInfoDO::getOrderCode, orderCode)
                .eq(BaseDO::getIsDeleted, false));
        if(orderInfoDO == null){
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        return orderInfoDO;
    }

    public OrderRefundDO getOrderRefundDO(String orderRefundCode) {
        OrderRefundDO orderRefundDO = orderRefundMapper.selectOne(new LambdaQueryWrapperX<OrderRefundDO>()
                .eq(OrderRefundDO::getRefundOrderCode, orderRefundCode)
                .eq(BaseDO::getIsDeleted, false));
        if(orderRefundDO == null){
            throw exception(ErrorCodeConstants.ORDER_REFUND_NOT_EXIST);
       }
        return orderRefundDO;
     }

    /**
     * 检查专票类型发票的状态 只有在未开票和已红冲的情况下才允许退款
     * true-开票检查通过 false-开票检查不通过
     * @return
     */
    private boolean checkInvoiceStatus(CommonResult<InvoiceDetailVO> detailVOCommonResult){
        log.info("退款申请  checkInvoiceStatus - 开始检查专票状态 回传对象: {}", detailVOCommonResult);
        if(detailVOCommonResult == null ||detailVOCommonResult.isError()){
            throw exception(ErrorCodeConstants.REFUND_APPLY_FULFILMENT_ERROR);
        }
        if(detailVOCommonResult.getData() == null
                ||(detailVOCommonResult.getData().getPaperInvoiceVO() == null
                && detailVOCommonResult.getData().getESpecialInvoiceVO()==null)){//没有找到专票 则认为不需要红冲 直接返回true
            return true;
        }else{
            boolean paperInvoiceStatus;
            boolean eSpecialInvoiceStatus;
            //如果纸质专票存在则检查纸质专票状态
            if(detailVOCommonResult.getData().getPaperInvoiceVO()!=null){
                paperInvoiceStatus = SpecialInvoiceStatusEnum.REVOKE.getCode().equals(detailVOCommonResult.getData().getPaperInvoiceVO().getInvoiceStatus());
            }else{
                paperInvoiceStatus = true;
            }
            //如果电子专票存在则检查电子专票状态
            if(detailVOCommonResult.getData().getESpecialInvoiceVO()!=null){
                eSpecialInvoiceStatus = SpecialInvoiceStatusEnum.REVOKE.getCode().equals(detailVOCommonResult.getData().getESpecialInvoiceVO().getInvoiceStatus());
            }else{
                eSpecialInvoiceStatus = true;
            }
            return paperInvoiceStatus && eSpecialInvoiceStatus;
        }
    }


    private boolean checkOrderStatus(OrderInfoDO orderInfoDO,Integer operationType) {
        //需要订单状态为已支付
        if (orderInfoDO.getPaymentStatus().equals(PaymentStatusEnum.PAID.getCode())) {
            return true;
        }
        //订单电子券状态为待核销且不是运营发起的退款申请
        return OrderCouponStatusEnum.PENDING_VERIFICATION.getCode().equals(orderInfoDO.getCouponStatus())&&!operationType.equals(RefundOrderOperationTypeEnum.OPERATION_INITIATED.getCode());
    }


    /**
     * 退款金额不能超过剩余退款金额或实付金额
     * @param refundMoneyAmount
     * @param orderItemDO
     * @param remainRefundMoney
     */
    private void checkRefundAmount(int refundMoneyAmount, OrderItemDO orderItemDO,int remainRefundMoney) {
        BigDecimal bigDecimal = MoneyUtil.convertToCents(orderItemDO.getCostAmount().toString());
        //需要退款 但传值退款金额为空 传值金额为空说明是全退的情况

        if(BigDecimal.valueOf(refundMoneyAmount).compareTo(BigDecimal.valueOf(remainRefundMoney)) >0){//退款金额超过剩余金额
            throw exception(ErrorCodeConstants.REMAIN_AMOUNT_NOT_ENOUGH);
        }
        //比较大小
        if (BigDecimal.valueOf(refundMoneyAmount).compareTo(bigDecimal) > 0 ) {//退款金额超过实付金额
            throw exception(ErrorCodeConstants.REFUND_MONEY_OVER_TOTAL);
        }
    }


    /**
     * 构建退款详情
     * @param detailVO
     * @param orderRefundDO
     * @param orderItemDO
     * @param orderRefundItemDO
     */
    public void buildRefundItemDetailVO(BaseRefundItemDetailVO detailVO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO, OrderRefundItemDO orderRefundItemDO) {
        detailVO.setRefundOrderCode(orderRefundDO.getRefundOrderCode());
        detailVO.setOriginOrderCode(orderRefundDO.getOriginOrderCode());
        detailVO.setRefundSource(orderRefundDO.getRefundSource());
        detailVO.setCreatedTime(orderRefundDO.getCreatedTime());
        detailVO.setRefundReason(orderRefundDO.getRefundReason());
        if(orderRefundDO.getRefundFulfilmentType().equals(OrderTypeEnum.ELECTRONIC_COUPON.getCode())){
            detailVO.setRefundReasonStr(CouponRefundReasonEnum.getByCode(orderRefundDO.getRefundReason()).getName());
        } else if (orderRefundDO.getRefundFulfilmentType().equals(OrderTypeEnum.BRAND_GOOD.getCode())) {
            detailVO.setRefundReasonStr(LogisticsRefundReasonEnum.getByCode(orderRefundDO.getRefundReason()).getName());
        }
        detailVO.setRefundPoints(orderRefundItemDO.getRefundPoint());
        detailVO.setSupDesc(orderRefundDO.getSupDesc());
        detailVO.setRefundQuantity(orderRefundItemDO.getRefundQuantity());
        if(orderRefundItemDO.getRefundMoney()!=null){
            detailVO.setRefundMoneyAmount(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderRefundItemDO.getRefundMoney())));
        }
        detailVO.setRefundOrderStatus(orderRefundDO.getRefundOrderStatus());
        detailVO.setRefundStatusSup(orderRefundDO.getRefundStatusSup());
        if(orderRefundDO.getRefundStatusSup()!=null){
            detailVO.setRefundStatusSupStr(RefundStatusSupEnum.getByCode(orderRefundDO.getRefundStatusSup()).getName());
        }
        detailVO.setProductItemInfo(buildProductItemInfoAppVO(orderItemDO));

        //查询退单状态记录
        List<OrderStatusLogDO> statusLogList = statusLogMapper
                .selectList(new LambdaQueryWrapperX<OrderStatusLogDO>()
                        .eq(OrderStatusLogDO::getOrderCode, orderRefundDO.getRefundOrderCode())
                        .eq(BaseDO::getIsDeleted, false)
                        .orderByAsc(OrderStatusLogDO::getId));
        List<RefundDetailLogVO> logList = buildStatusLogList(statusLogList,orderRefundDO.getRefundFulfilmentType());
        detailVO.setLogList(logList);

        //根据refundOrderCode查询附件信息
        List<OrderRefundAttachmentDO> attachmentDOList = orderRefundAttachmentDOMapper.selectList(new LambdaQueryWrapperX<OrderRefundAttachmentDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(OrderRefundAttachmentDO::getRefundOrderCode, orderRefundDO.getRefundOrderCode()));
        if (CollectionUtils.isNotEmpty(attachmentDOList)) {
            List<String> attachmentUrl = attachmentDOList.stream().map(OrderRefundAttachmentDO::getAttachmentUrl).collect(Collectors.toList());
            detailVO.setAttachmentUrls(attachmentUrl);
        }
    }


    /**
     * 构建退单状态记录
     * @param statusLogList
     * @return
     */
    private List<RefundDetailLogVO> buildStatusLogList(List<OrderStatusLogDO> statusLogList,Integer fulfilmentType) {
        List<RefundDetailLogVO> logList = new ArrayList<>();
        for (OrderStatusLogDO statusLog : statusLogList) {
            if(statusLog.getAfterStatus().equals(RefundCouponStatusEnum.SPLIT_REFUND_PROCESSING.getCode())
            ||statusLog.getAfterStatus().equals(RefundLogisticsStatusEnum.SPLIT_REFUND_PROCESSING.getCode())){//分账退款状态不在小程序售后详情中显示
                continue;
            }
            RefundDetailLogVO refundDetailLogVO = new RefundDetailLogVO();
            refundDetailLogVO.setChangeTime(statusLog.getChangeTime());
            refundDetailLogVO.setRefundOrderStatus(statusLog.getAfterStatus());
            if(OrderTypeEnum.ELECTRONIC_COUPON.getCode()==fulfilmentType){
                refundDetailLogVO.setText(RefundCouponStatusEnum.getByCode(statusLog.getAfterStatus()).getNameOnApp());
                refundDetailLogVO.setDetail(RefundCouponStatusEnum.getByCode(statusLog.getAfterStatus()).getDetailDescriptionOnFrontend());
            } else if (OrderTypeEnum.BRAND_GOOD.getCode()==fulfilmentType) {
                refundDetailLogVO.setText(RefundLogisticsStatusEnum.getByCode(statusLog.getAfterStatus()).getNameOnApp());
                refundDetailLogVO.setDetail(RefundLogisticsStatusEnum.getByCode(statusLog.getAfterStatus()).getTextOnApp());
            }
            logList.add(refundDetailLogVO);
        }
        return logList;
    }

    private ProductItemInfoAppVO buildProductItemInfoAppVO(OrderItemDO orderItemDO) {
        ProductItemInfoAppVO productItemInfoAppVO = new ProductItemInfoAppVO();
        productItemInfoAppVO.setProductName(orderItemDO.getProductName());
        productItemInfoAppVO.setProductCode(orderItemDO.getProductCode());
        productItemInfoAppVO.setProductMarketPrice(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderItemDO.getProductMarketPrice()==null?0:orderItemDO.getProductMarketPrice())));
        productItemInfoAppVO.setProductSalePrice(MoneyUtil.convertFromCents(BigDecimal.valueOf(orderItemDO.getProductSalePrice())));
        productItemInfoAppVO.setProductImageUrl(orderItemDO.getProductImageUrl());
        productItemInfoAppVO.setProductQuantity(orderItemDO.getProductQuantity());
        productItemInfoAppVO.setProductAttribute(AttributeUtil.formatProductAttributes(orderItemDO.getProductAttribute()));

        return productItemInfoAppVO;

    }




    // 用于在方法间传递数据的辅助类
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RefundProcessResult {
        private  OrderInfoDO orderInfoDO;
        private  OrderRefundDO orderRefundDO;
        private  OrderItemDO orderItemDO;
        private  OrderRefundItemDO refundItemDO;
        private  List<String> refundCoupons;//剩余可退券的数量
        private OrderPaymentRecordsDO orderPaymentRecordsDO;//支付记录

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class RefundOrderInfoResult{
        private  OrderRefundDO orderRefundDO;
        private  OrderRefundItemDO refundItemDO;
        private  List<String> refundCoupons;//剩余可退券的数量
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class OrderInfoResult{
        private  OrderInfoDO orderInfoDO;
        private  OrderItemDO orderItemDO;
        private OrderPaymentRecordsDO orderPaymentRecordsDO;//支付记录
        private  Integer realRefundMoney;
        private  Integer refundPoints;
    }

    public Integer getMaxRefundMoney(OrderItemDO item, Integer operationType) {
        return calculateMaxRefundMoney(item,operationType);
    }

    /**
     * 获取物流订单的最大可退金额
     * @param item
     * @param orderRefundItemId
     * @return
     */
    public Integer getLogisticsMaxRefundMoney(OrderItemDO item,Long orderRefundItemId) {
        return calculateLogisticsMaxRefundMoney(item,orderRefundItemId);
    }
}

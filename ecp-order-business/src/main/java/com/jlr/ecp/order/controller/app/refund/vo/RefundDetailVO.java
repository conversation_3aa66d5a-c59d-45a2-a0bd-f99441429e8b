package com.jlr.ecp.order.controller.app.refund.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jlr.ecp.order.api.order.vo.detail.ProductItemInfoAppVO;
import com.jlr.ecp.order.api.order.vo.detail.ProductItemInfoVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Schema(description = "App端 - order refund detail VO")
public class RefundDetailVO {

    @Schema(description = "退单订单号")
    private String refundOrderCode;

    @Schema(description = "原始订单号")
    private String originOrderCode;

    @Schema(description = "退款金额")
    private String refundMoneyAmount;

    @Schema(description = "退单状态")
    private Integer refundOrderStatus;

    @Schema(description = "通过创建时间排序方式（asc：升序，desc：降序）")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;

    /**
     * 商品item list
     */
    @Schema(description = "商品信息")
    List<ProductItemInfoAppVO> productItemInfoList;

    @Schema(description = "状态记录")
    List<RefundDetailLogVO> logList;

    @Schema(description = "车机完整信息")
    private IcrVehicleRespVO icrVehicleRespVO;


    @Schema(description = "下单时间")
    private String orderTime;

    /**
     * 客户留言信息
     */
    @Schema(description = "客户留言信息")
    private String customerRemark;


}

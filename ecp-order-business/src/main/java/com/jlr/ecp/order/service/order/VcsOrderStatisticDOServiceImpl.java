package com.jlr.ecp.order.service.order;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppTabsVO;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.app.order.vo.SeriesMappingVO;
import com.jlr.ecp.order.dal.dataobject.order.VcsOrderStatisticDO;
import com.jlr.ecp.order.dal.mysql.order.VcsOrderStatisticDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【t_vcs_order_statistic(t_vcs_order_statistic)】的数据库操作Service实现
 * @createDate 2024-01-31 10:33:22
 */
@Service
@Slf4j
public class VcsOrderStatisticDOServiceImpl extends ServiceImpl<VcsOrderStatisticDOMapper, VcsOrderStatisticDO>
        implements VcsOrderStatisticDOService {

    @Resource
    private VcsOrderStatisticDOMapper vcsOrderStatisticDOMapper;

    @Resource
    private RedisService redisService;

    @Override
    public List<OrderAppTabsVO> getTabs(String consumerCode, String clientId) {
        List<OrderAppTabsVO> list = new ArrayList<>();
        List<VcsOrderStatisticDO> vcsOrderStatisticList = vcsOrderStatisticDOMapper.selectList(new LambdaQueryWrapperX<VcsOrderStatisticDO>()
                .eq(VcsOrderStatisticDO::getConsumerCode, consumerCode)
                //品牌数据隔离
//                .eq(VcsOrderStatisticDO::getBrandCode, clientId)
                .eq(BaseDO::getIsDeleted, false));
        if (CollectionUtils.isEmpty(vcsOrderStatisticList)) {
            return list;
        }
        Set<Object> seriesCodes = vcsOrderStatisticList.stream()
                .map(VcsOrderStatisticDO::getSeriesCode)
                .collect(Collectors.toSet());
        log.info("当前小程序:{},用户:{},seriesCodes:{}", clientId, consumerCode, seriesCodes);

        //一次性拉去redis数据
        List<String> multiCacheMapValue = redisService.getMultiCacheMapValue(Constants.REDIS_KEY.SERIES_CACHE_KEY, seriesCodes);
        List<SeriesMappingVO> mappingVOList = multiCacheMapValue.stream()
                .map(s -> JSON.parseObject(s, SeriesMappingVO.class))
                .collect(Collectors.toList());
        log.info("redis拿到的品牌映射,mappingVOList:{}", mappingVOList);

        if (CollUtil.isEmpty(mappingVOList)) {
            return list;
        }
        //根据SeriesCode
        Map<String, SeriesMappingVO> seriesMapping = mappingVOList.stream()
                // 过滤掉列表中的 null 元素
                .filter(Objects::nonNull)
                // 确保键不为 null
                .filter(vo -> vo.getSeriesCode() != null)
                .collect(Collectors.toMap(SeriesMappingVO::getSeriesCode, Function.identity()));
        log.info("过滤掉列表中null元素，过滤掉key为null。最终seriesMapping:{}", seriesMapping);

        //组装最后数据
        List<VcsOrderStatisticDO> collect = vcsOrderStatisticList.stream().map(vcsOrderStatisticDO -> {
            SeriesMappingVO seriesMappingVO = seriesMapping.get(vcsOrderStatisticDO.getSeriesCode());
            if (seriesMappingVO == null) {
                return null;
            }
            vcsOrderStatisticDO.setSeriesName(seriesMappingVO.getBrandNameView());
            return vcsOrderStatisticDO;
        }).collect(Collectors.toList());

        Map<String, List<VcsOrderStatisticDO>> groupedBySeriesName = collect.stream()
                // 过滤掉seriesName为null的记录
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(VcsOrderStatisticDO::getSeriesName));

        //for循环map组装数据
        for (Map.Entry<String, List<VcsOrderStatisticDO>> entry : groupedBySeriesName.entrySet()) {
            List<String> seriesCodeList = entry.getValue().stream()
                    .map(VcsOrderStatisticDO::getSeriesCode)
                    .collect(Collectors.toList());
            OrderAppTabsVO tabsVO = new OrderAppTabsVO();
            tabsVO.setBrandNameView(entry.getKey());
            tabsVO.setSeriesCodes(seriesCodeList);
            list.add(tabsVO);
        }
        List<String> brandList = Arrays.asList("揽胜", "卫士", "发现");
        list.sort((o1, o2) -> {
            Integer index1 = brandList.indexOf(o1.getBrandNameView());
            Integer index2 = brandList.indexOf(o2.getBrandNameView());
            return index1.compareTo(index2);
        });
        return list;
    }
}





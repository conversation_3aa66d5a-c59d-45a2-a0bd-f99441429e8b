package com.jlr.ecp.order.config;

import cn.hutool.core.lang.Snowflake;
//import org.apache.http.util.Asserts;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SnowFlakeConfig {

    @Value("${dataCenterFlag:null}")
    private String dataCenterFlag;

    @Value("${workFlag}:null")
    private String workFlag;

    private static final long DATA_BIT = 32L;

    @Bean("ecpIdUtil")
    public Snowflake snowflake(RedisService redisService) {
//        Asserts.notEmpty(dataCenterFlag, "dataCenterFlag 需要配置");
//        Asserts.notEmpty(workFlag, "workFlag 需要配置");
        Long dataCenter = redisService.increment(dataCenterFlag);
        long finalDataCenter = dataCenter == null ? 0L : dataCenter % DATA_BIT;

        Long work = redisService.increment(workFlag);
        long finalWorkId = work == null ? 0L : work % DATA_BIT;
        return new Snowflake(finalWorkId, finalDataCenter);
    }
}

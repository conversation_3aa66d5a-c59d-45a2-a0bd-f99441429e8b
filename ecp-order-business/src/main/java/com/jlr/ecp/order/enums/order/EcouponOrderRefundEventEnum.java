package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 * 触发事件：
 * 1：发起退款申请
 * 2：退款处理中
 * 3：退款审核中
 * 4：分账退款中
 * 5: 售后完成
 */
@AllArgsConstructor
@Getter
public enum EcouponOrderRefundEventEnum {


    /****
     * 触发事件：
     * 1：发起退款申请
     * 2：退款处理中
     * 3：退款审核中
     * 4：分账退款中
     * 5: 售后完成
     * 6: 退款审核拒绝
     * 7: 买家撤销申请
     * 8: 主订单关闭
     */
    ECOUPON_REFUND_APPLY(1, "发起退款申请"),
    ECOUPON_REFUND_AUDIT(2, "退款审核同意"),
    ECOUPON_REFUND_PROCESSING(3, "退款处理中"),
    ECOUPON_REFUND_INDEPENDENT(4, "分账退款中"),
    ECOUPON_REFUND_COMPLETED(5, "售后完成"),
    ECOUPON_REFUND_REJECT(6, "退款审核拒绝"),
    ECOUPON_REFUND_BUYER_CANCLE(7, "买家撤销申请"),
    ECOUPON_REFUND_ORDER_CLOSED(8, "主订单关闭");


    /**
     * code
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String description;


}

package com.jlr.ecp.order.enums.order;

import com.jlr.ecp.order.enums.cart.CartItemTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单item商品的类型
 */

@AllArgsConstructor
@Getter
public enum OrderItemSpuTypeEnum {

    /**
     * 普通商品
     */
    NORMAL_GOOD(1, "普通商品"),

    /**
     * 组合商品
     */
    BUNDLE_GOOD(2, "组合商品");

    private final Integer code;

    private final String description;

    public static Integer getSpuType(Integer cartItemType){
        if(CartItemTypeEnum.BUNDLED_GOODS.getCode().equals(cartItemType)){
            return BUNDLE_GOOD.getCode();
        }
        return NORMAL_GOOD.getCode();
    }
}
package com.jlr.ecp.order.service.coupon.status.dto;


import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.dal.dataobject.order.OrderCouponDetailDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @projectName: ecp-order-service
 * @package: com.jlr.ecp.order.service.coupon.status.dto
 * @className: CouponStatusChangedKafkaDto
 * @author: gaoqig
 * @description: 优惠券(LRE商品状态)发生变化时，
 * @date: 2025/3/21 09:42
 * @version: 1.0
 */
@Data
public class CouponStatusChangedKafkaDto {
    /**
     * 优惠券模版code
     */
    private String couponModelCode;

    /**
     * 优惠券code
     */
    private String couponCode;

    /**
     * 优惠券状态
     */
    private Integer couponStatus;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 订单行编号
     */
    private String orderItemCode;

    /**
     * 优惠券状态变化时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户号
     */
    private Long TenantId;

    /**
     * 是否为该订单行下的最后一个核销的券
     * true：是
     */
    private Boolean theLastOrderItemUsed;

    public static String getInstanceJsonStr(OrderCouponDetailDO couponDetailDO, CouponStatusNotifyDto couponStatusNotifyDto){
        CouponStatusChangedKafkaDto couponStatusChangedKafkaDto = new CouponStatusChangedKafkaDto();
        couponStatusChangedKafkaDto.setCouponCode(couponDetailDO.getCouponCode());
        couponStatusChangedKafkaDto.setCouponModelCode(couponDetailDO.getCouponModelCode());
        couponStatusChangedKafkaDto.setCouponStatus(couponStatusNotifyDto.getCouponStatus());
        couponStatusChangedKafkaDto.setOrderCode(couponDetailDO.getOrderCode());
        couponStatusChangedKafkaDto.setOrderItemCode(couponDetailDO.getOrderItemCode());
        couponStatusChangedKafkaDto.setUpdateTime(couponStatusNotifyDto.getUpdateTime());
        couponStatusChangedKafkaDto.setTenantId(TenantContextHolder.getTenantId());
        return JSON.toJSONString(couponStatusChangedKafkaDto);
    }
}

package com.jlr.ecp.order.util.machine.handler.ecoupon;

import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusLogDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.order.OrderCouponStatusEnum;
import com.jlr.ecp.order.enums.order.OrderItemAftersalesStatusEnum;
import com.jlr.ecp.order.enums.order.OrderItemCouponStatusEnum;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import com.jlr.ecp.order.service.coupon.status.dto.OrderStatusChangedKafkaDto;
import com.jlr.ecp.order.util.machine.handler.EcouponEventHandler;
import com.jlr.ecp.order.util.order.OrderAssembleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 关闭主订单
 */
@Component
@Slf4j
public class EcouponRefundOrderClosedHandler implements EcouponEventHandler {

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;
    @Resource
    private OrderStatusLogDOMapper statusLogMapper;

    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Resource
    private OrderRefundItemDOMapper orderRefundItemDOMapper;

    @Resource
    private ProducerTool producerTool;


    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO) {
        //打印各个参数到日志中
        LocalDateTime current = LocalDateTime.now();

        log.info("EcouponRefundOrderClosedHandler handleEvent orderInfoDO before:{},orderRefundDO:{},orderItemDO:{}",orderInfoDO,orderRefundDO,orderItemDO);
        Integer orderStatus = calculateOrderStatus(orderInfoDO);
        log.info("EcouponRefundOrderClosedHandler handleEvent orderStatus :{}",orderStatus);
        OrderStatusLogDO logDO = OrderAssembleUtil.assembleOrderStatusLogDO(orderInfoDO.getOrderCode(),orderInfoDO.getOrderStatus(),orderStatus);
        orderInfoDO.setOrderStatus(orderStatus);
        if(OrderStatusEnum.COMPLETED.getCode().equals(orderStatus)){
            orderInfoDO.setCouponStatus(OrderCouponStatusEnum.VERIFIED.getCode());
            orderInfoDO.setCompletedTime(current);
        }else{
            orderInfoDO.setCouponStatus(OrderCouponStatusEnum.ORDER_CLOSED.getCode());
            orderInfoDO.setClosedTime(current);
        }
        orderInfoDO.setUpdatedTime(current);
        orderInfoDOMapper.updateById(orderInfoDO);
        statusLogMapper.insert(logDO);
        orderItemDO.setAftersalesStatus(OrderItemAftersalesStatusEnum.COMPLETED.getCode());
        orderItemDOMapper.updateById(orderItemDO);
        log.info("EcouponRefundOrderClosedHandler handleEvent orderInfoDO after:{},orderRefundDO:{},orderItemDO:{}",orderInfoDO,orderRefundDO,orderItemDO);
        if(OrderStatusEnum.COMPLETED.getCode().equals(orderStatus)){
            // 注册事务同步器，确保事务提交后执行
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 事务提交后执行的代码，发送kafka消息
                    sendOrderStatusKafkaMessage(orderInfoDO);
                }
            });
        }
    }

    //如果全部款项已经退回,则设置主订单为已关闭,否则设置为已完成
    private Integer calculateOrderStatus(OrderInfoDO orderInfoDO){
        //根据orderCode查询出所有子订单行
        List<OrderItemDO>  orderItemDOList = orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderCode, orderInfoDO.getOrderCode())
                .eq(OrderItemDO::getIsDeleted, Boolean.FALSE)
        );
        log.info("EcouponRefundOrderClosedHandler handleEvent  calculateOrderStatus orderItemDOList:{}",orderItemDOList);
        if(CollectionUtils.isNotEmpty(orderItemDOList)){
            List<String> itemCodeList = orderItemDOList.stream().map(OrderItemDO::getOrderItemCode).collect(Collectors.toList());
            //根据itemCodeList 查询出orderRefundItemDOMapper所有的退单子订单行
            List<OrderRefundItemDO> orderRefundItemDOList = orderRefundItemDOMapper.selectList(new LambdaQueryWrapperX<OrderRefundItemDO>()
                    .in(OrderRefundItemDO::getOrderItemCode,itemCodeList)
                    .eq(OrderRefundItemDO::getIsDeleted,Boolean.FALSE)
            );
            if(CollectionUtils.isNotEmpty(orderRefundItemDOList)){
                Integer refundMoneyFromItem = orderRefundItemDOList.stream().mapToInt(OrderRefundItemDO::getRefundMoney).sum();
                Integer verifiedCount = getVerifiedOrderItem(orderInfoDO.getOrderCode());
                log.info("EcouponRefundOrderClosedHandler handleEvent  calculateOrderStatus refundMoneyFromItem:{} ,verifiedCount:{}",refundMoneyFromItem,verifiedCount);
                if(!(orderInfoDO.getCostAmount().compareTo(refundMoneyFromItem) == 0) || verifiedCount != 0){//如果钱款没有全部退回或存在已核销的订单行则订单完成,反之订单关闭
                     return OrderStatusEnum.COMPLETED.getCode();
                }else{
                    return OrderStatusEnum.CLOSED.getCode();
                }
            }
        }
            return OrderStatusEnum.COMPLETED.getCode();
    }

    private void sendOrderStatusKafkaMessage(OrderInfoDO orderInfoDO){
        log.info("EcouponRefundOrderClosedHandler handleEvent sendOrderStatusKafkaMessage orderInfoDO:{}",orderInfoDO);
        //所有消息状态都要向kafka发送
        String kafkaMessage = null;
        try {
            kafkaMessage = OrderStatusChangedKafkaDto.getOrderStatusChangedKafkaDto(orderInfoDO);
            producerTool.sendMsg(KafkaConstants.ORDER_STATUS_CHANGE_TOPIC,"", kafkaMessage);
            log.info("优惠券状态消息发送成功，优惠券code:{},消息内容{}", orderInfoDO.getOrderCode(), kafkaMessage);
        } catch (Exception e) {
            log.info("优惠券状态消息发送失败，优惠券code:{},消息内容{}", orderInfoDO.getOrderCode(), kafkaMessage);

        }
    }

    /**
     * 逻辑到这里CouponStatus只会是已核销或者已回收
     * @param orderCode
     * @return
     */
    private Integer getVerifiedOrderItem(String orderCode){
        List<OrderItemDO> orderItemDOS =orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(BaseDO::getIsDeleted, Boolean.FALSE)
                .eq(OrderItemDO::getOrderCode, orderCode)
                .eq(OrderItemDO::getItemStatus, OrderItemCouponStatusEnum.VERIFIED.getCode())
        );
        if(CollectionUtils.isNotEmpty(orderItemDOS)){
            return orderItemDOS.size();
        }
        return 0;
    }
}

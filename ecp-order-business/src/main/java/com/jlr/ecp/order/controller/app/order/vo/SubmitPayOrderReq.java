package com.jlr.ecp.order.controller.app.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;

/**
 * 提交支付订单req
 *
 * <AUTHOR>
 * @date 2024/12/02
 */
@Schema(description = "提交支付订单req")
@AllArgsConstructor
@NoArgsConstructor
@Data
@SuperBuilder
@Accessors(chain = true)
public class SubmitPayOrderReq {

    /**
     * 应用编号
     */
    @Schema(description = "应用编号")
    private String appNo;
    /**
     * 支付渠道，CUSC-JAGUAR，CUSC-LANDROVER
     */
    @Schema(description = "支付渠道，CUSC-JAGUAR，CUSC-LANDROVER")
    @NotBlank(message = "支付渠道不能为空")
    private String channelCode;
    /**
     * 支付渠道的额外参数，例如说，微信公众号需要传递 openid 参数
     */
    @Schema(description = "支付渠道的额外参数，例如说，微信公众号需要传递 openid 参数")
    private Map<String, Object> channelExtras;
    /**
     * JLRID
     */
    @Schema(description = "JLRID")
    @NotBlank(message = "JLRID不能为空")
    private String jlrid;
    /**
     * 业务订单号
     */
    @Schema(description = "业务订单号")
    @NotBlank(message = "业务订单号不能为空")
    private String orderNo;
    /**
     * 商品信息
     */
    @Schema(description = "商品信息")
    @NotEmpty(message = "商品信息不能为空")
    private List<@Valid OrderItem> orderItems;
    /**
     * 支付金额，单位分
     */
    @Schema(description = "支付金额，单位分")
    @NotNull(message = "支付金额不能为空")
    @Min(value = 0, message = "支付金额必须大于0")
    private Long payAmount;
    /**
     * 支付方式,，ALIPAY("支付宝支付"), WXPAY("微信支付"), CHINAPAY("银联支付");
     */
    @Schema(description = "支付方式,，ALIPAY(\"支付宝支付\"), WXPAY(\"微信支付\"), CHINAPAY(\"银联支付\");")
    @NotBlank(message = "支付方式不能为空")
    @Pattern(regexp = "^(WXPAY)$", message = "支付方式格式不正确")
    private String payMethod;

    /**
     * 支付类型，CASH：现金支付 COMBINE：组合支付 POINTS：积分抵扣
     */
    @Schema(description = "支付类型，CASH：现金支付 COMBINE：组合支付 POINTS：积分抵扣")
    @NotBlank(message = "支付类型不能为空")
    @Pattern(regexp = "^(CASH)", message = "支付类型格式不正确")
    private String payType;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;


    /**
     * 订单标题
     */
    @Schema(description = "订单标题")
    @NotBlank(message = "订单标题不能为空")
    private String orderTitle;


    /**
     * 订购项目
     *
     * <AUTHOR>
     * @date 2024/12/02
     */
    @Schema(description = "订购项目")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Accessors(chain = true)
    public static class OrderItem {
        /**
         * 积分券码
         */
        @Schema(description = "积分券码")
        private String couponCode;
        /**
         * 明细类型，CASH:现金 ，POINTS：积分，COUPON:优惠券
         */
        @Schema(description = "明细类型，CASH:现金 ，POINTS：积分，COUPON:优惠券")
        @NotBlank(message = "明细类型不能为空")
        private String itemType;
        /**
         * 明细数值，现金单位分，积分值，券无value值
         */
        @Schema(description = "明细数值，现金单位分，积分值，券无value值")
        @Min(value = 1, message = "明细数值不能小于1")
        private Long itemValue;
        /**
         * 商品支付金额，单位分
         */
        @Schema(description = "商品支付金额，单位分")
        @Min(value = 0, message = "商品支付金额必须大于0")
        @NotNull
        private Long payAmount;
        /**
         * 商品名称
         */
        @Schema(description = "商品名称")
        @NotBlank(message = "商品名称不能为空")
        private String productName;
        /**
         * 商品编号
         */
        @Schema(description = "商品编号")
        @NotBlank(message = "商品编号不能为空")
        private String productNo;

    }




}

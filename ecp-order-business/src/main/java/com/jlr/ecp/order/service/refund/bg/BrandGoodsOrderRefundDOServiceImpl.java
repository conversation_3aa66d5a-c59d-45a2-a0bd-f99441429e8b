package com.jlr.ecp.order.service.refund.bg;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.order.api.refund.dto.BaseOrderRefundApplyDTO;
import com.jlr.ecp.order.api.refund.dto.LogisticsOrderRefundApproveDTO;
import com.jlr.ecp.order.api.refund.dto.LogisticsOrderReturnApproveDTO;
import com.jlr.ecp.order.api.refund.dto.OrderRefundLogisticsDTO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.app.refund.vo.LogisticsRefundItemDetailVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemLogisticsDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderPaymentRecordsDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderPaymentRecordsMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.order.LogisticsOrderRefundEventEnum;
import com.jlr.ecp.order.enums.refund.RefundLogisticsStatusEnum;
import com.jlr.ecp.order.enums.refund.RefundOrderTypeEnum;
import com.jlr.ecp.order.handle.RefundHandler;
import com.jlr.ecp.order.kafka.BaseOrderRefundSuccessMessage;
import com.jlr.ecp.order.kafka.producer.BrandedGoodsNotificationProducer;
import com.jlr.ecp.order.service.order.OrderItemLogisticsDOService;
import com.jlr.ecp.order.util.PIPLDataUtil;
import com.jlr.ecp.order.util.RedisReentrantLockUtil;
import com.jlr.ecp.order.util.machine.LogisticsOrderRefundStatusMachine;
import com.jlr.ecp.order.util.money.MoneyUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @description 虚拟电子券类型退款
 * @createDate 20250306
 */
@Service
@Slf4j
public class BrandGoodsOrderRefundDOServiceImpl extends ServiceImpl<OrderRefundDOMapper, OrderRefundDO>
        implements BrandGoodsOrderRefundDOService {

    @Resource
    private RefundHandler refundHandler;
    @Resource
    private OrderRefundItemDOMapper orderRefundItemDOMapper;
    @Resource
    private LogisticsOrderRefundStatusMachine refundStatusMachine;
    @Resource
    private OrderItemLogisticsDOService orderItemLogisticsDOService;
    @Resource
    private PIPLDataUtil piplDataUtil;
    @Resource
    private BrandedGoodsNotificationProducer notificationProducer;

    @Resource
    private OrderPaymentRecordsMapper orderPaymentRecordsMapper;

    @Resource
    private RedisReentrantLockUtil redisReentrantLockUtil;

    @Resource
    private Redisson redisson;


    @Override
    public String logisticsOrderRefundApply(List<BaseOrderRefundApplyDTO> refundApplyDTOs, Integer operationType) {
        if(refundApplyDTOs.get(0).getRefundOrderType().equals(RefundOrderTypeEnum.REFUND_ONLY.getCode())){
            return refundHandler.refundProcess(refundApplyDTOs, operationType);
        }else if(refundApplyDTOs.get(0).getRefundOrderType().equals(RefundOrderTypeEnum.REFUND_RETURN_GOODS.getCode())){
            return refundHandler.returnProcess(refundApplyDTOs, operationType);
        }else {
            return  "";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    public String logisticsOrderRefundAuditApprove(LogisticsOrderRefundApproveDTO orderRefundApproveDTO) {

        log.info("退款审核  refundProcess - 开始");
        //1.1 加锁 防止重复提交
        String idempotentKey =Constants.BG_REFUND_IDEMPOTENT_KEY +":"+orderRefundApproveDTO.getRefundOrderCode();
        RLock rLock = redisson.getLock(idempotentKey);
        //try catch异常
        try {
            log.info("退款审核  refundProcess -加锁 防止重复提交, RLock: {}", rLock);
            if (!redisReentrantLockUtil.tryLock(rLock, 10, 60, TimeUnit.SECONDS)) {
                log.warn("退款审核  refundProcess - 重复提交, RLock: {}", rLock);
                throw exception(ErrorCodeConstants.REPEAT_SUBMISSION);
            }
            RefundApproveResult result = getRefundApproveResult(orderRefundApproveDTO.getRefundOrderCode(),orderRefundApproveDTO.getOrderItemCode());
            int event;
            OrderRefundDO  orderRefundDO = result.getOrderRefundDO();
            //检查退款单的状态,不是待退款审核，则报错
            if (RefundLogisticsStatusEnum.PENDING_REFUND_REVIEW.getCode() != orderRefundDO.getLogisticsRefundStatus()) {
                log.error("退款审核  refundProcess - 退款单状态不为待退款审核, orderRefundDO: {}", orderRefundDO);
                throw exception(ErrorCodeConstants.REFUND_APPROVE_STATUS_ERROR);
            }
            if(orderRefundApproveDTO.getApproveStatus()) {//审核通过则进入退款处理流程
                //检查专票状态
                log.info("退款审核 检查专票状态");
                refundHandler.validInoviceStatus(orderRefundDO.getOriginOrderCode());
                //根据OrderRefundCode查询退款单
                OrderRefundItemDO orderRefundItemDO  = getOrderRefundItemByOrderRefundCode(orderRefundApproveDTO.getRefundOrderCode());
                Integer maxRemainRefundMoney = refundHandler.getLogisticsMaxRefundMoney(result.getOrderItemDO(),orderRefundItemDO.getId());
                Integer refundAmount = MoneyUtil.convertToCents(orderRefundApproveDTO.getRefundAmount()).intValue();
                if(refundAmount > maxRemainRefundMoney){
                    throw exception(ErrorCodeConstants.REMAIN_AMOUNT_NOT_ENOUGH);
                }
                //更新退款金额
                orderRefundItemDO.setRefundMoney(refundAmount);
                orderRefundItemDOMapper.updateById(orderRefundItemDO);
                orderRefundDO.setRefundAuditRemark(orderRefundApproveDTO.getAuditReason());
                orderRefundDO.setRefundMoneyAmount(refundAmount);
                OrderPaymentRecordsDO orderPaymentRecordsDO = orderPaymentRecordsMapper.queryByOrderCode(result.getOrderInfoDO().getOrderCode(),result.getOrderInfoDO().getParentOrderCode());
                RefundHandler.RefundProcessResult refundProcessResult = new RefundHandler.RefundProcessResult(result.getOrderInfoDO(),orderRefundDO,result.getOrderItemDO(),orderRefundItemDO,null,orderPaymentRecordsDO);
                //调用退款申请
                refundHandler.processExternalServices(refundProcessResult);
            }else{
                event = LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_REJECT.getCode();
                orderRefundDO.setRefundAuditRemark(orderRefundApproveDTO.getAuditReason());
                result.setOrderRefundDO(orderRefundDO);
                refundStatusMachine.changeOrderStatus(event, result.getOrderInfoDO(), result.getOrderRefundDO(), result.getOrderItemDO());
            }
        }catch (Exception e){
            log.error("退货退款申请  logisticsOrderRefundAuditApprove - 处理退款异常: {}", e.getMessage(), e);
            throw e;//抛异常确保事务正常回滚
        }finally {
            redisReentrantLockUtil.unlock(rLock, 3);//释放锁
        }
        return orderRefundApproveDTO.getRefundOrderCode();
    }

    @Override
    public String logisticsOrderReturnAuditApprove(LogisticsOrderReturnApproveDTO orderRefundApproveDTO) {
        RefundApproveResult result = getRefundApproveResult(orderRefundApproveDTO.getRefundOrderCode(),orderRefundApproveDTO.getOrderItemCode());
        int event;
        OrderRefundDO  orderRefundDO = result.getOrderRefundDO();
        if (RefundLogisticsStatusEnum.PENDING_RETURN_REVIEW.getCode() != orderRefundDO.getLogisticsRefundStatus()) {
            log.error("退款审核  refundProcess - 退款单状态不为待退货审核, orderRefundDO: {}", orderRefundDO);
            throw exception(ErrorCodeConstants.RETURN_APPROVE_STATUS_ERROR);
        }
        if(orderRefundApproveDTO.getApproveStatus()) {
            //检查退款单状态,如果已经是待商品寄回则报错
            log.info("退款审核 检查专票状态");
            refundHandler.validInoviceStatus(orderRefundDO.getOriginOrderCode());
            event = LogisticsOrderRefundEventEnum.LOGISTICS_RETURN_AUDIT.getCode();
            //这里catch异常 确保事务不会回滚
            try {
                //发送短信通知
                BaseOrderRefundSuccessMessage baseOrderRefundSuccessMessage = refundHandler.buildBaseOrderRefundSuccessMessage(result.getOrderInfoDO(),result.getOrderRefundDO(),result.getOrderItemDO());
                notificationProducer.sendReturnRefundNotification(baseOrderRefundSuccessMessage.getBgorderNumber(),baseOrderRefundSuccessMessage.getPhoneNumber(), baseOrderRefundSuccessMessage.getWxUrl());
                log.info("退货退款申请  refundProcess - 发送短信通知成功");
            }catch (Exception e){
                log.info("退货退款申请  refundProcess - 发送短信通知异常: {}", e.getMessage(), e);
            }
        }else{
            event = LogisticsOrderRefundEventEnum.LOGISTICS_RETURN_REJECT.getCode();
        }
        //设置退款备注(同意和拒绝用的是同一个字段)
        orderRefundDO.setReturnAuditRemark(orderRefundApproveDTO.getAuditReason());
        result.setOrderRefundDO(orderRefundDO);
        refundStatusMachine.changeOrderStatus(event, result.getOrderInfoDO(), result.getOrderRefundDO(), result.getOrderItemDO());
        return orderRefundApproveDTO.getRefundOrderCode();
    }

    @Override
    public String logisticsOrderRefundUserCancel(LogisticsOrderRefundApproveDTO orderRefundApproveDTO) {
        RefundApproveResult result = getRefundApproveResult(orderRefundApproveDTO.getRefundOrderCode(),orderRefundApproveDTO.getOrderItemCode());
        if(!RefundLogisticsStatusEnum.isPendingReturnOrPendingItemReturn(result.getOrderRefundDO().getLogisticsRefundStatus())){
            throw exception(ErrorCodeConstants.USER_CANCEL_STATUS_ERROR);
        }
        int event = LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_BUYER_CANCEL.getCode();
        refundStatusMachine.changeOrderStatus(event, result.getOrderInfoDO(), result.getOrderRefundDO(), result.getOrderItemDO());
        return orderRefundApproveDTO.getRefundOrderCode();
    }

    @Override
    public String submitLogisticsInfo(OrderRefundLogisticsDTO orderRefundLogisticsDTO) {
        RefundApproveResult result = getRefundApproveResult(orderRefundLogisticsDTO.getRefundOrderCode(),orderRefundLogisticsDTO.getRefundOrderItemCode());
        OrderRefundDO orderRefundDO = result.getOrderRefundDO();
        setLogisticsInfo(orderRefundDO,orderRefundLogisticsDTO);
        int event = LogisticsOrderRefundEventEnum.LOGISTICS_REFUND_AUDIT.getCode();;
        refundStatusMachine.changeOrderStatus(event, result.getOrderInfoDO(), orderRefundDO, result.getOrderItemDO());
        return orderRefundLogisticsDTO.getRefundOrderCode();
    }

    @Override
    public LogisticsRefundItemDetailVO getLogisticsRefundItemDetail(String orderItemCode, String orderRefundCode) {
        LogisticsRefundItemDetailVO logisticsRefundItemDetailVO = new LogisticsRefundItemDetailVO();
        RefundApproveResult result = getRefundApproveResult(orderRefundCode,orderItemCode);
        OrderRefundItemDO orderRefundItemDO=getOrderRefundItemByOrderRefundCode(orderRefundCode);
        refundHandler.buildRefundItemDetailVO(logisticsRefundItemDetailVO,result.getOrderRefundDO(),result.getOrderItemDO(),orderRefundItemDO);
        buildLogisticsInfo(logisticsRefundItemDetailVO,result.getOrderRefundDO());
        buildContactInfo(logisticsRefundItemDetailVO,orderItemCode);
        buildReceiverInfo(logisticsRefundItemDetailVO);
        buildSendTime(logisticsRefundItemDetailVO,orderItemCode);
        return logisticsRefundItemDetailVO;
    }

    private void buildSendTime(LogisticsRefundItemDetailVO logisticsRefundItemDetailVO,String orderItemCode){
        //根据orderItemCode查找物流单的发货时间
        OrderItemLogisticsDO orderItemLogisticsDO = orderItemLogisticsDOService.getOne(new LambdaQueryWrapper<OrderItemLogisticsDO>()
                .eq(OrderItemLogisticsDO::getOrderItemCode, orderItemCode)
                .eq(OrderItemLogisticsDO::getIsDeleted, Boolean.FALSE)
                .orderByDesc(OrderItemLogisticsDO::getId)
                .last(Constants.LIMIT_ONE));
        if(ObjectUtils.isNotEmpty(orderItemLogisticsDO)){
            logisticsRefundItemDetailVO.setSendTime(orderItemLogisticsDO.getSendTime());
        }
    }

    private void buildReceiverInfo(LogisticsRefundItemDetailVO logisticsRefundItemDetailVO){
        logisticsRefundItemDetailVO.setReceiverName("捷豹路虎中国小程序电商");
        logisticsRefundItemDetailVO.setReceiverPhone("18117116220");
        logisticsRefundItemDetailVO.setReceiverAddress("上海市上海城区宝山区真陈路1490号内4幢电梯5楼");
    }

    private void buildContactInfo(LogisticsRefundItemDetailVO logisticsRefundItemDetailVO,String orderItemCode){
        //用LambdaQueryWrapper根据orderItemCode从orderItemLogisticsDOService中查询一条记录
        OrderItemLogisticsDO orderItemLogisticsDO = orderItemLogisticsDOService.getOne(new LambdaQueryWrapper<OrderItemLogisticsDO>()
                .eq(OrderItemLogisticsDO::getOrderItemCode, orderItemCode)
                .eq(OrderItemLogisticsDO::getIsDeleted, Boolean.FALSE)
                .orderByDesc(OrderItemLogisticsDO::getId)
                .last(Constants.LIMIT_ONE));
        if(ObjectUtils.isNotEmpty(orderItemLogisticsDO)){
            //解密相关字段
            logisticsRefundItemDetailVO.setContactPhone(piplDataUtil.getDecodeText(orderItemLogisticsDO.getRecipientPhone()));
            logisticsRefundItemDetailVO.setContactName(piplDataUtil.getDecodeText(orderItemLogisticsDO.getRecipient()));
            logisticsRefundItemDetailVO.setContactAddress(piplDataUtil.getDecodeText(orderItemLogisticsDO.getFullDetailAddress()));
        }

    }

    /**
     * 组装物流退款详细信息
     * @param logisticsRefundItemDetailVO
     * @param orderRefundDO
     */
    private void buildLogisticsInfo(LogisticsRefundItemDetailVO logisticsRefundItemDetailVO,OrderRefundDO orderRefundDO){
        //通过orderRefundDO获取物流相关信息、退款审核备注和退货审核备注
        logisticsRefundItemDetailVO.setReturnAuditRemark(orderRefundDO.getReturnAuditRemark());
        logisticsRefundItemDetailVO.setRefundAuditRemark(orderRefundDO.getRefundAuditRemark());
        logisticsRefundItemDetailVO.setLogisticsCode(orderRefundDO.getLogisticsCode());
        logisticsRefundItemDetailVO.setLogisticsCompanyCode(orderRefundDO.getLogisticsCompanyCode());
        logisticsRefundItemDetailVO.setLogisticsCompanyName(orderRefundDO.getLogisticsCompanyName());
        if(StringUtils.isNotBlank(orderRefundDO.getLogisticsAttachment())){
            //将,分隔开的字符串转换成List
            logisticsRefundItemDetailVO.setAttachmentUrls(Arrays.asList(orderRefundDO.getLogisticsAttachment().split(",")));
        }
    }

    /**
     * 设置退款单物流信息
     * @param orderRefundDO
     * @param orderRefundLogisticsDTO
     */
    private void setLogisticsInfo(OrderRefundDO orderRefundDO,OrderRefundLogisticsDTO orderRefundLogisticsDTO){
        orderRefundDO.setLogisticsCode(orderRefundLogisticsDTO.getLogisticsCode());
        orderRefundDO.setLogisticsCompanyCode(orderRefundLogisticsDTO.getLogisticsCompanyCode());
        orderRefundDO.setLogisticsCompanyName(orderRefundLogisticsDTO.getLogisticsCompanyName());
        if(CollectionUtils.isNotEmpty(orderRefundLogisticsDTO.getAttachmentUrls())){
            //将List转换成,分隔的字符串
            orderRefundDO.setLogisticsAttachment(String.join(",",orderRefundLogisticsDTO.getAttachmentUrls()));
        }
    }

    private RefundApproveResult getRefundApproveResult(String orderRefundCode,String OrderItemCode) {
        //根据refund
        OrderRefundDO orderRefundDO = refundHandler.getOrderRefundDO(orderRefundCode);
        OrderInfoDO orderInfoDO = refundHandler.getOrderInfoDO(orderRefundDO.getOriginOrderCode());
        OrderItemDO orderItemDO = refundHandler.getOrderItemDo(orderRefundDO.getOriginOrderCode(),OrderItemCode);
        RefundApproveResult result = new RefundApproveResult(orderInfoDO,orderRefundDO,orderItemDO);
        return result;
    }


    private OrderRefundItemDO getOrderRefundItemByOrderRefundCode(String orderRefundCode){
        OrderRefundItemDO orderRefundItemDOList = orderRefundItemDOMapper.selectOne(new LambdaQueryWrapper<OrderRefundItemDO>()
                .eq(OrderRefundItemDO::getRefundOrderCode,orderRefundCode)
                .eq(OrderRefundItemDO::getIsDeleted,Boolean.FALSE)
                .orderByDesc(OrderRefundItemDO::getId)
                .last(Constants.LIMIT_ONE));
        if(ObjectUtils.isEmpty(orderRefundItemDOList)){
            return new OrderRefundItemDO();
        }
        return orderRefundItemDOList;
    };

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class RefundApproveResult {
        private  OrderInfoDO orderInfoDO;
        private  OrderRefundDO orderRefundDO;
        private  OrderItemDO orderItemDO;

    }
}





package com.jlr.ecp.order.controller.app.order.vo;

import lombok.Data;

import java.util.List;

/**
 * OrderLogisticsQryRespVO
 *
 * <AUTHOR> <PERSON>
 * @since 2025-04-03 14:48
 */
@Data
public class OrderLogisticsQryRespVO {

    /**
     * 订单 Code
     */
    private String orderCode;

    /**
     * 收件人姓名
     */
    private String recipient;

    /**
     * 收件人手机号码
     */
    private String recipientPhone;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String are;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 物流公司code
     */
    private String company;

    /**
     * 物流公司名称
     */
    private String companyName;

    /**
     * 物流单号
     */
    private String number;

    /**
     * 物流详细信息
     */
    private List<LogisticsDetailItemVO> detailItemList;

}

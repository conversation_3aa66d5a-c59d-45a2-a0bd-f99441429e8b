package com.jlr.ecp.order.enums.cart;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * ProductSkuStatusEnum
 *
 * <AUTHOR> <PERSON>
 * @since 2025-03-22 14:21:18
 */
@AllArgsConstructor
@Getter
public enum ProductSkuStatusEnum {

    /**
     * 已上架
     */
    UP(0,"已上架"),
    /**
     * 已下架
     */
    DOWN(1,"已下架");

    /**
     * 商品上下架状态编码
     */
    private final Integer code;

    /**
     * 名称
     */
    private final String name;

}

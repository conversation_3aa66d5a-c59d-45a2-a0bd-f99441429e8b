package com.jlr.ecp.order.service.independent;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.order.api.independent.dto.TriggerIndependentReqDTO;
import com.jlr.ecp.order.api.independent.dto.UpdateStatusToSuccReqDTO;
import com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentDO;
import com.jlr.ecp.order.service.coupon.status.dto.CouponStatusChangedKafkaDto;

/**
* <AUTHOR> Hongyi
*/
public interface OrderIndependentDOService extends IService<OrderIndependentDO> {

    /**
     * 触发类型为“券核销场景”的分账
     */
    Boolean triggerIndependentForCouponUsed(CouponStatusChangedKafkaDto message);

    /**
     * 触发类型为“订单完成”的分账
     */
    Boolean triggerIndependentForOrderSucc(TriggerIndependentReqDTO req);

    /**
     * 分账任务重试，为了在下游出现错误时能重试
     */
    Boolean triggerIndependentForRetry(TriggerIndependentReqDTO req);

    /**
     * 更新分账状态为分账成功
     * @param req
     * @return
     */
    Boolean updateStatusToSucc(UpdateStatusToSuccReqDTO req);

}

package com.jlr.ecp.order.controller.admin.dashboard.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MultiChoiceBarChartRespVo {
    private String title; // 标题
    private List<String> xAxisData; // X轴数据（选项）
    private List<BigDecimal> seriesData; // 柱状图数据（每个选项的统计值）
}

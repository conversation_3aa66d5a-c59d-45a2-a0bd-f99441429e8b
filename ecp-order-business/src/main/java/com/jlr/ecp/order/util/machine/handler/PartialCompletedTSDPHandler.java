package com.jlr.ecp.order.util.machine.handler;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import org.springframework.stereotype.Component;

/**
 * 完成部分退单事件处理
 * <AUTHOR>
 */
@Component
public class PartialCompletedTSDPHandler implements EventHandler {
    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO) {
        // 完成部分退单事件处理
        orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.PARTIAL_REFUND_COMPLETED.getCode());
        orderInfoDO.setOrderStatus(OrderStatusEnum.PARTIAL_CANCELLED.getCode());
    }
}

package com.jlr.ecp.order.enums.dashboard;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum KpiEnumForValet {

    /**
     * 总订单数
     */
    TOTAL_ORDER_NUMBER(1, "总订单数",  "Total On-behalf of Order Counts", "代客下单总订单数", "pcs"),

    /**
     * 成交订单数
     */
    DEAL_ORDER_NUMBER(2, "成交订单数", "Turnover On-behalf of Order Counts", "代客下单成交订单数", "pcs"),

    /**
     * 总订单交易额
     */
    TOTAL_ORDER_AMOUNT(3, "总订单交易额", "Turnover On-behalf of Order Transactional Volume","代客下单成交订单总交易额", "RMB"),

    /**
     * 总收入
     */
    INCOME(4, "总收入", "Total On-behalf of Income","代客下单总收入", "RMB"),

    /**
     * 总退款
     */
    REFUND(5, "总退款", "Total On-behalf of Refund Volume","代客下单总退款额", "RMB"),

    /**
     * 成交客单价
     */
    UNIT_PRICE_PER_CUSTOMER(6, "成交客单价", "ATV for On-behalf of","代客下单成交客单价", "RMB");

    /**
     * 成交商品总数
     */
//    TOTAL_GOODS_NUMBER(7, "成交商品总数", "Turnover Product Counts","代客下单成交商品总数", "pcs");

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型名称
     */
    private final String desc;

    /**
     * 类型名称英文
     */
    private final String descEn;

    /**
     * 类型名称 -for 代客下单
     */
    private final String descForValet;

    /**
     * 单位名称
     */
    private final String unit;
}
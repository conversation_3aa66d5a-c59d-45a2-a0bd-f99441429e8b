package com.jlr.ecp.order.enums.dashboard;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum TypeFilterEnum {

    /**
     * 渠道
     */
    CHANNEL(1, "渠道"),

    /**
     * 车型
     */
    VEHICLE_MODEL(2, "车型"),

    /**
     * 商品
     */
    PRODUCT(3, "商品");

    /**
     * 类型编码
     */
    private final Integer code;

    /**
     * 类型名称
     */
    private final String desc;
}
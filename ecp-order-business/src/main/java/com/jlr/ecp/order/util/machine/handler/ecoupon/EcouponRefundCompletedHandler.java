package com.jlr.ecp.order.util.machine.handler.ecoupon;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.dal.dataobject.order.OrderCouponDetailDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusLogDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.mysql.order.OrderCouponDetailDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusLogDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.enums.order.*;
import com.jlr.ecp.order.enums.refund.OrderRefundStatusEnum;
import com.jlr.ecp.order.util.machine.EcouponOrderRefundStatusMachine;
import com.jlr.ecp.order.util.machine.handler.EcouponEventHandler;
import com.jlr.ecp.order.util.machine.handler.EventHandler;
import com.jlr.ecp.order.util.order.OrderAssembleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 退款完成
 */
@Component
@Slf4j
public class EcouponRefundCompletedHandler implements EcouponEventHandler {
    @Resource
    private OrderRefundDOMapper orderRefundDOMapper;

    @Resource
    private OrderItemDOMapper orderItemDOMapper;

    @Resource
    private OrderStatusLogDOMapper statusLogMapper;

    @Resource
    @Lazy
    private EcouponOrderRefundStatusMachine ecouponOrderRefundStatusMachine;

    @Resource
    private OrderCouponDetailDOMapper orderCouponDetailDOMapper;

    @Override
    public void handleEvent(OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO, OrderItemDO orderItemDO) {
        //打印各个参数到日志中
        log.info("EcouponRefundCompletedHandler handleEvent orderInfoDO:{},orderRefundDO:{},orderItemDO:{}",orderInfoDO,orderRefundDO,orderItemDO);
        OrderStatusLogDO logDO = OrderAssembleUtil.assembleOrderStatusLogDO(orderRefundDO.getRefundOrderCode(),orderRefundDO.getCouponRefundStatus(),RefundCouponStatusEnum.REFUND_COMPLETED.getCode());
        orderRefundDO.setCouponRefundStatus(RefundCouponStatusEnum.REFUND_COMPLETED.getCode());
        orderRefundDO.setRefundOrderStatus(OrderRefundStatusEnum.FULL_REFUND_COMPLETED.getCode());
        orderItemDO.setAftersalesStatus(OrderItemAftersalesStatusEnum.COMPLETED.getCode());
        //如果有券已核销则订单行状态为已经核销否则为已回收
        Long verifiedCouponCount = orderCouponDetailDOMapper.getCouponCountByStatus(orderItemDO.getOrderItemCode(),List.of(EcouponStatusEnum.VERIFIED.getCode()));
        log.info("EcouponRefundCompletedHandler handleEvent verifiedCouponCount:{}",verifiedCouponCount);
        if(verifiedCouponCount == null || verifiedCouponCount==0L){
            orderItemDO.setItemStatus(OrderItemCouponStatusEnum.RECLAIMED.getCode());
        }else{
            orderItemDO.setItemStatus(OrderItemCouponStatusEnum.VERIFIED.getCode());
        }
        orderRefundDOMapper.updateById(orderRefundDO);
        orderItemDOMapper.updateById(orderItemDO);
        statusLogMapper.insert(logDO);
        if(checkIfAllRefundCompleted(orderItemDO)){//如果子订单全部是售后关闭或者完成状态 则设置主订单为已关闭或已完成
            log.info("EcouponRefundCompletedHandler handleEvent  checkIfAllRefundCompleted true");
            int event = EcouponOrderRefundEventEnum.ECOUPON_REFUND_ORDER_CLOSED.getCode();
            ecouponOrderRefundStatusMachine.changeOrderStatus(event,orderInfoDO,orderRefundDO,orderItemDO);
        }
    }

    /**
     * 检查是否所有子订单都是已退款 是的话需要设置主订单为已关闭
     * @param orderItemDO
     * @return
     */
    private boolean checkIfAllRefundCompleted(OrderItemDO orderItemDO){
        log.info("EcouponRefundCompletedHandler handleEvent  checkIfAllRefundCompleted orderItemDO:{}",orderItemDO);
        List<Integer> completedStatus = new ArrayList<>();
        completedStatus.add(OrderItemCouponStatusEnum.VERIFIED.getCode());
        completedStatus.add(OrderItemCouponStatusEnum.RECLAIMED.getCode());
        List<OrderItemDO> orderItemAftersalesDOS =orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(BaseDO::getIsDeleted, Boolean.FALSE)
                .eq(OrderItemDO::getOrderCode, orderItemDO.getOrderCode())
                .in(OrderItemDO::getItemStatus, completedStatus)
                );
        log.info("EcouponRefundCompletedHandler handleEvent  checkIfAllRefundCompleted orderItemAftersalesDOS:{}",orderItemAftersalesDOS);
        List<OrderItemDO> orderItemDOS =orderItemDOMapper.selectList(new LambdaQueryWrapperX<OrderItemDO>()
                .eq(BaseDO::getIsDeleted, Boolean.FALSE)
                .eq(OrderItemDO::getOrderCode, orderItemDO.getOrderCode())
        );
        log.info("EcouponRefundCompletedHandler handleEvent  checkIfAllRefundCompleted orderItemDOS:{}",orderItemDOS);
        if(CollectionUtils.isNotEmpty(orderItemDOS) && CollectionUtils.isNotEmpty(orderItemAftersalesDOS)
        && orderItemDOS.size() == orderItemAftersalesDOS.size()){
            return true;
        }

        return false;
    }

}

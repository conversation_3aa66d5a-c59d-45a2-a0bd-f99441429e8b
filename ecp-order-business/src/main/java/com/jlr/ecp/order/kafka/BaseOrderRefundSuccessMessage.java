package com.jlr.ecp.order.kafka;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 * 用于取消订单 发送短信的消息体
 */
@Data
public class BaseOrderRefundSuccessMessage implements Serializable {
    /**
     * 消息id
     */
    private String messageId;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 任务编码
     */
    private String taskCode;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 订单编号
     */
    private String bgorderNumber;

    /**
     * 微信短连接
     */
    private String wxUrl;
}

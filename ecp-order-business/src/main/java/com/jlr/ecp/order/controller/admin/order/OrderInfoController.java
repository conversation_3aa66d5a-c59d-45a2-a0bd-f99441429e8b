package com.jlr.ecp.order.controller.admin.order;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.order.dto.OrderEditDTO;
import com.jlr.ecp.order.api.order.dto.OrderModifyPageReqDTO;
import com.jlr.ecp.order.api.order.dto.OrderPageReqDTO;
import com.jlr.ecp.order.api.order.vo.*;
import com.jlr.ecp.order.api.order.vo.detail.OrderDetailRespVO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.order.OrderSourceEnum;
import com.jlr.ecp.order.enums.order.OrderStatusLogEnum;
import com.jlr.ecp.order.enums.order.OrderTypeEnum;
import com.jlr.ecp.order.service.order.OrderInfoDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * 订单
 *
 * <AUTHOR>
 */
@Tag(name = "后台管理端 - 订单相关")
@RestController
@RequestMapping("v1/order")
@Validated
public class OrderInfoController {
    @Resource
    private OrderInfoDOService orderInfoService;

    /**
     * 订单列表
     *
     * @param dto 分页参数
     * @return OrderInfoPageVO
     */
    @GetMapping("/page")
    @Operation(summary = "订单列表列表、分页")
    @PreAuthorize("@ss.hasAnyPermissions('trade:order:list', 'trade:on-behalf-of-order:list')")
//    @PermitAll
    CommonResult<PageResult<OrderInfoPageVO>> page(@SpringQueryMap @Validated OrderPageReqDTO dto) {
        // 为了兼容前端传入的大小写，这里统一转换为小写
        dto.validateParameters();
        if (dto.getCreatedTimeSort() != null) {
            dto.setCreatedTimeSort(dto.getCreatedTimeSort().toLowerCase(Locale.ROOT));
        }

        PageResult<OrderInfoPageVO> pageResult = orderInfoService.getPage(dto);
        return CommonResult.success(pageResult);
    }

    /**
     * 订单详情
     *
     * @param orderCode 订单编号
     * @return OrderInfoPageVO
     */
    @GetMapping("/detail")
    @Operation(summary = "订单详情")
    @Parameter(name = "orderCode", description = "产品编号", required = true)
    @PreAuthorize("@ss.hasAnyPermissions('trade:order:detail','trade:lreorder:detail','trade:bgorder:detail')")
    //PermitAll
    CommonResult<OrderDetailRespVO> getOrderDetail(@RequestParam("orderCode") @NotBlank(message = "订单编码不能为空") String orderCode) {

        OrderDetailRespVO orderDetailRespVO = orderInfoService.getOrderDetail(orderCode);
        return CommonResult.success(orderDetailRespVO);
    }

    /**
     * 订单编辑
     *
     * @param orderEditDTO 订单编辑DTO
     * @return OrderInfoPageVO
     */
    @PutMapping("/edit")
    @Operation(summary = "订单编辑")
    @PreAuthorize("@ss.hasAnyPermissions('trade:order:edit','trade:lreorder:edit','trade:bgorder:edit')")
    CommonResult<String> editOrderDetail(@Validated @RequestBody OrderEditDTO orderEditDTO) {
        Boolean success = orderInfoService.editOrderDetail(orderEditDTO);
        if (Boolean.TRUE.equals(success)) {
            return CommonResult.success(Constants.ORDER_EDIT_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.ORDER_UPDATE_FAIL);
    }


    @PostMapping("/modifyPage")
    @Operation(summary = "操作记录分页查询")
    @PreAuthorize("@ss.hasAnyPermissions('trade:order:detail','trade:lreorder:detail','trade:bgorder:detail')")
    CommonResult<PageResult<OrderModifyPageVO>> getModifyPage(@Validated @RequestBody OrderModifyPageReqDTO dto) {

        PageResult<OrderModifyPageVO> pageResult = orderInfoService.selectModifyPage(dto);
        return CommonResult.success(pageResult);
    }

    @GetMapping("/log/detail")
    @Operation(summary = "操作记录详情查询")
    @PreAuthorize("@ss.hasAnyPermissions('trade:order:modify-log', 'trade:lreorder:modify-log', 'trade:bgorder:modify-log')")
    CommonResult<OrderModifyDetailVO> getLogDetail(@RequestParam("id") Long id) {

        OrderModifyDetailVO detail = orderInfoService.getLogDetail(id);
        return CommonResult.success(detail);
    }

    @GetMapping("/getOrderStatusList")
    @Operation(summary = "订单状态下拉列表接口")
    @PermitAll
    CommonResult<List<OrderStatusVO>> getOrderStatusList() {
        List<OrderStatusVO> orderStatusList = new ArrayList<>();
        for (OrderStatusLogEnum value : OrderStatusLogEnum.values()) {
            if(value.equals(OrderStatusLogEnum.AFTER_SALES)){
                continue;
            }
            OrderStatusVO vo = new OrderStatusVO();
            vo.setStatus(value.getCode());
            vo.setText(value.getDescription());
            orderStatusList.add(vo);
        }
        return CommonResult.success(orderStatusList);
    }

    @GetMapping("/getOrderTypeList")
    @Operation(summary = "订单商品类型下拉列表接口")
    @PermitAll
    CommonResult<List<OrderTypeVO>> getOrderTypeList() {
        List<OrderTypeVO> orderTypeList = new ArrayList<>();
        for (OrderTypeEnum value : OrderTypeEnum.values()) {
            if(value.equals(OrderTypeEnum.PARENT) || value.equals(OrderTypeEnum.BRAND_GOOD)){
                continue;
            }
            OrderTypeVO vo = new OrderTypeVO();
            vo.setStatus(value.getCode());
            vo.setText(value.getDesc());
            orderTypeList.add(vo);
        }
        return CommonResult.success(orderTypeList);
    }

    @GetMapping("/getOrderTypeListForVCS")
    @Operation(summary = "订单商品类型下拉列表接口ForVCS")
    @PermitAll
    CommonResult<List<OrderTypeVO>> getOrderTypeListForVCS() {
        List<OrderTypeVO> orderTypeList = new ArrayList<>();
        for (OrderTypeEnum value : OrderTypeEnum.values()) {
            if(value.equals(OrderTypeEnum.PARENT) || value.equals(OrderTypeEnum.BRAND_GOOD) || value.equals(OrderTypeEnum.ELECTRONIC_COUPON)){
                continue;
            }
            OrderTypeVO vo = new OrderTypeVO();
            vo.setStatus(value.getCode());
            vo.setText(value.getDesc());
            orderTypeList.add(vo);
        }
        return CommonResult.success(orderTypeList);
    }

    @GetMapping("/getOrderChannelList")
    @Operation(summary = "订单来源下拉列表接口")
    @PermitAll
    CommonResult<List<OrderTypeVO>> getOrderChannelList() {
        List<OrderTypeVO> orderTypeList = new ArrayList<>();
        for (OrderSourceEnum value : OrderSourceEnum.values()) {
            OrderTypeVO vo = new OrderTypeVO();
            vo.setStatus(value.getCode());
            vo.setText(value.getDesc());
            orderTypeList.add(vo);
        }
        return CommonResult.success(orderTypeList);
    }

    /**
     * 完成订单
     *
     */
    @GetMapping("/finish")
    @Operation(summary = "完成订单")
    @PreAuthorize("@ss.hasAnyPermissions('trade:order:complete-alert')")
    CommonResult<Boolean> finishOrder(@RequestParam("orderCode") @NotBlank(message = "订单编码不能为空") String orderCode) {
        return CommonResult.success(orderInfoService.finishOrder(orderCode));
    }

    /**
     * 订单状态异常提示
     *
     */
    @GetMapping("/getFailAlert")
    @Operation(summary = "订单异常提示")
    @PreAuthorize("@ss.hasAnyPermissions('trade:order:complete-alert')")
    CommonResult<Boolean> getFailAlert() {
        return CommonResult.success(orderInfoService.getFailAlert());
    }
}

package com.jlr.ecp.order.controller.app.order;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.order.dto.*;
import com.jlr.ecp.order.api.order.dto.customer.OrderBindDTO;
import com.jlr.ecp.order.api.order.vo.OrderBindRespVO;
import com.jlr.ecp.order.api.order.vo.OrderCancelVO;
import com.jlr.ecp.order.api.order.vo.OrderCreateRespVO;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppDetailPage;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppDetailPolicyRespVO;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppTabsVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.OrderBrandRespVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.OrderNewBrandRespVO;
import com.jlr.ecp.order.controller.app.order.dto.OrderLatestListReqDTO;
import com.jlr.ecp.order.controller.app.order.vo.Integration.OrderIntegrationLatestOrderRespVO;
import com.jlr.ecp.order.controller.app.order.vo.Integration.crcQueryOrderInfo.OrderIntegrationRespVO;
import com.jlr.ecp.order.enums.cart.ClientIdEnum;
import com.jlr.ecp.order.enums.order.OrderCloseReasonEnum;
import com.jlr.ecp.order.service.order.CancelOrderService;
import com.jlr.ecp.order.service.order.OrderInfoDOService;
import com.jlr.ecp.order.service.order.VcsOrderStatisticDOService;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.order.enums.ErrorCodeConstants.JLR_ID_NOT_EXIST;

/**
 * 订单
 *
 * <AUTHOR>
 */
@Tag(name = "app端 - 订单")
@RestController
@RequestMapping("v1/order")
@Validated
@Slf4j
public class OrderAppController {

    @Resource
    private OrderInfoDOService orderInfoDOService;
    @Resource
    private VcsOrderStatisticDOService vcsOrderStatisticDOService;
    @Resource
    private CancelOrderService cancelOrderService;

    /**
     * /integration/getorder CRCAPIM
     * 为CRC提供查询订单信息
     *
     * sprint50&51 crc的咨询当前订单卡片中，商品信息使用接口方案传输
     * 可查询BG VCS的订单及商品信息
     */
    @GetMapping("/integration/getorder")
    @Operation(summary = "为CRC提供查询订单信息")
//    @PermitAll
    CommonResult<OrderIntegrationRespVO> getOrder(@RequestParam(name = "orderCode") String orderCode,
                                                  @RequestHeader(value = "jlrId") String headerJlrId) {
        log.info("为CRC提供查询订单信息,orderCode={},jlrId={}", orderCode, headerJlrId);
        OrderIntegrationRespVO respVO = orderInfoDOService.getOrder(orderCode, headerJlrId);
        log.info("为CRC提供查询订单信息成功,respVO={}", respVO);
        return CommonResult.success(respVO);
    }

    /**
     * /integration/getlatestorders CRCAPIM
     */
    @GetMapping("/integration/getlatestorders")
    @Operation(summary = "为CRC提供查询最近三年订单信息")
//    @PermitAll
    CommonResult<OrderIntegrationLatestOrderRespVO> getlatestOrders(@SpringQueryMap @Validated OrderLatestListReqDTO dto,
                                                                    @RequestHeader(value = "jlrId") String jlrId,
                                                                    @RequestHeader(value = "client-id", required = false) String clientId
    ) {
        dto.setJlrId(jlrId);
        log.info("为CRC提供查询最近三年订单信息,dto={}", dto);
        // 校验参数
        dto.verifyParamValidity();

        // 为了兼容前端传入的大小写，这里统一转换为小写
        if (dto.getCreatedTimeSort() != null) {
            dto.setCreatedTimeSort(dto.getCreatedTimeSort().toLowerCase(Locale.ROOT));
        }
        //解析品牌code,数据隔离
        Integer orderChannel = null;
        if (EnumUtil.getBy(ClientIdEnum::getClientId, clientId) != null) {
            orderChannel = EnumUtil.getBy(ClientIdEnum::getClientId, clientId).getOrderChannel();
        }
        dto.setChannelCode(orderChannel);
//        PageResult<OrderIntegrationPageRespVO> pageResult = orderInfoDOService.getLatestOrders(dto);
        OrderIntegrationLatestOrderRespVO respVO = orderInfoDOService.getLatestOrders(dto);
        log.info("为CRC提供查询最近三年订单信息成功,respVO={}", respVO);
        return CommonResult.success(respVO);
    }

    /**
     * 订单tab item下的 订单列表
     * old version 已废弃
     *
     * @param orderBrandListDTO
     * @return
     */
    @Deprecated
    @PostMapping("/tab/order/list")
    @Operation(summary = "已废弃：订单tab列表 eg:揽胜，卫士，发现")
//    @PermitAll
    CommonResult<List<OrderBrandRespVO>> getOrderList(@Valid @RequestBody OrderBrandListDTO orderBrandListDTO) {
        return CommonResult.success(null);
    }

    /**
     * 订单tab item下的订单列表 by consumerCode
     * new version
     */
    @PostMapping("/tab/listByConsumerCode")
    @Operation(summary = "查询订单tab列表  new version")
    @PermitAll
    CommonResult<List<OrderNewBrandRespVO>> getAppOrderList(@Valid @RequestBody OrderBrandListNewDTO dto,
                                                            @RequestHeader(value = "client-id", required = false) String clientId,
                                                            @RequestHeader(value = "jlrId") String jlrId) {
        dto.setConsumerCode(jlrId);
        log.info("获取订单tab列表为,dto={},clientId={}", dto, clientId);
        //解析品牌code,数据隔离
        Integer orderChannel = null;
//        if (EnumUtil.getBy(ClientIdEnum::getClientId, clientId) != null) {
//            orderChannel = EnumUtil.getBy(ClientIdEnum::getClientId, clientId).getOrderChannel();
//        }
        List<OrderNewBrandRespVO> list = orderInfoDOService.getNewOrderList(dto, orderChannel);
        return CommonResult.success(list);
    }

    /***
     * <AUTHOR>
     * @description 订单tab item下的订单列表 by consumerCode;支持分页，支持新业务线LRE和BG
     * @date 2025/3/8 09:45
     * @param dto:
     * @param clientId:
     * @param jlrId:
     * @return: com.jlr.ecp.framework.common.pojo.CommonResult<java.util.List<com.jlr.ecp.order.api.order.vo.brandcategory.OrderNewBrandRespVO>>
    */

    @PostMapping("/tab/pageByConsumerCode")
    @Operation(summary = "查询订单tab列表  new version")
    @PermitAll
    CommonResult<PageResult<OrderNewBrandRespVO>> getAppOrderPageList(@Valid @RequestBody OrderPageReqDTO dto,
                                                                      @RequestHeader(value = "client-id", required = false) String clientId,
                                                                      @RequestHeader(value = "jlrId") String jlrId) {
        log.info("获取订单tab列表为,dto={},clientId={}", dto, clientId);
        PageResult<OrderNewBrandRespVO> list = orderInfoDOService.getOrderPage(jlrId, dto);
        return CommonResult.success(list);
    }

//    /**
//     * 我的订阅tab item下的 订单列表
//     *
//     * @param orderBrandListDTO
//     * @return
//     */
//    @PostMapping("/tab/subscription/list")
//    @Operation(summary = "我的订阅tab列表 eg:揽胜，卫士，发现")
//    @PermitAll
//    CommonResult<List<OrderSubscriptionRespVO>> getSubscriptionList(@Valid @RequestBody OrderBrandListDTO orderBrandListDTO) {
//        List<OrderSubscriptionRespVO> list = orderInfoDOService.getSubscriptionList(orderBrandListDTO);
//        return CommonResult.success(list);
//    }


    /**
     * 创建订单API接口
     *
     * @param orderCreateDTO
     * @return
     */
    @PostMapping("/create")
    @Operation(summary = "创建订单")
//    @PermitAll
    CommonResult<OrderCreateRespVO> createOrderInfo(@Valid @RequestBody OrderCreateDTO orderCreateDTO,
                                                    @RequestHeader(value = "client-id", required = false) String clientId,
                                                    @RequestHeader(value = "jlrId") String jlrId) throws JsonProcessingException {
        orderCreateDTO.getGlobalInfoDTO().setConsumerCode(jlrId);
        orderCreateDTO.getShopCarItemList().forEach(item -> {
            item.validatePayPrice(); // 添加这行代码来校验实付价格
            item.validateCartItemDetails(); // 假设存在一个通用的validate方法
        });
        log.info("请求头clientId:{}, shopCarItemList={}", clientId, orderCreateDTO.getShopCarItemList());
        if (StrUtil.isBlank(clientId)) {
            throw exception(ErrorCodeConstants.CLIENT_ID_HEADER_NEEDED);
        }

        // 校验联系人电话号码
        orderCreateDTO.validateContactPhone();

        OrderCreateRespVO result = orderInfoDOService.createOrderInfo(orderCreateDTO, clientId);
        return CommonResult.success(result);
    }

    /**
     * 创建订单API接口,通过pdp跳进提交订单页面
     *
     * @param orderCreatePdpDTO
     * @return
     */
    @PostMapping("/pdp/create")
    @Operation(summary = "从pdp页面直接跳转创建订单")
//    @PermitAll
    CommonResult<OrderCreateRespVO> createOrderInfoByPdp(@Valid @RequestBody OrderCreatePdpDTO orderCreatePdpDTO,
                                                         @RequestHeader(value = "client-id", required = false) String clientId,
                                                         @RequestHeader(value = "jlrId") String jlrId) throws JsonProcessingException {
        orderCreatePdpDTO.getGlobalInfoDTO().setConsumerCode(jlrId);
        orderCreatePdpDTO.getShopCarItemList().forEach(OrderShopCarItemPdpDTO::validatePayPrice);
        log.info("请求头clientId:{}, shopCarItemList={}", clientId, orderCreatePdpDTO.getShopCarItemList());
        if (StrUtil.isBlank(clientId)) {
            throw exception(ErrorCodeConstants.CLIENT_ID_HEADER_NEEDED);
        }

        // 校验联系人电话号码
        orderCreatePdpDTO.validateContactPhone();

        OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        BeanUtils.copyProperties(orderCreatePdpDTO, orderCreateDTO);

        // 复制 shopCarItemList
        List<OrderShopCarItemDTO> shopCarItemDTOList = orderCreatePdpDTO.getShopCarItemList().stream()
                .map(pdpItem -> {
                    OrderShopCarItemDTO dtoItem = new OrderShopCarItemDTO();
                    BeanUtils.copyProperties(pdpItem, dtoItem);
                    return dtoItem;
                })
                .collect(Collectors.toList());

        orderCreateDTO.setShopCarItemList(shopCarItemDTOList);

        OrderCreateRespVO result = orderInfoDOService.createOrderInfo(orderCreateDTO, clientId);
        return CommonResult.success(result);
    }

    /**
     * 用户与代客下单的订单回绑接口
     *
     * @param orderBindDTO 请求参数
     * @param clientId     客户端标识符
     * @param jlrId        用户ID（consumerCode）
     * @return 绑定结果
     */
    @PostMapping("/bind/consumerCode")
    @Operation(summary = "用户与代客下单的订单回绑")
        @PermitAll
    public CommonResult<OrderBindRespVO> bindOrder(@Valid @RequestBody OrderBindDTO orderBindDTO,
                                                   @RequestHeader(value = "client-id") String clientId,
                                                   @RequestHeader("jlrId") String jlrId) {
        log.info("用户与代客下单的订单回绑 请求头clientId:{}, jlrId:{}, orderBindDTO={}", clientId, jlrId, orderBindDTO);
        // 参数校验 clientId;jlrIdB
        if (StrUtil.isBlank(clientId)) {
            throw exception(ErrorCodeConstants.CLIENT_ID_HEADER_NEEDED);
        }
        if (StrUtil.isBlank(jlrId)) {
            throw exception(JLR_ID_NOT_EXIST);
        }
        OrderBindRespVO respVO = orderInfoDOService.bindOrder(orderBindDTO.getOrderNumber(), orderBindDTO.getWxPhone(), jlrId, clientId);
        return CommonResult.success(respVO);
    }

    /**
     * 小程序端订单详情
     * 当前这个详情，要么只有VCS商品，要么只有非VCS商品，在逻辑处理中暂时分开处理
     * @param orderCode 订单编码
     * @return 订单分页信息
     *
     * vcs走vcs的订单详情
     */
    @GetMapping("/view")
    @Operation(summary = "小程序端订单详情")
    @PermitAll
    CommonResult<OrderAppDetailPage> getAppOrderDetail(@RequestHeader(value = "jlrId") String jlrId,
                                                       @RequestParam("orderCode") @NotBlank(message = "订单编码不能为空") String orderCode) {
        log.info("小程序端订单详情,consumerCode={},orderCode={}", jlrId, orderCode);
        OrderAppDetailPage orderAppDetailPage = orderInfoDOService.getAppOrderDetail(orderCode, jlrId);
        return CommonResult.success(orderAppDetailPage);
    }

    /***
     * <AUTHOR>
     * @description 小程序段获取订单详情 （LRE商品接入）
     * @date 2025/3/9 17:41
     * @param jlrId:
     * @param orderCode:
     * @return: com.jlr.ecp.framework.common.pojo.CommonResult<com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppDetailPage>
     *
     * bg走bg的
    */

    @GetMapping("/orderDetailView")
    @Operation(summary = "小程序端订单详情")
//    @PermitAll
    CommonResult<OrderAppDetailPage> getAppOrderDetail2(@RequestHeader(value = "jlrId") String jlrId,
                                                       @RequestParam("orderCode") @NotBlank(message = "订单编码不能为空") String orderCode) {
        log.info("小程序端订单详情,consumerCode={},orderCode={}", jlrId, orderCode);
        OrderAppDetailPage orderAppDetailPage = orderInfoDOService.getAppOrderDetail2(orderCode, jlrId);
        return CommonResult.success(orderAppDetailPage);
    }

    /**
     * 小程序端订单详情-购买条款与条件
     *
     * @param orderCode
     * @return
     */
    @GetMapping("/view/terms")
    @Operation(summary = "小程序端订单详情-购买条款与条件")
//    @PermitAll
    CommonResult<List<OrderAppDetailPolicyRespVO>> getAppOrderDetailPolicyList(@RequestParam("orderCode") @NotBlank(message = "订单item编码不能为空") String orderCode) {
        List<OrderAppDetailPolicyRespVO> list = orderInfoDOService.getAppOrderDetailPolicyList(orderCode);
        return CommonResult.success(list);
    }

    @PostMapping("/cancel")
    @Operation(summary = "小程序取消订单")
//    @PermitAll
    public CommonResult<OrderCancelVO> cancelOrder(@RequestBody OrderCancelDTO orderCancelDTO) {
        return CommonResult.success(cancelOrderService.orderCancel(orderCancelDTO.getOrderCode(), OrderCloseReasonEnum.PROACTIVELY_CLOSE));
    }

    /**
     * 订单列表获取tab页
     *
     * @param consumerCode 客户编码
     * @return
     */
    @GetMapping("/getTabs")
    @Operation(summary = "查询车型编码和标签")
    @Parameter(name = "consumerCode", description = "客户编码")
//    @PermitAll
    CommonResult<List<OrderAppTabsVO>> getTabs(@RequestParam(value = "consumerCode") String consumerCode,
                                               @RequestHeader(value = "client-id", required = false) String clientId,
                                               @RequestHeader(value = "jlrId") String jlrId) {
        log.info("获取订单tab列表为,consumerCode={},clientId={}", jlrId, clientId);
//        if (StrUtil.isBlank(clientId)) {
//            throw exception(ErrorCodeConstants.CLIENT_ID_HEADER_NEEDED);
//        }

        List<OrderAppTabsVO> list = vcsOrderStatisticDOService.getTabs(jlrId, clientId);
        return CommonResult.success(list);
    }

    /**
     * 支付时设置reids key 接口
     *
     * @param orderCode 订单编号
     */
    @GetMapping("/callPayment")
    @Operation(summary = "支付时设置reids key 接口")
    CommonResult<Boolean> callPayment(@RequestParam(value = "orderCode") String orderCode,
                                      @RequestParam(value = "type", required = false) String type) {
        log.info("支付时设置reids key 接口,orderCode={},type = {}", orderCode,type);

        return CommonResult.success(orderInfoDOService.callPayment(orderCode,type));
    }

    /**
     * 支付失败，清除redis缓存
     *
     * @param orderCode 订单编号
     */
    @GetMapping("/paymentFail")
    @Operation(summary = "唤起支付后支付失败")
    CommonResult<Boolean> paymentFail(@RequestParam(value = "orderCode") String orderCode) {
        log.info("唤起支付后支付失败,orderCode={}", orderCode);

        return CommonResult.success(orderInfoDOService.paymentFail(orderCode));
    }
}

package com.jlr.ecp.order.controller.app.payment;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.payment.dto.PayRequestDTO;
import com.jlr.ecp.order.controller.app.order.vo.PayOrderRespVO;
import com.jlr.ecp.order.controller.app.refund.vo.RefundDetailVO;
import com.jlr.ecp.order.service.order.OrderInfoDOService;
import com.jlr.ecp.order.service.refund.OrderRefundDOService;
import com.jlr.ecp.payment.api.order.vo.PayOrderSubmitRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Tag(name = "app端 - 支付订单")
@RestController
@RequestMapping("v1/order")
@Validated
@Slf4j
public class PaymentController {
    @Resource
    private OrderInfoDOService orderInfoDOService;

    /**
     * 转发支付请求到payment-service
     *
     * @param dto 请求支付参数对象
     * @return 支付结果响应
     */
    @PostMapping("/payment/initiate")
    @Operation(summary = "初始化支付请求")
    public CommonResult<PayOrderRespVO> initiatePayment(@RequestBody @Validated PayRequestDTO dto) {
        log.info("初始化支付请求，dto={}", JSON.toJSONString(dto));

        // 构建支付请求
        PayOrderRespVO respVO = orderInfoDOService.buildPaymentRequest(dto);

        // 返回支付结果
        return CommonResult.success(respVO);
    }
}

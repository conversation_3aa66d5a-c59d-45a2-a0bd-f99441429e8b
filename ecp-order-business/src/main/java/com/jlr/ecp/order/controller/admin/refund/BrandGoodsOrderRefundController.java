package com.jlr.ecp.order.controller.admin.refund;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.logistics.enums.company.LogisticsCompanyEnum;
import com.jlr.ecp.order.api.order.vo.RefundReasonStatusVO;
import com.jlr.ecp.order.api.order.vo.logistics.LogisticsCompanyVO;
import com.jlr.ecp.order.api.refund.dto.BaseOrderRefundApplyDTO;
import com.jlr.ecp.order.api.refund.dto.LogisticsOrderRefundApproveDTO;
import com.jlr.ecp.order.api.refund.dto.LogisticsOrderReturnApproveDTO;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.order.LogisticsRefundReasonEnum;
import com.jlr.ecp.order.enums.order.RefundOrderOperationTypeEnum;
import com.jlr.ecp.order.enums.refund.RefundOrderTypeEnum;
import com.jlr.ecp.order.service.refund.bg.BrandGoodsOrderRefundDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 实物订单Controller
 *
 * <AUTHOR>
 */
@Tag(name = "后台管理端 - 退单管理")
@RestController
@RequestMapping("v1/refund/bg")
@Validated
public class BrandGoodsOrderRefundController {


    @Resource
    private BrandGoodsOrderRefundDOService brandGoodsOrderRefundDOService;

    /**
     * BG退货审核
     */
    @PostMapping("/orderReturnApprove")
    @Operation(summary = "BG退货审核")
    @PreAuthorize("@ss.hasPermission('trade:bgchargeback:approve')")
    CommonResult<String> bgOrderReturnApprove(@Validated @RequestBody LogisticsOrderReturnApproveDTO logisticsOrderRefundApproveDTO) {
        return CommonResult.success(brandGoodsOrderRefundDOService.logisticsOrderReturnAuditApprove(logisticsOrderRefundApproveDTO));
    }

    /**
     * BG退款审核
     */
    @PostMapping("/orderRefundApprove")
    @Operation(summary = "BG退款审核")
    @PreAuthorize("@ss.hasPermission('trade:bgchargeback:approve')")
    CommonResult<String> bgOrderRefundApprove(@Validated @RequestBody LogisticsOrderRefundApproveDTO logisticsOrderRefundApproveDTO) {
        return CommonResult.success(brandGoodsOrderRefundDOService.logisticsOrderRefundAuditApprove(logisticsOrderRefundApproveDTO));
    }

    @GetMapping("/getRefundReasonList")
    @Operation(summary = "获取所有BG退款原因选项 type 1-退货退款 2-仅退款")
    @PermitAll
    CommonResult<List<RefundReasonStatusVO>> getBgRefundReasonList(int type) {
        List<RefundReasonStatusVO>  refundReasonList= LogisticsRefundReasonEnum.getByType(type);
        return CommonResult.success(refundReasonList);
    }


    @GetMapping("/getLogisticsCompanyList")
    @Operation(summary = "获取所有快递公司名称和code")
    @PermitAll
    CommonResult<List<LogisticsCompanyVO>> getLogisticsCompanyList() {
        List<LogisticsCompanyVO> refundReasonList= new ArrayList<>();
        for (LogisticsCompanyEnum reason : LogisticsCompanyEnum.values()) {
            refundReasonList.add(new LogisticsCompanyVO(reason.getCode(), reason.getName()));

        }
        return CommonResult.success(refundReasonList);
    }


    @PostMapping("/apply")
    @Operation(summary = "运营发起退货申请")
    @PreAuthorize("@ss.hasPermission('trade:bgorder:refund')")
    CommonResult<String> refund(@RequestBody @NotEmpty(message = "退款申请对象不能为空") List<BaseOrderRefundApplyDTO> baseOrderRefundApplyDTOList) {
        for(BaseOrderRefundApplyDTO baseOrderRefundApplyDTO:baseOrderRefundApplyDTOList){
            if(baseOrderRefundApplyDTO.getRefundOrderType() == null || RefundOrderTypeEnum.getByCode(baseOrderRefundApplyDTO.getRefundOrderType()) == null){
                throw exception(ErrorCodeConstants.ORDER_REFUND_TYPE_EMPTY);
            }
        }
        String orderRefundCode = brandGoodsOrderRefundDOService.logisticsOrderRefundApply(baseOrderRefundApplyDTOList, RefundOrderOperationTypeEnum.OPERATION_INITIATED.getCode());
        return CommonResult.success(orderRefundCode);
    }
}

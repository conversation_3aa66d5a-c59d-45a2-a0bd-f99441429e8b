package com.jlr.ecp.order.kafka.listener;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.service.coupon.status.Enum.CouponStatusFromSqsEnum;
import com.jlr.ecp.order.service.coupon.status.dto.CouponStatusChangedKafkaDto;
import com.jlr.ecp.order.service.independent.OrderIndependentDOService;
import com.jlr.ecp.order.util.RedisReentrantLockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.errors.TimeoutException;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.kafka.KafkaException;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;


/**
 * 券核销后触发分账
 * <AUTHOR> Hongyi
 * */
@Component
@Slf4j
public class CouponUsedTriggerIndependentListener {

    @Resource
    private Redisson redisson;
    @Resource
    private RedisReentrantLockUtil redisReentrantLockUtil;
    @Resource
    private OrderIndependentDOService orderIndependentDOService;

    @KafkaListener(topics = KafkaConstants.COUPON_STATUS_CHANGE_TOPIC, groupId = "order-coupon-status-group", properties = "max.poll.records:1")
    @Retryable(value = {KafkaException.class, TimeoutException.class}, exclude = Exception.class, backoff = @Backoff(delay = 5000, multiplier = 2, maxDelay = 30000), maxAttempts = 2)
    public void onMessage(String messageStr) {
        CouponStatusChangedKafkaDto message = JSON.parseObject(messageStr, CouponStatusChangedKafkaDto.class);
        if (!CouponStatusFromSqsEnum.USED.getCouponStatus().equals(message.getCouponStatus())) {
            log.info("券核销分账，券状态变化不为核销，放弃分账，message={}", messageStr);
            return;
        }

        log.info("券核销分账，尝试获取锁，orderCode={}，message={}", message.getOrderCode(), messageStr);
        RLock rLock = redisson.getLock(Constants.REDIS_ORDER_INDEPENDENT_LOCK_KEY + message.getOrderCode());
        if (!redisReentrantLockUtil.tryLock(rLock, 15,  30, TimeUnit.SECONDS)) {
            throw new KafkaException("券核销分账，尝试获取锁失败，orderCode=" + message.getOrderCode());
        }
        try {
            log.info("券核销分账开始，couponCode={}，message={}", message.getCouponCode(), messageStr);
            TenantContextHolder.setTenantId(message.getTenantId());
            orderIndependentDOService.triggerIndependentForCouponUsed(message);
        } catch (Exception e) {
            log.error("券核销分账异常，couponCode={}，message={}", message.getCouponCode(), messageStr);
            throw new KafkaException("券核销分账异常");
        } finally {
            TenantContextHolder.clear();
            redisReentrantLockUtil.unlock(rLock, 3);
        }
    }

}

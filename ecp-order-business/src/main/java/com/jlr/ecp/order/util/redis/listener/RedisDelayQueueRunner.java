package com.jlr.ecp.order.util.redis.listener;

import cn.hutool.extra.spring.SpringUtil;
import com.jlr.ecp.order.enums.redis.RedisDelayQueueEnum;
import com.jlr.ecp.order.handle.RedisDelayQueueHandle;
import com.jlr.ecp.order.util.redis.RedisDelayQueueUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class RedisDelayQueueRunner implements CommandLineRunner {

    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;

    @Override
    public void run(String... args) {
        //包含枚举类中所有枚举值的数组
        RedisDelayQueueEnum[] queueEnums = RedisDelayQueueEnum.values();
        log.info("开始");
        for (RedisDelayQueueEnum queueEnum : queueEnums) {
            new Thread(() -> {
                try {
                    while (true) {
                        Object value = redisDelayQueueUtil.getDelayQueue(queueEnum.getCode());
                        // 每次拿到value值，就新开一个线程去执行，避免阻塞
                        CompletableFuture.runAsync(()->{
                            // 通过beanid 获得 RedisDelayQueueHandle接口的实现类
                            RedisDelayQueueHandle redisDelayQueueHandle = SpringUtil.getBean(queueEnum.getBeanId());
                            redisDelayQueueHandle.execute(value);
                        });
                    }
                } catch (InterruptedException e) {
                    // 捕捉当前异常，避免影响其他服务
                    log.error("(Redis延迟队列异常中断) {}", e.getMessage());
                    Thread.currentThread().interrupt();
                }
            }).start();
            log.info("一个线程开始执行了");
        }
        log.info("(Redis延迟队列启动成功)");
    }
}
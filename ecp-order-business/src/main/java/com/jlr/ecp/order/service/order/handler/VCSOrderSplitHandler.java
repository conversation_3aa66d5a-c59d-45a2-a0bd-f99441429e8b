package com.jlr.ecp.order.service.order.handler;

import com.jlr.ecp.order.api.order.dto.OrderCreateDTO;
import com.jlr.ecp.order.api.order.dto.OrderShopCarItemDTO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

//@Slf4j
//public class VCSOrderSplitHandler implements OrderSplitHandler{
//    private OrderCreateDTO orderCreateDTOData = null;
//    private OrderSplitHandler nextSplitHandler = null;
//    public OrderSplitHandler setNext(OrderSplitHandler handler) {
//        nextSplitHandler = handler;
//        return nextSplitHandler;
//    }
//
//    public OrderSplitHandler getNext() {
//        return this.nextSplitHandler;
//    }
//
//    public List<OrderShopCarItemDTO> matchItems() {
//        List<OrderShopCarItemDTO> list = new ArrayList<OrderShopCarItemDTO>();
//
//
//        return list;
//    }
//
//    public boolean canItemMatch() {
//        return true;
//    }
//
//    public List<OrderInfoDO> handleRequest(OrderCreateDTO orderCreateDTO, List<OrderInfoDO> lastOrderInfoList) {
//        log.info("开始处理VCS订单逻辑");
//        orderCreateDTOData = orderCreateDTO;
//
//        //##TODO VCS商品拆单逻辑在这里实现
//        log.info("结束处理VCS订单逻辑");
//
//
//
//        if(nextSplitHandler != null)
//            lastOrderInfoList = this.nextSplitHandler.handleRequest(orderCreateDTO,lastOrderInfoList);
//        return lastOrderInfoList;
//    }
//}

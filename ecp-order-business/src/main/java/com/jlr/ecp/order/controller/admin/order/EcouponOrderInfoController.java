package com.jlr.ecp.order.controller.admin.order;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.order.dto.ECouponOrderPageReqDTO;
import com.jlr.ecp.order.api.order.vo.ECouponOrderInfoPageVO;
import com.jlr.ecp.order.service.order.EcouponOrderInfoDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * BG&LRE订单
 *
 * <AUTHOR>
 */
@Tag(name = "后台管理端 - LRE订单相关")
@RestController
@RequestMapping("v1/order/ecoupon")
@Validated
@Slf4j
public class EcouponOrderInfoController {
    @Resource
    private EcouponOrderInfoDOService ecouponOrderInfoDOService;

    /**
     * LRE订单列表
     * 
     * @param dto 分页参数
     * @return EcouponOrderInfoPageVO
     */
    @GetMapping("/page")
    @Operation(summary = "LRE订单列表列表、分页")
    @PreAuthorize("@ss.hasPermission('trade:lreorder:list')")
    CommonResult<PageResult<ECouponOrderInfoPageVO>> page(@SpringQueryMap @Validated ECouponOrderPageReqDTO dto) {
        log.info("查询LRE订单列表, 参数处理前: {}", dto);
        // DTO 自身处理参数验证和转换
        dto.prepareForQuery();
        log.info("查询LRE订单列表, 参数处理后: {}", dto);
        PageResult<ECouponOrderInfoPageVO> pageResult = ecouponOrderInfoDOService.getPage(dto);
        log.info("查询LRE订单列表成功, 返回结果数量: {}", pageResult.getTotal());
        return CommonResult.success(pageResult);
    }
}

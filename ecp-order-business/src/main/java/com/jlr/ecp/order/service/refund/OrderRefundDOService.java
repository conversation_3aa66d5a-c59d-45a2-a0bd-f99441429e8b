package com.jlr.ecp.order.service.refund;

import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.refund.dto.*;
import com.jlr.ecp.order.api.refund.vo.*;
import com.jlr.ecp.order.controller.app.refund.vo.RefundDetailVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_order_refund(t_order_refund)】的数据库操作Service
* @createDate 2024-01-15 11:28:44
*/
public interface OrderRefundDOService extends IService<OrderRefundDO> {


    /**
     * 退单分页列表
     *
     * @param dto 分页入参
     * @return PageResult<OrderRefundPageVO>
     */
    PageResult<OrderRefundPageVO> getPage(OrderRefundPageReqDTO dto);

    /***
     * 取消订单申请
     * @param refundApplyDTO refundApplyDTO
     * @return Boolean
     */
    Boolean applyOrderRefund(OrderRefundApplyDTO refundApplyDTO);

    /**
     * 详情
     * @param orderRefundCode 退单编码
     * @return RefundDetailRespVO
     */
    RefundDetailRespVO getOrderRefundDetail(String orderRefundCode);

    /**
     * 取消订单审批
     * @param refundApproveDTO 审批dto
     * @return Boolean
     */
    Boolean orderRefundApprove(OrderRefundApproveDTO refundApproveDTO);

    /**
     * 根据OrderCode查询最新退单详情
     * @param orderCode 订单code
     * @return
     */
    RefundDetailVO getOrderRefundDetailByOrderCode(String orderCode);

    /**
     *
     * 待办数量
     * @return count
     */
    Integer getCount();

    /**
     * 退款回调处理
     * @param orderRefundCode 退单编码
     * @return Integer
     */
    Integer paymentCallbackProcess(String orderRefundCode);


    /**
     * 分账退款回调处理
     * @param orderRefundCode 退单编码
     * @return Integer
     */
    Integer transPaymentCallbackProcess(String orderRefundCode);

    /**
     * tsdpMock
     * @param orderRefundCode 退单编码
     * @return Integer
     */
    Integer tsdpRefundCallBack(String orderRefundCode,Boolean updateStatus);

    /**
     * tsdpMock
     * @param orderCode 订单编码
     * @return Integer
     */
    Integer tsdpCallBack(String orderCode,Boolean updateStatus);


    /***
     * 小程序 支付流程mock接口
     * @param orderCode
     * @return
     */
    Integer paymentMock(String orderCode,String parentCode);

    Boolean checkOrderRefund(OrderRefundCheckDTO refundCheckDTO);

    /**
     * 退单分页列表(新版, 与VCS分开使用)
     * @param dto
     * @return
     */
    PageResult<OrderRefundPageRespNewVO> orderRefundPageNew(OrderRefundPageReqNewDTO dto);

    /**
     * 编辑退单备注接口
     * @param orderRefundEditDTO
     * @return
     */
    Boolean editOrderRefund(OrderRefundEditDTO orderRefundEditDTO);

    /**
     * 获取退单详情
     * @param orderRefundCode
     * @return
     */
    OrderRefundDetailVO getOrderRefundDetailNew(@NotBlank(message = "退单编码不能为空") String orderRefundCode);

    /**
     * 根据 OrderCode 查询 OrderRefund
     *
     * @param orderCode OrderCode
     * @return List of OrderRefundDto
     */
    List<OrderRefundDto> getOrderRefundInfoByOrderCode(String orderCode);

    /**
     * 根据 List OrderRefundCode 查询 OrderRefundItem
     *
     * @return List of OrderRefundItemDto
     */
    List<OrderRefundItemDto> getOrderRefundItemByOrderRefundCode(List<String> orderRefundCodeList);
}

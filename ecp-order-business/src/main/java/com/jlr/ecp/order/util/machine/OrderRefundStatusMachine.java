package com.jlr.ecp.order.util.machine;

import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.enums.order.OrderEventEnum;
import com.jlr.ecp.order.enums.order.OrderRefundEventEnum;
import com.jlr.ecp.order.util.machine.handler.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 单例退单状态变更
 * <AUTHOR>
 */
@Component
public class OrderRefundStatusMachine {

    private Map<Integer, EventHandler> eventHandlerMap;

    @Autowired
    public OrderRefundStatusMachine(FullApplyHandler fullRefundApplyHandler,
                                    PartialApplyHandler partialRefundApplyHandler,
                                    FullApproveHandler fullRefundApproveHandler,
                                    PartialApproveHandler partialRefundApproveHandler,
                                    FullCompletedPaymentHandler fullRefundCompletedPaymentHandler,
                                    PartialCompletedPaymentHandler partialRefundCompletedPaymentHandler,
                                    FullRefuseHandler fullRefundRefuseHandler,
                                    PartialRefuseHandler partialRefundRefuseHandler,
                                    FullCompletedTSDPHandler fullRefundCompletedTSDPHandler,
                                    PartialCompletedTSDPHandler partialRefundCompletedTSDPHandler,
                                    PaymentSuccessCallbackHandler paymentSuccessCallbackHandler,
                                    PaymentHandler paymentHandler,
                                    TsdpCallbackHandler tsdpCallBackHandler) {
        // 初始化事件处理器映射
        eventHandlerMap = new HashMap<>();
        eventHandlerMap.put(OrderRefundEventEnum.EVENT_FULL_REFUND_APPLY.getCode(), fullRefundApplyHandler);
        eventHandlerMap.put(OrderRefundEventEnum.EVENT_PARTIAL_REFUND_APPLY.getCode(), partialRefundApplyHandler);
        eventHandlerMap.put(OrderRefundEventEnum.EVENT_FULL_REFUND_APPROVE.getCode(), fullRefundApproveHandler);
        eventHandlerMap.put(OrderRefundEventEnum.EVENT_PARTIAL_REFUND_APPROVE.getCode(), partialRefundApproveHandler);
        eventHandlerMap.put(OrderRefundEventEnum.EVENT_FULL_REFUND_COMPLETED_PAYMENT.getCode(), fullRefundCompletedPaymentHandler);
        eventHandlerMap.put(OrderRefundEventEnum.EVENT_PARTIAL_REFUND_COMPLETED_PAYMENT.getCode(), partialRefundCompletedPaymentHandler);
        eventHandlerMap.put(OrderRefundEventEnum.EVENT_FULL_REFUND_REFUSE.getCode(), fullRefundRefuseHandler);
        eventHandlerMap.put(OrderRefundEventEnum.EVENT_PARTIAL_REFUND_REFUSE.getCode(), partialRefundRefuseHandler);
        eventHandlerMap.put(OrderRefundEventEnum.EVENT_FULL_REFUND_COMPLETED_TSDP.getCode(), fullRefundCompletedTSDPHandler);
        eventHandlerMap.put(OrderRefundEventEnum.EVENT_PARTIAL_REFUND_COMPLETED_TSDP.getCode(), partialRefundCompletedTSDPHandler);
        eventHandlerMap.put(OrderEventEnum.EVENT_PAYMENT_SUCCESS_CALLBACK.getCode(), paymentSuccessCallbackHandler);
        eventHandlerMap.put(OrderEventEnum.EVENT_PAYMENT.getCode(), paymentHandler);
        eventHandlerMap.put(OrderEventEnum.EVENT_TSDP_CALLBACK.getCode(),  tsdpCallBackHandler);
        // 其他处理类映射...
    }

    public void changeOrderStatus(Integer event,OrderInfoDO orderInfoDO, OrderRefundDO orderRefundDO) {
        EventHandler eventHandler = eventHandlerMap.get(event);
        if (eventHandler != null) {
            eventHandler.handleEvent(orderInfoDO, orderRefundDO);
        } else {
            // 处理默认情况
        }
    }

}

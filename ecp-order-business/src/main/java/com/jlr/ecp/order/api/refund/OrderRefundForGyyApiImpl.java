package com.jlr.ecp.order.api.refund;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.refund.dto.OrderRefundForGyyPageReqDTO;
import com.jlr.ecp.order.api.refund.dto.OrderRefundForGyyPageRespDTO;
import com.jlr.ecp.order.service.refund.OrderRefundForGyyService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> Hongyi
 */
@RestController
@Validated
public class OrderRefundForGyyApiImpl implements OrderRefundForGyyApi {

    @Resource
    private OrderRefundForGyyService orderRefundForGyyService;

    @Override
    public CommonResult<PageResult<OrderRefundForGyyPageRespDTO>> pageOrderRefund(@RequestBody OrderRefundForGyyPageReqDTO req) {
        return CommonResult.success(orderRefundForGyyService.pageOrderRefund(req));
    }

}

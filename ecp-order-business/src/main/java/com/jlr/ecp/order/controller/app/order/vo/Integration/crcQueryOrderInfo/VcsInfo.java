package com.jlr.ecp.order.controller.app.order.vo.Integration.crcQueryOrderInfo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VCS订单 相关信息 eg.车、商品
 */
@Schema(description = "VCS订单 相关信息 eg.车、商品")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VcsInfo {
    /**
     * 车相关信息
     */
    @Schema(description = "车辆 VIN 码")
    private String carVin;

    @Schema(description = "品牌代码")
    private String brandCode;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "车型年份")
    private String modelYear;

    @Schema(description = "配置代码")
    private String configCode;

    @Schema(description = "配置名称")
    private String configName;

    @Schema(description = "系列代码")
    private String seriesCode;

    @Schema(description = "系列名称")
    private String seriesName;

    @Schema(description = "产品类型EN")
    private String productionEn;

    @Schema(description = "House of Brand 英文描述描述")
    private String hobEn;

    /**
     * 商品相关信息
     */
    @Schema(description = "商品主图URL")
    private String productImageUrl;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品数量")
    private String productQuantity;

    @Schema(description = "'stock组合属性,attribute_value_code:attribute_value_name, 多个属性值用，隔开例如：   service_year:一年,car_system:PIVI'")
    private String productAttribute;

    @Schema(description = "商品销售价格")
    private String productSalePrice;
}
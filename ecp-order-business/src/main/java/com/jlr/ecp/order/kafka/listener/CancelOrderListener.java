package com.jlr.ecp.order.kafka.listener;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.kafka.message.CancelOrderMessage;
import com.jlr.ecp.order.delayqueue.service.RedissonOrderCancelDelayService;
import com.jlr.ecp.order.service.order.CancelOrderService;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 超时取消订单
 *
 * <AUTHOR>
 * */
@Component
@Slf4j
public class CancelOrderListener {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private static final String OK = "OK";

    @Resource
    private RedissonOrderCancelDelayService delayService;

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;

    /**
     *  订单超时未支付自动取消订单
     * @param records 消息体
     * */
    @KafkaListener(topics = KafkaConstants.ORDER_TIMEOUT_CANCEL_TOPIC, groupId = "order-timeout-cancel-group",
            batch = "true", properties = {"max.poll.records:50"})
    public void timeoutCancelOrder(List<ConsumerRecord<String, String>> records) {
        log.info("订单超时自动取消，records:{}", records);
        List<CancelOrderMessage> cancelMessageList = buildCancelOrderMessageList(records);


        for (CancelOrderMessage message : cancelMessageList) {
            try {
                TenantContextHolder.setTenantId(message.getTenantId());
                // 查询订单获取业务线编码
                OrderInfoDO orderInfo = orderInfoDOMapper.getOrderInfoByOrderCode(message.getOrderCode());
                if (orderInfo != null) {
                    message.setBusinessCode(orderInfo.getBusinessCode());
                }

                // 添加到Redis延迟队列
                delayService.addDelayTask(message);

                log.info("订单延迟取消任务已提交，orderCode={}, businessCode={}",
                        message.getOrderCode(), message.getBusinessCode());

            } catch (Exception e) {
                log.error("处理订单延迟取消失败，orderCode={}", message.getOrderCode(), e);
            }finally {
                TenantContextHolder.clear();
            }
        }
    }

    /**
     * 计算自指定时间以来的超时时间。
     *
     * @param createdTime 任务的创建时间，用于与当前时间进行比较。
     * @return 自创建时间以来的毫秒数，表示已过去的时间。
     */
    private long calculateTimeout(LocalDateTime createdTime) {
        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(createdTime, now);
        return duration.toMillis();
    }

    /**
     * 获取CancelOrderMessageList
     * @param records 消息记录
     * @return List<PayTimeoutMessage>
     * */
    public List<CancelOrderMessage> buildCancelOrderMessageList(List<ConsumerRecord<String, String>> records) {
        List<CancelOrderMessage> cancelOrderMessageList = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return cancelOrderMessageList;
        }
        for (ConsumerRecord<String, String> record : records) {
            try {
                CancelOrderMessage cancelOrderMessage = JSON.parseObject(record.value(), CancelOrderMessage.class);
                cancelOrderMessageList.add(cancelOrderMessage);
            } catch (Exception e) {
                log.error("转化为CancelOrderMessage异常, record:{}, 原因:", record, e);
            }
        }
        return cancelOrderMessageList;
    }

    /**
     *   批量幂等校验
     *  @param cancelOrderMessage 消息实体列表
     *  @return boolean
     * */
    public Boolean idempotentCheck(CancelOrderMessage cancelOrderMessage) {
        if (Objects.isNull(redisTemplate) || Objects.isNull(cancelOrderMessage)) {
            log.error("订单超时，幂等校验，redisTemplate或payTimeoutMessage为空");
            return false;
        }
        Boolean result = redisTemplate.opsForValue().setIfAbsent(Constants.TIMEOUT_CANCEL_ORDER_KEY +
                cancelOrderMessage.getMessageId(), OK, 3600, TimeUnit.SECONDS);
        // 显式处理可能的null值，避免NullPointerException
        return Objects.nonNull(result) && result;
    }

}
package com.jlr.ecp.order.delayqueue.service;

import com.jlr.ecp.order.delayqueue.config.OrderCancelDelayConfig;
import com.jlr.ecp.order.delayqueue.dto.CancelOrderTask;
import com.jlr.ecp.order.kafka.message.CancelOrderMessage;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * Redisson延迟队列服务
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class RedissonOrderCancelDelayService {
    
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private OrderCancelDelayConfig delayConfig;

    /**
     *  获取BG+LRE队列（供消费者使用）
     */
    @Getter
    private RBlockingQueue<CancelOrderTask> bgLreQueue;

    // BG+LRE业务线延迟队列（15分钟）
    private RDelayedQueue<CancelOrderTask> bgLreDelayedQueue;
    
    /**
     *  获取VCS队列（供消费者使用）
     */
    @Getter
    private RBlockingQueue<CancelOrderTask> vcsQueue;

    // VCS业务线延迟队列（2小时）
    private RDelayedQueue<CancelOrderTask> vcsDelayedQueue;
    
    @PostConstruct
    public void init() {
        // 初始化BG+LRE业务线队列（15分钟）
        bgLreQueue = redissonClient.getBlockingQueue("bg-lre-cancel-order-queue");
        if (!bgLreQueue.isExists()) {
            bgLreDelayedQueue = redissonClient.getDelayedQueue(bgLreQueue);
            log.info("BG+LRE延迟队列初始化完成");
        } else {
            bgLreDelayedQueue = redissonClient.getDelayedQueue(bgLreQueue);
            log.info("BG+LRE延迟队列已存在，跳过初始化");
        }

        // 初始化VCS业务线队列（2小时）
        vcsQueue = redissonClient.getBlockingQueue("vcs-cancel-order-queue");
        if (!vcsQueue.isExists()) {
            vcsDelayedQueue = redissonClient.getDelayedQueue(vcsQueue);
            log.info("VCS延迟队列初始化完成");
        } else {
            vcsDelayedQueue = redissonClient.getDelayedQueue(vcsQueue);
            log.info("VCS延迟队列已存在，跳过初始化");
        }

        log.info("Redis延迟队列服务初始化完成");
    }
    
    /**
     * 添加延迟取消任务
     */
    public void addDelayTask(CancelOrderMessage message) {
        try {
            CancelOrderTask task = buildCancelOrderTask(message);
            long delayTime = delayConfig.calculateActualDelay(message.getBusinessCode(), message.getSendTime());
            String businessLineDesc = delayConfig.getBusinessLineDescription(message.getBusinessCode());

            if (delayConfig.isVcsBusinessLine(message.getBusinessCode())) {
                vcsDelayedQueue.offer(task, delayTime, TimeUnit.MILLISECONDS);
                log.info("VCS订单延迟取消任务已添加，orderCode={}, businessLine={}, delayTime={}ms",
                        message.getOrderCode(), businessLineDesc, delayTime);
            } else {
                // BG和LRE都使用同一个队列（15分钟）
                bgLreDelayedQueue.offer(task, delayTime, TimeUnit.MILLISECONDS);
                log.info("BG/LRE订单延迟取消任务已添加，orderCode={}, businessLine={}, delayTime={}ms",
                        message.getOrderCode(), businessLineDesc, delayTime);
            }
            
        } catch (Exception e) {
            log.error("添加延迟取消任务失败，orderCode={}", message.getOrderCode(), e);
        }
    }
    
    /**
     * 构建取消订单任务
     */
    private CancelOrderTask buildCancelOrderTask(CancelOrderMessage message) {
        CancelOrderTask task = new CancelOrderTask();
        task.setTaskId(UUID.randomUUID().toString());
        task.setOrderCode(message.getOrderCode());
        task.setParentOrderCode(message.getParentOrderCode());
        task.setTenantId(message.getTenantId());
        task.setSendTime(message.getSendTime());
        task.setBusinessCode(message.getBusinessCode());
        task.setCreateTime(LocalDateTime.now());
        return task;
    }

}

package com.jlr.ecp.order.controller.admin.dashboard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "dashboard-响应参数VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FeedbackPieChartRespVo {

  @Schema(description = "名称")
  private String name;

  @Schema(description = "显示值")
  private String value;

  @Schema(description = "悬停显示")
  private List<FeedbackPieChartRespVo> data;
}

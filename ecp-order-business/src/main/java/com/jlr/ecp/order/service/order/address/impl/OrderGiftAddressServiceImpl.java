package com.jlr.ecp.order.service.order.address.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.order.dto.address.OrderGiftAddressDTO;
import com.jlr.ecp.order.api.order.vo.address.AppOrderGiftAddressDetailVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderGiftAddressDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.mysql.order.OrderGiftAddressMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.enums.order.GiftAddressEnum;
import com.jlr.ecp.order.service.order.address.OrderGiftAddressService;
import com.jlr.ecp.system.api.address.AddressConfigApi;
import com.jlr.ecp.system.api.address.dto.AddressNameDTO;
import com.jlr.ecp.system.enums.AddressRedisKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.extension.toolkit.Db.updateBatchById;

@Service
@Slf4j
public class OrderGiftAddressServiceImpl implements OrderGiftAddressService {

    @Resource
    private OrderGiftAddressMapper orderGiftAddressMapper;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private AddressConfigApi addressConfigApi;

    @Resource
    private OrderInfoDOMapper orderInfoDOMapper;

    @Override
    public AppOrderGiftAddressDetailVO getOrderGiftAddressByOrderCode(String orderCode) {
        log.info("根据订单号查询赠品地址信息, orderCode={}", orderCode);
        AppOrderGiftAddressDetailVO detailVO = new AppOrderGiftAddressDetailVO();
        OrderGiftAddressDO giftAddressDO = orderGiftAddressMapper.selectOne(new LambdaQueryWrapperX<OrderGiftAddressDO>()
                .eq(OrderGiftAddressDO::getOrderCode, orderCode)
                .eq(OrderGiftAddressDO::getIsDeleted, false)
                .last("LIMIT 1"));
        if(Objects.isNull(giftAddressDO)){
            log.info("订单号{}没有赠品地址信息", orderCode);
            return detailVO;
        }
        BeanUtil.copyProperties(giftAddressDO, detailVO);
        detailVO.setAdCode(giftAddressDO.getAreaCode());
        // 获取省市区的名称
        // 从redis获取名称，redis没有则去数据库获取
        Boolean hasKey = false;
        try {
            hasKey = redisTemplate.hasKey(AddressRedisKeyConstants.PROVINCE_DATA);
        } catch (Exception e) {
            log.warn("redis查询PROVINCE_DATA是否存在异常", e);
        }
        if(Boolean.TRUE.equals(hasKey)){
            getNameFromRedis(detailVO, giftAddressDO);
        }else {
            // 调用system api查询省市区的名称
            callAddressConfigApi(giftAddressDO, detailVO);
        }
        return detailVO;
    }

    /**
     * 调用地址配置API获取省市区名称信息
     * 此方法根据提供的地址代码信息，调用地址配置API以获取详细的省市区名称信息，并将其映射到详情对象中
     *
     * @param giftAddressDO 地址数据对象，包含地址代码信息
     * @param detailVO 地址详情视图对象，用于存储从API获取的地址名称信息
     */
    private void callAddressConfigApi(OrderGiftAddressDO giftAddressDO, AppOrderGiftAddressDetailVO detailVO) {
        try {
            CommonResult<AddressNameDTO> commonResult = addressConfigApi.getNameByCode(giftAddressDO.getProvinceCode(), giftAddressDO.getCityCode(), giftAddressDO.getAreaCode());
            if (commonResult != null && commonResult.getData() != null) {
                BeanUtil.copyProperties(commonResult.getData(), detailVO);
                log.info("调用system api查询省市区的名称成功");
                return;
            }
        } catch (Exception e) {
            log.warn("调用system api查询省市区的名称异常", e);
        }
        log.info("调用system api查询查询省市区名称失败或为空");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderGiftAddress(OrderGiftAddressDTO dto, List<String> orderCodeList) {
        log.info("保存or更新 订单赠品地址信息, dto={}, orderCodeList={}", dto, orderCodeList);
        if (Objects.isNull(dto) || CollUtil.isEmpty(orderCodeList)) {
            return;
        }
        // 如果needGift != 1则不保存
        if (!GiftAddressEnum.YES.getCode().equals(dto.getNeedGift())) {
            return;
        }

        // 1. 查询所有 orderCode 对应的现有记录
        List<OrderGiftAddressDO> existList = orderGiftAddressMapper.selectList(
                new LambdaQueryWrapper<OrderGiftAddressDO>()
                        .in(OrderGiftAddressDO::getOrderCode, orderCodeList)
                        .eq(OrderGiftAddressDO::getIsDeleted, false)
        );
        Map<String, OrderGiftAddressDO> existMap = existList.stream()
                .collect(Collectors.toMap(OrderGiftAddressDO::getOrderCode, Function.identity()));

        // 2. 分组处理：需更新的列表和需插入的列表
        List<OrderGiftAddressDO> insertList = new ArrayList<>();
        List<OrderGiftAddressDO> updateList = new ArrayList<>();

        for (String orderCode : orderCodeList) {
            OrderGiftAddressDO giftAddressDO = BeanUtil.copyProperties(dto, OrderGiftAddressDO.class);
            giftAddressDO.setOrderCode(orderCode);

            OrderGiftAddressDO exist = existMap.get(orderCode);

            if (exist != null) {
                // 存在记录：更新数据（保留ID和乐观锁）
                giftAddressDO.setId(exist.getId());
                updateList.add(giftAddressDO);
            } else {
                insertList.add(giftAddressDO);
            }
        }

        // 3. 批量操作
        if (!updateList.isEmpty()) {
            log.info("批量更新订单赠品地址信息, updateList={}", JSON.toJSONString(updateList));
            // 使用 MyBatis-Plus 的 updateBatchById 进行批量更新
            updateBatchById(updateList);
        }
        if (!insertList.isEmpty()) {
            log.info("批量插入订单赠品地址信息, insertList={}", JSON.toJSONString(insertList));
            orderGiftAddressMapper.insertBatch(insertList);
        }

        // 4. 更新 t_order_info 的 gift_address 字段为 1
        log.info("更新订单表的 gift_address 字段为 1, orderCodeList={}", orderCodeList);
        updateOrderGiftAddressFlag(orderCodeList);
    }

    /**
     * 代客下单 在等待支付页面 可配置地址
     * <p>
     * 优先更新订单表状态（gift_address字段）
     * 当needGift=0: 更新订单表 gift_address=0，不保存地址信息
     * 当needGift=1: 保存地址信息并更新 gift_address=1
     * 当needGift=2: 不做任何操作
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderGiftAddressForValet(OrderGiftAddressDTO dto, List<String> orderCodeList) {
        log.info("代客下单 在等待支付页面 可配置地址, dto={}, orderCodeList={}", JSON.toJSONString(dto), JSON.toJSONString(orderCodeList));
        if (Objects.isNull(dto) || CollUtil.isEmpty(orderCodeList)) {
            return;
        }

        Integer needGift = dto.getNeedGift();
        if (GiftAddressEnum.NOT_REQUIRED.getCode().equals(needGift)) {
            log.info("无需处理地址配置，needGift=2");
            return;
        }

        if (GiftAddressEnum.NO.getCode().equals(needGift)) {
            log.info("更新订单地址标识为0，订单列表: {}", JSON.toJSONString(orderCodeList));
            updateOrderGiftAddressFlagForValet(orderCodeList, 0);
            return;
        }

        // needGift=1 时处理地址保存
        log.info("开始处理赠品地址保存，订单列表: {}", JSON.toJSONString(orderCodeList));
        // 1. 查询所有 orderCode 对应的现有记录
        List<OrderGiftAddressDO> existList = orderGiftAddressMapper.selectList(
                new LambdaQueryWrapper<OrderGiftAddressDO>()
                        .in(OrderGiftAddressDO::getOrderCode, orderCodeList)
                        .eq(OrderGiftAddressDO::getIsDeleted, false)
        );
        Map<String, OrderGiftAddressDO> existMap = existList.stream()
                .collect(Collectors.toMap(OrderGiftAddressDO::getOrderCode, Function.identity()));

        // 2. 分组处理：需更新的列表和需插入的列表
        List<OrderGiftAddressDO> insertList = new ArrayList<>();
        List<OrderGiftAddressDO> updateList = new ArrayList<>();

        for (String orderCode : orderCodeList) {
            OrderGiftAddressDO newAddress = BeanUtil.copyProperties(dto, OrderGiftAddressDO.class);
            newAddress.setOrderCode(orderCode);

            OrderGiftAddressDO exist = existMap.get(orderCode);
            if (exist != null) {
                newAddress.setId(exist.getId());  // 存在记录：更新数据（保留ID和乐观锁）
                updateList.add(newAddress);
            } else {
                insertList.add(newAddress);
            }
        }

        // 3. 批量操作
        if (!updateList.isEmpty()) {
            log.info("saveOrderGiftAddressForValet批量更新订单赠品地址信息, updateList={}", JSON.toJSONString(updateList));
            // 使用 MyBatis-Plus 的 updateBatchById 进行批量更新
            updateBatchById(updateList);
        }
        if (!insertList.isEmpty()) {
            log.info("saveOrderGiftAddressForValet批量插入订单赠品地址信息, insertList={}", JSON.toJSONString(insertList));
            orderGiftAddressMapper.insertBatch(insertList);
        }

        // 4. 更新 t_order_info 的 gift_address 字段为 1
        log.info("更新订单表的 gift_address 字段为 1, orderCodeList={}", JSON.toJSONString(orderCodeList));
        updateOrderGiftAddressFlagForValet(orderCodeList, 1);
    }

    /**
     * 更新订单表的 gift_address 字段为 0 or 1
     */
    private void updateOrderGiftAddressFlagForValet(List<String> orderCodeList, int flag) {
        if (CollUtil.isEmpty(orderCodeList)) {
            return;
        }
        orderInfoDOMapper.update(null,
                new LambdaUpdateWrapper<OrderInfoDO>()
                        .in(OrderInfoDO::getOrderCode, orderCodeList)
                        .set(OrderInfoDO::getGiftAddress, flag)
        );
    }

    /**
     * 更新订单表的 gift_address 字段为 1
     */
    private void updateOrderGiftAddressFlag(List<String> orderCodeList) {
        if (CollUtil.isEmpty(orderCodeList)) {
            return;
        }
        orderInfoDOMapper.update(null,
                new LambdaUpdateWrapper<OrderInfoDO>()
                        .in(OrderInfoDO::getOrderCode, orderCodeList)
                        .set(OrderInfoDO::getGiftAddress, 1)
        );
    }

    /**
     * 从Redis中获取省份、城市和区域名称，并设置到OrderGiftAddressVO对象中
     * 此方法解释了如何通过Redis存储的行政区划数据来丰富订单赠品地址信息
     * 它尝试从Redis中获取对应的省市区名称，并处理可能发生的异常
     *
     * @param detailVO AppOrderGiftAddressDetailVO
     * @param giftAddressDO 订单赠品地址的数据对象，包含地址相关的字段
     */
    private void getNameFromRedis(AppOrderGiftAddressDetailVO detailVO, OrderGiftAddressDO giftAddressDO) {
        try {
            Object provinceName = redisTemplate.opsForHash().get(AddressRedisKeyConstants.PROVINCE_DATA, giftAddressDO.getProvinceCode());
            detailVO.setProvince(Objects.nonNull(provinceName) ? provinceName.toString() : null);
            Object cityName = redisTemplate.opsForHash().get(AddressRedisKeyConstants.CITY_DATA, giftAddressDO.getCityCode());
            detailVO.setCity(Objects.nonNull(cityName) ? cityName.toString() : null);
            Object areaName = redisTemplate.opsForHash().get(AddressRedisKeyConstants.AREA_DATA, giftAddressDO.getAreaCode());
            detailVO.setArea(Objects.nonNull(areaName) ? areaName.toString() : null);
        } catch (Exception e) {
            log.error("redis查询省市区名称异常", e);
        }
    }
}

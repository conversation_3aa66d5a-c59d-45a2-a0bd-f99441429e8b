package com.jlr.ecp.order.enums.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务线code枚举
 */
@Getter
@AllArgsConstructor
public enum BusinessCodeEnum {

    /**
     * 根节点
     */
    ROOT("0L", "根节点"),
    VCS("BUSINESS:001", "VCS"),
    BRAND_GOODS("BrandedGoods", "Branded Goods"),
    LRE("LRE", "LRE"),
    ;

    /**
     * 编码
     */
    private final String code;
    /**
     * 名称
     */
    private final String name;

}

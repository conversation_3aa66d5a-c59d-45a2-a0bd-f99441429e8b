package com.jlr.ecp.order.controller.admin.dashboard.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

@Schema(description = "仪表盘-销售总览 查询请求参数")
@Data
public class SalesSummaryReqDTO {
    @Schema(description = "开始时间，格式：yyyy-MM-dd")
    @Valid
    @NotBlank(message = "订单创建开始时间不能为空")
    private String startTime;

    @Schema(description = "结束时间，格式：yyyy-MM-dd")
    @Valid
    @NotBlank(message = "订单创建结束时间不能为空")
    private String endTime;

    @Schema(description = "业务线编码")
    private String businessCode;
}

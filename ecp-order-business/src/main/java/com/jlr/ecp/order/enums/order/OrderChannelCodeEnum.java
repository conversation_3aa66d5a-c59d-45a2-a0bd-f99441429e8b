package com.jlr.ecp.order.enums.order;

import com.jlr.ecp.order.constant.Constants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum OrderChannelCodeEnum {

    /**
     * 路虎小程序
     */
    LR_WECHAT("MLR", 1, "路虎小程序"),

    /**
     * 捷豹小程序
     */
    JA_WECHAT("MJA", 2, "捷豹小程序"),

//    /**
//     * 官网
//     */
//    OFFICIAL_WEBSITE("S", 3, "官网"),
//
//    /**
//     * APP
//     */
//    APP("A", 4, "APP");

    /**
     * 客户服务: CS （CustomerService）: 待客下单
     */
    CUSTOMER_SERVICE("CS", 5, "代客下单");

    /**
     * 渠道编码 用于生成订单
     * 渠道名 渠道编码
     * 小程序 M
     * 官网  S
     * APP  A
     * <p>
     * 改造逻辑
     * 路虎小程序：MLR
     * 捷豹小程序：MJA
     * 官网  S
     * APP  A
     */

    /**
     * 渠道编码 用于生成订单
     * <p>
     * 渠道名 渠道编码
     * 小程序 M
     * 官网  S
     * APP  A
     * <p>
     * 改造逻辑
     * 路虎小程序：MLR
     * 捷豹小程序：MJA
     * 官网  S
     * APP  A
     */
    private final String channelCode;


    /**
     * 下单渠道 -数据库中
     * 1：路虎小程序
     * 2：捷豹小程序
//     * 3：官网
//     * 4：APP
     * 5：客户服务-待客下单
     */
    private final Integer OrderChannelCode;

    /**
     * 渠道描述 -渠道名
     * 小程序
     * 官网
     * APP
     */
    private final String description;

    /**
     * 根据渠道编码获取对应的描述。
     *
     * @param channelCode 渠道编码
     * @return 对应的渠道描述
     */
    public static String getDescriptionByChannelCode(String channelCode) {
        for (OrderChannelCodeEnum channel : OrderChannelCodeEnum.values()) {
            if (channel.getChannelCode().equals(channelCode)) {
                return channel.getDescription();
            }
        }
        throw new IllegalArgumentException("Invalid channel code: " + channelCode);
    }

    /**
     * 根据数据库中的下单渠道编码获取对应的描述。
     *
     * @param orderChannelCode 下单渠道编码
     * @return 对应的渠道描述
     */
    public static String getDescriptionByOrderChannelCode(Integer orderChannelCode) {
        for (OrderChannelCodeEnum channel : OrderChannelCodeEnum.values()) {
            if (channel.getOrderChannelCode().equals(orderChannelCode)) {
                return channel.getDescription();
            }
        }
        throw new IllegalArgumentException("Invalid order channel code: " + orderChannelCode);
    }

    public static String getOrderSourceByOrderChannelCode(Integer orderChannelCode) {
        if (OrderChannelCodeEnum.CUSTOMER_SERVICE.getOrderChannelCode().equals(orderChannelCode)) {
            return OrderSourceEnum.CUSTOMER_ORDER.getDesc();
        } else if (OrderChannelCodeEnum.LR_WECHAT.getOrderChannelCode().equals(orderChannelCode)
                || OrderChannelCodeEnum.JA_WECHAT.getOrderChannelCode().equals(orderChannelCode)) {
            return OrderSourceEnum.MINI_PROGRAM_ORDER.getDesc();
        } else {
            return "-";
        }
    }

    /**
     * 根据OrderChannelCodeEnum的渠道编码 获取 对应的下单渠道编码
     */
    public static Integer getOrderChannelCodeByChannelCode(String channelCode) {
        for (OrderChannelCodeEnum type : OrderChannelCodeEnum.values()) {
            if (type.getChannelCode().equals(channelCode)) {
                return type.getOrderChannelCode();
            }
        }
        throw new IllegalArgumentException("Invalid channel code: " + channelCode);
    }

    /**
     * Test method to validate getDescriptionByChannelCode()
     */
    public static void testGetDescriptionByChannelCode() {
        // 验证路虎小程序的描述是否正确
        assertEquals(Constants.LAND_ROVER_MINI_PROGRAM, getDescriptionByChannelCode("MLR"));

        // 验证捷豹小程序的描述是否正确
        assertEquals(Constants.JAGUAR_MINI_PROGRAM, getDescriptionByChannelCode("MJA"));

        // 验证官网的描述是否正确
        assertEquals("官网", getDescriptionByChannelCode("S"));

        // 验证APP的描述是否正确
        assertEquals("APP", getDescriptionByChannelCode("A"));

        // 测试非法渠道编码时抛出IllegalArgumentException
        try {
            getDescriptionByChannelCode("INVALID");
        } catch (IllegalArgumentException e) {
            assertEquals("Invalid channel code: INVALID", e.getMessage());
        }
    }

    /**
     *  Test method to validate getDescriptionByOrderChannelCode()
     */
    public static void testGetDescriptionByOrderChannelCode() {
        // 验证下单渠道1对应的描述是否为路虎小程序
        assertEquals(Constants.LAND_ROVER_MINI_PROGRAM, getDescriptionByOrderChannelCode(1));

        // 验证下单渠道2对应的描述是否为捷豹小程序
        assertEquals(Constants.JAGUAR_MINI_PROGRAM, getDescriptionByOrderChannelCode(2));

        // 验证下单渠道3对应的描述是否为官网
        assertEquals("官网", getDescriptionByOrderChannelCode(3));

        // 验证下单渠道4对应的描述是否为APP
        assertEquals("APP", getDescriptionByOrderChannelCode(4));

        // 测试非法下单渠道编码时抛出IllegalArgumentException
        try {
            getDescriptionByOrderChannelCode(99);
        } catch (IllegalArgumentException e) {
            assertEquals("Invalid order channel code: 99", e.getMessage());
        }
    }

    public static void testGetOrderChannelCodeByChannelCode() {
        // 验证路虎小程序的渠道编码转换是否正确
        assertEquals(1, getOrderChannelCodeByChannelCode("MLR").intValue());

        // 验证捷豹小程序的渠道编码转换是否正确
        assertEquals(2, getOrderChannelCodeByChannelCode("MJA").intValue());

        // 验证官网的渠道编码转换是否正确
        assertEquals(3, getOrderChannelCodeByChannelCode("S").intValue());

        // 验证APP的渠道编码转换是否正确
        assertEquals(4, getOrderChannelCodeByChannelCode("A").intValue());

        // 测试非法渠道编码时抛出IllegalArgumentException
        try {
            getOrderChannelCodeByChannelCode("INVALID");
        } catch (IllegalArgumentException e) {
            assertEquals("Invalid channel code: INVALID", e.getMessage());
        }
    }

    public static void main(String[] args) {
        testGetDescriptionByChannelCode();
        testGetDescriptionByOrderChannelCode();
        testGetOrderChannelCodeByChannelCode();
    }
}
package com.jlr.ecp.order.service.order.handler;

import com.jlr.ecp.order.api.order.dto.OrderCreateDTO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;

import java.util.ArrayList;
import java.util.List;

//public class OrderSplitUtil {
//
//    public static List<OrderInfoDO> splitOrders(OrderCreateDTO orderCreateDTO){
//        List<OrderInfoDO> orderInfoDOList = new ArrayList<OrderInfoDO>();
//        OrderSplitHandler handler1 = new ParentOrderHandler();
//        OrderSplitHandler handler2 = new VCSOrderSplitHandler();
//        OrderSplitHandler handler3 = new BrandGoodsOrderHandler();
//
//        handler1.setNext(handler2)
//                .setNext(handler3)
//                .handleRequest(orderCreateDTO,orderInfoDOList);
//
//        return orderInfoDOList;
//    }
//}

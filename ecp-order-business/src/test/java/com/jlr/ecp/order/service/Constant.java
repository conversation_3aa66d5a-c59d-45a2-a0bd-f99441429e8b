package com.jlr.ecp.order.service;

public class Constant {
    private Constant() {
        throw new IllegalStateException("Utility class");
    }
    public static final String CONSUMER_CODE = "427307504914010113";
    public static final String CART_ITEM_CODE = "cartItemCode";
    public static final String PRODUCT_CODE = "productCode";
    public static final String PRODUCT_NAME = "productName";
    public static final String BRAND_CODE = "brandCode";
    public static final String BRAND_NAME = "brandName";
    public static final String CART_CODE = "cartCode";
    public static final String INCONTROL_NAME = "incontrolName";
    public static final String SERIES_NAME = "seriesName";
    public static final String SERIES_CODE = "seriesCode";
    public static final String CAR_VIN = "carVin";
    public static final String PRODUCT_SKU_CODE = "productSkuCode";
    public static final String ATTRIBUTE_VALUES = "attributeValues";
    public static final String RESULT = "result";
    public static final String IN_CONTROL_ID = "<EMAIL>";
    public static final String CUSTOMER_REMARK = "customerRemark";
    public static final String CONTACT_PHONE = "contactPhone";
    public static final String CONTACT_PHONE_MIX = "contactPhoneMix";
    public static final String CONTACT_PHONE_MD5 = "contactPhoneMd5";
    public static final String WX_NICK_NAME = "wxNickName";
    public static final String WX_PHONE = "wxPhone";
    public static final String WX_PHONE_MD5 = "wxPhoneMd5";
    public static final String WX_PHONE_MIX = "wxPhoneMix";
    public static final String CLIENT_ID = "clientId";
    public static final String IN_CONTROL_ID_MIX = "incontrolIdMix";
    public static final String IN_CONTROL_ID_MD5 = "incontrolIdMd5";
    public static final String CAR_VIN_MIX = "carVinMix";
    public static final String CAR_VIN_MD5 = "carVinMd5";
    public static final String VCS_ORDER_CODE = "LRM00120250422194157001185985";
    public static final String LRE_ORDER_CODE = "LEM520250422184954001399232";
    public static final String CODE = "code";
    public static final String ORDER_ITEM_CODE = "orderItemCode";
    public static final String PRODUCT_VERSION_CODE = "productVersionCode";
    public static final String PRODUCT_IMAGE_URL = "productImageUrl";
    public static final String PRODUCT_ATTRIBUTE = "productAttribute";
    public static final String LR = "LR";
    public static final String MLR = "MLR";
    public static final String ORDER_CODE = "orderCode";
    public static final String OPERATOR_REMARK = "operatorRemark";
    public static final String VALUE = "value";
    public static final String PARENT_ORDER_CODE = "parentOrderCode";
    public static final String MODEL_YEAR = "modelYear";
    public static final String CONFIG_CODE = "configCode";
    public static final String CONFIG_NAME = "configName";
    public static final String FULFILMENT_ID = "fulfilmentId";
    public static final String CUSTOMER_ORDER_STATUS_VIEW = "customerOrderStatusView";
    public static final String CUSTOMER_AFTER_SALES_ORDER_STATUS_VIEW = "customerAfterSalesOrderStatusView";
    public static final String OPERATION_ORIGIN_ORDER_CANCEL_STATUS_VIEW = "operationOriginOrderCancelStatusView";
    public static final String REFUND_ORDER_CODE = "refundOrderCode";
    public static final String REJECT_REASON = "rejectReason";
    public static final String INVOICE_TITLE_NAME = "invoiceTitleName";
    public static final String RECIPIENT_NAME = "recipientName";
    public static final String MODIFY_FIELD_OLD_VALUE = "modifyFieldOldValue";
    public static final String DESC = "desc";
    public static final String PHONE_NUMBER = "phoneNumber";
    public static final String PHONE_ENCRYPT = "phoneEncrypt";
    public static final String PHONE_MD5 = "phoneMd5";
    public static final String JLR_ID = "jlrId";
    public static final String REMARK = "remark";
    public static final String LR_CODE = "LRcode";
    public static final String JA_CODE = "JAcode";
    public static final String BG_CODE = "BGM320250417171701001900608";
    public static final String APP_NO = "appNo";
    public static final String CHANNEL_CODE = "channelCode";
    public static final String BRAND_NAME_VIEW = "brandNameView";
    public static final String REFUND_REMARK = "refundRemark";
    public static final String SUBMIT_USER = "submitUser";
    public static final String R_CODE001 = "Rcode001";
    public static final String ORIGIN_ORDER_CODE = "originOrderCode";
    public static final String PRODUCT_SALE_PRICE = "productSalePrice";
    public static final String MESSAGE_ID = "messageId";
    public static final String LR_ORDER_CODE = "LRorderCode";
    public static final String APPLY_CANCEL = "申请部分取消";
    public static final String ORDER_REFUND_CODE = "orderRefundCode";
}

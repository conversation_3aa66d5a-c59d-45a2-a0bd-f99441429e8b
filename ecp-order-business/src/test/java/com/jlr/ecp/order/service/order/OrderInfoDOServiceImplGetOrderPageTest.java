package com.jlr.ecp.order.service.order;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.order.dto.OrderPageReqDTO;
import com.jlr.ecp.order.api.order.dto.OrderRefundStatusMapDTO;
import com.jlr.ecp.order.api.order.vo.brandcategory.OrderNewBrandRespVO;
import com.jlr.ecp.order.api.order.vo.orderlist.OrderSkuItemVo;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackConfigDO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackRecordsDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderStatusMappingDO;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackConfigDOMapper;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackRecordsDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderCouponDetailDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderDiscountDetailDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemLogisticsDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusMappingDOMapper;
import com.jlr.ecp.order.dal.mysql.order.VcsOrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.po.OrderItemRefundVcsPo;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.feedback.FeedBackEnableStatusEnum;
import com.jlr.ecp.order.enums.order.BusinessCodeEnum;
import com.jlr.ecp.order.enums.order.OrderItemAftersalesStatusEnum;
import com.jlr.ecp.order.enums.order.OrderTypeEnum;
import com.jlr.ecp.order.util.PIPLDataUtil;

@RunWith(MockitoJUnitRunner.class)
public class OrderInfoDOServiceImplGetOrderPageTest {

    @Mock
    private OrderInfoDOMapper orderInfoDOMapper;

    @Mock
    private OrderRefundItemDOMapper orderRefundItemDOMapper;

    @Mock
    private OrderStatusMappingDOMapper orderStatusMappingDOMapper;

    @Mock
    private FeedbackRecordsDOMapper feedbackRecordsDOMapper;

    @Mock
    private FeedbackConfigDOMapper feedbackConfigDOMapper;

    @Mock
    private OrderItemLogisticsDOMapper orderItemLogisticsDOMapper;

    @Mock
    private OrderItemDOMapper orderItemDOMapper;

    @Mock
    private RedisService redisService;

    @Mock
    private PIPLDataUtil piplDataUtil;

    @Mock
    private VcsOrderInfoDOMapper vcsOrderInfoDOMapper;

    @Mock
    private OrderCouponDetailDOMapper orderCouponDetailDOMapper;

    @Mock
    private OrderDiscountDetailDOMapper orderDiscountDetailDOMapper;

    @Mock
    private com.jlr.ecp.payment.api.invoice.InvoiceApiV2 invoiceApi;

    @Mock
    private com.jlr.ecp.product.api.snapshot.ProductSnapshotApi productSnapshotApi;

    @InjectMocks
    private OrderInfoDOServiceImpl orderInfoDOService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(vcsOrderInfoDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(orderCouponDetailDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(orderDiscountDetailDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(invoiceApi.getInvokeStatusList(any())).thenReturn(CommonResult.success(new ArrayList<>()));
        when(productSnapshotApi.getProductSnapshotList(any())).thenReturn(CommonResult.success(new ArrayList<>()));
    }

    @Test
    public void testGetOrderPage_EmptyResult() {
        // 准备测试数据
        String consumerCode = "testConsumer";
        OrderPageReqDTO dto = new OrderPageReqDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setOrderStatus("1");

        // 模拟空结果
        Page<OrderInfoDO> page = new Page<>(1, 10);
        page.setRecords(new ArrayList<>());
        when(orderInfoDOMapper.selectConsumerOrderPage(any(), eq(consumerCode), eq("1")))
                .thenReturn(page);

        // 模拟其他必要的依赖
        
        // 模拟订单项查询
        
        // 模拟订单状态映射
        OrderStatusMappingDO statusMapping = new OrderStatusMappingDO();
        statusMapping.setOrderStatus(1);
        statusMapping.setCustomerOrderStatusView("待付款");

        // 执行测试
        PageResult<OrderNewBrandRespVO> result = orderInfoDOService.getOrderPage(consumerCode, dto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList()==null||result.getList().isEmpty());
        assertNull(result.getTotal());
    }

    @Test
    public void testGetOrderPage_WithVCSOrder() {
        // 准备测试数据
        String consumerCode = "testConsumer";
        OrderPageReqDTO dto = new OrderPageReqDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setOrderStatus("1");

        // 模拟订单数据
        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setOrderCode("TEST001");
        orderInfoDO.setBusinessCode(BusinessCodeEnum.VCS.getCode());
        orderInfoDO.setOrderType(OrderTypeEnum.PARENT.getCode());
        orderInfoDO.setOrderStatus(1);
        orderInfoDO.setOrderTime(LocalDateTime.now());
        orderInfoDO.setCostAmount(10000);
        orderInfoDO.setPointAmount(100);
        orderInfoDO.setFreightAmount(1000);
        orderInfoDO.setDiscountTotalAmount(100);
        orderInfoDO.setConsumerCode(consumerCode);
        orderInfoDO.setPaymentStatus(1);
        orderInfoDO.setRefundStatus(0);
        orderInfoDO.setOrderChannel(1);
        orderInfoDO.setCustomerRemark("测试备注");

        Page<OrderInfoDO> page = new Page<>(1, 10);
        page.setRecords(Collections.singletonList(orderInfoDO));
        when(orderInfoDOMapper.selectConsumerOrderPage(any(), eq(consumerCode), eq("1")))
                .thenReturn(page);

        // 模拟VCS订单项数据
        OrderItemRefundVcsPo vcsPo = new OrderItemRefundVcsPo();
        vcsPo.setOrderCode("TEST001");
        vcsPo.setOrderItemCode("ITEM001");
        vcsPo.setAftersalesStatus(OrderItemAftersalesStatusEnum.COMPLETED.getCode());
        vcsPo.setCostAmount(10000);
        vcsPo.setProductVersionCode("PROD001");
        vcsPo.setProductCode("PC001");
        vcsPo.setProductSkuCode("SKU001");
        vcsPo.setProductName("测试商品");
        vcsPo.setProductImageUrl("http://test.com/image.jpg");
        vcsPo.setProductAttribute("颜色:红色");
        vcsPo.setProductMarketPrice(12000);
        vcsPo.setProductSalePrice(10000);
        vcsPo.setProductQuantity(1);
        when(orderInfoDOMapper.selectOrderItemInfo(any())).thenReturn(Collections.singletonList(vcsPo));

        // 模拟状态映射数据
        OrderStatusMappingDO statusMapping = new OrderStatusMappingDO();
        statusMapping.setOrderStatus(1);
        statusMapping.setCustomerOrderStatusView("待付款");
        when(orderStatusMappingDOMapper.selectList(any())).thenReturn(Collections.singletonList(statusMapping));

        // 模拟其他必要数据
        when(redisService.getCacheMap(any())).thenReturn(new HashMap<>());
        when(piplDataUtil.getDecodeListText(any())).thenReturn(new HashMap<>());
        when(feedbackRecordsDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(feedbackConfigDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(orderRefundItemDOMapper.getRefundInfoByOrderItemCodeList(any())).thenReturn(new ArrayList<>());
        when(orderCouponDetailDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(orderDiscountDetailDOMapper.selectList(any())).thenReturn(new ArrayList<>());

        // 执行测试
        PageResult<OrderNewBrandRespVO> result = orderInfoDOService.getOrderPage(consumerCode, dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        OrderNewBrandRespVO respVO = result.getList().get(0);
        
        // 验证订单基本信息
        assertEquals("TEST001", respVO.getOrderCode());
        assertEquals(BusinessCodeEnum.VCS.getCode(), respVO.getBusinessCode());
        assertEquals(OrderTypeEnum.PARENT.getCode(), respVO.getOrderInfo().getOrderType());
        
        // 验证商品信息
        assertNotNull(respVO.getProductList());
        assertEquals(1, respVO.getProductList().size());
        OrderSkuItemVo product = respVO.getProductList().get(0);
        assertEquals("ITEM001", product.getOrderItemCode());
        assertEquals("测试商品", product.getProductName());
        assertEquals("http://test.com/image.jpg", product.getProductImageUrl());
        assertEquals("红色", product.getProductAttribute());
        assertEquals(1, product.getProductQuantity().intValue());
        
        // 验证金额信息
        assertEquals("100.00", respVO.getOrderInfo().getCostAmount());
        assertNull(respVO.getOrderInfo().getShipping());
        assertEquals("100.00", respVO.getOrderInfo().getCostAmountIncludeShipping());
    }

    @Test
    public void testGetOrderPage_WithParentAndChildOrders() {
        // 准备测试数据
        String consumerCode = "testConsumer";
        OrderPageReqDTO dto = new OrderPageReqDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setOrderStatus("1");

        // 模拟父订单数据
        OrderInfoDO parentOrder = new OrderInfoDO();
        parentOrder.setOrderCode("PARENT001");
        parentOrder.setBusinessCode(BusinessCodeEnum.LRE.getCode());
        parentOrder.setOrderType(OrderTypeEnum.PARENT.getCode());
        parentOrder.setOrderStatus(1);
        parentOrder.setOrderTime(LocalDateTime.now());
        parentOrder.setCostAmount(20000);
        parentOrder.setPointAmount(200);
        parentOrder.setFreightAmount(1000);
        parentOrder.setDiscountTotalAmount(200);
        parentOrder.setConsumerCode(consumerCode);
        parentOrder.setPaymentStatus(1);
        parentOrder.setRefundStatus(0);
        parentOrder.setOrderChannel(1);
        parentOrder.setCustomerRemark("测试备注");

        // 模拟子订单数据
        OrderInfoDO childOrder = new OrderInfoDO();
        childOrder.setOrderCode("CHILD001");
        childOrder.setParentOrderCode("PARENT001");
        childOrder.setBusinessCode(BusinessCodeEnum.BRAND_GOODS.getCode());
        childOrder.setOrderType(OrderTypeEnum.VCS.getCode());
        childOrder.setOrderStatus(1);
        childOrder.setCostAmount(10000);
        childOrder.setPointAmount(100);
        childOrder.setFreightAmount(500);
        childOrder.setDiscountTotalAmount(100);
        childOrder.setConsumerCode(consumerCode);
        childOrder.setPaymentStatus(1);
        childOrder.setRefundStatus(0);
        childOrder.setOrderChannel(1);

        Page<OrderInfoDO> page = new Page<>(1, 10);
        page.setRecords(Collections.singletonList(parentOrder));
        when(orderInfoDOMapper.selectConsumerOrderPage(any(), eq(consumerCode), eq("1")))
                .thenReturn(page);

        // 模拟子订单查询结果
        when(orderInfoDOMapper.selectList(any())).thenReturn(Collections.singletonList(childOrder));

        // 模拟订单项数据
        OrderItemRefundVcsPo parentItem = new OrderItemRefundVcsPo();
        parentItem.setOrderCode("PARENT001");
        parentItem.setOrderItemCode("PARENT_ITEM001");
        parentItem.setProductVersionCode("PROD001");
        parentItem.setCostAmount(10000);
        parentItem.setProductCode("PC001");
        parentItem.setProductSkuCode("SKU001");
        parentItem.setProductName("父商品");
        parentItem.setProductImageUrl("http://test.com/parent.jpg");
        parentItem.setProductAttribute("颜色:黑色");
        parentItem.setProductMarketPrice(12000);
        parentItem.setProductSalePrice(10000);
        parentItem.setProductQuantity(1);

        OrderItemRefundVcsPo childItem = new OrderItemRefundVcsPo();
        childItem.setOrderCode("CHILD001");
        childItem.setOrderItemCode("CHILD_ITEM001");
        childItem.setProductVersionCode("PROD002");
        childItem.setCostAmount(5000);
        childItem.setProductCode("PC002");
        childItem.setProductSkuCode("SKU002");
        childItem.setProductName("子商品");
        childItem.setProductImageUrl("http://test.com/child.jpg");
        childItem.setProductAttribute("颜色:白色");
        childItem.setProductMarketPrice(6000);
        childItem.setProductSalePrice(5000);
        childItem.setProductQuantity(1);

        List<OrderItemRefundVcsPo> orderItems = Arrays.asList(parentItem, childItem);
        when(orderInfoDOMapper.selectOrderItemInfo(any())).thenReturn(orderItems);

        // 模拟其他必要数据
        when(redisService.getCacheMap(any())).thenReturn(new HashMap<>());
        when(piplDataUtil.getDecodeListText(any())).thenReturn(new HashMap<>());
        when(orderStatusMappingDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(feedbackRecordsDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(feedbackConfigDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(orderRefundItemDOMapper.getRefundInfoByOrderItemCodeList(any())).thenReturn(new ArrayList<>());
        when(orderCouponDetailDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(orderDiscountDetailDOMapper.selectList(any())).thenReturn(new ArrayList<>());

        // 执行测试
        PageResult<OrderNewBrandRespVO> result = orderInfoDOService.getOrderPage(consumerCode, dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        OrderNewBrandRespVO respVO = result.getList().get(0);
        assertEquals("PARENT001", respVO.getOrderCode());
        assertEquals(BusinessCodeEnum.LRE.getCode(), respVO.getBusinessCode());
        assertNotNull(respVO.getProductList());
        assertEquals(1, respVO.getProductList().size());
    }

    @Test
    public void testGetOrderPage_WithRefundStatus() {
        // 准备测试数据
        String consumerCode = "testConsumer";
        OrderPageReqDTO dto = new OrderPageReqDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setOrderStatus("1");

        // 模拟订单数据
        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setOrderCode("TEST001");
        orderInfoDO.setBusinessCode(BusinessCodeEnum.VCS.getCode());
        orderInfoDO.setOrderType(OrderTypeEnum.PARENT.getCode());
        orderInfoDO.setOrderStatus(1);
        orderInfoDO.setOrderTime(LocalDateTime.now());
        orderInfoDO.setCostAmount(10000);
        orderInfoDO.setPointAmount(100);
        orderInfoDO.setFreightAmount(1000);
        orderInfoDO.setDiscountTotalAmount(100);
        orderInfoDO.setConsumerCode(consumerCode);
        orderInfoDO.setPaymentStatus(1);
        orderInfoDO.setRefundStatus(0);
        orderInfoDO.setOrderChannel(1);
        orderInfoDO.setCustomerRemark("测试备注");

        Page<OrderInfoDO> page = new Page<>(1, 10);
        page.setRecords(Collections.singletonList(orderInfoDO));
        when(orderInfoDOMapper.selectConsumerOrderPage(any(), eq(consumerCode), eq("1")))
                .thenReturn(page);

        // 模拟订单项数据
        OrderItemRefundVcsPo vcsPo = new OrderItemRefundVcsPo();
        vcsPo.setOrderCode("TEST001");
        vcsPo.setOrderItemCode("ITEM001");
        vcsPo.setAftersalesStatus(OrderItemAftersalesStatusEnum.COMPLETED.getCode());
        vcsPo.setCostAmount(10000);
        vcsPo.setProductVersionCode("PROD001");
        vcsPo.setProductCode("PC001");
        vcsPo.setProductSkuCode("SKU001");
        vcsPo.setProductName("测试商品");
        vcsPo.setProductImageUrl("http://test.com/image.jpg");
        vcsPo.setProductAttribute("颜色:红色");
        vcsPo.setProductMarketPrice(12000);
        vcsPo.setProductSalePrice(10000);
        vcsPo.setProductQuantity(1);
        when(orderInfoDOMapper.selectOrderItemInfo(any())).thenReturn(Collections.singletonList(vcsPo));

        // 模拟退款状态数据
        OrderRefundStatusMapDTO refundStatus = new OrderRefundStatusMapDTO();
        refundStatus.setOrderItemCode("ITEM001");
        refundStatus.setRefundOrderStatus(2);
        when(orderRefundItemDOMapper.getRefundInfoByOrderItemCodeList(any())).thenReturn(Collections.singletonList(refundStatus));

        // 模拟状态映射数据
        OrderStatusMappingDO statusMapping = new OrderStatusMappingDO();
        statusMapping.setOrderStatus(1);
        statusMapping.setRefundOrderStatus(2);
        statusMapping.setCustomerOrderStatusView("退款中");
        when(orderStatusMappingDOMapper.selectList(any())).thenReturn(Collections.singletonList(statusMapping));

        // 模拟其他必要数据
        when(redisService.getCacheMap(any())).thenReturn(new HashMap<>());
        when(piplDataUtil.getDecodeListText(any())).thenReturn(new HashMap<>());
        when(feedbackRecordsDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(feedbackConfigDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(orderRefundItemDOMapper.getRefundInfoByOrderItemCodeList(any())).thenReturn(new ArrayList<>());
        when(orderCouponDetailDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(orderDiscountDetailDOMapper.selectList(any())).thenReturn(new ArrayList<>());

        // 执行测试
        PageResult<OrderNewBrandRespVO> result = orderInfoDOService.getOrderPage(consumerCode, dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        OrderNewBrandRespVO respVO = result.getList().get(0);
        assertEquals("TEST001", respVO.getOrderCode());
        assertNotNull(respVO.getOrderInfo());
    }

    @Test
    public void testGetOrderPage_WithFeedback() {
        // 准备测试数据
        String consumerCode = "testConsumer";
        OrderPageReqDTO dto = new OrderPageReqDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setOrderStatus("1");

        // 模拟订单数据
        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setOrderCode("TEST001");
        orderInfoDO.setBusinessCode(BusinessCodeEnum.VCS.getCode());
        orderInfoDO.setOrderType(OrderTypeEnum.PARENT.getCode());
        orderInfoDO.setOrderStatus(1);
        orderInfoDO.setOrderTime(LocalDateTime.now());
        orderInfoDO.setCostAmount(10000);
        orderInfoDO.setPointAmount(100);
        orderInfoDO.setFreightAmount(1000);
        orderInfoDO.setDiscountTotalAmount(100);
        orderInfoDO.setConsumerCode(consumerCode);
        orderInfoDO.setPaymentStatus(1);
        orderInfoDO.setRefundStatus(0);
        orderInfoDO.setOrderChannel(1);
        orderInfoDO.setCustomerRemark("测试备注");

        Page<OrderInfoDO> page = new Page<>(1, 10);
        page.setRecords(Collections.singletonList(orderInfoDO));
        when(orderInfoDOMapper.selectConsumerOrderPage(any(), eq(consumerCode), eq("1")))
                .thenReturn(page);

        // 模拟订单项数据
        OrderItemRefundVcsPo vcsPo = new OrderItemRefundVcsPo();
        vcsPo.setOrderCode("TEST001");
        vcsPo.setOrderItemCode("ITEM001");
        vcsPo.setAftersalesStatus(OrderItemAftersalesStatusEnum.COMPLETED.getCode());
        vcsPo.setCostAmount(10000);
        vcsPo.setProductVersionCode("PROD001");
        vcsPo.setProductCode("PC001");
        vcsPo.setProductSkuCode("SKU001");
        vcsPo.setProductName("测试商品");
        vcsPo.setProductImageUrl("http://test.com/image.jpg");
        vcsPo.setProductAttribute("颜色:红色");
        vcsPo.setProductMarketPrice(12000);
        vcsPo.setProductSalePrice(10000);
        vcsPo.setProductQuantity(1);
        when(orderInfoDOMapper.selectOrderItemInfo(any())).thenReturn(Collections.singletonList(vcsPo));

        // 模拟评价记录数据
        FeedbackRecordsDO feedbackRecord = new FeedbackRecordsDO();
        feedbackRecord.setOrderCode("TEST001");
        feedbackRecord.setIsDeleted(false);
        when(feedbackRecordsDOMapper.selectList(any())).thenReturn(Collections.singletonList(feedbackRecord));

        // 模拟评价配置数据
        FeedbackConfigDO feedbackConfig = new FeedbackConfigDO();
        feedbackConfig.setEnableStatus(FeedBackEnableStatusEnum.ENABLE.getCode());
        feedbackConfig.setIsDeleted(false);
        when(feedbackConfigDOMapper.selectList(any())).thenReturn(Collections.singletonList(feedbackConfig));

        // 模拟状态映射数据
        OrderStatusMappingDO statusMapping = new OrderStatusMappingDO();
        statusMapping.setOrderStatus(1);
        statusMapping.setCustomerOrderStatusView("待付款");
        when(orderStatusMappingDOMapper.selectList(any())).thenReturn(Collections.singletonList(statusMapping));

        // 模拟其他必要数据
        when(redisService.getCacheMap(any())).thenReturn(new HashMap<>());
        when(piplDataUtil.getDecodeListText(any())).thenReturn(new HashMap<>());
        when(orderRefundItemDOMapper.getRefundInfoByOrderItemCodeList(any())).thenReturn(new ArrayList<>());
        when(orderCouponDetailDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(orderDiscountDetailDOMapper.selectList(any())).thenReturn(new ArrayList<>());

        // 执行测试
        PageResult<OrderNewBrandRespVO> result = orderInfoDOService.getOrderPage(consumerCode, dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        OrderNewBrandRespVO respVO = result.getList().get(0);
        assertEquals("TEST001", respVO.getOrderCode());
        assertNotNull(respVO.getTagInfo());
    }
} 
package com.jlr.ecp.order.service.logistics;

import com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil;
import com.jlr.ecp.logistics.api.packageinfo.dto.PackageInfoQueryRespDTO;
import com.jlr.ecp.order.api.order.dto.OrderLogisticStatusChangeReqDto;
import com.jlr.ecp.order.dal.dataobject.order.*;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemLogisticsDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.logistic.DeliveryNotificationEnum;
import com.jlr.ecp.order.enums.order.BgOrderItemLogisticsStatusEnum;
import com.jlr.ecp.order.enums.order.OrderItemLogisticsStatusEnum;
import com.jlr.ecp.order.enums.order.OrderLogisticsStatusEnum;
import com.jlr.ecp.order.service.logistic.OrderItemLogisticService;
import com.jlr.ecp.order.service.order.OrderItemLogisticsDOService;
import com.jlr.ecp.order.util.PIPLDataUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrderItemLogisticServiceTest {

    @InjectMocks
    private OrderItemLogisticService orderItemLogisticService;

    @Mock
    private OrderItemLogisticsDOService orderItemLogisticsDOService;

    @Mock
    private OrderItemLogisticsDOMapper orderItemLogisticsDOMapper;

    @Mock
    private OrderItemDOMapper orderItemDOMapper;

    @Mock
    private OrderInfoDOMapper orderInfoDOMapper;

    @Mock
    private PIPLDataUtil piplDataUtil;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testUpdateOrderLogisticSign_NoData() {
        // Mock 数据为空的情况
        when(orderItemLogisticsDOMapper.getNotSignList(any())).thenReturn(null);

        Integer result = orderItemLogisticService.updateOrderLogisticSign();

        assertEquals(0, result.intValue());
        verify(orderItemLogisticsDOMapper, times(1)).getNotSignList(any());
    }

    @Test
    public void testUpdateOrderLogisticSign_WithData() {
        // Mock 数据不为空的情况
        List<OrderItemLogisticsDO> mockLogisticsList = new ArrayList<>();
        OrderItemLogisticsDO logisticsDO = new OrderItemLogisticsDO();
        logisticsDO.setId(1L);
        logisticsDO.setLogisticsCompany("SF");
        logisticsDO.setLogisticsNo("123456");
        logisticsDO.setRecipientPhone("encryptedPhone");
        logisticsDO.setIsSignTask(0);
        mockLogisticsList.add(logisticsDO);

        when(orderItemLogisticsDOMapper.getNotSignList(any())).thenReturn(mockLogisticsList);
        when(piplDataUtil.getDecodeText(anyString())).thenReturn("1234567890");
        PackageInfoQueryRespDTO packageInfo = new PackageInfoQueryRespDTO();
        packageInfo.setState("签收");
        when(orderItemLogisticsDOService.getLogisticsInfo(anyString(), anyString(), anyString())).thenReturn(packageInfo);

        Integer result = orderItemLogisticService.updateOrderLogisticSign();

        assertEquals(1, result.intValue());
        verify(orderItemLogisticsDOMapper, times(1)).updateBatch(anyList());
    }

    @Test
    public void testCheckAndBuildInfoStatus_AllShipped() {
        // Mock 输入数据
        OrderLogisticStatusChangeReqDto reqDto = new OrderLogisticStatusChangeReqDto();
        OrderLogisticStatusChangeReqDto.OrderItemLogisticStatusChangeReqDto item = new OrderLogisticStatusChangeReqDto.OrderItemLogisticStatusChangeReqDto();
        item.setOrderItemCode("item1");
        item.setLogisticsCode("SF");
        item.setTrackingNumber("123456");
        reqDto.setOrderItemLogisticStatusChangeList(List.of(item));

        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setLogisticsStatus(OrderLogisticsStatusEnum.PENDING_SHIPMENT.getCode());

        List<OrderItemDO> orderItemDOList = new ArrayList<>();
        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode("item1");
        orderItemDOList.add(orderItemDO);

        List<OrderItemLogisticsDO> orderItemLogisticsDOS = new ArrayList<>();
        OrderItemLogisticsDO logisticsDO = new OrderItemLogisticsDO();
        logisticsDO.setOrderItemCode("item1");
        logisticsDO.setLogisticsStatus(BgOrderItemLogisticsStatusEnum.IN_TRANSIT.getCode());
        orderItemLogisticsDOS.add(logisticsDO);

        DeliveryNotificationEnum result = orderItemLogisticService.checkAndBuildInfoStatus(reqDto, orderInfoDO, orderItemDOList, orderItemLogisticsDOS);

        assertEquals(DeliveryNotificationEnum.FULLY_SHIPPED, result);
        verify(orderInfoDOMapper, times(0)).updateById(any());
    }

    @Test
    public void testCheckAndBuildInfoStatus_PartiallyShipped() {
        // Mock 输入数据
        OrderLogisticStatusChangeReqDto reqDto = new OrderLogisticStatusChangeReqDto();
        OrderLogisticStatusChangeReqDto.OrderItemLogisticStatusChangeReqDto item = new OrderLogisticStatusChangeReqDto.OrderItemLogisticStatusChangeReqDto();
        item.setOrderItemCode("item1");
        item.setLogisticsCode("SF");
        item.setTrackingNumber("123456");
        reqDto.setOrderItemLogisticStatusChangeList(List.of(item));

        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setLogisticsStatus(OrderLogisticsStatusEnum.PARTIALLY_SHIPPED.getCode());

        List<OrderItemDO> orderItemDOList = new ArrayList<>();
        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode("item1");
        orderItemDOList.add(orderItemDO);

        List<OrderItemLogisticsDO> orderItemLogisticsDOS = new ArrayList<>();
        OrderItemLogisticsDO logisticsDO = new OrderItemLogisticsDO();
        logisticsDO.setOrderItemCode("item1");
        logisticsDO.setLogisticsStatus(BgOrderItemLogisticsStatusEnum.IN_TRANSIT.getCode());
        orderItemLogisticsDOS.add(logisticsDO);

        DeliveryNotificationEnum result = orderItemLogisticService.checkAndBuildInfoStatus(reqDto, orderInfoDO, orderItemDOList, orderItemLogisticsDOS);

        assertEquals(DeliveryNotificationEnum.FULLY_SHIPPED, result);
        verify(orderInfoDOMapper, times(0)).updateById(any());
    }
}

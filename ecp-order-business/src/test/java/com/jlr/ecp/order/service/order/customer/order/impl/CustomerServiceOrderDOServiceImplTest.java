package com.jlr.ecp.order.service.order.customer.order.impl;

import com.jlr.ecp.order.dal.mysql.customer.order.CustomerServiceOrderDOMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CustomerServiceOrderDOServiceImplTest {
    @Mock
    private CustomerServiceOrderDOMapper customerServiceOrderDOMapper;

    @InjectMocks
    private CustomerServiceOrderDOServiceImpl customerServiceOrderDOServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        customerServiceOrderDOServiceImplUnderTest = new CustomerServiceOrderDOServiceImpl();
    }

    @Test
    public void testGetBaseMapper() throws Exception {
        // Setup

        // Run the test
        final CustomerServiceOrderDOMapper result = customerServiceOrderDOServiceImplUnderTest.getBaseMapper();

        // Verify the results
    }
}

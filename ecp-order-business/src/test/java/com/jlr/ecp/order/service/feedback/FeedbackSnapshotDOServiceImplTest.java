package com.jlr.ecp.order.service.feedback;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.controller.admin.feedback.dto.FeedbackPageReqDTO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedbackVserionListVO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackConfigDO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackDimensionsDO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackRecordsDO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackSnapshotDO;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackRecordsDOMapper;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackSnapshotDOMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
@RunWith(MockitoJUnitRunner.class)
public class FeedbackSnapshotDOServiceImplTest {

    @Mock
    private FeedbackSnapshotDOMapper mockFeedbackSnapshotDOMapper; // 修正为 FeedbackSnapshotDOMapper

    @InjectMocks
    private FeedbackSnapshotDOServiceImpl feedbackSnapshotDOServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), FeedbackConfigDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), FeedbackSnapshotDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), FeedbackDimensionsDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), FeedbackRecordsDO.class);
    }

    @Test
    public void testSelectHistoryPage() {
        // Setup
        final FeedbackPageReqDTO dto = new FeedbackPageReqDTO();
        dto.setFeedbackCode("feedbackCode");

        final FeedbackVserionListVO feedbackVserionListVO = new FeedbackVserionListVO();
        feedbackVserionListVO.setFeedbackCode("feedbackCode");
        feedbackVserionListVO.setSnapshotCode("snapshotCode");
        feedbackVserionListVO.setDowntime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackVserionListVO.setEnableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackVserionListVO.setSubmitNum(0);
        final PageResult<FeedbackVserionListVO> expectedResult = new PageResult<>(List.of(feedbackVserionListVO), 0L);

        // Configure FeedbackSnapshotDOMapper.selectPage(...).
        final FeedbackSnapshotDO feedbackSnapshotDO = new FeedbackSnapshotDO();
        feedbackSnapshotDO.setFeedbackCode("feedbackCode");
        feedbackSnapshotDO.setSnapshotCode("snapshotCode");
        feedbackSnapshotDO.setDowntime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackSnapshotDO.setEnableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackSnapshotDO.setSubmitNum(0);
        final PageResult<FeedbackSnapshotDO> feedbackSnapshotDOPageResult = new PageResult<>(List.of(feedbackSnapshotDO), 1L);
        when(mockFeedbackSnapshotDOMapper.selectPage(eq(dto), any(LambdaQueryWrapperX.class)))
                .thenReturn(feedbackSnapshotDOPageResult);

        // Run the test
        final PageResult<FeedbackVserionListVO> result = feedbackSnapshotDOServiceImplUnderTest.selectHistoryPage(dto);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetSnapshotCodeByFeedbackCode() {
        // Setup
        final FeedbackVserionListVO feedbackVserionListVO = new FeedbackVserionListVO();
        feedbackVserionListVO.setFeedbackCode("feedbackCode");
        feedbackVserionListVO.setSnapshotCode("snapshotCode");
        feedbackVserionListVO.setDowntime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackVserionListVO.setEnableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackVserionListVO.setSubmitNum(0);
        final List<FeedbackVserionListVO> expectedResult = List.of(feedbackVserionListVO);

        // Configure FeedbackSnapshotDOMapper.getSnapshotCodeByFeedbackCode(...).
        final FeedbackSnapshotDO feedbackSnapshotDO = new FeedbackSnapshotDO();
        feedbackSnapshotDO.setFeedbackCode("feedbackCode");
        feedbackSnapshotDO.setSnapshotCode("snapshotCode");
        feedbackSnapshotDO.setDowntime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackSnapshotDO.setEnableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackSnapshotDO.setSubmitNum(0);
        when(mockFeedbackSnapshotDOMapper.getSnapshotCodeByFeedbackCode("feedbackCode"))
                .thenReturn(List.of(feedbackSnapshotDO));

        // Run the test
        final List<FeedbackVserionListVO> result = feedbackSnapshotDOServiceImplUnderTest.getSnapshotCodeByFeedbackCode(
                "feedbackCode");

        assertThat(result).isNotNull();
    }
}


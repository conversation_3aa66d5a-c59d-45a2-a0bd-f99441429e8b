package com.jlr.ecp.order.service.dashboard.impl;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.controller.admin.dashboard.dto.*;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ChartRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.SalesSummaryRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.TableRespVo;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class SalesOverviewServiceImplTest {

    @Mock
    private OrderInfoDOMapper mockOrderInfoDOMapper;
    @Mock
    private OrderRefundItemDOMapper mockOrderRefundItemDOMapper;
    @Mock
    private RedisService mockRedisService;

    @InjectMocks
    private SalesOverviewServiceImpl salesOverviewServiceImplUnderTest;

    @Test
    public void testGetSalesSummary() {
        // Setup
        final SalesSummaryReqDTO reqDTO = new SalesSummaryReqDTO();
        reqDTO.setStartTime("2020-01-01");
        reqDTO.setEndTime("2021-01-01");
        reqDTO.setBusinessCode("businessCode");

        final SalesSummaryRespVo expectedResult = new SalesSummaryRespVo();
        expectedResult.setTotalOrderCount(new BigDecimal("0.00"));
        expectedResult.setTotalPaidCount(new BigDecimal("0.00"));
        expectedResult.setTotalGmv(new BigDecimal("0.00"));
        expectedResult.setTotalIncome(new BigDecimal("0.00"));
        expectedResult.setTotalCustomerPrice(new BigDecimal("0"));
        expectedResult.setTotalRefundAmount(new BigDecimal("0.00"));

        // Configure OrderInfoDOMapper.countTotalOrderCount(...).
        final ValetDashboardTempDTO valetDashboardTempDTO = new ValetDashboardTempDTO();
        valetDashboardTempDTO.setValetOrderCount(new BigDecimal("0.00"));
        valetDashboardTempDTO.setTotalOrderCount(new BigDecimal("0.00"));
        valetDashboardTempDTO.setTotalPaidCount(new BigDecimal("0.00"));
        valetDashboardTempDTO.setTotalGmv(new BigDecimal("0.00"));
        valetDashboardTempDTO.setTotalRefundAmount(new BigDecimal("0.00"));
        when(mockOrderInfoDOMapper.countTotalOrderCount(anyString(), anyString(), eq("businessCode")))
                .thenReturn(valetDashboardTempDTO);

        // Configure OrderInfoDOMapper.countTotalOrderByPaymentStatus(...).
        final ValetDashboardTempDTO valetDashboardTempDTO1 = new ValetDashboardTempDTO();
        valetDashboardTempDTO1.setValetOrderCount(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setTotalOrderCount(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setTotalPaidCount(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setTotalGmv(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setTotalRefundAmount(new BigDecimal("0.00"));
        when(mockOrderInfoDOMapper.countTotalOrderByPaymentStatus(anyString(), anyString(), eq("businessCode")))
                .thenReturn(valetDashboardTempDTO1);

        // Configure OrderRefundItemDOMapper.getTotalRefundAmounts(...).
        final ValetDashboardTempDTO valetDashboardTempDTO2 = new ValetDashboardTempDTO();
        valetDashboardTempDTO2.setValetOrderCount(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setTotalOrderCount(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setTotalPaidCount(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setTotalGmv(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setTotalRefundAmount(new BigDecimal("0.00"));
        when(mockOrderRefundItemDOMapper.getTotalRefundAmounts(anyString(), anyString(), eq("businessCode")))
                .thenReturn(valetDashboardTempDTO2);

        // Run the test
        final SalesSummaryRespVo result = salesOverviewServiceImplUnderTest.getSalesSummary(reqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySalesSummaryCharts_TOTAL_ORDER_NUMBER() {
        // Setup
        final SummaryQueryReqDTO dto = new SummaryQueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setKpi(1);

        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult("0");

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = salesOverviewServiceImplUnderTest.querySalesSummaryCharts(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @NotNull
    private CommonResult<ProductSalesTrendRespVo> getProductSalesTrendRespVoCommonResult(String number) {
        // 构建期望结果
        final ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();

        // 构建 ChartRespVo
        final ChartRespVo chart = new ChartRespVo();
        chart.setXAxis(List.of("2020", "2021"));

        // 构建 ChartRespVo.ChartItemVo
        final ChartRespVo.ChartItemVo chartItemVo1 = new ChartRespVo.ChartItemVo();
        chartItemVo1.setName("Land Rover");
        chartItemVo1.setData(List.of("0", "0"));

        final ChartRespVo.ChartItemVo chartItemVo2 = new ChartRespVo.ChartItemVo();
        chartItemVo2.setName("Jaguar");
        chartItemVo2.setData(List.of("0", "0"));

        chart.setDataList(List.of(chartItemVo1, chartItemVo2));
        productSalesTrendRespVo.setChart(chart);

        // 构建 TableRespVo
        final TableRespVo table = new TableRespVo();

        // 构建 TableRespVo.HeaderItem
        final TableRespVo.HeaderItem headerItem1 = new TableRespVo.HeaderItem();
        headerItem1.setProp("firstColumn");
        headerItem1.setLabel("");

        final TableRespVo.HeaderItem headerItem2 = new TableRespVo.HeaderItem();
        headerItem2.setProp("total");
        headerItem2.setLabel("");

        final TableRespVo.HeaderItem headerItem3 = new TableRespVo.HeaderItem();
        headerItem3.setProp("2020");
        headerItem3.setLabel("2020");

        final TableRespVo.HeaderItem headerItem4 = new TableRespVo.HeaderItem();
        headerItem4.setProp("2021");
        headerItem4.setLabel("2021");

        table.setHeaders(List.of(headerItem1, headerItem2, headerItem3, headerItem4));

        // 构建 TableRespVo.TableData
        final Map<String, String> tableData1 = Map.ofEntries(
                Map.entry("total", number),
                Map.entry("firstColumn", "Land Rover"),
                Map.entry("2021", "0"),
                Map.entry("2020", "0")
        );

        final Map<String, String> tableData2 = Map.ofEntries(
                Map.entry("total", number),
                Map.entry("firstColumn", "Jaguar"),
                Map.entry("2021", "0"),
                Map.entry("2020", "0")
        );

        table.setTableData(List.of(tableData1, tableData2));
        productSalesTrendRespVo.setTable(table);

        final CommonResult<ProductSalesTrendRespVo> expectedResult = CommonResult.success(productSalesTrendRespVo);

        // Configure OrderInfoDOMapper.getSalesSummaryGroupChannel(...).
        final SummarySqlResultDTO summarySqlResultDTO = new SummarySqlResultDTO();
        summarySqlResultDTO.setCode("code");
        summarySqlResultDTO.setLabel("label");
        summarySqlResultDTO.setQuantity(0L);
        summarySqlResultDTO.setAmount(0L);
        final List<SummarySqlResultDTO> summarySqlResultDTOS = List.of(summarySqlResultDTO);
        when(mockOrderInfoDOMapper.getSalesSummaryGroupChannel(any(SqlQueryDTO.class))).thenReturn(summarySqlResultDTOS);

        // Configure OrderInfoDOMapper.getSalesSummaryRefund(...).
        final SummarySqlResultDTO summarySqlResultDTO1 = new SummarySqlResultDTO();
        summarySqlResultDTO1.setCode("code");
        summarySqlResultDTO1.setLabel("label");
        summarySqlResultDTO1.setQuantity(0L);
        summarySqlResultDTO1.setAmount(0L);
        final List<SummarySqlResultDTO> summarySqlResultDTOS1 = List.of(summarySqlResultDTO1);
        when(mockOrderInfoDOMapper.getSalesSummaryRefund(any(SqlQueryDTO.class))).thenReturn(summarySqlResultDTOS1);

        return expectedResult;
    }

    @Test
    public void testQuerySalesSummaryCharts_TOTAL_ORDER_AMOUNT() {
        // Setup
        final SummaryQueryReqDTO dto = new SummaryQueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setKpi(3);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult("0.00");

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = salesOverviewServiceImplUnderTest.querySalesSummaryCharts(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }


    @Test
    public void testQuerySalesSummaryCharts_INCOME() {
        // Setup
        final SummaryQueryReqDTO dto = new SummaryQueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setKpi(4);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult("0.00");

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = salesOverviewServiceImplUnderTest.querySalesSummaryCharts(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }


    @Test
    public void testQuerySalesSummaryCharts_REFUND() {
        // Setup
        final SummaryQueryReqDTO dto = new SummaryQueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setKpi(5);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult("0.00");


        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = salesOverviewServiceImplUnderTest.querySalesSummaryCharts(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySalesSummaryCharts_vehicle_INCOME() {
        // Setup
        final SummaryQueryReqDTO dto = new SummaryQueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setKpi(4);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult("0.00");


        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = salesOverviewServiceImplUnderTest.querySalesSummaryCharts(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySalesSummaryCharts_vehicle_TOTAL_ORDER_NUMBER() {
        // Setup
        final SummaryQueryReqDTO dto = new SummaryQueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setKpi(1);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult("0.00");


        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = salesOverviewServiceImplUnderTest.querySalesSummaryCharts(dto);

        // Verify the results
        assertEquals(expectedResult.getData().getChart(), result.getData().getChart());
    }

    @Test
    public void testQuerySalesSummaryCharts_vehicle_TOTAL_ORDER_AMOUNT() {
        // Setup
        final SummaryQueryReqDTO dto = new SummaryQueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setKpi(3);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult("0.00");


        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = salesOverviewServiceImplUnderTest.querySalesSummaryCharts(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySalesSummaryCharts_vehicle_REFUND() {
        // Setup
        final SummaryQueryReqDTO dto = new SummaryQueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setKpi(5);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult("0.00");


        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = salesOverviewServiceImplUnderTest.querySalesSummaryCharts(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }


    @Test
    public void testSalesKpiList() {
        // Setup
        final CommonResult<List<KpiQueryDTO>> expectedResult = CommonResult.success(
                List.of(
                        new KpiQueryDTO(1, "总订单数", "Total Order Counts", "总订单数", "pcs"),
                        new KpiQueryDTO(2, "成交订单数", "Turnover Order Counts", "成交订单数", "pcs"),
                        new KpiQueryDTO(3, "成交订单总交易额", "Turnover Order Transactional Volume", "成交订单总交易额", "RMB"),
                        new KpiQueryDTO(4, "总收入", "Total Income", "总收入", "RMB"),
                        new KpiQueryDTO(5, "总退款额", "Total Refund Volume", "总退款额", "RMB")
                )
        );

        // Run the test
        final CommonResult<List<KpiQueryDTO>> result = salesOverviewServiceImplUnderTest.salesKpiList();

        // Verify the results
        assertEquals(expectedResult, result);
    }

}

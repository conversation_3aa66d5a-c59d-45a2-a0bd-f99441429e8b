package com.jlr.ecp.order.service.refund;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.security.core.service.SecurityFrameworkService;
import com.jlr.ecp.notification.api.jaguarlandover.ShorLinkAPI;
import com.jlr.ecp.order.api.order.vo.coupon.ECouponOrderDetailVO;
import com.jlr.ecp.order.api.order.vo.detail.ProductItemInfoAppVO;
import com.jlr.ecp.order.api.refund.dto.*;
import com.jlr.ecp.order.api.refund.vo.*;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.app.refund.vo.RefundDetailLogVO;
import com.jlr.ecp.order.controller.app.refund.vo.RefundDetailVO;
import com.jlr.ecp.order.convert.OrderRefundDOConvert;
import com.jlr.ecp.order.convert.OrderRefundItemDOConvert;
import com.jlr.ecp.order.dal.dataobject.order.*;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundAttachmentDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundPaymentRecordsDO;
import com.jlr.ecp.order.dal.mysql.order.*;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundAttachmentDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundPaymentRecordsMapper;
import com.jlr.ecp.order.enums.phone.PhoneNumberDecodeUtil;
import com.jlr.ecp.order.handle.RefundHandler;
import com.jlr.ecp.order.service.refund.bg.BrandGoodsOrderRefundDOService;
import com.jlr.ecp.order.util.IdempotentCheckUtil;
import com.jlr.ecp.order.util.PIPLDataUtil;
import com.jlr.ecp.order.util.machine.EcouponOrderRefundStatusMachine;
import com.jlr.ecp.order.util.machine.LogisticsOrderRefundStatusMachine;
import com.jlr.ecp.order.util.machine.OrderRefundStatusMachine;
import com.jlr.ecp.payment.api.order.PayCenterOrderApi;
import com.jlr.ecp.payment.api.order.vo.SubmitRefundOrderResp;
import com.jlr.ecp.payment.api.refund.dto.PayRefundCreateReqDTO;
import com.jlr.ecp.subscription.api.icrvehicle.IcrVehicleApi;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleRespVO;
import com.jlr.ecp.subscription.api.subscripiton.SubscriptionServiceApi;
import com.jlr.ecp.subscription.api.vcsorderfufilment.VcsOrderFulfilmentApi;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.VcsOrderFulfilmentRespVO;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static com.jlr.ecp.order.service.Constant.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrderRefundDOServiceImplTest {

    @Mock
    private OrderRefundItemDOConvert mockOrderRefundItemDOConvert;
    @Mock
    private OrderRefundDOConvert mockOrderRefundDOConvert;
    @Mock
    private OrderRefundDOMapper orderRefundMapper;
    @Mock
    private OrderInfoDOMapper mockOrderInfoMapper;
    @Mock
    private OrderRefundItemDOMapper refundItemMapper;
    @Mock
    private OrderStatusLogDOMapper mockStatusLogMapper;
    @Mock
    private OrderRefundAttachmentDOMapper mockAttachmentMapper;
    @Mock
    private OrderItemDOMapper mockOrderItemMapper;
    @Mock
    private OrderRefundStatusMachine mockOrderRefundStatusMachine;
    @Mock
    private VcsOrderInfoDOMapper mockVcsOrderInfoDOMapper;
    @Mock
    private PIPLDataUtil mockPiplDataUtil;
    @Mock
    private ProducerTool mockProducerTool;
    @Mock
    private OrderModifyDetailLogDOMapper mockModifyDetailLogMapper;
    @Mock
    private OrderItemLogisticsDOMapper mockOrderItemLogisticsMapper;
    @Mock
    private OrderDiscountDetailDOMapper mockOrderDiscountDetailDOMapper;
    @Mock
    private IcrVehicleApi mockIcrVehicleApi;
    @Mock
    private SecurityFrameworkService mockSecurityFrameworkService;
    @Mock
    private OrderRefundItemDOMapper mockOrderRefundItemMapper;
    @Mock
    private EcouponOrderRefundStatusMachine mockEcouponOrderRefundStatusMachine;
    @Mock
    private LogisticsOrderRefundStatusMachine mockLogisticsOrderRefundStatusMachine;
    @Mock
    private RefundHandler mockRefundHandler;
    @Mock
    private BrandGoodsOrderRefundDOService mockBrandGoodsOrderRefundDOService;
    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private SubscriptionServiceApi mockSubscriptionServiceApi;
    @Mock
    private VcsOrderFulfilmentApi mockVcsOrderFulfilmentApi;
    @Mock
    private OrderStatusMappingDOMapper mockStatusMappingMapper;
    @Mock
    private ShorLinkAPI mockShorLinkAPI;
    @Mock
    private PayCenterOrderApi mockPayOrderApi;
    @Mock
    private PhoneNumberDecodeUtil mockPhoneNumberDecodeUtil;
    @Mock
    private IdempotentCheckUtil mockIdempotentCheckUtil;
    @Mock
    private OrderPaymentRecordsMapper mockOrderPaymentRecordsMapper;
    @Mock
    private OrderRefundPaymentRecordsMapper mockOrderRefundPaymentRecordsMapper;
    @Mock
    private OrderCouponDetailDOMapper mockOrderCouponDetailDOMapper;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private OrderRefundDOMapper mockOrderRefundDOMapper;

    @InjectMocks
    private OrderRefundDOServiceImpl orderRefundDOServiceImplUnderTest;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(orderRefundDOServiceImplUnderTest, "cancel", "cancel");
        ReflectionTestUtils.setField(orderRefundDOServiceImplUnderTest, "payment", "payment");
        ReflectionTestUtils.setField(orderRefundDOServiceImplUnderTest, "activate", "taskCode");
        orderRefundDOServiceImplUnderTest.ecpIdUtil = mockEcpIdUtil;
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), VCSOrderInfoDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderRefundDO.class);
    }

    @Test
    public void testGetPage() {
        // Setup
        final OrderRefundPageReqDTO dto = new OrderRefundPageReqDTO();
        dto.setPageNo(0);
        dto.setPageSize(0);
        dto.setOriginOrderCode("parentOrderCode");
        dto.setInControlId("inControlId");
        dto.setVin("vin");
        dto.setMobile("mobile");
        dto.setRefundOrderStatus("1");
        dto.setStatusList(List.of(0));
        dto.setOrderSource(2);
        dto.setOrderChannelList(List.of(0));
        dto.setBusinessCode("businessCode");

        final PageResult<OrderRefundPageVO> expectedResult = new PageResult<>(Collections.emptyList(), 0L);

        // Configure OrderInfoDOMapper.getParentCode(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.getParentCode("parentOrderCode")).thenReturn(orderInfoDO);

        // Configure OrderRefundDOMapper.getPage(...).
        when(orderRefundMapper.getPage(any(Page.class), any(OrderRefundPageReqDTO.class))).thenReturn(new Page<>(0L, 0L, 0L, false));

        // Run the test
        final PageResult<OrderRefundPageVO> result = orderRefundDOServiceImplUnderTest.getPage(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @NotNull
    private static OrderRefundApplyDTO getOrderRefundApplyDTO() {
        final OrderRefundApplyDTO refundApplyDTO = new OrderRefundApplyDTO();
        refundApplyDTO.setRefundType(1);
        refundApplyDTO.setOriginOrderCode(CODE);
        refundApplyDTO.setAttachmentUrls(List.of(VALUE));
        refundApplyDTO.setRefundMoney(1);
        refundApplyDTO.setRefundMoneyAmount("1");
        final ProductItemInfoDTO productItemInfoDTO = new ProductItemInfoDTO();
        productItemInfoDTO.setOrderItemCode(ORDER_ITEM_CODE);
        productItemInfoDTO.setProductQuantity(1);
        productItemInfoDTO.setProductSalePrice("1");
        refundApplyDTO.setProductItemInfoList(List.of(productItemInfoDTO));
        refundApplyDTO.setRemark(REFUND_REMARK);
        return refundApplyDTO;
    }
    
    @Test
    public void testApplyOrderRefund() {
        // Setup
        final OrderRefundApplyDTO refundApplyDTO = getOrderRefundApplyDTO();

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode(CODE)
                .parentOrderCode(CODE)
                .orderStatus(3)
                .paymentStatus(1)
                .costAmount(1)
                .orderType(1)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .build();
        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Configure OrderRefundDOMapper.selectList(...).
        final List<OrderRefundDO> orderRefundDOS = List.of(OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("rcode")
                .originOrderCode(CODE)
                .refundOrderStatus(3)
                .submitUser(SUBMIT_USER)
                .refundMoney(1)
                .refundMoneyAmount(1)
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundRemark(REFUND_REMARK)
                .rejectReason(REJECT_REASON)
                .build());
        when(orderRefundMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundDOS);

        // Configure OrderRefundItemDOMapper.selectList(...).
        final List<OrderRefundItemDO> orderRefundItemDOS = List.of(OrderRefundItemDO.builder()
                .refundOrderCode(CODE)
                .orderItemCode("orderItemCode0")
                .build());
        when(refundItemMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundItemDOS);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute(PRODUCT_ATTRIBUTE)
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .build());
        when(mockOrderItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(list);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .incontrolId(IN_CONTROL_ID)
                .carVin(CAR_VIN)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDOS);

        when(mockPiplDataUtil.getDecodeText(CAR_VIN)).thenReturn(CAR_VIN);

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("rcode")
                .originOrderCode(CODE)
                .refundOrderStatus(0)
                .submitUser(SUBMIT_USER)
                .refundMoney(0)
                .refundMoneyAmount(0)
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundRemark(REFUND_REMARK)
                .rejectReason(REJECT_REASON)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundDO);
        when(mockIdempotentCheckUtil.checkIdempotent(Constants.REFUND_IDEMPOTENT_KEY + refundApplyDTO.getOriginOrderCode()))
                .thenReturn(true);
        // Run the test
        final Boolean result = orderRefundDOServiceImplUnderTest.applyOrderRefund(refundApplyDTO);

        // Verify the results
        assertThat(result).isFalse();
        verify(mockAttachmentMapper).insertBatch(List.of(OrderRefundAttachmentDO.builder()
                .refundOrderCode(R_CODE001)
                .attachmentUrl(VALUE)
                .build()));
        verify(refundItemMapper).insertBatch(List.of(OrderRefundItemDO.builder()
                .refundOrderCode(R_CODE001)
                .orderItemCode(ORDER_ITEM_CODE)
                .build()));
        verify(mockOrderRefundStatusMachine).changeOrderStatus(2, OrderInfoDO.builder()
                .orderCode(CODE)
                .parentOrderCode(CODE)
                .orderStatus(3)
                .paymentStatus(1)
                .costAmount(1)
                .orderType(1)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .build(), OrderRefundDO.builder()
                .refundOrderCode(R_CODE001)
                .originOrderCode(CODE)
                .refundOrderStatus(2)
                .refundMoney(1)
                .refundMoneyAmount(100)
                .refundRemark(REFUND_REMARK)
                .build());
        verify(mockOrderInfoMapper).updateById(any());
        verify(mockStatusLogMapper, times(2)).insert(any());
        verify(mockModifyDetailLogMapper).createModifyLog(eq(CODE), eq(APPLY_CANCEL), any(), any());
    }

    @Test
    public void testCheckOrderRefund() {
        // Setup
        final OrderRefundCheckDTO refundCheckDTO = new OrderRefundCheckDTO();
        refundCheckDTO.setOriginOrderCode("originOrderCode");
        final ProductItemInfoDTO productItemInfoDTO = new ProductItemInfoDTO();
        productItemInfoDTO.setOrderItemCode("orderItemCode");
        productItemInfoDTO.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfoDTO.setNext(List.of(new ProductItemInfoDTO()));
        refundCheckDTO.setProductItemInfoList(List.of(productItemInfoDTO));

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Configure OrderInfoDOMapper.queryCarVinAndServiceTypeByItemCodes(...).
        final List<com.jlr.ecp.order.api.order.dto.CarVinAndServiceTypeDTO> carVinAndServiceTypeDTOS = List.of(
                new com.jlr.ecp.order.api.order.dto.CarVinAndServiceTypeDTO("carVin", 0));
        when(mockOrderInfoMapper.queryCarVinAndServiceTypeByItemCodes(anyList()))
                .thenReturn(carVinAndServiceTypeDTOS);

        when(mockOrderInfoMapper.findOrderCodeByCarVinAndServiceType(
                new com.jlr.ecp.order.api.order.dto.CarVinAndServiceTypeDTO("carVin", 0))).thenReturn("code");

        // Run the test
        final Boolean result = orderRefundDOServiceImplUnderTest.checkOrderRefund(refundCheckDTO);

        // Verify the results
        assertTrue(result);
    }

    @Test
    public void testCheckOrderRefund_OrderInfoDOMapperSelectOneReturnsNull() {
        // Setup
        final OrderRefundCheckDTO refundCheckDTO = new OrderRefundCheckDTO();
        refundCheckDTO.setOriginOrderCode("originOrderCode");
        final ProductItemInfoDTO productItemInfoDTO = new ProductItemInfoDTO();
        productItemInfoDTO.setOrderItemCode("orderItemCode");
        productItemInfoDTO.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfoDTO.setNext(List.of(new ProductItemInfoDTO()));
        refundCheckDTO.setProductItemInfoList(List.of(productItemInfoDTO));

        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(null);

        // Run the test
        assertThrows(ServiceException.class, () -> orderRefundDOServiceImplUnderTest.checkOrderRefund(refundCheckDTO));
    }

    @Test
    public void testOrderRefundPageNew() {
        // Setup
        final OrderRefundPageReqNewDTO dto = new OrderRefundPageReqNewDTO();
        dto.setPageNo(0);
        dto.setPageSize(0);
        dto.setRecipientPhone("recipientPhone");
        dto.setWxPhone("wxPhone");
        dto.setContactPhone("contactPhone");
        dto.setBusinessCode("businessCode");

        final OrderRefundPageRespNewVO orderRefundPageRespNewVO = new OrderRefundPageRespNewVO();
        orderRefundPageRespNewVO.setRefundOrderCode("refundOrderCode");
        orderRefundPageRespNewVO.setRefundSource(0);
        orderRefundPageRespNewVO.setRefundSourceName("refundSourceName");
        orderRefundPageRespNewVO.setFreightAmount("1");
        orderRefundPageRespNewVO.setRefundFreight("1");
        final RefundOrderItemVO refundOrderItemVO = new RefundOrderItemVO();
        refundOrderItemVO.setRefundOrderCode("refundOrderCode");
        orderRefundPageRespNewVO.setRefundOrderItemVOList(List.of(refundOrderItemVO));
        final PageResult<OrderRefundPageRespNewVO> expectedResult = new PageResult<>(List.of(orderRefundPageRespNewVO),
                0L);

        // Configure OrderRefundDOMapper.getPageNew(...).
        Page<OrderRefundPageRespNewVO> orderPage = new Page<>();
        orderPage.setRecords(List.of(orderRefundPageRespNewVO));
        when(orderRefundMapper.getPageNew(any(Page.class), any(OrderRefundPageReqNewDTO.class))).thenReturn(orderPage);

        // Configure OrderRefundItemDOMapper.getRefundOrderItemList(...).
        final RefundOrderItemVO refundOrderItemVO1 = new RefundOrderItemVO();
        refundOrderItemVO1.setRefundOrderCode("refundOrderCode");
        refundOrderItemVO1.setOrderCode("orderCode");
        refundOrderItemVO1.setProductImageUrl("productImageUrl");
        refundOrderItemVO1.setProductName("productName");
        refundOrderItemVO1.setProductSkuCode("productSkuCode");
        final List<RefundOrderItemVO> refundOrderItemVOS = List.of(refundOrderItemVO1);
        when(refundItemMapper.getRefundOrderItemList(anyList())).thenReturn(refundOrderItemVOS);

        // Run the test
        final PageResult<OrderRefundPageRespNewVO> result = orderRefundDOServiceImplUnderTest.orderRefundPageNew(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testOrderRefundPageNew_OrderRefundItemDOMapperReturnsNoItems() {
        // Setup
        final OrderRefundPageReqNewDTO dto = new OrderRefundPageReqNewDTO();
        dto.setPageNo(0);
        dto.setPageSize(0);
        dto.setRecipientPhone("recipientPhone");
        dto.setWxPhone("wxPhone");
        dto.setContactPhone("contactPhone");
        dto.setBusinessCode("businessCode");

        final OrderRefundPageRespNewVO orderRefundPageRespNewVO = new OrderRefundPageRespNewVO();
        orderRefundPageRespNewVO.setRefundOrderCode("refundOrderCode");
        orderRefundPageRespNewVO.setRefundSource(0);
        orderRefundPageRespNewVO.setRefundSourceName("refundSourceName");
        orderRefundPageRespNewVO.setFreightAmount("freightAmount");
        orderRefundPageRespNewVO.setRefundFreight("refundFreight");
        final RefundOrderItemVO refundOrderItemVO = new RefundOrderItemVO();
        refundOrderItemVO.setRefundOrderCode("refundOrderCode");
        orderRefundPageRespNewVO.setRefundOrderItemVOList(List.of(refundOrderItemVO));
        final PageResult<OrderRefundPageRespNewVO> expectedResult = new PageResult<>(Collections.emptyList(),
                0L);

        // Configure OrderRefundDOMapper.getPageNew(...).
        when(orderRefundMapper.getPageNew(any(Page.class), any(OrderRefundPageReqNewDTO.class))).thenReturn(new Page<>(0L, 0L, 0L, false));

        // Run the test
        final PageResult<OrderRefundPageRespNewVO> result = orderRefundDOServiceImplUnderTest.orderRefundPageNew(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testEditOrderRefund() {
        // Setup
        final OrderRefundEditDTO orderRefundEditDTO = new OrderRefundEditDTO();
        orderRefundEditDTO.setRefundOrderCode("code");
        orderRefundEditDTO.setRefundRemark("refundRemark1");
        orderRefundEditDTO.setLogisticsCode("logisticsCode1");
        orderRefundEditDTO.setLogisticsCompanyCode("logisticsCompanyCode1");
        orderRefundEditDTO.setLogisticsCompanyName("logisticsCompanyName");

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(90102)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundDO);

        // Configure OrderRefundItemDOMapper.selectList(...).
        final List<OrderRefundItemDO> orderRefundItemDOS = List.of(OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build());
        when(refundItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDOS);

        when(orderRefundMapper.update(any(OrderRefundDO.class), any(LambdaUpdateWrapper.class))).thenReturn(1);

        // Run the test
        final Boolean result = orderRefundDOServiceImplUnderTest.editOrderRefund(orderRefundEditDTO);

        // Verify the results
        assertTrue(result);
        verify(mockModifyDetailLogMapper).createModifyLog("code", "退单信息", "{\"运营人员备注\":\"refundRemark\"}", "{\"运营人员备注\":\"refundRemark1\"}");

        // Confirm BrandGoodsOrderRefundDOService.submitLogisticsInfo(...).
        verify(mockBrandGoodsOrderRefundDOService).submitLogisticsInfo(any(OrderRefundLogisticsDTO.class));
    }

    @Test
    public void testEditOrderRefund_OrderRefundDOMapperSelectOneReturnsNull() {
        // Setup
        final OrderRefundEditDTO orderRefundEditDTO = new OrderRefundEditDTO();
        orderRefundEditDTO.setRefundOrderCode("code");
        orderRefundEditDTO.setRefundRemark("refundRemark");
        orderRefundEditDTO.setLogisticsCode("logisticsCode");
        orderRefundEditDTO.setLogisticsCompanyCode("logisticsCompanyCode");
        orderRefundEditDTO.setLogisticsCompanyName("logisticsCompanyName");

        when(orderRefundMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        assertThrows(ServiceException.class,
                () -> orderRefundDOServiceImplUnderTest.editOrderRefund(orderRefundEditDTO));
    }

    @Test
    public void testEditOrderRefund_OrderRefundItemDOMapperReturnsNoItems() {
        // Setup
        final OrderRefundEditDTO orderRefundEditDTO = new OrderRefundEditDTO();
        orderRefundEditDTO.setRefundOrderCode("code");
        orderRefundEditDTO.setRefundRemark("refundRemark");
        orderRefundEditDTO.setLogisticsCode("logisticsCode");
        orderRefundEditDTO.setLogisticsCompanyCode("logisticsCompanyCode");
        orderRefundEditDTO.setLogisticsCompanyName("logisticsCompanyName");

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundDO);

        when(refundItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        assertThrows(ServiceException.class,
                () -> orderRefundDOServiceImplUnderTest.editOrderRefund(orderRefundEditDTO));
    }

    @Test
    public void testGetOrderRefundDetailNew() {
// Setup
        final OrderRefundDetailVO expectedResult = new OrderRefundDetailVO();
        expectedResult.setRefundOrderCode("code");
        expectedResult.setParentOrderCode("parentOrderCode");
        expectedResult.setOriginOrderCode("orderCode");
        expectedResult.setCouponRefundStatus(0);
        expectedResult.setLogisticsRefundStatus(0);
        expectedResult.setCouponRefundStatusName("售后关闭，已退款");
        expectedResult.setFulfillmentType(0);
        expectedResult.setFulfillmentTypeName("Bundle");
        expectedResult.setRefundSourceName("");
        expectedResult.setRefundType(0);
        expectedResult.setRefundTypeName("");
        expectedResult.setSupDesc("supDesc");
        expectedResult.setRefundStatusSup("");
        expectedResult.setAttachmentUrl(List.of("attachmentUrl"));
        expectedResult.setWxPhone("wxPhone");
        expectedResult.setWxPhoneMix("wxPhoneMix");
        expectedResult.setRecipientPhone("recipientPhone");
        expectedResult.setRecipientPhoneMix("recipientPhoneMix");
        expectedResult.setRecipientAddressMix("detailAddressMix");
        expectedResult.setRecipientAddress("detailAddress");
        expectedResult.setRecipientMix("recipientMix");
        expectedResult.setRecipient("recipient");
        expectedResult.setContactPhone("contactPhone");
        expectedResult.setContactPhoneMix("contactPhoneMix");
        final RefundOrderProductInfoVO refundOrderProductInfoVO = new RefundOrderProductInfoVO();
        refundOrderProductInfoVO.setOrderItemCode("orderItemCode");
        refundOrderProductInfoVO.setProductSkuCode("productSkuCode");
        refundOrderProductInfoVO.setProductCode("productCode");
        refundOrderProductInfoVO.setKingdeeSkuCode("kingdeeSkuCode");
        refundOrderProductInfoVO.setProductImageUrl("productImageUrl");
        refundOrderProductInfoVO.setProductName("productName");
        refundOrderProductInfoVO.setCouponModelCode("couponModelCode");
        refundOrderProductInfoVO.setValidStartTime("2020-01-01T00:00");
        refundOrderProductInfoVO.setValidEndTime("2020-01-01T00:00");
        refundOrderProductInfoVO.setProductQuantity(0);
        refundOrderProductInfoVO.setProductSalePrice("0.00");
        refundOrderProductInfoVO.setCostAmount("0.00");
        refundOrderProductInfoVO.setAfterSalesStatus(0);
        final ECouponOrderDetailVO eCouponOrderDetailVO = new ECouponOrderDetailVO();
        eCouponOrderDetailVO.setCouponModelCode("couponModelCode");
        eCouponOrderDetailVO.setCouponCode("couponCode");
        eCouponOrderDetailVO.setStatus(0);
        eCouponOrderDetailVO.setValidStartTime("2020-01-01T00:00");
        eCouponOrderDetailVO.setValidEndTime("2020-01-01T00:00");
        eCouponOrderDetailVO.setUsedTime("2020-01-01T00:00");
        eCouponOrderDetailVO.setSendTime("2020-01-01T00:00");
        refundOrderProductInfoVO.setCouponCodeList(List.of(eCouponOrderDetailVO));
        refundOrderProductInfoVO.setProductAttribute("");
        refundOrderProductInfoVO.setUsedCouponCount(0);
        refundOrderProductInfoVO.setRefundCouponCount(0);
        refundOrderProductInfoVO.setProductMarketPrice("0.00");
        refundOrderProductInfoVO.setRefundMoney("0.00");
        refundOrderProductInfoVO.setRefundPoint(0);
        refundOrderProductInfoVO.setIsCouponRefunded(true);
        refundOrderProductInfoVO.setMaxRefundMoney("0.00");
        expectedResult.setRefundOrderProductInfoVOList(List.of(refundOrderProductInfoVO));
        final OrderRefundDetailVO.RefundDetailLogVO refundDetailLogVO = new OrderRefundDetailVO.RefundDetailLogVO();
        refundDetailLogVO.setRefundOrderStatus(0);
        refundDetailLogVO.setText("");
        refundDetailLogVO.setDetail("");
        refundDetailLogVO.setChangeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setLogList(List.of(refundDetailLogVO));
        expectedResult.setRefundRemark("refundRemark");
        expectedResult.setLogisticsCode("logisticsCode");
        expectedResult.setLogisticsCompanyCode("logisticsCompanyCode");
        expectedResult.setLogisticsCompanyName("logisticsCompanyName");
        expectedResult.setLogisticsAttachmentList(List.of("logisticsAttachment"));
        expectedResult.setReturnAuditRemark("returnAuditRemark");
        expectedResult.setRefundAuditRemark("refundAuditRemark");
        expectedResult.setPaymentMethod("现金+优惠券");
        expectedResult.setCostAmount("0.00");
        expectedResult.setPointAmount(0);
        expectedResult.setFreightAmount("0.00");
        expectedResult.setRefundFreight("0.00");
        expectedResult.setCouponType("积分");
        expectedResult.setCouponName("couponModelName");
        expectedResult.setCouponCode("couponCode");
        expectedResult.setPaymentTime("2020/01/01 00:00");


        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundDO);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderInfoDO);

        // Configure OrderRefundItemDOMapper.selectList(...).
        final List<OrderRefundItemDO> orderRefundItemDOS = List.of(OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build());
        when(mockOrderRefundItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDOS);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> orderItemDOS = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build());
        when(mockOrderItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderItemDOS);

        // Configure OrderItemLogisticsDOMapper.selectList(...).
        final List<OrderItemLogisticsDO> orderItemLogisticsDOS = List.of(OrderItemLogisticsDO.builder()
                .orderCode("orderCode")
                .recipient("recipient")
                .recipientMix("recipientMix")
                .recipientPhone("recipientPhone")
                .recipientPhoneMix("recipientPhoneMix")
                .detailAddress("detailAddress")
                .detailAddressMix("detailAddressMix")
                .build());
        when(mockOrderItemLogisticsMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderItemLogisticsDOS);

        // Configure OrderCouponDetailDOMapper.selectList(...).
        final List<OrderCouponDetailDO> orderCouponDetailDOS = List.of(OrderCouponDetailDO.builder()
                .orderItemCode("orderItemCode")
                .couponModelCode("couponModelCode")
                .couponCode("couponCode")
                .status(0)
                .validStartTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .validEndTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .usedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .sendTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockOrderCouponDetailDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderCouponDetailDOS);

        // Configure OrderDiscountDetailDOMapper.selectOne(...).
        final OrderDiscountDetailDO orderDiscountDetailDO = OrderDiscountDetailDO.builder()
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .discountType(0)
                .couponCode("couponCode")
                .couponModelName("couponModelName")
                .couponModelClassify(0)
                .costPoints(0)
                .discountAmount(0)
                .build();
        when(mockOrderDiscountDetailDOMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(orderDiscountDetailDO);

        when(mockRefundHandler.getLogisticsMaxRefundMoney(OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build(), 0L)).thenReturn(0);

        // Configure OrderStatusLogDOMapper.selectList(...).
        final List<OrderStatusLogDO> orderStatusLogDOS = List.of(OrderStatusLogDO.builder()
                .id(0L)
                .orderCode("code")
                .beforeStatus(0)
                .afterStatus(0)
                .changeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockStatusLogMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderStatusLogDOS);

        // Configure OrderRefundAttachmentDOMapper.selectList(...).
        final List<OrderRefundAttachmentDO> orderRefundAttachmentDOS = List.of(OrderRefundAttachmentDO.builder()
                .refundOrderCode("code")
                .attachmentUrl("attachmentUrl")
                .build());
        when(mockAttachmentMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundAttachmentDOS);

        // Configure OrderDiscountDetailDOMapper.selectList(...).
        final List<OrderDiscountDetailDO> orderDiscountDetailDOS = List.of(OrderDiscountDetailDO.builder()
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .discountType(2)
                .couponCode("couponCode")
                .couponModelName("couponModelName")
                .couponModelClassify(0)
                .costPoints(0)
                .discountAmount(0)
                .build());
        when(mockOrderDiscountDetailDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(orderDiscountDetailDOS);

        // Configure OrderPaymentRecordsMapper.queryLastByOrderCode(...).
        final OrderPaymentRecordsDO orderPaymentRecordsDO = new OrderPaymentRecordsDO();
        orderPaymentRecordsDO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderPaymentRecordsDO.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderPaymentRecordsDO.setIsDeleted(false);
        orderPaymentRecordsDO.setPayApplyNo("payApplyNo");
        orderPaymentRecordsDO.setPayFinishTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderPaymentRecordsMapper.queryLastByOrderCode("code")).thenReturn(orderPaymentRecordsDO);

        // Run the test
        final OrderRefundDetailVO result = orderRefundDOServiceImplUnderTest.getOrderRefundDetailNew("orderRefundCode");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOrderRefundDetailNew_OrderRefundDOMapperReturnsNull() {
        // Setup
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        assertThrows(ServiceException.class,
                () -> orderRefundDOServiceImplUnderTest.getOrderRefundDetailNew("orderRefundCode"));
    }

    @Test
    public void testGetOrderRefundDetailNew_OrderInfoDOMapperReturnsNull() {
        // Setup
        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundDO);

        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Run the test
        assertThrows(ServiceException.class,
                () -> orderRefundDOServiceImplUnderTest.getOrderRefundDetailNew("orderRefundCode"));
    }

    @Test
    public void testGetOrderRefundInfoByOrderCode() {
        // Setup
        final OrderRefundDto orderRefundDto = new OrderRefundDto();
        orderRefundDto.setRefundOrderCode("refundOrderCode");
        orderRefundDto.setOriginOrderCode("originOrderCode");
        orderRefundDto.setRefundOrderStatus(0);
        orderRefundDto.setSubmitUser("submitUser");
        orderRefundDto.setRefundMoney(0);
        final List<OrderRefundDto> expectedResult = List.of(orderRefundDto);

        // Configure OrderRefundDOMapper.selectList(...).
        final List<OrderRefundDO> orderRefundDOS = List.of(OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build());
        when(mockOrderRefundDOMapper.selectList(any(QueryWrapper.class))).thenReturn(orderRefundDOS);

        // Configure OrderRefundDOConvert.toDtoList(...).
        final OrderRefundDto orderRefundDto1 = new OrderRefundDto();
        orderRefundDto1.setRefundOrderCode("refundOrderCode");
        orderRefundDto1.setOriginOrderCode("originOrderCode");
        orderRefundDto1.setRefundOrderStatus(0);
        orderRefundDto1.setSubmitUser("submitUser");
        orderRefundDto1.setRefundMoney(0);
        final List<OrderRefundDto> orderRefundDtos = List.of(orderRefundDto1);
        when(mockOrderRefundDOConvert.toDtoList(List.of(OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build()))).thenReturn(orderRefundDtos);

        // Run the test
        final List<OrderRefundDto> result = orderRefundDOServiceImplUnderTest.getOrderRefundInfoByOrderCode(
                "orderCode");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOrderRefundItemByOrderRefundCode() {
        // Setup
        final OrderRefundItemDto orderRefundItemDto = new OrderRefundItemDto();
        orderRefundItemDto.setRefundOrderCode("refundOrderCode");
        orderRefundItemDto.setOrderItemCode("orderItemCode");
        orderRefundItemDto.setOrderRefundItemCode("orderRefundItemCode");
        orderRefundItemDto.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderRefundItemDto.setRefundMoney(0);
        final List<OrderRefundItemDto> expectedResult = List.of(orderRefundItemDto);

        // Configure OrderRefundItemDOMapper.selectList(...).
        final List<OrderRefundItemDO> orderRefundItemDOS = List.of(OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build());
        when(mockOrderRefundItemMapper.selectList(any(QueryWrapper.class))).thenReturn(orderRefundItemDOS);

        // Configure OrderRefundItemDOConvert.toDtoList(...).
        final OrderRefundItemDto orderRefundItemDto1 = new OrderRefundItemDto();
        orderRefundItemDto1.setRefundOrderCode("refundOrderCode");
        orderRefundItemDto1.setOrderItemCode("orderItemCode");
        orderRefundItemDto1.setOrderRefundItemCode("orderRefundItemCode");
        orderRefundItemDto1.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderRefundItemDto1.setRefundMoney(0);
        final List<OrderRefundItemDto> orderRefundItemDtos = List.of(orderRefundItemDto1);
        when(mockOrderRefundItemDOConvert.toDtoList(List.of(OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build()))).thenReturn(orderRefundItemDtos);

        // Run the test
        final List<OrderRefundItemDto> result = orderRefundDOServiceImplUnderTest.getOrderRefundItemByOrderRefundCode(
                List.of("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOrderRefundDetail() {
        // Setup
        final RefundDetailRespVO expectedResult = new RefundDetailRespVO();
        expectedResult.setRefundOrderCode("code");
        expectedResult.setOriginOrderCode("orderCode");
        expectedResult.setAttachmentUrls(List.of("attachmentUrl"));
        expectedResult.setRefundMoney(0);
        expectedResult.setRefundMoneyAmount("0.00");
        expectedResult.setRefundOrderStatus(0);
        expectedResult.setSubmitUser("submitUser");
        expectedResult.setRejectReason("rejectReason");
        final ProductItemInfoAppVO productItemInfoAppVO = new ProductItemInfoAppVO();
        productItemInfoAppVO.setOrderItemCode("orderItemCode");
        productItemInfoAppVO.setProductVersionCode("productVersionCode");
        productItemInfoAppVO.setProductCode("productCode");
        productItemInfoAppVO.setProductSkuCode("productSkuCode");
        productItemInfoAppVO.setProductName("productName");
        productItemInfoAppVO.setProductQuantity(0);
        productItemInfoAppVO.setProductAttribute("");
        productItemInfoAppVO.setProductMarketPrice("0.00");
        productItemInfoAppVO.setProductSalePrice("0.00");
        productItemInfoAppVO.setServiceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfoAppVO.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfoAppVO.setActualEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfoAppVO.setCostAmount("0.00");
        productItemInfoAppVO.setProductImageUrl("productImageUrl");
        expectedResult.setProductItemInfoList(List.of(productItemInfoAppVO));
        expectedResult.setRefundRemark("refundRemark");

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundDO);

        // Configure OrderRefundAttachmentDOMapper.selectList(...).
        final List<OrderRefundAttachmentDO> orderRefundAttachmentDOS = List.of(OrderRefundAttachmentDO.builder()
                .refundOrderCode("code")
                .attachmentUrl("attachmentUrl")
                .build());
        when(mockAttachmentMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundAttachmentDOS);

        // Configure OrderRefundItemDOMapper.selectList(...).
        final List<OrderRefundItemDO> orderRefundItemDOS = List.of(OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build());
        when(refundItemMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundItemDOS);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> orderItemDOS = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build());
        when(mockOrderItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderItemDOS);

        // Configure VcsOrderInfoDOMapper.selectOne(...).
        final VCSOrderInfoDO vcsOrderInfoDO = VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode("vcsOrderCode")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .incontrolId("incontrolId")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build();
        when(mockVcsOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderInfoDO);

        // Run the test
        final RefundDetailRespVO result = orderRefundDOServiceImplUnderTest.getOrderRefundDetail("orderRefundCode");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testOrderRefundApprove() {
        // Setup
        final OrderRefundApproveDTO refundApproveDTO = new OrderRefundApproveDTO();
        refundApproveDTO.setApproveStatus(true);
        refundApproveDTO.setRefundOrderCode("refundOrderCode");
        refundApproveDTO.setRejectReason("rejectReason");

        when(mockIdempotentCheckUtil.checkIdempotent(anyString())).thenReturn(true);

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(1)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundDO);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("LRcode")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Configure OrderPaymentRecordsMapper.queryByOrderCode(...).
        final OrderPaymentRecordsDO orderPaymentRecordsDO = new OrderPaymentRecordsDO();
        orderPaymentRecordsDO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderPaymentRecordsDO.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderPaymentRecordsDO.setIsDeleted(false);
        orderPaymentRecordsDO.setPayApplyNo("payApplyNo");
        orderPaymentRecordsDO.setPayFinishTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderPaymentRecordsMapper.queryByOrderCode("LRcode", "parentOrderCode"))
                .thenReturn(orderPaymentRecordsDO);

        // Configure PayCenterOrderApi.submitRefundOrder(...).
        final CommonResult<SubmitRefundOrderResp> submitRefundOrderRespCommonResult = CommonResult.success(
                new SubmitRefundOrderResp("refundApplyNo", "refundStatus", "msg"));
        when(mockPayOrderApi.submitRefundOrder(any(PayRefundCreateReqDTO.class))).thenReturn(submitRefundOrderRespCommonResult);

        // Configure OrderRefundItemDOMapper.selectList(...).
        final List<OrderRefundItemDO> orderRefundItemDOS = List.of(OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build());
        when(refundItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDOS);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode("vcsOrderCode")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .incontrolId("incontrolId")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDOS);

        when(mockEcpIdUtil.nextIdStr()).thenReturn("messageId");

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> orderItemDOS = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build());
        when(mockOrderItemMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDOS);

        when(mockPiplDataUtil.getDecodeText("carVin")).thenReturn("vin");
        when(mockPhoneNumberDecodeUtil.getDecodePhone("contactPhone")).thenReturn("phoneNumber");

        // Run the test
        final Boolean result = orderRefundDOServiceImplUnderTest.orderRefundApprove(refundApproveDTO);

        // Verify the results
        assertTrue(result);
        verify(mockOrderRefundStatusMachine).changeOrderStatus(eq(4), any(OrderInfoDO.class), any(OrderRefundDO.class));
        verify(orderRefundMapper).updateById(any(OrderRefundDO.class));
        verify(mockOrderInfoMapper).updateById(any(OrderInfoDO.class));

        // Confirm OrderRefundPaymentRecordsMapper.insert(...).
        verify(mockOrderRefundPaymentRecordsMapper).insert(any(OrderRefundPaymentRecordsDO.class));
    }

    @Test
    public void testOrderRefundApprove_IdempotentCheckUtilReturnsFalse() {
        // Setup
        final OrderRefundApproveDTO refundApproveDTO = new OrderRefundApproveDTO();
        refundApproveDTO.setApproveStatus(false);
        refundApproveDTO.setRefundOrderCode("refundOrderCode");
        refundApproveDTO.setRejectReason("rejectReason");

        // Run the test
        assertThrows(ServiceException.class,
                () -> orderRefundDOServiceImplUnderTest.orderRefundApprove(refundApproveDTO));
    }

    @Test
    public void testOrderRefundApprove_refundMoney() {
        // Setup
        final OrderRefundApproveDTO refundApproveDTO = new OrderRefundApproveDTO();
        refundApproveDTO.setApproveStatus(true);
        refundApproveDTO.setRefundOrderCode("refundOrderCode");
        refundApproveDTO.setRejectReason("rejectReason");

        when(mockIdempotentCheckUtil.checkIdempotent(anyString())).thenReturn(true);

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundDO);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("LRcode")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Configure OrderPaymentRecordsMapper.queryByOrderCode(...).
        final OrderPaymentRecordsDO orderPaymentRecordsDO = new OrderPaymentRecordsDO();
        orderPaymentRecordsDO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderPaymentRecordsDO.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderPaymentRecordsDO.setIsDeleted(false);
        orderPaymentRecordsDO.setPayApplyNo("payApplyNo");
        orderPaymentRecordsDO.setPayFinishTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderPaymentRecordsMapper.queryByOrderCode("LRcode", "parentOrderCode"))
                .thenReturn(orderPaymentRecordsDO);

        // Configure OrderRefundItemDOMapper.selectList(...).
        final List<OrderRefundItemDO> orderRefundItemDOS = List.of(OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build());
        when(refundItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDOS);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode("vcsOrderCode")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .incontrolId("incontrolId")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDOS);

        when(mockEcpIdUtil.nextIdStr()).thenReturn("messageId");

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> orderItemDOS = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build());
        when(mockOrderItemMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDOS);

        when(mockPiplDataUtil.getDecodeText("carVin")).thenReturn("vin");
        when(mockPhoneNumberDecodeUtil.getDecodePhone("contactPhone")).thenReturn("phoneNumber");

        // Run the test
        final Boolean result = orderRefundDOServiceImplUnderTest.orderRefundApprove(refundApproveDTO);

        // Verify the results
        assertTrue(result);
        verify(mockOrderRefundStatusMachine).changeOrderStatus(eq(4), any(OrderInfoDO.class), any(OrderRefundDO.class));
        verify(orderRefundMapper).updateById(any(OrderRefundDO.class));
        verify(mockOrderInfoMapper).updateById(any(OrderInfoDO.class));
    }

    @Test
    public void testGetOrderRefundDetailByOrderCode() {
        // Setup
        final RefundDetailVO expectedResult = new RefundDetailVO();
        expectedResult.setRefundOrderCode("code");
        expectedResult.setOriginOrderCode("orderCode");
        expectedResult.setRefundMoneyAmount("0.00");
        expectedResult.setRefundOrderStatus(0);
        expectedResult.setCreatedTime(null);
        expectedResult.setCustomerRemark("customerRemark");

        final ProductItemInfoAppVO productItemInfoAppVO = new ProductItemInfoAppVO();
        productItemInfoAppVO.setOrderItemCode("orderItemCode");
        productItemInfoAppVO.setProductVersionCode("productVersionCode");
        productItemInfoAppVO.setProductCode("productCode");
        productItemInfoAppVO.setProductSkuCode("productSkuCode");
        productItemInfoAppVO.setProductName("productName");
        productItemInfoAppVO.setProductQuantity(0);
        productItemInfoAppVO.setProductAttribute("");
        productItemInfoAppVO.setProductMarketPrice("0.00");
        productItemInfoAppVO.setProductSalePrice("0.00");
        productItemInfoAppVO.setServiceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfoAppVO.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfoAppVO.setActualEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfoAppVO.setCostAmount("0.00");
        productItemInfoAppVO.setProductImageUrl("productImageUrl");
        productItemInfoAppVO.setNext(null);
        expectedResult.setProductItemInfoList(List.of(productItemInfoAppVO));

        final RefundDetailLogVO refundDetailLogVO = new RefundDetailLogVO();
        refundDetailLogVO.setRefundOrderStatus(0);
        refundDetailLogVO.setText("customerRefundOrderStatusView");
        refundDetailLogVO.setDetail(null);
        refundDetailLogVO.setChangeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        expectedResult.setLogList(List.of(refundDetailLogVO));

        final IcrVehicleRespVO icrVehicleRespVO = new IcrVehicleRespVO();
        icrVehicleRespVO.setCarVin("carVin");
        icrVehicleRespVO.setIncontrolId("incontrolId");
        icrVehicleRespVO.setSeriesCode("seriesCode");
        icrVehicleRespVO.setSeriesName("seriesName");
        icrVehicleRespVO.setBrandCode("brandCode");
        icrVehicleRespVO.setBrandName(null);
        icrVehicleRespVO.setHobEn(null);
        icrVehicleRespVO.setProductionEn(null);
        icrVehicleRespVO.setConfigCode(null);
        icrVehicleRespVO.setConfigName(null);
        icrVehicleRespVO.setModelYear(null);
        icrVehicleRespVO.setCarSystemModel(null);
        icrVehicleRespVO.setBindTime(null);
        expectedResult.setIcrVehicleRespVO(icrVehicleRespVO);

        expectedResult.setOrderTime("");

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundDO);

        // Configure OrderStatusLogDOMapper.selectList(...).
        final List<OrderStatusLogDO> orderStatusLogDOS = List.of(OrderStatusLogDO.builder()
                .id(0L)
                .orderCode("code")
                .beforeStatus(0)
                .afterStatus(0)
                .changeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockStatusLogMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderStatusLogDOS);

        // Configure OrderStatusMappingDOMapper.selectList(...).
        final List<OrderStatusMappingDO> orderStatusMappingDOS = List.of(OrderStatusMappingDO.builder()
                .refundOrderStatus(0)
                .customerRefundOrderStatusView("customerRefundOrderStatusView")
                .build());
        when(mockStatusMappingMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderStatusMappingDOS);

        // Configure OrderRefundItemDOMapper.selectList(...).
        final List<OrderRefundItemDO> orderRefundItemDOS = List.of(OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build());
        when(refundItemMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundItemDOS);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> orderItemDOS = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build());
        when(mockOrderItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderItemDOS);

        // Configure VcsOrderInfoDOMapper.selectOne(...).
        final VCSOrderInfoDO vcsOrderInfoDO = VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode("vcsOrderCode")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .incontrolId("incontrolId")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build();
        when(mockVcsOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderInfoDO);

        when(mockPiplDataUtil.getDecodeText("carVin")).thenReturn("s");

        // Configure IcrVehicleApi.view(...).
        final IcrVehicleRespVO icrVehicleRespVO1 = new IcrVehicleRespVO();
        icrVehicleRespVO1.setCarVin("carVin");
        icrVehicleRespVO1.setIncontrolId("incontrolId");
        icrVehicleRespVO1.setSeriesCode("seriesCode");
        icrVehicleRespVO1.setSeriesName("seriesName");
        icrVehicleRespVO1.setBrandCode("brandCode");
        final CommonResult<IcrVehicleRespVO> icrVehicleRespVOCommonResult = CommonResult.success(icrVehicleRespVO1);
        when(mockIcrVehicleApi.view("s")).thenReturn(icrVehicleRespVOCommonResult);

        // Configure OrderInfoDOMapper.queryOrderDoByOrderCode(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.queryOrderDoByOrderCode("orderCode")).thenReturn(orderInfoDO);

        // Run the test
        final RefundDetailVO result = orderRefundDOServiceImplUnderTest.getOrderRefundDetailByOrderCode("orderCode");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCount() {
        // Setup
        when(orderRefundMapper.selectCount(any(LambdaQueryWrapperX.class))).thenReturn(0L);

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.getCount();

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testPaymentCallbackProcess() {
        // Setup
        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundDO);

        // Configure OrderRefundPaymentRecordsMapper.selectByRefundOrderCode(...).
        final OrderRefundPaymentRecordsDO orderRefundPaymentRecordsDO = new OrderRefundPaymentRecordsDO();
        orderRefundPaymentRecordsDO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderRefundPaymentRecordsDO.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderRefundPaymentRecordsDO.setIsDeleted(false);
        orderRefundPaymentRecordsDO.setOrderCode("orderCode");
        orderRefundPaymentRecordsDO.setRefundOrderCode("code");
        orderRefundPaymentRecordsDO.setPayApplyNo("payApplyNo");
        orderRefundPaymentRecordsDO.setRefundApplyNo("refundApplyNo");
        orderRefundPaymentRecordsDO.setTradeStatus("code");
        when(mockOrderRefundPaymentRecordsMapper.selectByRefundOrderCode("orderRefundCode"))
                .thenReturn(orderRefundPaymentRecordsDO);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(5)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Configure OrderRefundItemDOMapper.selectOne(...).
        final OrderRefundItemDO orderRefundItemDO = OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build();
        when(mockOrderRefundItemMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundItemDO);

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build();
        when(mockOrderItemMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDO);

        // Configure OrderRefundItemDOMapper.selectList(...).
        final List<OrderRefundItemDO> orderRefundItemDOS = List.of(OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build());
        when(refundItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDOS);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode("vcsOrderCode")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .incontrolId("incontrolId")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDOS);

        when(mockEcpIdUtil.nextIdStr()).thenReturn("messageId");

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.paymentCallbackProcess("orderRefundCode");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
        verify(mockEcouponOrderRefundStatusMachine).changeOrderStatus(eq(5), any(OrderInfoDO.class),
                any(OrderRefundDO.class), any(OrderItemDO.class));
        verify(mockOrderRefundPaymentRecordsMapper).updateById(any(OrderRefundPaymentRecordsDO.class));
    }

    @Test
    public void testPaymentCallbackProcess_BRAND_GOOD() {
        // Setup
        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(5)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundDO);

        // Configure OrderRefundPaymentRecordsMapper.selectByRefundOrderCode(...).
        final OrderRefundPaymentRecordsDO orderRefundPaymentRecordsDO = new OrderRefundPaymentRecordsDO();
        orderRefundPaymentRecordsDO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderRefundPaymentRecordsDO.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderRefundPaymentRecordsDO.setIsDeleted(false);
        orderRefundPaymentRecordsDO.setOrderCode("orderCode");
        orderRefundPaymentRecordsDO.setRefundOrderCode("code");
        orderRefundPaymentRecordsDO.setPayApplyNo("payApplyNo");
        orderRefundPaymentRecordsDO.setRefundApplyNo("refundApplyNo");
        orderRefundPaymentRecordsDO.setTradeStatus("code");
        when(mockOrderRefundPaymentRecordsMapper.selectByRefundOrderCode("orderRefundCode"))
                .thenReturn(orderRefundPaymentRecordsDO);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(3)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.paymentCallbackProcess("orderRefundCode");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testPaymentCallbackProcess_PIVI() {
        // Setup
        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundDO);

        // Configure OrderRefundPaymentRecordsMapper.selectByRefundOrderCode(...).
        final OrderRefundPaymentRecordsDO orderRefundPaymentRecordsDO = new OrderRefundPaymentRecordsDO();
        orderRefundPaymentRecordsDO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderRefundPaymentRecordsDO.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderRefundPaymentRecordsDO.setIsDeleted(false);
        orderRefundPaymentRecordsDO.setOrderCode("orderCode");
        orderRefundPaymentRecordsDO.setRefundOrderCode("code");
        orderRefundPaymentRecordsDO.setPayApplyNo("payApplyNo");
        orderRefundPaymentRecordsDO.setRefundApplyNo("refundApplyNo");
        orderRefundPaymentRecordsDO.setTradeStatus("code");
        when(mockOrderRefundPaymentRecordsMapper.selectByRefundOrderCode("orderRefundCode"))
                .thenReturn(orderRefundPaymentRecordsDO);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(2)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Configure OrderRefundItemDOMapper.selectList(...).
        final List<OrderRefundItemDO> orderRefundItemDOS = List.of(OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build());
        when(refundItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDOS);

        // Configure VcsOrderFulfilmentApi.fulfilmentViewList(...).
        final VcsOrderFulfilmentRespVO vcsOrderFulfilmentRespVO = new VcsOrderFulfilmentRespVO();
        vcsOrderFulfilmentRespVO.setFulfilmentId("fulfilmentId");
        vcsOrderFulfilmentRespVO.setOrderCode("orderCode");
        vcsOrderFulfilmentRespVO.setOrderItemCode("orderItemCode");
        vcsOrderFulfilmentRespVO.setCarVin("carVin");
        vcsOrderFulfilmentRespVO.setServiceStatus(0);
        final CommonResult<List<VcsOrderFulfilmentRespVO>> listCommonResult = CommonResult.success(
                List.of(vcsOrderFulfilmentRespVO));
        when(mockVcsOrderFulfilmentApi.fulfilmentViewList(anyList())).thenReturn(listCommonResult);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode("vcsOrderCode")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .incontrolId("incontrolId")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDOS);

        when(mockEcpIdUtil.nextIdStr()).thenReturn("messageId");

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.paymentCallbackProcess("orderRefundCode");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
        verify(mockOrderRefundPaymentRecordsMapper).updateById(any(OrderRefundPaymentRecordsDO.class));
    }

    @Test
    public void testTransPaymentCallbackProcess() {
        // Setup
        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundDO);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Configure OrderRefundItemDOMapper.selectOne(...).
        final OrderRefundItemDO orderRefundItemDO = OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build();
        when(mockOrderRefundItemMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundItemDO);

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build();
        when(mockOrderItemMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDO);

        // Configure OrderPaymentRecordsMapper.queryByOrderCode(...).
        final OrderPaymentRecordsDO orderPaymentRecordsDO = new OrderPaymentRecordsDO();
        orderPaymentRecordsDO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderPaymentRecordsDO.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderPaymentRecordsDO.setIsDeleted(false);
        orderPaymentRecordsDO.setPayApplyNo("payApplyNo");
        orderPaymentRecordsDO.setPayFinishTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockOrderPaymentRecordsMapper.queryByOrderCode("code", "parentOrderCode"))
                .thenReturn(orderPaymentRecordsDO);

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.transPaymentCallbackProcess("orderRefundCode");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);

        // Confirm RefundHandler.refundMoneyProcess(...).
        final OrderPaymentRecordsDO orderPaymentRecordsDO1 = new OrderPaymentRecordsDO();
        orderPaymentRecordsDO1.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderPaymentRecordsDO1.setUpdatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderPaymentRecordsDO1.setIsDeleted(false);
        orderPaymentRecordsDO1.setPayApplyNo("payApplyNo");
        orderPaymentRecordsDO1.setPayFinishTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        verify(mockRefundHandler).refundMoneyProcess(OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .orderItemCode("orderItemCode")
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .refundMoney(0)
                .refundPoint(0)
                .build(), OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build(), OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build(), OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build(), orderPaymentRecordsDO1);
    }

    @Test
    public void testTransPaymentCallbackProcess_OrderRefundDOMapperReturnsNull() {
        // Setup
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(null);

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.transPaymentCallbackProcess("orderRefundCode");

        // Verify the results
        assertEquals(Integer.valueOf(-1), result);
    }

    @Test
    public void testTsdpRefundCallBack() {
        // Setup
        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode("code")
                .originOrderCode("orderCode")
                .refundOrderStatus(0)
                .submitUser("submitUser")
                .refundMoney(0)
                .refundMoneyAmount(0)
                .refundRemark("refundRemark")
                .supDesc("supDesc")
                .rejectReason("rejectReason")
                .refundSource(0)
                .refundOrderType(0)
                .refundReason(0)
                .logisticsRefundStatus(0)
                .couponRefundStatus(0)
                .refundCouponCode("refundCouponCode")
                .refundFulfilmentType(0)
                .refundStatusSup(0)
                .logisticsCode("logisticsCode")
                .logisticsCompanyCode("logisticsCompanyCode")
                .logisticsCompanyName("logisticsCompanyName")
                .returnAuditRemark("returnAuditRemark")
                .refundAuditRemark("refundAuditRemark")
                .logisticsAttachment("logisticsAttachment")
                .refundFreight(0)
                .build();
        when(orderRefundMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundDO);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        when(mockOrderInfoMapper.updateById(OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build())).thenReturn(0);

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.tsdpRefundCallBack("orderRefundCode", true);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
        verify(mockOrderRefundStatusMachine).changeOrderStatus(eq(9), any(OrderInfoDO.class), any(OrderRefundDO.class));
        verify(orderRefundMapper).updateById(any(OrderRefundDO.class));
        verify(mockStatusLogMapper).insert(any(OrderStatusLogDO.class));
    }

    @Test
    public void testTsdpCallBack() {
        // Setup
        // Configure VcsOrderInfoDOMapper.selectOne(...).
        final VCSOrderInfoDO vcsOrderInfoDO = VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode("vcsOrderCode")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .incontrolId("incontrolId")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build();
        when(mockVcsOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderInfoDO);

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build();
        when(mockOrderItemMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDO);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        when(mockOrderInfoMapper.updateById(OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build())).thenReturn(0);

        when(mockEcpIdUtil.nextIdStr()).thenReturn("messageId");
        when(mockPiplDataUtil.getDecodeText("carVin")).thenReturn("carVin");
        when(mockPhoneNumberDecodeUtil.getDecodePhone("contactPhone")).thenReturn("phoneNumber");

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.tsdpCallBack("vcsOrderCode", true);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
        verify(mockOrderRefundStatusMachine).changeOrderStatus(eq(102), any(OrderInfoDO.class), eq(null));
    }

    @Test
    public void testTsdpCallBack_VcsOrderInfoDOMapperSelectOneReturnsNull() {
        // Setup
        when(mockVcsOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(null);

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.tsdpCallBack("vcsOrderCode", false);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testTsdpCallBack_OrderItemDOMapperReturnsNull() {
        // Setup
        // Configure VcsOrderInfoDOMapper.selectOne(...).
        final VCSOrderInfoDO vcsOrderInfoDO = VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode("vcsOrderCode")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .incontrolId("incontrolId")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build();
        when(mockVcsOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderInfoDO);

        when(mockOrderItemMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(null);

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.tsdpCallBack("vcsOrderCode", false);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testTsdpCallBack_OrderInfoDOMapperSelectOneReturnsNull() {
        // Setup
        // Configure VcsOrderInfoDOMapper.selectOne(...).
        final VCSOrderInfoDO vcsOrderInfoDO = VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode("vcsOrderCode")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .incontrolId("incontrolId")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build();
        when(mockVcsOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderInfoDO);

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build();
        when(mockOrderItemMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDO);

        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(null);

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.tsdpCallBack("vcsOrderCode", false);

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testPaymentMock() {
        // Setup
        // Configure OrderInfoDOMapper.selectList(...).
        final List<OrderInfoDO> infoDOS = List.of(OrderInfoDO.builder()
                .orderCode("LRcode")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build());
        when(mockOrderInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(infoDOS);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> orderItemDOS = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build());
        when(mockOrderItemMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDOS);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode("vcsOrderCode")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .incontrolId("incontrolId")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDOS);

        when(mockPhoneNumberDecodeUtil.getDecodePhone("contactPhone")).thenReturn("phoneNumber");
        when(mockEcpIdUtil.nextIdStr()).thenReturn("messageId");
        when(mockPiplDataUtil.getDecodeText("carVin")).thenReturn("carVin");

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build();
        when(mockOrderItemMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDO);

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.paymentMock("orderCode", "parentOrderCode");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testPaymentMock_OrderInfoDOMapperSelectListReturnsNoItems() {
        // Setup
        when(mockOrderInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.paymentMock("orderCode", "parentOrderCode");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testPaymentMock_JAGUAR() {
        // Setup
        // Configure OrderInfoDOMapper.selectList(...).
        final List<OrderInfoDO> infoDOS = List.of(OrderInfoDO.builder()
                .orderCode("JAcode")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build());
        when(mockOrderInfoMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(infoDOS);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> orderItemDOS = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build());
        when(mockOrderItemMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDOS);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode("vcsOrderCode")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .incontrolId("incontrolId")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDOS);

        when(mockPhoneNumberDecodeUtil.getDecodePhone("contactPhone")).thenReturn("phoneNumber");
        when(mockEcpIdUtil.nextIdStr()).thenReturn("messageId");
        when(mockPiplDataUtil.getDecodeText("carVin")).thenReturn("carVin");

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build();
        when(mockOrderItemMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDO);

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.paymentMock("orderCode", "parentOrderCode");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testPaymentMock_parentOrderCode_null() {
        // Setup
        when(mockOrderInfoMapper.updateById(OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build())).thenReturn(0);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> orderItemDOS = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build());
        when(mockOrderItemMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDOS);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .id(0L)
                .vcsOrderCode("vcsOrderCode")
                .orderCode("orderCode")
                .orderItemCode("orderItemCode")
                .incontrolId("incontrolId")
                .carVin("carVin")
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build());
        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDOS);

        when(mockPhoneNumberDecodeUtil.getDecodePhone("contactPhone")).thenReturn("phoneNumber");
        when(mockEcpIdUtil.nextIdStr()).thenReturn("messageId");
        when(mockPiplDataUtil.getDecodeText("carVin")).thenReturn("carVin");

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productVersionCode("productVersionCode")
                .productCode("productCode")
                .productSkuCode("productSkuCode")
                .productName("productName")
                .productImageUrl("productImageUrl")
                .productAttribute("productAttribute")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .costAmount(0)
                .orderItemSpuType(0)
                .aftersalesStatus(0)
                .couponModelCode("couponModelCode")
                .kingdeeSkuCode("kingdeeSkuCode")
                .build();
        when(mockOrderItemMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDO);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("code")
                .costAmount(0)
                .freightAmount(0)
                .parentOrderCode("parentOrderCode")
                .orderStatus(0)
                .paymentStatus(0)
                .orderType(0)
                .wxPhone("wxPhone")
                .wxPhoneMix("wxPhoneMix")
                .customerRemark("customerRemark")
                .contactPhone("contactPhone")
                .contactPhoneMix("contactPhoneMix")
                .pointAmount(0)
                .build();
        when(mockOrderInfoMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.paymentMock("orderCode", "");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testPaymentMock_OrderInfoDOMapperSelectOneReturnsNull() {
        // Setup
        // Run the test
        final Integer result = orderRefundDOServiceImplUnderTest.paymentMock("orderCode", "parentOrderCode");

        // Verify the results
        assertEquals(Integer.valueOf(0), result);
    }
}

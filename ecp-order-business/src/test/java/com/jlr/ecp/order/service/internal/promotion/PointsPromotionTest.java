package com.jlr.ecp.order.service.internal.promotion;

import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.service.internal.promotion.dto.PromotionRespDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static junit.framework.TestCase.assertEquals;
import static org.assertj.core.api.Assertions.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class PointsPromotionTest {

    private PointsPromotion pointsPromotionUnderTest;

    @Before
    public void setUp() {
        pointsPromotionUnderTest = new PointsPromotion();
    }

    @Test
    public void executePromotional_SkuSupportingPointsAndEnoughPoints_AppliesPoints() {
        // Setup
        List<CartProductSkuInfo> skuInfos = new ArrayList<>();
        CartProductSkuInfo skuInfo = new CartProductSkuInfo();
        skuInfo.setSupportCashAndPoints(true);
        skuInfo.setSalePoints(5);
        skuInfo.setSalePrice("100");
        skuInfo.setSalePointsPrice("50");
        skuInfo.setJoinCalculateFlag(true);
        skuInfo.setFinalJoinCalculateFlag(true);
        skuInfo.setProductSkuCode("1");
        skuInfos.add(skuInfo);
        PromotionDto promotion = new PromotionDto();
        promotion.setPoints(10);

        // Run the test
        PromotionRespDto result = pointsPromotionUnderTest.executePromotional(skuInfos, promotion);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void checkUserChoose_ValidSelection_ReturnsCorrectList() {
        CartProductSkuInfo skuInfo = new CartProductSkuInfo();
        skuInfo.setChooseFlag(true);
        skuInfo.setSupportCashAndPoints(true);
        skuInfo.setSalePoints(10);

        List<CartProductSkuInfo> skuInfos = new ArrayList<>();
        skuInfos.add(skuInfo);

        PromotionDto promotionDto = new PromotionDto();
        promotionDto.setPoints(20);
        promotionDto.setCouponModelClassify(CouponTypeEnum.POINTS.getType());

        List<PromotionDto> promotions = new ArrayList<>();
        promotions.add(promotionDto);

        List<CartProductSkuInfo> result = pointsPromotionUnderTest.checkUserChoose(skuInfos, promotions, null);

        assertEquals(1, result.size());
    }

}

package com.jlr.ecp.order.service.cart;

import cn.hutool.core.lang.Snowflake;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.inventory.api.InventoryOrderApi;
import com.jlr.ecp.inventory.dto.SkuStockRespDTO;
import com.jlr.ecp.order.api.cart.dto.CarInfoDTO;
import com.jlr.ecp.order.api.cart.dto.CartCreatDTO;
import com.jlr.ecp.order.api.cart.dto.CartItemUpdateDTO;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.app.cart.vo.ShoppingCarItemVO;
import com.jlr.ecp.order.dal.dataobject.cart.ShoppingCarDO;
import com.jlr.ecp.order.dal.dataobject.cart.ShoppingCarItemDO;
import com.jlr.ecp.order.dal.mysql.cart.ShoppingCarItemMapper;
import com.jlr.ecp.order.dal.mysql.cart.ShoppingCarMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.product.api.product.ProductApi;
import com.jlr.ecp.product.api.product.dto.ProductSkuCartDTO;
import com.jlr.ecp.product.api.product.vo.ProductCartViewVO;
import com.jlr.ecp.product.api.product.vo.ProductDetailReqVO;
import com.jlr.ecp.product.api.product.vo.ProductSpuInfoVO;
import com.jlr.ecp.product.enums.product.ProductStockLimitFlagEnum;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.jlr.ecp.order.service.Constant.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ShoppingCarServiceImplTest {

    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private ShoppingCarItemMapper mockCarItemMapper;
    @Mock
    private ShoppingCarMapper mockCarMapper;
    @Mock
    private ProductApi mockProductApi;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private InventoryOrderApi mockInventoryOrderApi;

    @InjectMocks
    private ShoppingCarServiceImpl shoppingCarServiceImplUnderTest;

    private CartCreatDTO cartCreatDTO;
    private CarInfoDTO carInfoDTO;
    private ProductSkuCartDTO productSkuCartDTO;
    private ProductSpuInfoVO productSpuInfoVO;

    private CartItemUpdateDTO cartItemUpdateDTO;
    private ProductSkuCartDTO productSkuCartDTOForBrandGoodsChooseSku;
    private ProductSpuInfoVO productSpuInfoVOForBrandGoodsChooseSku;
    private ShoppingCarItemDO existingItem;
    private ShoppingCarItemDO duplicateItem;

    @Before
    public void setUp() {
        cartItemUpdateDTO = new CartItemUpdateDTO();
        cartItemUpdateDTO.setId(123);
        cartItemUpdateDTO.setProductSkuCode("sku123");
        cartItemUpdateDTO.setQuantity(2);

        productSkuCartDTOForBrandGoodsChooseSku = new ProductSkuCartDTO();
        productSkuCartDTOForBrandGoodsChooseSku.setProductSpuCode("spu123");
        productSkuCartDTOForBrandGoodsChooseSku.setModelCode("model123");

        productSpuInfoVOForBrandGoodsChooseSku = new ProductSpuInfoVO();
        productSpuInfoVOForBrandGoodsChooseSku.setStockLimitFlag(ProductStockLimitFlagEnum.LIMITED.getCode());

        existingItem = new ShoppingCarItemDO();
        existingItem.setId(123L);
        existingItem.setProductCode("spu123");
        existingItem.setProductSkuCode("sku123");
        existingItem.setQuantity(1);
        existingItem.setIsDeleted(false);

        duplicateItem = new ShoppingCarItemDO();
        duplicateItem.setId(456L);
        duplicateItem.setProductCode("spu123");
        duplicateItem.setProductSkuCode("sku123");
        duplicateItem.setQuantity(1);
        duplicateItem.setIsDeleted(false);



        cartCreatDTO = new CartCreatDTO();
        cartCreatDTO.setConsumerCode("consumer123");
        cartCreatDTO.setCartCode("cart456");
        cartCreatDTO.setIncontrolName("incontrolName");

        carInfoDTO = new CarInfoDTO();
        carInfoDTO.setProductSkuCode("sku123");
        carInfoDTO.setQuantity(2);
        carInfoDTO.setCartItemType(1);
        cartCreatDTO.setCarInfoDTOList(Collections.singletonList(carInfoDTO));

        productSkuCartDTO = new ProductSkuCartDTO();
        productSkuCartDTO.setProductSpuCode("spu123");
        productSkuCartDTO.setModelCode("model123");

        productSpuInfoVO = new ProductSpuInfoVO();
        productSpuInfoVO.setStockLimitFlag(ProductStockLimitFlagEnum.LIMITED.getCode());
    }

    @Test
    public void testSupplementShoppingCartItem_AllFieldsSet() {
        // Arrange
        ShoppingCarItemVO shoppingCarItemVO = new ShoppingCarItemVO();
        ProductCartViewVO productInfo = new ProductCartViewVO();

        // Set up productInfo with all fields
        productInfo.setIsDeleted(true);
        productInfo.setProductName("Test Product");
        productInfo.setProductDetailReqVO(new ProductDetailReqVO());
        productInfo.setShelfStatus(1);
        productInfo.setChildFulfilmentType(Collections.singleton(2));
        productInfo.setBusinessCode("BUSINESS_001");
        productInfo.setCategoryCodeLevel1Name("Category1");
        productInfo.setCategoryCodeLevel2Name("Category2");
        productInfo.setCategoryCodeLevel3Name("Category3");

        // Act
        ShoppingCarServiceImpl.supplementShoppingCartItem(shoppingCarItemVO, productInfo);

        // Assert
//        assertEquals(productInfo.getProductAttr(), shoppingCarItemVO.getSkuList());
//        assertEquals(productInfo.getIsDeleted(), shoppingCarItemVO.getIsDeleted());
//        assertEquals(productInfo.getProductName(), shoppingCarItemVO.getProductName());
//        assertEquals(productInfo.getProductDetailReqVO(), shoppingCarItemVO.getProductDetailReqVO());
//        assertEquals(productInfo.getShelfStatus(), shoppingCarItemVO.getShelfStatus());
//        assertEquals(productInfo.getPolicyInfo(), shoppingCarItemVO.getPolicyInfo());
//        assertEquals(productInfo.getChildFulfilmentType(), shoppingCarItemVO.getChildFulfilmentType());
//        assertEquals(productInfo.getBusinessCode(), shoppingCarItemVO.getBusinessCode());
//        assertEquals(productInfo.getCategoryCodeLevel1Name(), shoppingCarItemVO.getCategoryCodeLevel1Name());
//        assertEquals(productInfo.getCategoryCodeLevel2Name(), shoppingCarItemVO.getCategoryCodeLevel2Name());
//        assertEquals(productInfo.getCategoryCodeLevel3Name(), shoppingCarItemVO.getCategoryCodeLevel3Name());
    }

    @Test
    public void testBrandGoodsChooseSku_Success() {
        // Arrange
        when(mockCarItemMapper.selectById(123)).thenReturn(existingItem);
        when(mockProductApi.getSkuByCode("sku123")).thenReturn(CommonResult.success(productSkuCartDTO));
        when(mockProductApi.findProductList(any())).thenReturn(CommonResult.success(Collections.singletonList(productSpuInfoVO)));
        when(mockInventoryOrderApi.querySkuStock(any())).thenReturn(CommonResult.success(Collections.singletonList(new SkuStockRespDTO("model123", 5))));
        when(mockCarItemMapper.selectList(any())).thenReturn(Collections.emptyList());

        // Act
        CommonResult<String> result = shoppingCarServiceImplUnderTest.brandGoodsChooseSku(cartItemUpdateDTO, "consumer123");

        // Assert
        assertEquals(Constants.CART_UPDATE_SUCCESS_MESSAGE, result.getData());
//        assertEquals(2, existingItem.getQuantity());
        verify(mockCarItemMapper).updateBatch(anyList());
    }

    @Test(expected = ServiceException.class)
    public void testBrandGoodsChooseSku_ItemNotFound() {
        // Arrange
        when(mockCarItemMapper.selectById(123)).thenReturn(null);

        // Act
        shoppingCarServiceImplUnderTest.brandGoodsChooseSku(cartItemUpdateDTO, "consumer123");
    }

    @Test(expected = ServiceException.class)
    public void testBrandGoodsAddCart_SkuNotFound_ThrowsException() {
        // Arrange
        when(mockProductApi.getSkuByCode(any())).thenReturn(CommonResult.error(ErrorCodeConstants.CART_SKU_ERROR));

        // Act
        shoppingCarServiceImplUnderTest.brandGoodsAddCart(cartCreatDTO, "brandCode");
    }

    @Test
    public void testBrandGoodsAddCart_UpdateExistingItem_Success() {
        // Arrange
        ShoppingCarItemDO existingItem = new ShoppingCarItemDO();
        existingItem.setQuantity(1);
        existingItem.setProductCode("spu123");
        existingItem.setProductSkuCode("sku123");

        when(mockProductApi.getSkuByCode(any())).thenReturn(CommonResult.success(productSkuCartDTO));
        when(mockCarItemMapper.selectOne(any())).thenReturn(existingItem);
        when(mockProductApi.findProductList(any())).thenReturn(CommonResult.success(Collections.singletonList(productSpuInfoVO)));
        when(mockInventoryOrderApi.querySkuStock(any())).thenReturn(CommonResult.success(Collections.singletonList(new SkuStockRespDTO("model123", 10))));

        // Act
        Boolean result = shoppingCarServiceImplUnderTest.brandGoodsAddCart(cartCreatDTO, "brandCode");

        // Assert
        assertTrue(result);
        verify(mockCarItemMapper).updateBatch(anyList());
    }

    @Test(expected = ServiceException.class)
    public void testBrandGoodsAddCart_InvalidQuantity_ThrowsException() {
        // Arrange
        carInfoDTO.setQuantity(0);

        // Act
        shoppingCarServiceImplUnderTest.brandGoodsAddCart(cartCreatDTO, "brandCode");
    }

    @Test
    public void testBrandGoodsAddCart_NewCartAndNewItem_Success() {
        // Arrange
        when(mockEcpIdUtil.nextIdStr()).thenReturn("newCartCode123");
        when(mockProductApi.getSkuByCode(any())).thenReturn(CommonResult.success(productSkuCartDTO));
        when(mockCarItemMapper.selectOne(any())).thenReturn(null);
        when(mockProductApi.findProductList(any())).thenReturn(CommonResult.success(Collections.singletonList(productSpuInfoVO)));
        when(mockInventoryOrderApi.querySkuStock(any())).thenReturn(CommonResult.success(Collections.singletonList(new SkuStockRespDTO("model123", 10))));

        // Act
        Boolean result = shoppingCarServiceImplUnderTest.brandGoodsAddCart(cartCreatDTO, "brandCode");

        // Assert
        assertTrue(result);
//        verify(mockCarMapper).insert(any(ShoppingCarDO.class));
//        verify(mockCarItemMapper).insertBatch(anyList());
    }



    @Test
    public void testAddCart_SuccessfulInsert() {
        // Arrange
        CartCreatDTO cartDTO = createCartDTO();
        mockProductApiSuccess();
        mockCartCheck(null);
        mockIdGeneration();
        mockCartItemCheck(null);

        // Act
        Boolean result = shoppingCarServiceImplUnderTest.addCart(cartDTO, BRAND_CODE);

        // Assert
        assertTrue(result);
        verify(mockCarMapper, times(1)).insert(any(ShoppingCarDO.class));
        verify(mockCarItemMapper, times(1)).insertBatch(anyList());
        verify(mockCarItemMapper, never()).updateBatch(anyList());
    }

    @Test(expected = ServiceException.class)
    public void testAddCart_InvalidQuantity() {
        // Arrange
        CartCreatDTO cartDTO = createCartDTO();
        cartDTO.getCarInfoDTOList().get(0).setQuantity(0);

        // Act
        shoppingCarServiceImplUnderTest.addCart(cartDTO, BRAND_CODE);
    }

    @Test(expected = ServiceException.class)
    public void testAddCart_SkuNotFound() {
        // Arrange
        CartCreatDTO cartDTO = createCartDTO();
        when(mockProductApi.getSkuByCode(anyString())).thenReturn(CommonResult.error(ErrorCodeConstants.CART_SKU_ERROR));

        // Act
        shoppingCarServiceImplUnderTest.addCart(cartDTO, BRAND_CODE);
    }

    @Test(expected = ServiceException.class)
    public void testAddCart_InsufficientStock() {
        // Arrange
        CartCreatDTO cartDTO = createCartDTO();
        ProductSkuCartDTO sku = new ProductSkuCartDTO();
        sku.setSkuQuantity(0);
        when(mockProductApi.getSkuByCode(anyString())).thenReturn(CommonResult.success(sku));

        // Act
        shoppingCarServiceImplUnderTest.addCart(cartDTO, BRAND_CODE);
    }

    @Test
    public void testAddCart_UpdateExistingItem() {
        // Arrange
        CartCreatDTO cartDTO = createCartDTO();
        mockProductApiSuccess();
        mockCartCheck(createExistingCart());
        mockIdGeneration();
        mockCartItemCheck(createExistingCartItem());

        // Act
        Boolean result = shoppingCarServiceImplUnderTest.addCart(cartDTO, BRAND_CODE);

        // Assert
        assertTrue(result);
        verify(mockCarMapper, never()).insert(any());
        verify(mockCarItemMapper, times(1)).updateBatch(anyList());
    }

    private void mockProductApiSuccess() {
        ProductSkuCartDTO sku = new ProductSkuCartDTO();
        sku.setSkuQuantity(10);
        when(mockProductApi.getSkuByCode(anyString())).thenReturn(CommonResult.success(sku));
    }

    private void mockCartCheck(ShoppingCarDO existingCart) {
        when(mockCarMapper.selectOne(any())).thenReturn(existingCart);
        when(mockEcpIdUtil.nextIdStr()).thenReturn(CART_CODE);
    }

    private void mockIdGeneration() {
        when(mockEcpIdUtil.nextIdStr()).thenReturn("NEW_ID");
    }

    private void mockCartItemCheck(ShoppingCarItemDO existingItem) {
        when(mockCarItemMapper.selectOne(any())).thenReturn(existingItem);
    }

    private CartCreatDTO createCartDTO() {
        CartCreatDTO dto = new CartCreatDTO();
        dto.setConsumerCode(CONSUMER_CODE);
        dto.setIncontrolName(INCONTROL_NAME);

        CarInfoDTO carInfo = new CarInfoDTO();
        carInfo.setProductSkuCode(PRODUCT_SKU_CODE);
        carInfo.setQuantity(1);
        carInfo.setSeriesCode(SERIES_CODE);
        carInfo.setCarVin(CAR_VIN);
        dto.setCarInfoDTOList(List.of(carInfo));

        return dto;
    }

    private ShoppingCarDO createExistingCart() {
        ShoppingCarDO cart = new ShoppingCarDO();
        cart.setCartCode(CART_CODE);
        return cart;
    }

    private ShoppingCarItemDO createExistingCartItem() {
        ShoppingCarItemDO item = new ShoppingCarItemDO();
        item.setCartItemCode("EXISTING_ITEM");
        return item;
    }

    @Test
    public void testAddCart_EmptyCarInfoList_ThrowsException() {
        // Arrange
        CartCreatDTO dto = new CartCreatDTO();
        dto.setCarInfoDTOList(Collections.emptyList());

        // Act
        Boolean result =  shoppingCarServiceImplUnderTest.addCart(dto, BRAND_CODE);
        assertEquals(false, result);
    }

    @Test(expected = ServiceException.class)
    public void testAddCart_InvalidQuantity_ThrowsException() {
        // Arrange
        CartCreatDTO dto = getCartCreatDTO();
        dto.getCarInfoDTOList().get(0).setQuantity(0); // Invalid quantity

        // Act
        shoppingCarServiceImplUnderTest.addCart(dto, BRAND_CODE);
    }

    @Test
    public void testAddCart() {
        // Setup
        final CartCreatDTO cartCreatDTO = getCartCreatDTO();

        // Configure ProductApi.getSkuByCode(...).
        final ProductSkuCartDTO productSkuCartDTO = new ProductSkuCartDTO();
        productSkuCartDTO.setProductSkuCode(PRODUCT_SKU_CODE);
        productSkuCartDTO.setProductSpuCode(PRODUCT_CODE);
        productSkuCartDTO.setAttributeValues(ATTRIBUTE_VALUES);
        productSkuCartDTO.setSkuQuantity(1);
        productSkuCartDTO.setSalePrice(0L);
        final CommonResult<ProductSkuCartDTO> productSkuCartDTOCommonResult = CommonResult.success(productSkuCartDTO);
        when(mockProductApi.getSkuByCode(PRODUCT_SKU_CODE)).thenReturn(productSkuCartDTOCommonResult);

        // Configure ShoppingCarItemMapper.selectOne(...).
        final ShoppingCarItemDO itemDO = ShoppingCarItemDO.builder()
                .cartCode(CART_CODE)
                .cartItemCode(CART_ITEM_CODE)
                .cartItemType(0)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .quantity(1)
                .seriesCode(SERIES_CODE)
                .seriesName(SERIES_NAME)
                .carVin(CAR_VIN)
                .brandCode(BRAND_CODE)
                .build();
        when(mockCarItemMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(itemDO);

        // Run the test
        final Boolean result = shoppingCarServiceImplUnderTest.addCart(cartCreatDTO, BRAND_CODE);

        // Verify the results
        assertThat(result).isTrue();

    }

    @NotNull
    private static CartCreatDTO getCartCreatDTO() {
        final CartCreatDTO cartCreatDTO = new CartCreatDTO();
        cartCreatDTO.setConsumerCode(CONSUMER_CODE);
        cartCreatDTO.setCartCode(CART_CODE);
        cartCreatDTO.setIncontrolName(INCONTROL_NAME);
        final CarInfoDTO carInfoDTO = new CarInfoDTO();
        carInfoDTO.setSeriesCode(SERIES_CODE);
        carInfoDTO.setSeriesName(SERIES_NAME);
        carInfoDTO.setCarVin(CAR_VIN);
        carInfoDTO.setCartItemType(0);
        carInfoDTO.setProductSkuCode(PRODUCT_SKU_CODE);
        carInfoDTO.setQuantity(1);
        cartCreatDTO.setCarInfoDTOList(List.of(carInfoDTO));
        return cartCreatDTO;
    }

    @Test
    public void testChooseSku() {
        // Setup
        final CartItemUpdateDTO cartItemUpdateDTO = new CartItemUpdateDTO();
        cartItemUpdateDTO.setId(0);
        cartItemUpdateDTO.setCartCode(CART_CODE);
        cartItemUpdateDTO.setProductSkuCode(PRODUCT_SKU_CODE);
        cartItemUpdateDTO.setQuantity(0);

        // Configure ShoppingCarItemMapper.selectById(...).
        final ShoppingCarItemDO itemDO = ShoppingCarItemDO.builder()
                .cartCode(CART_CODE)
                .cartItemCode(CART_ITEM_CODE)
                .cartItemType(0)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .quantity(0)
                .seriesCode(SERIES_CODE)
                .seriesName(SERIES_NAME)
                .carVin(CAR_VIN)
                .brandCode(BRAND_CODE)
                .build();
        when(mockCarItemMapper.selectById(0)).thenReturn(itemDO);

        // Configure ProductApi.getSkuByCode(...).
        final ProductSkuCartDTO productSkuCartDTO = new ProductSkuCartDTO();
        productSkuCartDTO.setProductSkuCode(PRODUCT_SKU_CODE);
        productSkuCartDTO.setProductSpuCode(PRODUCT_CODE);
        productSkuCartDTO.setAttributeValues(ATTRIBUTE_VALUES);
        productSkuCartDTO.setSkuQuantity(0);
        productSkuCartDTO.setSalePrice(0L);
        final CommonResult<ProductSkuCartDTO> productSkuCartDTOCommonResult = CommonResult.success(productSkuCartDTO);
        when(mockProductApi.getSkuByCode(PRODUCT_SKU_CODE)).thenReturn(productSkuCartDTOCommonResult);

        when(mockCarItemMapper.updateById(ShoppingCarItemDO.builder()
                .cartCode(CART_CODE)
                .cartItemCode(CART_ITEM_CODE)
                .cartItemType(0)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .quantity(0)
                .seriesCode(SERIES_CODE)
                .seriesName(SERIES_NAME)
                .carVin(CAR_VIN)
                .brandCode(BRAND_CODE)
                .build())).thenReturn(0);

        // Run the test
        final Boolean result = shoppingCarServiceImplUnderTest.chooseSku(cartItemUpdateDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testChooseSkuShoppingCarItemMapperSelectByIdReturnsNull() {
        // Setup
        final CartItemUpdateDTO cartItemUpdateDTO = new CartItemUpdateDTO();
        cartItemUpdateDTO.setId(0);
        cartItemUpdateDTO.setCartCode(CART_CODE);
        cartItemUpdateDTO.setProductSkuCode(PRODUCT_SKU_CODE);
        cartItemUpdateDTO.setQuantity(0);

        when(mockCarItemMapper.selectById(0)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> shoppingCarServiceImplUnderTest.chooseSku(cartItemUpdateDTO))
                .isInstanceOf(ServiceException.class);
    }

    @Test
    public void testChooseSkuProductApiReturnsError() {
        // Setup
        final CartItemUpdateDTO cartItemUpdateDTO = new CartItemUpdateDTO();
        cartItemUpdateDTO.setId(0);
        cartItemUpdateDTO.setCartCode(CART_CODE);
        cartItemUpdateDTO.setProductSkuCode(PRODUCT_SKU_CODE);
        cartItemUpdateDTO.setQuantity(0);

        // Configure ShoppingCarItemMapper.selectById(...).
        final ShoppingCarItemDO itemDO = ShoppingCarItemDO.builder()
                .cartCode(CART_CODE)
                .cartItemCode(CART_ITEM_CODE)
                .cartItemType(0)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .quantity(0)
                .seriesCode(SERIES_CODE)
                .seriesName(SERIES_NAME)
                .carVin(CAR_VIN)
                .brandCode(BRAND_CODE)
                .build();
        when(mockCarItemMapper.selectById(0)).thenReturn(itemDO);

        // Configure ProductApi.getSkuByCode(...).
        final CommonResult<ProductSkuCartDTO> productSkuCartDTOCommonResult = CommonResult.error(
                new ServiceException(500, "message"));
        when(mockProductApi.getSkuByCode(PRODUCT_SKU_CODE)).thenReturn(productSkuCartDTOCommonResult);

        // Run the test
        try {
            shoppingCarServiceImplUnderTest.chooseSku(cartItemUpdateDTO);
        } catch (Exception e) {
            // Verify the results
            assertEquals(ServiceException.class, e.getClass());
        }
    }
}

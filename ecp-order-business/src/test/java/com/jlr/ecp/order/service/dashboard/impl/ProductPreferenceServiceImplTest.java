package com.jlr.ecp.order.service.dashboard.impl;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.admin.dashboard.dto.KpiQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.QueryReqDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SqlQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.dto.SqlResultDTO;
import com.jlr.ecp.order.controller.admin.dashboard.vo.*;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProductPreferenceServiceImplTest {

    @Mock
    private OrderInfoDOMapper mockOrderInfoDOMapper;
    @Mock
    private RedisService mockRedisService;

    @InjectMocks
    private ProductPreferenceServiceImpl productPreferenceServiceImplUnderTest;

    @Test
    public void testVehicleModelList() {
        // Setup
        final CommonResult<List<String>> expectedResult = CommonResult.success(List.of("value"));

        // Configure RedisService.getCacheMap(...).
        final Map<String, Object> map = Map.ofEntries(Map.entry("value", "value"));
        when(mockRedisService.getCacheMap(Constants.REDIS_KEY.SERIES_CACHE_KEY)).thenReturn(map);

        // Run the test
        final CommonResult<List<String>> result = productPreferenceServiceImplUnderTest.vehicleModelList();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testVehicleModelList_RedisServiceReturnsNull() {
        // Setup
        final CommonResult<List<String>> expectedResult = CommonResult.success(Collections.emptyList());
        when(mockRedisService.getCacheMap(Constants.REDIS_KEY.SERIES_CACHE_KEY)).thenReturn(null);

        // Run the test
        final CommonResult<List<String>> result = productPreferenceServiceImplUnderTest.vehicleModelList();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSalesTrendKpiList() {
        // Setup
        final CommonResult<List<KpiQueryDTO>> expectedResult = CommonResult.success(
                List.of(new KpiQueryDTO(1, "总订单数", "Total Order Counts", "代客下单总订单数", "pcs"),
                        new KpiQueryDTO(2, "成交订单数", "Turnover Order Counts", "代客下单成交订单数", "pcs"),
                        new KpiQueryDTO(3, "总订单交易额", "Total Order Volume", "代客下单总订单交易额", "RMB"),
                        new KpiQueryDTO(4, "总收入", "Total Income", "代客下单总收入", "RMB"),
                        new KpiQueryDTO(5, "成交客单价", "ATV", "代客下单成交客单价", "RMB"),
                        new KpiQueryDTO(6, "成交商品总数", "Turnover Product Counts", "代客下单成交商品总数", "pcs")));

        // Run the test
        final CommonResult<List<KpiQueryDTO>> result = productPreferenceServiceImplUnderTest.salesTrendKpiList();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSalesProportionByAttributesKpiList() {
        // Setup
        final CommonResult<List<KpiQueryDTO>> expectedResult = CommonResult.success(
                List.of(new KpiQueryDTO(1, "总订单数", "Total Order Counts", "代客下单总订单数", "pcs"),
                        new KpiQueryDTO(2, "成交订单数", "Turnover Order Counts", "代客下单成交订单数", "pcs"),
                        new KpiQueryDTO(3, "总订单交易额", "Total Order Volume", "代客下单总订单交易额", "RMB"),
                        new KpiQueryDTO(4, "总收入", "Total Income", "代客下单总收入", "RMB"),
                        new KpiQueryDTO(6, "成交商品总数", "Turnover Product Counts", "代客下单成交商品总数", "pcs")));

        // Run the test
        final CommonResult<List<KpiQueryDTO>> result = productPreferenceServiceImplUnderTest.salesProportionByAttributesKpiList();

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySalesTrend_TOTAL_ORDER_NUMBER() {
        // Setup
        final QueryReqDTO dto = new QueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setVehicleModel("vehicleModel");
        dto.setKpi(1);

        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult();

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = productPreferenceServiceImplUnderTest.querySalesTrend(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @NotNull
    private CommonResult<ProductSalesTrendRespVo> getProductSalesTrendRespVoCommonResult() {
        // 构建期望结果
        final ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();

        // 构建 ChartRespVo
        final ChartRespVo chart = new ChartRespVo();
        chart.setXAxis(List.of("2020", "2021"));

        // 构建 ChartRespVo.ChartItemVo
        final ChartRespVo.ChartItemVo chartItemVo1 = new ChartRespVo.ChartItemVo();
        chartItemVo1.setName("InControl Remote Service");
        chartItemVo1.setData(List.of("0", "0"));

        final ChartRespVo.ChartItemVo chartItemVo2 = new ChartRespVo.ChartItemVo();
        chartItemVo2.setName("InControl Online Service");
        chartItemVo2.setData(List.of("0", "0"));

        final ChartRespVo.ChartItemVo chartItemVo3 = new ChartRespVo.ChartItemVo();
        chartItemVo3.setName("InControl Remote + Online Service");
        chartItemVo3.setData(List.of("0", "0"));

        chart.setDataList(List.of(chartItemVo1, chartItemVo2, chartItemVo3));
        productSalesTrendRespVo.setChart(chart);

        // 构建 TableRespVo
        final TableRespVo table = new TableRespVo();

        // 构建 TableRespVo.HeaderItem
        final TableRespVo.HeaderItem headerItem1 = new TableRespVo.HeaderItem();
        headerItem1.setProp("firstColumn");
        headerItem1.setLabel("");

        final TableRespVo.HeaderItem headerItem2 = new TableRespVo.HeaderItem();
        headerItem2.setProp("2020");
        headerItem2.setLabel("2020");

        final TableRespVo.HeaderItem headerItem3 = new TableRespVo.HeaderItem();
        headerItem3.setProp("2021");
        headerItem3.setLabel("2021");

        table.setHeaders(List.of(headerItem1, headerItem2, headerItem3));

        // 构建 TableRespVo.TableData
        final Map<String, String> tableData1 = Map.ofEntries(
                Map.entry("firstColumn", "InControl Remote Service"),
                Map.entry("2020", "0"),
                Map.entry("2021", "0")
        );

        final Map<String, String> tableData2 = Map.ofEntries(
                Map.entry("firstColumn", "InControl Online Service"),
                Map.entry("2020", "0"),
                Map.entry("2021", "0")
        );

        final Map<String, String> tableData3 = Map.ofEntries(
                Map.entry("firstColumn", "InControl Remote + Online Service"),
                Map.entry("2020", "0"),
                Map.entry("2021", "0")
        );

        table.setTableData(List.of(tableData1, tableData2, tableData3));
        productSalesTrendRespVo.setTable(table);

        final CommonResult<ProductSalesTrendRespVo> expectedResult = CommonResult.success(productSalesTrendRespVo);

        // Configure OrderInfoDOMapper.getSalesTrendByChannel(...).
        final SqlResultDTO sqlResultDTO = new SqlResultDTO();
        sqlResultDTO.setOrderType(0);
        sqlResultDTO.setLabel("label");
        sqlResultDTO.setQuantity(0L);
        sqlResultDTO.setAmount(0L);
        final List<SqlResultDTO> resultDTOS = List.of(sqlResultDTO);
        when(mockOrderInfoDOMapper.getSalesTrendByChannel(any(SqlQueryDTO.class))).thenReturn(resultDTOS);

        // Configure OrderInfoDOMapper.getSalesTrendProductCountByChannel(...).
        final SqlResultDTO sqlResultDTO2 = new SqlResultDTO();
        sqlResultDTO2.setOrderType(0);
        sqlResultDTO2.setLabel("label");
        sqlResultDTO2.setQuantity(0L);
        sqlResultDTO2.setAmount(0L);

        // Configure OrderInfoDOMapper.getSalesTrendBySeriesCode(...).
        final SqlResultDTO sqlResultDTO3 = new SqlResultDTO();
        sqlResultDTO3.setOrderType(0);
        sqlResultDTO3.setLabel("label");
        sqlResultDTO3.setQuantity(0L);
        sqlResultDTO3.setAmount(0L);

        return expectedResult;
    }

    @NotNull
    private CommonResult<ProductSalesTrendRespVo> getSalesTrendBySeriesCodeCommonResult() {
        // 构建期望结果
        final ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();

        // 构建 ChartRespVo
        final ChartRespVo chart = new ChartRespVo();
        chart.setXAxis(List.of("2020", "2021"));

        // 构建 ChartRespVo.ChartItemVo
        final ChartRespVo.ChartItemVo chartItemVo1 = new ChartRespVo.ChartItemVo();
        chartItemVo1.setName("InControl Remote Service");
        chartItemVo1.setData(List.of("0", "0"));

        final ChartRespVo.ChartItemVo chartItemVo2 = new ChartRespVo.ChartItemVo();
        chartItemVo2.setName("InControl Online Service");
        chartItemVo2.setData(List.of("0", "0"));

        final ChartRespVo.ChartItemVo chartItemVo3 = new ChartRespVo.ChartItemVo();
        chartItemVo3.setName("InControl Remote + Online Service");
        chartItemVo3.setData(List.of("0", "0"));

        chart.setDataList(List.of(chartItemVo1, chartItemVo2, chartItemVo3));
        productSalesTrendRespVo.setChart(chart);

        // 构建 TableRespVo
        final TableRespVo table = new TableRespVo();

        // 构建 TableRespVo.HeaderItem
        final TableRespVo.HeaderItem headerItem1 = new TableRespVo.HeaderItem();
        headerItem1.setProp("firstColumn");
        headerItem1.setLabel("");

        final TableRespVo.HeaderItem headerItem2 = new TableRespVo.HeaderItem();
        headerItem2.setProp("2020");
        headerItem2.setLabel("2020");

        final TableRespVo.HeaderItem headerItem3 = new TableRespVo.HeaderItem();
        headerItem3.setProp("2021");
        headerItem3.setLabel("2021");

        table.setHeaders(List.of(headerItem1, headerItem2, headerItem3));

        // 构建 TableRespVo.TableData
        final Map<String, String> tableData1 = Map.ofEntries(
                Map.entry("firstColumn", "InControl Remote Service"),
                Map.entry("2020", "0"),
                Map.entry("2021", "0")
        );

        final Map<String, String> tableData2 = Map.ofEntries(
                Map.entry("firstColumn", "InControl Online Service"),
                Map.entry("2020", "0"),
                Map.entry("2021", "0")
        );

        final Map<String, String> tableData3 = Map.ofEntries(
                Map.entry("firstColumn", "InControl Remote + Online Service"),
                Map.entry("2020", "0"),
                Map.entry("2021", "0")
        );

        table.setTableData(List.of(tableData1, tableData2, tableData3));
        productSalesTrendRespVo.setTable(table);

        final CommonResult<ProductSalesTrendRespVo> expectedResult = CommonResult.success(productSalesTrendRespVo);

        // Configure OrderInfoDOMapper.getSalesTrendByChannel(...).
        final SqlResultDTO sqlResultDTO = new SqlResultDTO();
        sqlResultDTO.setOrderType(0);
        sqlResultDTO.setLabel("label");
        sqlResultDTO.setQuantity(0L);
        sqlResultDTO.setAmount(0L);
        final List<SqlResultDTO> resultDTOS = List.of(sqlResultDTO);
        when(mockOrderInfoDOMapper.getSalesTrendBySeriesCode(any(SqlQueryDTO.class))).thenReturn(resultDTOS);

        return expectedResult;
    }

    @Test
    public void testQuerySalesTrend_TOTAL_ORDER_AMOUNT() {
        // Setup
        final QueryReqDTO dto = new QueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setVehicleModel("vehicleModel");
        dto.setKpi(3);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult();

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = productPreferenceServiceImplUnderTest.querySalesTrend(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySalesTrend_INCOME() {
        // Setup
        final QueryReqDTO dto = new QueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setVehicleModel("vehicleModel");
        dto.setKpi(4);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult();

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = productPreferenceServiceImplUnderTest.querySalesTrend(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySalesTrend_vehicle_TOTAL_ORDER_NUMBER() {
        // Setup
        final QueryReqDTO dto = new QueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(2);
        dto.setOrderChannel("LR");
        dto.setVehicleModel("vehicleModel");
        dto.setKpi(1);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getSalesTrendBySeriesCodeCommonResult();

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = productPreferenceServiceImplUnderTest.querySalesTrend(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySalesTrend_vehicle_TOTAL_ORDER_AMOUNT() {
        // Setup
        final QueryReqDTO dto = new QueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(2);
        dto.setOrderChannel("LR");
        dto.setVehicleModel("vehicleModel");
        dto.setKpi(3);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getSalesTrendBySeriesCodeCommonResult();

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = productPreferenceServiceImplUnderTest.querySalesTrend(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySalesTrend_vehicle_INCOME() {
        // Setup
        final QueryReqDTO dto = new QueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(2);
        dto.setOrderChannel("LR");
        dto.setVehicleModel("vehicleModel");
        dto.setKpi(4);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getSalesTrendBySeriesCodeCommonResult();

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = productPreferenceServiceImplUnderTest.querySalesTrend(dto);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testQuerySalesProportionByAttributes_TOTAL_ORDER_NUMBER() {
        // Setup
        final QueryReqDTO dto = new QueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setVehicleModel("vehicleModel");
        dto.setKpi(1);

        // Run the test
        final CommonResult<ProductSalesProportionByAttributesRespVo> result = productPreferenceServiceImplUnderTest.querySalesProportionByAttributes(dto);

        // Verify the results
        assertNotNull(result);
    }

    @NotNull
    private CommonResult<ProductSalesProportionByAttributesRespVo> getProductSalesProportionByAttributesRespVoCommonResult() {
        // 构建期望结果
        final ProductSalesProportionByAttributesRespVo attributesRespVo = new ProductSalesProportionByAttributesRespVo();

        // 构建 PieChartRespVo
        final PieChartRespVo pieChartRespVo1 = new PieChartRespVo();
        pieChartRespVo1.setName("InControl Remote Service");
        pieChartRespVo1.setDisplayValue("0.00%");
        final PieChartRespVo hoverDisplayItem1 = new PieChartRespVo();
        hoverDisplayItem1.setName("One Year");
        hoverDisplayItem1.setDisplayValue("0.00%");
        final PieChartRespVo hoverDisplayItem2 = new PieChartRespVo();
        hoverDisplayItem2.setName("Three Years");
        hoverDisplayItem2.setDisplayValue("0.00%");
        pieChartRespVo1.setHoverDisplay(List.of(hoverDisplayItem1, hoverDisplayItem2));

        final PieChartRespVo pieChartRespVo2 = new PieChartRespVo();
        pieChartRespVo2.setName("InControl Online Service");
        pieChartRespVo2.setDisplayValue("0.00%");
        final PieChartRespVo hoverDisplayItem3 = new PieChartRespVo();
        hoverDisplayItem3.setName("One Year");
        hoverDisplayItem3.setDisplayValue("0.00%");
        final PieChartRespVo hoverDisplayItem4 = new PieChartRespVo();
        hoverDisplayItem4.setName("Three Years");
        hoverDisplayItem4.setDisplayValue("0.00%");
        pieChartRespVo2.setHoverDisplay(List.of(hoverDisplayItem3, hoverDisplayItem4));

        final PieChartRespVo pieChartRespVo3 = new PieChartRespVo();
        pieChartRespVo3.setName("InControl Remote + Online Service");
        pieChartRespVo3.setDisplayValue("0.00%");
        final PieChartRespVo hoverDisplayItem5 = new PieChartRespVo();
        hoverDisplayItem5.setName("One Year");
        hoverDisplayItem5.setDisplayValue("0.00%");
        final PieChartRespVo hoverDisplayItem6 = new PieChartRespVo();
        hoverDisplayItem6.setName("Three Years");
        hoverDisplayItem6.setDisplayValue("0.00%");
        pieChartRespVo3.setHoverDisplay(List.of(hoverDisplayItem5, hoverDisplayItem6));

        attributesRespVo.setPieChart(List.of(pieChartRespVo1, pieChartRespVo2, pieChartRespVo3));

        // 构建 TableRespVo
        final TableRespVo table = new TableRespVo();

        // 构建 TableRespVo.HeaderItem
        final TableRespVo.HeaderItem headerItem1 = new TableRespVo.HeaderItem();
        headerItem1.setProp("firstColumn");
        headerItem1.setLabel("");

        final TableRespVo.HeaderItem subHeaderItem1 = new TableRespVo.HeaderItem();
        subHeaderItem1.setProp("firstSubColumn");
        subHeaderItem1.setLabel("");
        headerItem1.setSubHeaders(List.of(subHeaderItem1));

        final TableRespVo.HeaderItem headerItem2 = new TableRespVo.HeaderItem();
        headerItem2.setProp("InControl Remote Service");
        headerItem2.setLabel("InControl Remote Service");

        final TableRespVo.HeaderItem subHeaderItem2 = new TableRespVo.HeaderItem();
        subHeaderItem2.setProp("InControl Remote ServiceOne Year");
        subHeaderItem2.setLabel("One Year");
        final TableRespVo.HeaderItem subHeaderItem3 = new TableRespVo.HeaderItem();
        subHeaderItem3.setProp("InControl Remote ServiceThree Years");
        subHeaderItem3.setLabel("Three Years");
        headerItem2.setSubHeaders(List.of(subHeaderItem2, subHeaderItem3));

        final TableRespVo.HeaderItem headerItem3 = new TableRespVo.HeaderItem();
        headerItem3.setProp("InControl Online Service");
        headerItem3.setLabel("InControl Online Service");

        final TableRespVo.HeaderItem subHeaderItem4 = new TableRespVo.HeaderItem();
        subHeaderItem4.setProp("InControl Online ServiceOne Year");
        subHeaderItem4.setLabel("One Year");
        final TableRespVo.HeaderItem subHeaderItem5 = new TableRespVo.HeaderItem();
        subHeaderItem5.setProp("InControl Online ServiceThree Years");
        subHeaderItem5.setLabel("Three Years");
        headerItem3.setSubHeaders(List.of(subHeaderItem4, subHeaderItem5));

        final TableRespVo.HeaderItem headerItem4 = new TableRespVo.HeaderItem();
        headerItem4.setProp("InControl Remote + Online Service");
        headerItem4.setLabel("InControl Remote + Online Service");

        final TableRespVo.HeaderItem subHeaderItem6 = new TableRespVo.HeaderItem();
        subHeaderItem6.setProp("InControl Remote + Online ServiceOne Year");
        subHeaderItem6.setLabel("One Year");
        final TableRespVo.HeaderItem subHeaderItem7 = new TableRespVo.HeaderItem();
        subHeaderItem7.setProp("InControl Remote + Online ServiceThree Years");
        subHeaderItem7.setLabel("Three Years");
        headerItem4.setSubHeaders(List.of(subHeaderItem6, subHeaderItem7));

        table.setHeaders(List.of(headerItem1, headerItem2, headerItem3, headerItem4));

        // 构建 TableRespVo.TableData
        final Map<String, String> tableData1 = Map.ofEntries(
                Map.entry("InControl Online ServiceOne Year", "0.00%"),
                Map.entry("InControl Remote + Online ServiceOne Year", "0.00%"),
                Map.entry("InControl Remote ServiceOne Year", "0.00%"),
                Map.entry("InControl Online ServiceThree Years", "0.00%"),
                Map.entry("firstSubColumn", "Land Rover"),
                Map.entry("InControl Remote ServiceThree Years", "0.00%"),
                Map.entry("InControl Remote + Online ServiceThree Years", "0.00%")
        );

        table.setTableData(List.of(tableData1));
        attributesRespVo.setTable(table);

        final CommonResult<ProductSalesProportionByAttributesRespVo> expectedResult = CommonResult.success(attributesRespVo);

        // Configure OrderInfoDOMapper.getSalesProportionByChannel(...).
        final SqlResultDTO sqlResultDTO = new SqlResultDTO();
        sqlResultDTO.setOrderType(0);
        sqlResultDTO.setLabel("label");
        sqlResultDTO.setQuantity(1L);
        sqlResultDTO.setAmount(1L);
        final List<SqlResultDTO> resultDTOS = List.of(sqlResultDTO);
        when(mockOrderInfoDOMapper.getSalesProportionByChannel(any(SqlQueryDTO.class))).thenReturn(resultDTOS);

        // Configure OrderInfoDOMapper.getSalesProportionRefundByChannel(...).
        final SqlResultDTO sqlResultDTO1 = new SqlResultDTO();
        sqlResultDTO1.setOrderType(0);
        sqlResultDTO1.setLabel("label");
        sqlResultDTO1.setQuantity(1L);
        sqlResultDTO1.setAmount(1L);
        final List<SqlResultDTO> resultDTOS1 = List.of(sqlResultDTO1);
        when(mockOrderInfoDOMapper.getSalesProportionRefundByChannel(any(SqlQueryDTO.class))).thenReturn(resultDTOS1);

        // Configure OrderInfoDOMapper.getSalesProportionProductCountByChannel(...).
        final SqlResultDTO sqlResultDTO2 = new SqlResultDTO();
        sqlResultDTO2.setOrderType(0);
        sqlResultDTO2.setLabel("label");
        sqlResultDTO2.setQuantity(1L);
        sqlResultDTO2.setAmount(1L);
        final List<SqlResultDTO> resultDTOS2 = List.of(sqlResultDTO2);
        when(mockOrderInfoDOMapper.getSalesProportionProductCountByChannel(any(SqlQueryDTO.class))).thenReturn(resultDTOS2);
        return expectedResult;
    }

    @Test
    public void testQuerySalesProportionByAttributes_TOTAL_ORDER_AMOUNT() {
        // Setup
        final QueryReqDTO dto = new QueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setVehicleModel("vehicleModel");
        dto.setKpi(3);

        // Run the test
        final CommonResult<ProductSalesProportionByAttributesRespVo> result = productPreferenceServiceImplUnderTest.querySalesProportionByAttributes(dto);

        // Verify the results
        assertNotNull(result);
    }

    @Test
    public void testQuerySalesProportionByAttributes_INCOME() {
        // Setup
        final QueryReqDTO dto = new QueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setVehicleModel("vehicleModel");
        dto.setKpi(4);

        // Run the test
        final CommonResult<ProductSalesProportionByAttributesRespVo> result = productPreferenceServiceImplUnderTest.querySalesProportionByAttributes(dto);

        // Verify the results
        assertNotNull(result);
    }

    @Test
    public void testQuerySalesProportionByAttributes_TOTAL_GOODS_NUMBER() {
        // Setup
        final QueryReqDTO dto = new QueryReqDTO();
        dto.setStartTime("2020-01-01");
        dto.setEndTime("2021-01-01");
        dto.setType(1);
        dto.setOrderChannel("LR");
        dto.setVehicleModel("vehicleModel");
        dto.setKpi(6);

        // Run the test
        final CommonResult<ProductSalesProportionByAttributesRespVo> result = productPreferenceServiceImplUnderTest.querySalesProportionByAttributes(dto);

        assertNotNull(result);
    }
}

package com.jlr.ecp.order.service.independent;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.independent.dto.TriggerIndependentReqDTO;
import com.jlr.ecp.order.api.independent.dto.UpdateStatusToSuccReqDTO;
import com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentDO;
import com.jlr.ecp.order.dal.dataobject.independent.OrderIndependentItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderCouponDetailDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.mysql.independent.OrderIndependentDOMapper;
import com.jlr.ecp.order.dal.mysql.independent.OrderIndependentItemDOMapper;
import com.jlr.ecp.order.dal.mysql.independent.OrderIndependentOrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderCouponDetailDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderPaymentRecordsMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.independent.OrderIndependentStatusEnum;
import com.jlr.ecp.order.enums.independent.OrderIndependentTypeEnum;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import com.jlr.ecp.order.service.coupon.status.dto.CouponStatusChangedKafkaDto;
import com.jlr.ecp.payment.api.independent.IndependentApi;
import com.jlr.ecp.payment.api.independent.dto.CreateIndependentReqDTO;
import com.jlr.ecp.payment.api.independent.dto.CreateIndependentRespDTO;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrderIndependentDOServiceImplTest {

    @Mock
    private OrderInfoDOMapper orderInfoDOMapper;
    @Mock
    private OrderItemDOMapper orderItemDOMapper;
    @Mock
    private OrderPaymentRecordsMapper orderPaymentRecordsMapper;
    @Mock
    private OrderIndependentItemDOService orderIndependentItemDOService;
    @Mock
    private OrderIndependentOrderItemDOService orderIndependentOrderItemDOService;
    @Mock
    private TransactionTemplate transactionTemplate;
    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private IndependentApi independentApi;
    @Mock
    private OrderRefundItemDOMapper orderRefundItemDOMapper;
    @Mock
    private OrderIndependentItemDOMapper orderIndependentItemDOMapper;
    @Mock
    private OrderIndependentOrderItemDOMapper orderIndependentOrderItemDOMapper;
    @Mock
    private OrderCouponDetailDOMapper orderCouponDetailDOMapper;
    @Mock
    private OrderIndependentDOMapper orderIndependentDOMapper;
    @Mock
    private OrderIndependentDOMapper baseMapper;

    @InjectMocks
    private OrderIndependentDOServiceImpl orderIndependentDOService;

    private static final String ORDER_CODE = "orderCode";
    private static final String ORDER_ITEM_CODE = "orderItemCode";
    private static final String COUPON_CODE = "couponCode";

    @Before
    public void setUp() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), OrderInfoDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), CouponStatusChangedKafkaDto.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), OrderIndependentDO.class);
    }


    @Test
    public void triggerIndependentForCouponUsed_CouponAlreadyIndependent_ReturnsTrue() {
        when(orderInfoDOMapper.queryOrderDoByOrderCode(ORDER_CODE)).thenReturn(new OrderInfoDO()
                .setIndependentStatus(OrderIndependentStatusEnum.TODO.getStatus())
                .setBusinessCode(BusinessIdEnum.BRAND_GOODS.getCode())
        );
        when(orderCouponDetailDOMapper.getByCouponCode(any())).thenReturn(new OrderCouponDetailDO());
        when(orderItemDOMapper.getByOrderItemCode(any())).thenReturn(new OrderItemDO().setProductQuantity(3).setCostAmount(6));

        CreateIndependentRespDTO resp = new CreateIndependentRespDTO();
        resp.setTransApplyNo("transApplyNo1");
        when(independentApi.create(any(CreateIndependentReqDTO.class))).thenReturn(CommonResult.success(resp));

        boolean result = orderIndependentDOService.triggerIndependentForCouponUsed(
                new CouponStatusChangedKafkaDto().setOrderCode(ORDER_CODE).setCouponCode(COUPON_CODE));

        assertTrue(result);
    }

    @Test
    public void triggerIndependentForOrderSucc_SuccessfulIndependent() {
        TriggerIndependentReqDTO req = new TriggerIndependentReqDTO();
        req.setOrderCodes(new ArrayList<>());

        OrderInfoDO order = new OrderInfoDO();
        order.setOrderCode("123");
        order.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
        order.setIndependentStatus(OrderIndependentStatusEnum.TODO.getStatus());

        OrderItemDO orderItem = new OrderItemDO();
        orderItem.setOrderItemCode("123");
        orderItem.setMerchantAccountNo("123");
        orderItem.setCostAmount(1000);

        List<OrderItemDO> orderItems = new ArrayList<>();
        orderItems.add(orderItem);

        when(orderInfoDOMapper.selectList(any())).thenReturn(List.of(order)).thenReturn(List.of());
        when(orderItemDOMapper.mapByOrderCode(any())).thenReturn(Map.of("123", orderItems));
        when(orderRefundItemDOMapper.listRefundByOrderCode(any(), any())).thenReturn(new ArrayList<>());
        when(orderPaymentRecordsMapper.mapPayApplyNo(any())).thenReturn(Map.of("123", "pay123"));
        when(orderIndependentOrderItemDOMapper.mapByOrderItemCode(any())).thenReturn(new HashMap<>());

        CreateIndependentRespDTO resp = new CreateIndependentRespDTO();
        resp.setTransApplyNo("trans123");


        Boolean result = orderIndependentDOService.triggerIndependentForOrderSucc(req);

        assertTrue(result);
    }

    @Test
    public void triggerIndependentForOrderSucc_SuccessfulIndependent1() {
        TriggerIndependentReqDTO req = new TriggerIndependentReqDTO();
        req.setBusinessCode(BusinessIdEnum.BRAND_GOODS.getCode());
        req.setOrderCodes(new ArrayList<>());

        OrderInfoDO order = new OrderInfoDO();
        order.setOrderCode("123");
        order.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
        order.setIndependentStatus(OrderIndependentStatusEnum.TODO.getStatus());

        OrderItemDO orderItem = new OrderItemDO();
        orderItem.setOrderItemCode("123");
        orderItem.setMerchantAccountNo("123");
        orderItem.setCostAmount(1000);

        List<OrderItemDO> orderItems = new ArrayList<>();
        orderItems.add(orderItem);

        when(orderInfoDOMapper.selectList(any())).thenReturn(List.of(order)).thenReturn(List.of());
        when(orderItemDOMapper.mapByOrderCode(any())).thenReturn(Map.of("123", orderItems));
        when(orderRefundItemDOMapper.listRefundByOrderCode(any(), any())).thenReturn(new ArrayList<>());
        when(orderPaymentRecordsMapper.mapPayApplyNo(any())).thenReturn(Map.of("123", "pay123"));
        when(orderIndependentOrderItemDOMapper.mapByOrderItemCode(any())).thenReturn(new HashMap<>());

        CreateIndependentRespDTO resp = new CreateIndependentRespDTO();
        resp.setTransApplyNo("trans123");


        Boolean result = orderIndependentDOService.triggerIndependentForOrderSucc(req);

        assertTrue(result);
    }

    @Test
    public void triggerIndependentForRetry_WithIndependentsToRetry_ProcessesSuccessfully() {
        OrderIndependentDO independent = new OrderIndependentDO();
        independent.setOrderCode("orderCode1");
        independent.setIndependentCode("independentCode1");
        independent.setBusinessCode("LRE");
        independent.setIndependentType("ORDER_SUCCESS");
        independent.setPayApplyNo("payApplyNo1");
        independent.setTotalDivAmt(100);

        OrderInfoDO order = new OrderInfoDO();
        order.setBusinessCode("LRE");

        OrderIndependentItemDO independentItem = new OrderIndependentItemDO();
        independentItem.setMerchantAccountNo("accountNo1");
        independentItem.setDivAmt(50);

        List<OrderIndependentDO> independents = new ArrayList<>();
        independents.add(independent);

        when(baseMapper.listTodoIndependent(any(), any(), any())).thenReturn(independents);
        when(orderInfoDOMapper.queryOrderDoByOrderCode("orderCode1")).thenReturn(order);
        when(orderIndependentItemDOMapper.listByIndependentCode("independentCode1")).thenReturn(List.of(independentItem));

        CreateIndependentRespDTO resp = new CreateIndependentRespDTO();
        resp.setTransApplyNo("transApplyNo1");
        CommonResult<CreateIndependentRespDTO> result = CommonResult.success(resp);
        when(independentApi.create(any(CreateIndependentReqDTO.class))).thenReturn(result);

        TriggerIndependentReqDTO req = new TriggerIndependentReqDTO();
        req.setOrderCodes(List.of("orderCode1"));
        Boolean triggerResult = orderIndependentDOService.triggerIndependentForRetry(req);

        assertTrue(triggerResult);
    }

    @Test
    public void updateStatusToSucc_TypeCouponUsed_UpdatesOrderCouponDetail() {
        OrderIndependentDO independent = new OrderIndependentDO();
        independent.setIndependentType(OrderIndependentTypeEnum.COUPON_USED.getType());
        independent.setCouponCode(COUPON_CODE);
        independent.setIndependentType(OrderIndependentTypeEnum.ORDER_SUCCESS.getType());

        when(baseMapper.selectOne(any())).thenReturn(independent);

        UpdateStatusToSuccReqDTO req = new UpdateStatusToSuccReqDTO();
        req.setTransApplyNo("transApplyNo");
        req.setFinishTime(LocalDateTime.now());

        Boolean result = orderIndependentDOService.updateStatusToSucc(req);

        assertTrue(result);
    }
}

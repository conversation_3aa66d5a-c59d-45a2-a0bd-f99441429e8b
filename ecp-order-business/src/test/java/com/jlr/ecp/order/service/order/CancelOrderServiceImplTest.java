
package com.jlr.ecp.order.service.order;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.inventory.api.InventoryOrderApi;
import com.jlr.ecp.inventory.dto.qry.InventoryOrderUpdateDTO;
import com.jlr.ecp.order.api.order.vo.OrderCancelVO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.constant.KafkaConstants;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderPaymentRecordsDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderPaymentRecordsMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderStatusLogDOMapper;
import com.jlr.ecp.order.enums.order.OrderCloseReasonEnum;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import com.jlr.ecp.order.kafka.message.CancelOrderMessage;
import com.jlr.ecp.order.util.RedisReentrantLockUtil;
import com.jlr.ecp.payment.api.order.PayCenterOrderApi;
import com.jlr.ecp.payment.api.order.dto.CancelPayOrderReqDTO;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CancelOrderServiceImplTest {

    @Mock
    private OrderInfoDOMapper orderInfoDOMapper;

    @Mock
    private OrderPaymentRecordsMapper orderPaymentRecordsMapper;

    @Mock
    private OrderStatusLogDOMapper orderStatusLogDOMapper;

    @Mock
    private InventoryOrderApi inventoryOrderApi;

    @Mock
    private PayCenterOrderApi payCenterOrderApi;

    @Mock
    private TransactionTemplate transactionTemplate;

    @Mock
    private Redisson redisson;

    @Mock
    private RLock rLock;
    @Mock
    private RedisReentrantLockUtil redisReentrantLockUtil;

    @InjectMocks
    private CancelOrderServiceImpl cancelOrderService;

    @Mock
    private ProducerTool producerTool;
    @Mock
    private OrderPaymentRecordsMapper mockorderPaymentRecordsMapper;

    @Mock
    private OrderInfoDOService orderInfoDOService;

    @Before
    public void setUp() {
        TenantContextHolder.setTenantId(1L);
    }

    @Test
    public void orderCancel_VCSOrder_Success() throws InterruptedException {
        OrderInfoDO orderInfo = new OrderInfoDO();
        orderInfo.setOrderStatus(OrderStatusEnum.ORDERED.getCode());
        orderInfo.setBusinessCode(BusinessIdEnum.VCS.getCode());

        when(orderInfoDOMapper.queryOrderDoByOrderCode("testOrderCode")).thenReturn(orderInfo);
        when(redisson.getLock(any())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(RLock.class),anyInt(), anyInt(), any(TimeUnit.class))).thenReturn(true);
        OrderPaymentRecordsDO paymentRecord = new OrderPaymentRecordsDO();
        paymentRecord.setPayApplyNo("123");
        OrderCancelVO result = cancelOrderService.orderCancel("testOrderCode", OrderCloseReasonEnum.PROACTIVELY_CLOSE);

        assertNotNull(result);
        assertEquals(orderInfo.getCustomerRemark(), result.getCustomerRemark());
    }


    @Test
    public void orderCancel_NonVCSOrder_Success() throws InterruptedException {
        OrderInfoDO orderInfo = new OrderInfoDO();
        orderInfo.setOrderStatus(OrderStatusEnum.ORDERED.getCode());
        orderInfo.setBusinessCode(BusinessIdEnum.BRAND_GOODS.getCode());

        OrderInfoDO childOrder = new OrderInfoDO();
        childOrder.setOrderStatus(OrderStatusEnum.ORDERED.getCode());

        List<OrderInfoDO> relationOrders = new ArrayList<>();
        relationOrders.add(childOrder);

        when(orderPaymentRecordsMapper.queryLastByOrderCode(any())).thenReturn(new OrderPaymentRecordsDO());
        when(orderInfoDOMapper.queryOrderDoByOrderCode("testOrderCode")).thenReturn(orderInfo);
        when(orderInfoDOMapper.queryOrderDoByParentCode("testOrderCode")).thenReturn(relationOrders);
        when(redisson.getLock(any())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(RLock.class),anyInt(), anyInt(), any(TimeUnit.class))).thenReturn(true);
        when(payCenterOrderApi.cancelPayOrder(any())).thenReturn(CommonResult.success(true));

        OrderCancelVO result = cancelOrderService.orderCancel("testOrderCode", OrderCloseReasonEnum.PROACTIVELY_CLOSE);

        assertNotNull(result);
    }

    @Test
    public void testCancelPayOrderVCS_NoPaymentRecord() throws Exception {
        OrderInfoDO orderInfo = new OrderInfoDO();
        orderInfo.setOrderCode("testOrderCode");
        when(orderPaymentRecordsMapper.queryLastByOrderCode(any())).thenReturn(null);
        // 反射调用private方法
        java.lang.reflect.Method method = CancelOrderServiceImpl.class.getDeclaredMethod("cancelPayOrderVCS", OrderInfoDO.class, String.class, OrderCloseReasonEnum.class);
        method.setAccessible(true);
        method.invoke(cancelOrderService, orderInfo, "testOrderCode", OrderCloseReasonEnum.PROACTIVELY_CLOSE);
    }

    @Test
    public void testCancelPayOrderVCS_CancelResNotSuccess() throws Exception {
        OrderInfoDO orderInfo = new OrderInfoDO();
        orderInfo.setOrderCode("testOrderCode");
        OrderPaymentRecordsDO paymentRecord = new OrderPaymentRecordsDO();
        paymentRecord.setPayApplyNo("123");
        when(orderPaymentRecordsMapper.queryLastByOrderCode(any())).thenReturn(paymentRecord);
        // 用CommonResult.error构造失败返回
        com.jlr.ecp.framework.common.pojo.CommonResult<com.jlr.ecp.payment.api.order.dto.PcCancelPayOrderRespDTO> failRes = com.jlr.ecp.framework.common.pojo.CommonResult.error(1, "fail");
        when(payCenterOrderApi.cancelPayOrderVCS(any())).thenReturn(failRes);
        java.lang.reflect.Method method = CancelOrderServiceImpl.class.getDeclaredMethod("cancelPayOrderVCS", OrderInfoDO.class, String.class, OrderCloseReasonEnum.class);
        method.setAccessible(true);
        try {
            method.invoke(cancelOrderService, orderInfo, "testOrderCode", OrderCloseReasonEnum.PROACTIVELY_CLOSE);
            org.junit.Assert.fail("should throw exception");
        } catch (Exception e) {
            // 断言抛出ServiceException
            assertTrue(e.getCause() instanceof com.jlr.ecp.framework.common.exception.ServiceException);
        }
    }

    @Test
    public void testCancelPayOrderVCS_CancelStatusFail_PayStatusSuccess() throws Exception {
        OrderInfoDO orderInfo = new OrderInfoDO();
        orderInfo.setOrderCode("testOrderCode");
        OrderPaymentRecordsDO paymentRecord = new OrderPaymentRecordsDO();
        paymentRecord.setPayApplyNo("123");
        when(orderPaymentRecordsMapper.queryLastByOrderCode(any())).thenReturn(paymentRecord);
        com.jlr.ecp.payment.api.order.dto.PcCancelPayOrderRespDTO respDTO = new com.jlr.ecp.payment.api.order.dto.PcCancelPayOrderRespDTO();
        respDTO.setCancelStatus(com.jlr.ecp.payment.enums.order.CancelOrderStatusEnum.FAIL.getCode());
        respDTO.setPayStatus(com.jlr.ecp.payment.enums.order.CancelOrderStatusEnum.SUCCESS.getCode());
        respDTO.setPayTime(LocalDateTime.now().toString());
        com.jlr.ecp.framework.common.pojo.CommonResult<com.jlr.ecp.payment.api.order.dto.PcCancelPayOrderRespDTO> res = com.jlr.ecp.framework.common.pojo.CommonResult.success(respDTO);
        when(payCenterOrderApi.cancelPayOrderVCS(any())).thenReturn(res);
        when(orderInfoDOService.updateOrderStatusOnSuccess(anyString(), any())).thenReturn(1);
        java.lang.reflect.Method method = CancelOrderServiceImpl.class.getDeclaredMethod("cancelPayOrderVCS", OrderInfoDO.class, String.class, OrderCloseReasonEnum.class);
        method.setAccessible(true);
        try {
            method.invoke(cancelOrderService, orderInfo, "testOrderCode", OrderCloseReasonEnum.PROACTIVELY_CLOSE);
        } catch (Exception e) {
            // 断言抛出ServiceException
            assertTrue(e.getCause() instanceof com.jlr.ecp.framework.common.exception.ServiceException);
        }

    }

    @Test
    public void testCancelPayOrderVCS_CancelStatusFail_PayStatusClosed() throws Exception {
        OrderInfoDO orderInfo = new OrderInfoDO();
        orderInfo.setOrderCode("testOrderCode");
        OrderPaymentRecordsDO paymentRecord = new OrderPaymentRecordsDO();
        paymentRecord.setPayApplyNo("123");
        when(orderPaymentRecordsMapper.queryLastByOrderCode(any())).thenReturn(paymentRecord);
        com.jlr.ecp.payment.api.order.dto.PcCancelPayOrderRespDTO respDTO = new com.jlr.ecp.payment.api.order.dto.PcCancelPayOrderRespDTO();
        respDTO.setCancelStatus(com.jlr.ecp.payment.enums.order.CancelOrderStatusEnum.FAIL.getCode());
        respDTO.setPayStatus(com.jlr.ecp.payment.enums.order.PayOrderStatusEnum.CLOSED.getCode());
        com.jlr.ecp.framework.common.pojo.CommonResult<com.jlr.ecp.payment.api.order.dto.PcCancelPayOrderRespDTO> res = com.jlr.ecp.framework.common.pojo.CommonResult.success(respDTO);
        when(payCenterOrderApi.cancelPayOrderVCS(any())).thenReturn(res);
        java.lang.reflect.Method method = CancelOrderServiceImpl.class.getDeclaredMethod("cancelPayOrderVCS", OrderInfoDO.class, String.class, OrderCloseReasonEnum.class);
        method.setAccessible(true);
        method.invoke(cancelOrderService, orderInfo, "testOrderCode", OrderCloseReasonEnum.PROACTIVELY_CLOSE);
    }

    @Test
    public void testCancelPayOrderVCS_CancelStatusFail_PayStatusOther() throws Exception {
        OrderInfoDO orderInfo = new OrderInfoDO();
        orderInfo.setOrderCode("testOrderCode");
        OrderPaymentRecordsDO paymentRecord = new OrderPaymentRecordsDO();
        paymentRecord.setPayApplyNo("123");
        when(orderPaymentRecordsMapper.queryLastByOrderCode(any())).thenReturn(paymentRecord);
        com.jlr.ecp.payment.api.order.dto.PcCancelPayOrderRespDTO respDTO = new com.jlr.ecp.payment.api.order.dto.PcCancelPayOrderRespDTO();
        respDTO.setCancelStatus(com.jlr.ecp.payment.enums.order.CancelOrderStatusEnum.FAIL.getCode());
        respDTO.setPayStatus("OTHER_STATUS");
        com.jlr.ecp.framework.common.pojo.CommonResult<com.jlr.ecp.payment.api.order.dto.PcCancelPayOrderRespDTO> res = com.jlr.ecp.framework.common.pojo.CommonResult.success(respDTO);
        when(payCenterOrderApi.cancelPayOrderVCS(any())).thenReturn(res);
        java.lang.reflect.Method method = CancelOrderServiceImpl.class.getDeclaredMethod("cancelPayOrderVCS", OrderInfoDO.class, String.class, OrderCloseReasonEnum.class);
        method.setAccessible(true);
        try {
            method.invoke(cancelOrderService, orderInfo, "testOrderCode", OrderCloseReasonEnum.PROACTIVELY_CLOSE);
        }catch (Exception e){
            // 断言抛出ServiceException
            assertTrue(e.getCause() instanceof com.jlr.ecp.framework.common.exception.ServiceException);
        }
    }




}
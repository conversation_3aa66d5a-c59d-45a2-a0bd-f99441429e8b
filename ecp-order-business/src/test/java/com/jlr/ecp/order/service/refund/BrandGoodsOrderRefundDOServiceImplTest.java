package com.jlr.ecp.order.service.refund;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.order.api.refund.dto.*;
import com.jlr.ecp.order.controller.app.refund.vo.LogisticsRefundItemDetailVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemLogisticsDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderPaymentRecordsDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderPaymentRecordsMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.ErrorCodeConstants;
import com.jlr.ecp.order.enums.refund.RefundLogisticsStatusEnum;
import com.jlr.ecp.order.enums.refund.RefundOrderTypeEnum;
import com.jlr.ecp.order.handle.RefundHandler;
import com.jlr.ecp.order.kafka.BaseOrderRefundSuccessMessage;
import com.jlr.ecp.order.kafka.producer.BrandedGoodsNotificationProducer;
import com.jlr.ecp.order.service.order.OrderItemLogisticsDOService;
import com.jlr.ecp.order.service.refund.bg.BrandGoodsOrderRefundDOService;
import com.jlr.ecp.order.service.refund.bg.BrandGoodsOrderRefundDOServiceImpl;
import com.jlr.ecp.order.util.PIPLDataUtil;
import com.jlr.ecp.order.util.RedisReentrantLockUtil;
import com.jlr.ecp.order.util.machine.LogisticsOrderRefundStatusMachine;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class BrandGoodsOrderRefundDOServiceImplTest {

    private static final String ORDER_CODE = "OR2025040100001";
    private static final String REFUND_ORDER_CODE = "RF2025040100001";
    private static final String ORDER_ITEM_CODE = "OI2025040100001";
    private static final String CONTACT_NAME = "张三";
    private static final String CONTACT_PHONE = "13812345678";
    private static final String CONTACT_ADDRESS = "上海市浦东新区";
    private static final String LOGISTICS_CODE = "LG123456789";
    private static final String LOGISTICS_COMPANY_CODE = "SF";
    private static final String LOGISTICS_COMPANY_NAME = "顺丰速运";

    @Mock
    private RefundHandler mockRefundHandler;
    @Mock
    private OrderRefundItemDOMapper mockOrderRefundItemDOMapper;
    @Mock
    private LogisticsOrderRefundStatusMachine mockRefundStatusMachine;
    @Mock
    private OrderItemLogisticsDOService mockOrderItemLogisticsDOService;
    @Mock
    private PIPLDataUtil mockPiplDataUtil;
    @Mock
    private BrandedGoodsNotificationProducer mockNotificationProducer;
    @Mock
    private OrderPaymentRecordsMapper mockOrderPaymentRecordsMapper;
    @Mock
    private RedisReentrantLockUtil mockRedisReentrantLockUtil;
    @Mock
    private Redisson mockRedisson;
    @Mock
    private RLock mockRLock;
    @Mock
    private OrderRefundDOMapper mockOrderRefundDOMapper;

    @InjectMocks
    private BrandGoodsOrderRefundDOServiceImpl serviceUnderTest;

    @Before
    public void setUp() {
        when(mockRedisson.getLock(anyString())).thenReturn(mockRLock);
        when(mockRedisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any(TimeUnit.class))).thenReturn(true);
    }

    @Test
    public void testLogisticsOrderRefundApply_RefundOnly() {
        // Setup
        List<BaseOrderRefundApplyDTO> refundApplyDTOs = new ArrayList<>();
        BaseOrderRefundApplyDTO dto = new BaseOrderRefundApplyDTO();
        dto.setRefundOrderType(RefundOrderTypeEnum.REFUND_ONLY.getCode());
        refundApplyDTOs.add(dto);
        
        when(mockRefundHandler.refundProcess(any(), anyInt())).thenReturn(REFUND_ORDER_CODE);

        // Execute
        String result = serviceUnderTest.logisticsOrderRefundApply(refundApplyDTOs, 1);

        // Verify
        assertThat(result).isEqualTo(REFUND_ORDER_CODE);
        verify(mockRefundHandler).refundProcess(eq(refundApplyDTOs), eq(1));
    }

    @Test
    public void testLogisticsOrderRefundApply_RefundReturnGoods() {
        // Setup
        List<BaseOrderRefundApplyDTO> refundApplyDTOs = new ArrayList<>();
        BaseOrderRefundApplyDTO dto = new BaseOrderRefundApplyDTO();
        dto.setRefundOrderType(RefundOrderTypeEnum.REFUND_RETURN_GOODS.getCode());
        refundApplyDTOs.add(dto);
        
        when(mockRefundHandler.returnProcess(any(), anyInt())).thenReturn(REFUND_ORDER_CODE);

        // Execute
        String result = serviceUnderTest.logisticsOrderRefundApply(refundApplyDTOs, 1);

        // Verify
        assertThat(result).isEqualTo(REFUND_ORDER_CODE);
        verify(mockRefundHandler).returnProcess(eq(refundApplyDTOs), eq(1));
    }

    @Test
    public void testLogisticsOrderRefundAuditApprove_Success() {
        // Setup
        LogisticsOrderRefundApproveDTO approveDTO = new LogisticsOrderRefundApproveDTO();
        approveDTO.setRefundOrderCode(REFUND_ORDER_CODE);
        approveDTO.setOrderItemCode(ORDER_ITEM_CODE);
        approveDTO.setApproveStatus(true);
        approveDTO.setAuditReason("审核通过");
        approveDTO.setRefundAmount("100");

        OrderRefundDO orderRefundDO = new OrderRefundDO();
        orderRefundDO.setRefundOrderCode(REFUND_ORDER_CODE);
        orderRefundDO.setOriginOrderCode(ORDER_CODE);
        orderRefundDO.setLogisticsRefundStatus(RefundLogisticsStatusEnum.PENDING_REFUND_REVIEW.getCode());

        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setOrderCode(ORDER_CODE);

        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode(ORDER_ITEM_CODE);

        OrderRefundItemDO orderRefundItemDO = new OrderRefundItemDO();
        orderRefundItemDO.setId(1L);
        orderRefundItemDO.setRefundOrderCode(REFUND_ORDER_CODE);

        OrderPaymentRecordsDO orderPaymentRecordsDO = new OrderPaymentRecordsDO();

        // Configure mocks
        when(mockRefundHandler.getOrderRefundDO(REFUND_ORDER_CODE)).thenReturn(orderRefundDO);
        when(mockRefundHandler.getOrderInfoDO(ORDER_CODE)).thenReturn(orderInfoDO);
        when(mockRefundHandler.getOrderItemDo(ORDER_CODE, ORDER_ITEM_CODE)).thenReturn(orderItemDO);
        when(mockOrderRefundItemDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDO);
        when(mockRefundHandler.getLogisticsMaxRefundMoney(any(), anyLong())).thenReturn(10000);
        when(mockOrderPaymentRecordsMapper.queryByOrderCode(anyString(), anyString())).thenReturn(orderPaymentRecordsDO);

        // Execute
        String result = serviceUnderTest.logisticsOrderRefundAuditApprove(approveDTO);

        // Verify
        assertThat(result).isEqualTo(REFUND_ORDER_CODE);
        verify(mockRefundHandler).validInoviceStatus(ORDER_CODE);
        verify(mockOrderRefundItemDOMapper).updateById(any(OrderRefundItemDO.class));
        verify(mockRefundHandler).processExternalServices(any(RefundHandler.RefundProcessResult.class));
    }

    @Test
    public void testLogisticsOrderRefundAuditApprove_Reject() {
        // Setup
        LogisticsOrderRefundApproveDTO approveDTO = new LogisticsOrderRefundApproveDTO();
        approveDTO.setRefundOrderCode(REFUND_ORDER_CODE);
        approveDTO.setOrderItemCode(ORDER_ITEM_CODE);
        approveDTO.setApproveStatus(false);
        approveDTO.setAuditReason("审核拒绝");

        OrderRefundDO orderRefundDO = new OrderRefundDO();
        orderRefundDO.setRefundOrderCode(REFUND_ORDER_CODE);
        orderRefundDO.setOriginOrderCode(ORDER_CODE);
        orderRefundDO.setLogisticsRefundStatus(RefundLogisticsStatusEnum.PENDING_REFUND_REVIEW.getCode());

        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setOrderCode(ORDER_CODE);

        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode(ORDER_ITEM_CODE);

        // Configure mocks
        when(mockRefundHandler.getOrderRefundDO(REFUND_ORDER_CODE)).thenReturn(orderRefundDO);
        when(mockRefundHandler.getOrderInfoDO(ORDER_CODE)).thenReturn(orderInfoDO);
        when(mockRefundHandler.getOrderItemDo(ORDER_CODE, ORDER_ITEM_CODE)).thenReturn(orderItemDO);

        // Execute
        String result = serviceUnderTest.logisticsOrderRefundAuditApprove(approveDTO);

        // Verify
        assertThat(result).isEqualTo(REFUND_ORDER_CODE);
        verify(mockRefundStatusMachine).changeOrderStatus(anyInt(), eq(orderInfoDO), eq(orderRefundDO), eq(orderItemDO));
    }

    @Test
    public void testLogisticsOrderRefundAuditApprove_InvalidStatus() {
        // Setup
        LogisticsOrderRefundApproveDTO approveDTO = new LogisticsOrderRefundApproveDTO();
        approveDTO.setRefundOrderCode(REFUND_ORDER_CODE);
        approveDTO.setOrderItemCode(ORDER_ITEM_CODE);
        approveDTO.setApproveStatus(true);

        OrderRefundDO orderRefundDO = new OrderRefundDO();
        orderRefundDO.setRefundOrderCode(REFUND_ORDER_CODE);
        orderRefundDO.setOriginOrderCode(ORDER_CODE);
        orderRefundDO.setLogisticsRefundStatus(RefundLogisticsStatusEnum.REFUND_COMPLETED.getCode());

        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setOrderCode(ORDER_CODE);

        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode(ORDER_ITEM_CODE);

        // Configure mocks
        when(mockRefundHandler.getOrderRefundDO(REFUND_ORDER_CODE)).thenReturn(orderRefundDO);
        when(mockRefundHandler.getOrderInfoDO(ORDER_CODE)).thenReturn(orderInfoDO);
        when(mockRefundHandler.getOrderItemDo(ORDER_CODE, ORDER_ITEM_CODE)).thenReturn(orderItemDO);

        // Execute & Verify
        assertThatThrownBy(() -> serviceUnderTest.logisticsOrderRefundAuditApprove(approveDTO))
                .isInstanceOf(Exception.class)
                .hasMessageContaining("退款单状态不为带退款审核");
    }

    @Test
    public void testLogisticsOrderReturnAuditApprove_Approve() {
        // Setup
        LogisticsOrderReturnApproveDTO approveDTO = new LogisticsOrderReturnApproveDTO();
        approveDTO.setRefundOrderCode(REFUND_ORDER_CODE);
        approveDTO.setOrderItemCode(ORDER_ITEM_CODE);
        approveDTO.setApproveStatus(true);
        approveDTO.setAuditReason("审核通过");

        OrderRefundDO orderRefundDO = new OrderRefundDO();
        orderRefundDO.setRefundOrderCode(REFUND_ORDER_CODE);
        orderRefundDO.setOriginOrderCode(ORDER_CODE);
        orderRefundDO.setLogisticsRefundStatus(RefundLogisticsStatusEnum.PENDING_RETURN_REVIEW.getCode());

        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setOrderCode(ORDER_CODE);

        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode(ORDER_ITEM_CODE);

        BaseOrderRefundSuccessMessage message = new BaseOrderRefundSuccessMessage();
        message.setBgorderNumber(ORDER_CODE);
        message.setPhoneNumber(CONTACT_PHONE);
        message.setWxUrl("https://wx.example.com");

        // Configure mocks
        when(mockRefundHandler.getOrderRefundDO(REFUND_ORDER_CODE)).thenReturn(orderRefundDO);
        when(mockRefundHandler.getOrderInfoDO(ORDER_CODE)).thenReturn(orderInfoDO);
        when(mockRefundHandler.getOrderItemDo(ORDER_CODE, ORDER_ITEM_CODE)).thenReturn(orderItemDO);
        when(mockRefundHandler.buildBaseOrderRefundSuccessMessage(any(), any(), any())).thenReturn(message);

        // Execute
        String result = serviceUnderTest.logisticsOrderReturnAuditApprove(approveDTO);

        // Verify
        assertThat(result).isEqualTo(REFUND_ORDER_CODE);
        verify(mockRefundHandler).validInoviceStatus(ORDER_CODE);
        verify(mockNotificationProducer).sendReturnRefundNotification(eq(ORDER_CODE), eq(CONTACT_PHONE), anyString());
        verify(mockRefundStatusMachine).changeOrderStatus(anyInt(), eq(orderInfoDO), eq(orderRefundDO), eq(orderItemDO));
    }

    @Test
    public void testLogisticsOrderReturnAuditApprove_Reject() {
        // Setup
        LogisticsOrderReturnApproveDTO approveDTO = new LogisticsOrderReturnApproveDTO();
        approveDTO.setRefundOrderCode(REFUND_ORDER_CODE);
        approveDTO.setOrderItemCode(ORDER_ITEM_CODE);
        approveDTO.setApproveStatus(false);
        approveDTO.setAuditReason("审核拒绝");

        OrderRefundDO orderRefundDO = new OrderRefundDO();
        orderRefundDO.setRefundOrderCode(REFUND_ORDER_CODE);
        orderRefundDO.setOriginOrderCode(ORDER_CODE);
        orderRefundDO.setLogisticsRefundStatus(RefundLogisticsStatusEnum.PENDING_RETURN_REVIEW.getCode());

        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setOrderCode(ORDER_CODE);

        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode(ORDER_ITEM_CODE);

        // Configure mocks
        when(mockRefundHandler.getOrderRefundDO(REFUND_ORDER_CODE)).thenReturn(orderRefundDO);
        when(mockRefundHandler.getOrderInfoDO(ORDER_CODE)).thenReturn(orderInfoDO);
        when(mockRefundHandler.getOrderItemDo(ORDER_CODE, ORDER_ITEM_CODE)).thenReturn(orderItemDO);

        // Execute
        String result = serviceUnderTest.logisticsOrderReturnAuditApprove(approveDTO);

        // Verify
        assertThat(result).isEqualTo(REFUND_ORDER_CODE);
        verify(mockRefundStatusMachine).changeOrderStatus(anyInt(), eq(orderInfoDO), eq(orderRefundDO), eq(orderItemDO));
    }

    @Test
    public void testLogisticsOrderRefundUserCancel() {
        // Setup
        LogisticsOrderRefundApproveDTO approveDTO = new LogisticsOrderRefundApproveDTO();
        approveDTO.setRefundOrderCode(REFUND_ORDER_CODE);
        approveDTO.setOrderItemCode(ORDER_ITEM_CODE);

        OrderRefundDO orderRefundDO = new OrderRefundDO();
        orderRefundDO.setRefundOrderCode(REFUND_ORDER_CODE);
        orderRefundDO.setOriginOrderCode(ORDER_CODE);
        orderRefundDO.setLogisticsRefundStatus(RefundLogisticsStatusEnum.PENDING_ITEM_RETURN.getCode());

        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setOrderCode(ORDER_CODE);

        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode(ORDER_ITEM_CODE);

        // Configure mocks
        when(mockRefundHandler.getOrderRefundDO(REFUND_ORDER_CODE)).thenReturn(orderRefundDO);
        when(mockRefundHandler.getOrderInfoDO(ORDER_CODE)).thenReturn(orderInfoDO);
        when(mockRefundHandler.getOrderItemDo(ORDER_CODE, ORDER_ITEM_CODE)).thenReturn(orderItemDO);

        // Execute
        String result = serviceUnderTest.logisticsOrderRefundUserCancel(approveDTO);

        // Verify
        assertThat(result).isEqualTo(REFUND_ORDER_CODE);
        verify(mockRefundStatusMachine).changeOrderStatus(anyInt(), eq(orderInfoDO), eq(orderRefundDO), eq(orderItemDO));
    }

    @Test
    public void testSubmitLogisticsInfo() {
        // Setup
        OrderRefundLogisticsDTO logisticsDTO = new OrderRefundLogisticsDTO();
        logisticsDTO.setRefundOrderCode(REFUND_ORDER_CODE);
        logisticsDTO.setRefundOrderItemCode(ORDER_ITEM_CODE);
        logisticsDTO.setLogisticsCode(LOGISTICS_CODE);
        logisticsDTO.setLogisticsCompanyCode(LOGISTICS_COMPANY_CODE);
        logisticsDTO.setLogisticsCompanyName(LOGISTICS_COMPANY_NAME);
        logisticsDTO.setAttachmentUrls(Arrays.asList("url1", "url2"));

        OrderRefundDO orderRefundDO = new OrderRefundDO();
        orderRefundDO.setRefundOrderCode(REFUND_ORDER_CODE);
        orderRefundDO.setOriginOrderCode(ORDER_CODE);

        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setOrderCode(ORDER_CODE);

        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode(ORDER_ITEM_CODE);

        // Configure mocks
        when(mockRefundHandler.getOrderRefundDO(REFUND_ORDER_CODE)).thenReturn(orderRefundDO);
        when(mockRefundHandler.getOrderInfoDO(ORDER_CODE)).thenReturn(orderInfoDO);
        when(mockRefundHandler.getOrderItemDo(ORDER_CODE, ORDER_ITEM_CODE)).thenReturn(orderItemDO);

        // Execute
        String result = serviceUnderTest.submitLogisticsInfo(logisticsDTO);

        // Verify
        assertThat(result).isEqualTo(REFUND_ORDER_CODE);
        assertThat(orderRefundDO.getLogisticsCode()).isEqualTo(LOGISTICS_CODE);
        assertThat(orderRefundDO.getLogisticsCompanyCode()).isEqualTo(LOGISTICS_COMPANY_CODE);
        assertThat(orderRefundDO.getLogisticsCompanyName()).isEqualTo(LOGISTICS_COMPANY_NAME);
        assertThat(orderRefundDO.getLogisticsAttachment()).isEqualTo("url1,url2");
        verify(mockRefundStatusMachine).changeOrderStatus(anyInt(), eq(orderInfoDO), eq(orderRefundDO), eq(orderItemDO));
    }

    @Test
    public void testGetLogisticsRefundItemDetail() {
        // Setup
        OrderRefundDO orderRefundDO = new OrderRefundDO();
        orderRefundDO.setRefundOrderCode(REFUND_ORDER_CODE);
        orderRefundDO.setOriginOrderCode(ORDER_CODE);
        orderRefundDO.setRefundAuditRemark("退款审核备注");
        orderRefundDO.setReturnAuditRemark("退货审核备注");
        orderRefundDO.setLogisticsCode(LOGISTICS_CODE);
        orderRefundDO.setLogisticsCompanyCode(LOGISTICS_COMPANY_CODE);
        orderRefundDO.setLogisticsCompanyName(LOGISTICS_COMPANY_NAME);
        orderRefundDO.setLogisticsAttachment("url1,url2");

        OrderInfoDO orderInfoDO = new OrderInfoDO();
        orderInfoDO.setOrderCode(ORDER_CODE);

        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode(ORDER_ITEM_CODE);

        OrderRefundItemDO orderRefundItemDO = new OrderRefundItemDO();
        orderRefundItemDO.setRefundOrderCode(REFUND_ORDER_CODE);

        OrderItemLogisticsDO orderItemLogisticsDO = new OrderItemLogisticsDO();
        orderItemLogisticsDO.setOrderItemCode(ORDER_ITEM_CODE);
        orderItemLogisticsDO.setRecipient("encrypted_name");
        orderItemLogisticsDO.setRecipientPhone("encrypted_phone");
        orderItemLogisticsDO.setFullDetailAddress("encrypted_address");
        orderItemLogisticsDO.setSendTime(LocalDateTime.now());

        // Configure mocks
        when(mockRefundHandler.getOrderRefundDO(REFUND_ORDER_CODE)).thenReturn(orderRefundDO);
        when(mockRefundHandler.getOrderInfoDO(ORDER_CODE)).thenReturn(orderInfoDO);
        when(mockRefundHandler.getOrderItemDo(ORDER_CODE, ORDER_ITEM_CODE)).thenReturn(orderItemDO);
        when(mockOrderRefundItemDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDO);
        when(mockOrderItemLogisticsDOService.getOne(any(LambdaQueryWrapper.class))).thenReturn(orderItemLogisticsDO);
        when(mockPiplDataUtil.getDecodeText("encrypted_name")).thenReturn(CONTACT_NAME);
        when(mockPiplDataUtil.getDecodeText("encrypted_phone")).thenReturn(CONTACT_PHONE);
        when(mockPiplDataUtil.getDecodeText("encrypted_address")).thenReturn(CONTACT_ADDRESS);

        // Execute
        LogisticsRefundItemDetailVO result = serviceUnderTest.getLogisticsRefundItemDetail(ORDER_ITEM_CODE, REFUND_ORDER_CODE);

        // Verify
        assertThat(result).isNotNull();
        assertThat(result.getRefundAuditRemark()).isEqualTo("退款审核备注");
        assertThat(result.getReturnAuditRemark()).isEqualTo("退货审核备注");
        assertThat(result.getLogisticsCode()).isEqualTo(LOGISTICS_CODE);
        assertThat(result.getLogisticsCompanyCode()).isEqualTo(LOGISTICS_COMPANY_CODE);
        assertThat(result.getLogisticsCompanyName()).isEqualTo(LOGISTICS_COMPANY_NAME);
        assertThat(result.getAttachmentUrls()).containsExactly("url1", "url2");
        assertThat(result.getContactName()).isEqualTo(CONTACT_NAME);
        assertThat(result.getContactPhone()).isEqualTo(CONTACT_PHONE);
        assertThat(result.getContactAddress()).isEqualTo(CONTACT_ADDRESS);
        assertThat(result.getReceiverName()).isEqualTo("捷豹路虎中国小程序电商");
        assertThat(result.getReceiverPhone()).isEqualTo("18117116220");
        assertThat(result.getReceiverAddress()).isEqualTo("上海市上海城区宝山区真陈路1490号内4幢电梯5楼");
        verify(mockRefundHandler).buildRefundItemDetailVO(any(), eq(orderRefundDO), eq(orderItemDO), eq(orderRefundItemDO));
    }
} 
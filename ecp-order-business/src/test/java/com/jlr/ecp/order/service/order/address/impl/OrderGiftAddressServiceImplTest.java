package com.jlr.ecp.order.service.order.address.impl;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.order.dto.address.OrderGiftAddressDTO;
import com.jlr.ecp.order.api.order.vo.address.AppOrderGiftAddressDetailVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderGiftAddressDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.mysql.order.OrderGiftAddressMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.system.api.address.AddressConfigApi;
import com.jlr.ecp.system.api.address.dto.AddressNameDTO;
import com.jlr.ecp.system.enums.AddressRedisKeyConstants;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrderGiftAddressServiceImplTest {

    @Mock
    private OrderGiftAddressMapper mockOrderGiftAddressMapper;
    @Mock
    private RedisTemplate<String, String> mockRedisTemplate;
    @Mock
    private AddressConfigApi mockAddressConfigApi;
    @Mock
    private OrderInfoDOMapper mockOrderInfoDOMapper;

    @InjectMocks
    private OrderGiftAddressServiceImpl orderGiftAddressServiceImplUnderTest;


    @Before
    public void setUp() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderInfoDO.class);
    }

    @Test
    public void testGetOrderGiftAddressByOrderCode() {
        // Setup
        final AppOrderGiftAddressDetailVO expectedResult = new AppOrderGiftAddressDetailVO();
        expectedResult.setAdCode("adCode");
        expectedResult.setProvinceCode("provinceCode");
        expectedResult.setCityCode("cityCode");
        expectedResult.setAreaCode("adCode");

        // Configure OrderGiftAddressMapper.selectOne(...).
        final OrderGiftAddressDO orderGiftAddressDO = new OrderGiftAddressDO();
        orderGiftAddressDO.setIsDeleted(false);
        orderGiftAddressDO.setId(0L);
        orderGiftAddressDO.setOrderCode("orderCode");
        orderGiftAddressDO.setProvinceCode("provinceCode");
        orderGiftAddressDO.setCityCode("cityCode");
        orderGiftAddressDO.setAreaCode("adCode");
        when(mockOrderGiftAddressMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderGiftAddressDO);

        when(mockRedisTemplate.hasKey(AddressRedisKeyConstants.PROVINCE_DATA)).thenReturn(true);
        HashOperations<String, Object, Object> hashOperations = mock(HashOperations.class);
        when(mockRedisTemplate.opsForHash()).thenReturn(hashOperations);

        // Configure AddressConfigApi.getNameByCode(...).
        final AddressNameDTO nameDTO = new AddressNameDTO();
        nameDTO.setProvince("province");
        nameDTO.setCity("city");
        nameDTO.setArea("area");
        final CommonResult<AddressNameDTO> commonResult = CommonResult.success(nameDTO);

        // Run the test
        final AppOrderGiftAddressDetailVO result = orderGiftAddressServiceImplUnderTest.getOrderGiftAddressByOrderCode(
                "orderCode");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetOrderGiftAddressByOrderCode_RedisTemplateHasKeyReturnsNull() {
        // Setup
        final AppOrderGiftAddressDetailVO expectedResult = new AppOrderGiftAddressDetailVO();
        expectedResult.setAdCode("adCode");
        expectedResult.setProvince("province");
        expectedResult.setProvinceCode("provinceCode");
        expectedResult.setCity("city");
        expectedResult.setCityCode("cityCode");
        expectedResult.setArea("area");
        expectedResult.setAreaCode("adCode");

        // Configure OrderGiftAddressMapper.selectOne(...).
        final OrderGiftAddressDO orderGiftAddressDO = new OrderGiftAddressDO();
        orderGiftAddressDO.setIsDeleted(false);
        orderGiftAddressDO.setId(0L);
        orderGiftAddressDO.setOrderCode("orderCode");
        orderGiftAddressDO.setProvinceCode("provinceCode");
        orderGiftAddressDO.setCityCode("cityCode");
        orderGiftAddressDO.setAreaCode("adCode");
        when(mockOrderGiftAddressMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderGiftAddressDO);

        when(mockRedisTemplate.hasKey(AddressRedisKeyConstants.PROVINCE_DATA)).thenReturn(null);

        // Configure AddressConfigApi.getNameByCode(...).
        final AddressNameDTO nameDTO = new AddressNameDTO();
        nameDTO.setProvince("province");
        nameDTO.setCity("city");
        nameDTO.setArea("area");
        final CommonResult<AddressNameDTO> commonResult = CommonResult.success(nameDTO);
        when(mockAddressConfigApi.getNameByCode("provinceCode", "cityCode", "adCode")).thenReturn(commonResult);

        // Run the test
        final AppOrderGiftAddressDetailVO result = orderGiftAddressServiceImplUnderTest.getOrderGiftAddressByOrderCode(
                "orderCode");

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSaveOrderGiftAddress() {
        // Setup
        final OrderGiftAddressDTO dto = new OrderGiftAddressDTO();
        dto.setNeedGift(1);
        dto.setAdCode("adCode");
        dto.setProvinceCode("provinceCode");
        dto.setCityCode("cityCode");
        dto.setAreaCode("areaCode");

        // Configure OrderGiftAddressMapper.selectList(...).
        final OrderGiftAddressDO orderGiftAddressDO = new OrderGiftAddressDO();
        orderGiftAddressDO.setIsDeleted(false);
        orderGiftAddressDO.setId(0L);
        orderGiftAddressDO.setOrderCode("orderCode");
        orderGiftAddressDO.setProvinceCode("provinceCode");
        orderGiftAddressDO.setCityCode("cityCode");
        orderGiftAddressDO.setAreaCode("adCode");
        final List<OrderGiftAddressDO> orderGiftAddressDOS = List.of(orderGiftAddressDO);
        when(mockOrderGiftAddressMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderGiftAddressDOS);

        // Run the test
        orderGiftAddressServiceImplUnderTest.saveOrderGiftAddress(dto, List.of("value"));

        // Verify the results
        // Confirm OrderGiftAddressMapper.insertBatch(...).
        verify(mockOrderGiftAddressMapper).insertBatch(anyList());
    }

    @Test
    public void testSaveOrderGiftAddressForValet() {
        // Setup
        final OrderGiftAddressDTO dto = new OrderGiftAddressDTO();
        dto.setNeedGift(1);
        dto.setAdCode("adCode");
        dto.setProvinceCode("provinceCode");
        dto.setCityCode("cityCode");
        dto.setAreaCode("areaCode");

        // Configure OrderGiftAddressMapper.selectList(...).
        final OrderGiftAddressDO orderGiftAddressDO = new OrderGiftAddressDO();
        orderGiftAddressDO.setIsDeleted(false);
        orderGiftAddressDO.setId(0L);
        orderGiftAddressDO.setOrderCode("orderCode");
        orderGiftAddressDO.setProvinceCode("provinceCode");
        orderGiftAddressDO.setCityCode("cityCode");
        orderGiftAddressDO.setAreaCode("adCode");
        final List<OrderGiftAddressDO> orderGiftAddressDOS = List.of(orderGiftAddressDO);
        when(mockOrderGiftAddressMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderGiftAddressDOS);

        // Run the test
        orderGiftAddressServiceImplUnderTest.saveOrderGiftAddressForValet(dto, List.of("value"));

        // Verify the results

        // Confirm OrderGiftAddressMapper.insertBatch(...).
        verify(mockOrderGiftAddressMapper).insertBatch(anyList());
    }
}

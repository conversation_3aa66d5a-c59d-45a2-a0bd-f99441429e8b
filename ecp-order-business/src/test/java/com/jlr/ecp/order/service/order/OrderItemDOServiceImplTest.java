package com.jlr.ecp.order.service.order;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.order.dto.OrderCarVinDTO;
import com.jlr.ecp.order.api.order.dto.OrderItemDTO;
import com.jlr.ecp.order.api.order.vo.base.OrderItemBaseVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.enums.phone.PhoneNumberDecodeUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static com.jlr.ecp.order.service.Constant.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderItemDOServiceImplTest {

    @Mock
    private OrderItemDOMapper mockOrderItemDOMapper;
    @Mock
    private PhoneNumberDecodeUtil mockPhoneNumberDecodeUtil;

    @InjectMocks
    private OrderItemDOServiceImpl orderItemDOServiceImplUnderTest;

    @Test
    public void testGetOrderItemInfo() {
        // Setup
        final OrderItemBaseVO orderItemBaseVO = new OrderItemBaseVO();
        orderItemBaseVO.setOrderItemCode(ORDER_ITEM_CODE);
        orderItemBaseVO.setProductCode(PRODUCT_CODE);
        orderItemBaseVO.setProductAttribute("");
        orderItemBaseVO.setProductMarketPrice("0.00");
        orderItemBaseVO.setProductSalePrice("0.00");
        final List<OrderItemBaseVO> expectedResult = List.of(orderItemBaseVO);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(ORDER_CODE)
                .productCode(PRODUCT_CODE)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .build());
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(list);

        // Run the test
        final List<OrderItemBaseVO> result = orderItemDOServiceImplUnderTest.getOrderItemInfo(List.of(VALUE));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOrderItemInfoOrderItemDOMapperReturnsNoItems() {
        // Setup
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderItemBaseVO> result = orderItemDOServiceImplUnderTest.getOrderItemInfo(List.of(VALUE));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testAddProductCode() {
        // Setup
        final OrderCarVinDTO vinDTO = new OrderCarVinDTO();
        vinDTO.setTenantId(0);
        vinDTO.setOrderCode(ORDER_CODE);
        vinDTO.setWxPhone(WX_PHONE);
        vinDTO.setProductCode(PRODUCT_CODE);
        final List<OrderCarVinDTO> orderCarVinDTOList = List.of(vinDTO);
        final OrderCarVinDTO vinDTO1 = new OrderCarVinDTO();
        vinDTO1.setTenantId(0);
        vinDTO1.setOrderCode(ORDER_CODE);
        vinDTO1.setWxPhone(WX_PHONE);
        vinDTO1.setProductCode(PRODUCT_CODE);
        final List<OrderCarVinDTO> expectedResult = List.of(vinDTO1);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(ORDER_CODE)
                .productCode(PRODUCT_CODE)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .build());
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(list);

        when(mockPhoneNumberDecodeUtil.getDecodePhone(WX_PHONE)).thenReturn(WX_PHONE);

        // Run the test
        final List<OrderCarVinDTO> result = orderItemDOServiceImplUnderTest.addProductCode(orderCarVinDTOList);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testAddProductCodeOrderItemDOMapperReturnsNoItems() {
        // Setup
        final OrderCarVinDTO vinDTO = new OrderCarVinDTO();
        vinDTO.setTenantId(0);
        vinDTO.setOrderCode(ORDER_CODE);
        vinDTO.setWxPhone(WX_PHONE);
        vinDTO.setContactPhone(CONTACT_PHONE);
        vinDTO.setProductCode(PRODUCT_CODE);
        final List<OrderCarVinDTO> orderCarVinDTOList = List.of(vinDTO);
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderCarVinDTO> result = orderItemDOServiceImplUnderTest.addProductCode(orderCarVinDTOList);

        // Verify the results
        assertThat(result).isEqualTo(orderCarVinDTOList);
    }

    @Test
    public void testQueryOrderItemByOrderCode() {
        // Setup
        final OrderItemDTO orderItemDTO = new OrderItemDTO();
        orderItemDTO.setOrderItemCode(ORDER_ITEM_CODE);
        orderItemDTO.setOrderCode(ORDER_CODE);
        orderItemDTO.setProductCode(PRODUCT_CODE);
        orderItemDTO.setProductAttribute("");
        orderItemDTO.setProductMarketPrice(0);
        orderItemDTO.setProductSalePrice(0);
        final List<OrderItemDTO> expectedResult = List.of(orderItemDTO);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(ORDER_CODE)
                .productCode(PRODUCT_CODE)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .build());
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(list);

        // Run the test
        final List<OrderItemDTO> result = orderItemDOServiceImplUnderTest.queryOrderItemByOrderCode(ORDER_CODE);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryOrderItemByOrderCodeOrderItemDOMapperReturnsNoItems() {
        // Setup
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderItemDTO> result = orderItemDOServiceImplUnderTest.queryOrderItemByOrderCode(ORDER_CODE);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}

package com.jlr.ecp.order.service.internal.price.impl;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.order.api.cart.dto.CalculateAmountDTO;
import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.ProductSpuSkuDTO;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.controller.app.cart.vo.CalculateAmountVO;
import com.jlr.ecp.order.dal.dataobject.cart.ShoppingCarItemDO;
import com.jlr.ecp.order.enums.cart.CarPaymentTypeEnum;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.service.internal.price.ShopCalculateAmountService;
import com.jlr.ecp.order.service.internal.promotion.dto.PromotionRespDto;
import com.jlr.ecp.product.api.couponrule.ProductCouponUsageRuleApi;
import com.jlr.ecp.product.api.couponrule.dto.ListCanUsedCouponModelRespDTO;
import com.jlr.ecp.product.api.product.vo.ProductAttributeRespVO;
import com.jlr.ecp.product.api.product.vo.ProductDetailsVO;
import com.jlr.ecp.product.api.product.vo.ProductSkuRespVO;
import com.jlr.ecp.product.api.sku.ProductSkuApi;
import com.jlr.ecp.virtual.api.coupon.CouponApi;
import com.jlr.ecp.virtual.api.point.PointApi;
import com.jlr.ecp.virtual.dto.coupon.response.AvailableCouponResp;
import com.jlr.ecp.virtual.dto.coupon.response.AvailablePointResp;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CalculateServiceImplTest {

    @InjectMocks
    private CalculateServiceImpl calculateService;

    @Mock
    private ProductSkuApi productSkuApi;

    @Mock
    private ShopCalculateAmountService shopCalculateAmountService;

    @Mock
    private ProductCouponUsageRuleApi productCouponUsageRuleApi;

    @Mock
    private PointApi pointApi;

    @Mock
    private CouponApi couponApi;

    private final String CONSUMER_CODE = "test-consumer-code";

    @Before
    public void setUp() {
        // 设置属性值
        ReflectionTestUtils.setField(calculateService, "couponFlag", true);
        ReflectionTestUtils.setField(calculateService, "useMockCoupons", false);
        
        // 使用anyList()和anyBoolean()设置通用的模拟返回值
        when(shopCalculateAmountService.calculateCartItemAmount(anyList(), anyBoolean()))
                .thenReturn(new BigDecimal("10000")); // 默认返回值
    }

    @Test
    public void testCalculateAmount_NoDiscountCase() {
        // 准备测试数据
        CalculateAmountDTO dto = createCalculateAmountDTO(true);
        List<ShoppingCarItemDO> cartItems = createShoppingCartItems();

        // Mock ShopCalculateAmountService
        when(shopCalculateAmountService.checkCartItem(any(), eq(CONSUMER_CODE)))
                .thenReturn(cartItems);

        // Mock ProductSkuApi
        List<ProductSkuRespVO> skuList = createProductSkuRespList();
        CommonResult<List<ProductSkuRespVO>> skuResult = CommonResult.success(skuList);
        when(productSkuApi.getSkuList(any())).thenReturn(skuResult);

        // Mock ProductCouponUsageRuleApi
        List<ListCanUsedCouponModelRespDTO> couponRelList = createCouponRelList();
        CommonResult<List<ListCanUsedCouponModelRespDTO>> couponRelResult = CommonResult.success(couponRelList);
        when(productCouponUsageRuleApi.listCanUsedCouponModel(any())).thenReturn(couponRelResult);

        // Mock PointApi
        AvailablePointResp pointResp = new AvailablePointResp();
        pointResp.setPoint(1000);
        CommonResult<AvailablePointResp> pointResult = CommonResult.success(pointResp);
        when(pointApi.obtainAvailablePoint(eq(CONSUMER_CODE))).thenReturn(pointResult);

        // Mock CouponApi - 需要在这里添加模拟返回值，以免产生空指针异常
        PageResult<AvailableCouponResp> couponPage = new PageResult<>();
        couponPage.setList(new ArrayList<>());
        couponPage.setTotal(0L);
        CommonResult<PageResult<AvailableCouponResp>> couponResult = CommonResult.success(couponPage);
        when(couponApi.obtainAvailableCouponList(any(), eq(CONSUMER_CODE))).thenReturn(couponResult);

        // Mock ShopCalculateAmountService计算金额
        when(shopCalculateAmountService.calculateCartItemAmount(anyList(), eq(true)))
                .thenReturn(new BigDecimal("10000")); // 100元转为分

        // 执行测试
        CalculateAmountVO result = calculateService.calculateAmount(dto, CONSUMER_CODE);

        // 验证结果
        assertNotNull(result);
        assertEquals("100.00", result.getTotalAmount());
        assertEquals("100.00", result.getCostAmount());
        assertEquals("0", result.getDiscountTotalAmount());
        assertFalse(result.isUseCouponCode());
        assertNull(result.getCostPoints());
        assertNotNull(result.getItemList());
        assertFalse(result.getItemList().isEmpty());
    }

    @Test
    public void testCalculateAmount_WithCouponCase() {
        // 设置couponFlag为true
        ReflectionTestUtils.setField(calculateService, "couponFlag", true);
        
        // 准备测试数据
        CalculateAmountDTO dto = createCalculateAmountDTOWithCoupon(false, "coupon-1", 2);
        List<ShoppingCarItemDO> cartItems = createShoppingCartItems();

        // Mock ShopCalculateAmountService
        when(shopCalculateAmountService.checkCartItem(any(), eq(CONSUMER_CODE)))
                .thenReturn(cartItems);

        // Mock ProductSkuApi
        List<ProductSkuRespVO> skuList = createProductSkuRespList();
        CommonResult<List<ProductSkuRespVO>> skuResult = CommonResult.success(skuList);
        when(productSkuApi.getSkuList(any())).thenReturn(skuResult);

        // Mock ProductCouponUsageRuleApi
        List<ListCanUsedCouponModelRespDTO> couponRelList = createCouponRelList();
        CommonResult<List<ListCanUsedCouponModelRespDTO>> couponRelResult = CommonResult.success(couponRelList);
        when(productCouponUsageRuleApi.listCanUsedCouponModel(any())).thenReturn(couponRelResult);

        // Mock PointApi
        AvailablePointResp pointResp = new AvailablePointResp();
        pointResp.setPoint(1000);
        CommonResult<AvailablePointResp> pointResult = CommonResult.success(pointResp);
        when(pointApi.obtainAvailablePoint(eq(CONSUMER_CODE))).thenReturn(pointResult);

        // Mock CouponApi
        PageResult<AvailableCouponResp> couponPage = createAvailableCouponPage();
        CommonResult<PageResult<AvailableCouponResp>> couponResult = CommonResult.success(couponPage);
        when(couponApi.obtainAvailableCouponList(any(), eq(CONSUMER_CODE))).thenReturn(couponResult);

        // Mock 优惠计算结果
        PromotionRespDto promotionRespDto = new PromotionRespDto();
        promotionRespDto.setCouponTypeEnum(CouponTypeEnum.CASH_BACK);
        promotionRespDto.setDiscountTotalAmount("10.00"); // 10元优惠
        List<CartProductSkuInfo> discountedSkuList = createDiscountedCartProductSkuList();
        promotionRespDto.setCartSkuProductList(discountedSkuList);
        promotionRespDto.setChooseCoupon(createPromotionDto("coupon-1", CouponTypeEnum.CASH_BACK.getType()));
        
        // 修改mock参数，使用any()而不是特定值
        when(shopCalculateAmountService.compareCouponAmount(anyList(), anyList(), any(), any()))
                .thenReturn(promotionRespDto);

        // 执行测试
        CalculateAmountVO result = calculateService.calculateAmount(dto, CONSUMER_CODE);

        // 验证结果
        assertNotNull(result);
        assertEquals("100.00", result.getTotalAmount()); // 总金额100元
        assertEquals("90.00", result.getCostAmount()); // 优惠后实付90元
        assertEquals("10.00", result.getDiscountTotalAmount()); // 优惠金额10元
        assertTrue(result.isUseCouponCode()); // 使用了优惠券
        assertNull(result.getCostPoints()); // 不使用积分
        assertNotNull(result.getItemList());
        assertFalse(result.getItemList().isEmpty());
        assertEquals(CarPaymentTypeEnum.COUPON.getCode(), result.getPaymentType()); // 使用优惠券支付
        assertNotNull(result.getChooseCoupon());
        assertEquals("coupon-1", result.getChooseCoupon().getCouponCode());
        
        // 测试完成后恢复couponFlag值
        ReflectionTestUtils.setField(calculateService, "couponFlag", true);
    }

    @Test
    public void testCalculateAmount_WithPointsCase() {
        // 准备测试数据 - 使用积分
        CalculateAmountDTO dto = new CalculateAmountDTO();
        dto.setCartItemCodeList(Arrays.asList("1", "2"));
        dto.setIgnoreDiscount(false);
        dto.setFromShopCartPage(false);
        
        // 添加选择了积分支付的商品信息
        List<CartProductSkuInfo> productItemList = new ArrayList<>();
        CartProductSkuInfo item = new CartProductSkuInfo();
        item.setProductSkuCode("sku-code-1");
        item.setChooseFlag(true); // 标记为选择了积分支付
        productItemList.add(item);
        dto.setProductItemList(productItemList);
        
        List<ShoppingCarItemDO> cartItems = createShoppingCartItems();

        // Mock ShopCalculateAmountService
        when(shopCalculateAmountService.checkCartItem(any(), eq(CONSUMER_CODE)))
                .thenReturn(cartItems);

        // Mock ProductSkuApi
        List<ProductSkuRespVO> skuList = createProductSkuRespList();
        CommonResult<List<ProductSkuRespVO>> skuResult = CommonResult.success(skuList);
        when(productSkuApi.getSkuList(any())).thenReturn(skuResult);

        // Mock ProductCouponUsageRuleApi
        List<ListCanUsedCouponModelRespDTO> couponRelList = createCouponRelList();
        CommonResult<List<ListCanUsedCouponModelRespDTO>> couponRelResult = CommonResult.success(couponRelList);
        when(productCouponUsageRuleApi.listCanUsedCouponModel(any())).thenReturn(couponRelResult);

        // Mock PointApi
        AvailablePointResp pointResp = new AvailablePointResp();
        pointResp.setPoint(1000);
        CommonResult<AvailablePointResp> pointResult = CommonResult.success(pointResp);
        when(pointApi.obtainAvailablePoint(eq(CONSUMER_CODE))).thenReturn(pointResult);

        // Mock CouponApi
        PageResult<AvailableCouponResp> couponPage = createAvailableCouponPage();
        CommonResult<PageResult<AvailableCouponResp>> couponResult = CommonResult.success(couponPage);
        when(couponApi.obtainAvailableCouponList(any(), eq(CONSUMER_CODE))).thenReturn(couponResult);

        // Mock ShopCalculateAmountService计算金额
        when(shopCalculateAmountService.calculateCartItemAmount(anyList(), eq(false)))
                .thenReturn(new BigDecimal("10000")); // 100元转为分

        // Mock 积分优惠计算结果
        PromotionRespDto promotionRespDto = new PromotionRespDto();
        promotionRespDto.setCouponTypeEnum(CouponTypeEnum.POINTS);
        promotionRespDto.setDiscountTotalAmount("20.00"); // 20元优惠
        promotionRespDto.setCostPoints(500); // 消耗500积分
        List<CartProductSkuInfo> pointsDiscountedSkuList = createPointsDiscountedCartProductSkuList();
        promotionRespDto.setCartSkuProductList(pointsDiscountedSkuList);
        
        when(shopCalculateAmountService.compareCouponAmount(anyList(), anyList(), any(), isNull()))
                .thenReturn(promotionRespDto);

        // 执行测试
        CalculateAmountVO result = calculateService.calculateAmount(dto, CONSUMER_CODE);

        // 验证结果
        assertNotNull(result);
        assertEquals("100.00", result.getTotalAmount()); // 总金额100元
        assertEquals("80.00", result.getCostAmount()); // 优惠后实付80元
        assertEquals("20.00", result.getDiscountTotalAmount()); // 优惠金额20元
        assertFalse(result.isUseCouponCode()); // 不使用优惠券
        assertEquals(500, result.getCostPoints().intValue()); // 使用500积分
        assertNotNull(result.getItemList());
        assertFalse(result.getItemList().isEmpty());
        assertEquals(CarPaymentTypeEnum.POINT.getCode(), result.getPaymentType()); // 使用积分支付
    }

    @Test
    public void testCalculateAmount_NoDiscountWithoutIgnoreFlag() {
        // 准备测试数据 - ignoreDiscount为false但不使用任何优惠券或积分
        CalculateAmountDTO dto = createCalculateAmountDTO(false);
        List<ShoppingCarItemDO> cartItems = createShoppingCartItems();

        // Mock ShopCalculateAmountService
        when(shopCalculateAmountService.checkCartItem(any(), eq(CONSUMER_CODE)))
                .thenReturn(cartItems);

        // Mock ProductSkuApi
        List<ProductSkuRespVO> skuList = createProductSkuRespList();
        CommonResult<List<ProductSkuRespVO>> skuResult = CommonResult.success(skuList);
        when(productSkuApi.getSkuList(any())).thenReturn(skuResult);

        // Mock ProductCouponUsageRuleApi
        List<ListCanUsedCouponModelRespDTO> couponRelList = createCouponRelList();
        CommonResult<List<ListCanUsedCouponModelRespDTO>> couponRelResult = CommonResult.success(couponRelList);
        when(productCouponUsageRuleApi.listCanUsedCouponModel(any())).thenReturn(couponRelResult);

        // Mock PointApi
        AvailablePointResp pointResp = new AvailablePointResp();
        pointResp.setPoint(1000);
        CommonResult<AvailablePointResp> pointResult = CommonResult.success(pointResp);
        when(pointApi.obtainAvailablePoint(eq(CONSUMER_CODE))).thenReturn(pointResult);

        // Mock CouponApi - 返回空列表，模拟没有可用优惠券
        PageResult<AvailableCouponResp> emptyCouponPage = new PageResult<>();
        emptyCouponPage.setList(new ArrayList<>());
        emptyCouponPage.setTotal(0L);
        CommonResult<PageResult<AvailableCouponResp>> emptyCouponResult = CommonResult.success(emptyCouponPage);
        when(couponApi.obtainAvailableCouponList(any(), eq(CONSUMER_CODE))).thenReturn(emptyCouponResult);


        // Mock 优惠计算结果 - 返回null表示没有可用优惠
        when(shopCalculateAmountService.compareCouponAmount(anyList(), anyList(), any(), any()))
                .thenReturn(null);

        // 执行测试
        CalculateAmountVO result = calculateService.calculateAmount(dto, CONSUMER_CODE);

        // 验证结果
        assertNotNull(result);
        assertEquals("100.00", result.getTotalAmount()); // 总金额100元
        assertEquals("100.00", result.getCostAmount()); // 没有优惠，实付100元
        assertEquals("0", result.getDiscountTotalAmount()); // 没有优惠金额
        assertFalse(result.isUseCouponCode()); // 不使用优惠券
        assertNull(result.getCostPoints()); // 不使用积分
        assertNotNull(result.getItemList());
        assertFalse(result.getItemList().isEmpty());
        assertEquals(CarPaymentTypeEnum.CASH.getCode(), result.getPaymentType()); // 使用现金支付
    }

    @Test
    public void testCalculateAmount_DisabledCouponFlag() {
        // 设置couponFlag为false
        ReflectionTestUtils.setField(calculateService, "couponFlag", false);
        
        // 准备测试数据 - 不使用优惠券
        CalculateAmountDTO dto = createCalculateAmountDTO(false); // 不使用优惠券
        List<ShoppingCarItemDO> cartItems = createShoppingCartItems();

        // Mock ShopCalculateAmountService
        when(shopCalculateAmountService.checkCartItem(any(), eq(CONSUMER_CODE)))
                .thenReturn(cartItems);

        // Mock ProductSkuApi - 确保每个sku都有完整数据，没有null字段
        List<ProductSkuRespVO> skuList = createProductSkuRespList();
        CommonResult<List<ProductSkuRespVO>> skuResult = CommonResult.success(skuList);
        when(productSkuApi.getSkuList(any())).thenReturn(skuResult);

        // Mock ProductCouponUsageRuleApi - 返回完整有效的商品与优惠券关系
        List<ListCanUsedCouponModelRespDTO> couponRelList = createCouponRelList();
        CommonResult<List<ListCanUsedCouponModelRespDTO>> couponRelResult = CommonResult.success(couponRelList);
        when(productCouponUsageRuleApi.listCanUsedCouponModel(any())).thenReturn(couponRelResult);

        // Mock PointApi
        AvailablePointResp pointResp = new AvailablePointResp();
        pointResp.setPoint(1000);
        CommonResult<AvailablePointResp> pointResult = CommonResult.success(pointResp);
        when(pointApi.obtainAvailablePoint(eq(CONSUMER_CODE))).thenReturn(pointResult);

        // Mock CouponApi - 返回空列表，模拟没有可用优惠券
        PageResult<AvailableCouponResp> emptyCouponPage = new PageResult<>();
        emptyCouponPage.setList(new ArrayList<>());
        emptyCouponPage.setTotal(0L);
        CommonResult<PageResult<AvailableCouponResp>> emptyCouponResult = CommonResult.success(emptyCouponPage);
        when(couponApi.obtainAvailableCouponList(any(), eq(CONSUMER_CODE))).thenReturn(emptyCouponResult);

        // Mock ShopCalculateAmountService计算金额
        when(shopCalculateAmountService.calculateCartItemAmount(anyList(), eq(true)))
                .thenReturn(new BigDecimal("10000")); // 100元转为分

        // 执行测试
        CalculateAmountVO result = calculateService.calculateAmount(dto, CONSUMER_CODE);

        // 验证结果
        assertNotNull(result);
        assertEquals("100.00", result.getTotalAmount()); // 总金额100元
        assertEquals("100.00", result.getCostAmount()); // 没有优惠，实付100元
        assertEquals("0", result.getDiscountTotalAmount()); // 没有优惠金额
        assertFalse(result.isUseCouponCode()); // 不使用优惠券
        assertNull(result.getCostPoints()); // 不使用积分
        assertNotNull(result.getItemList());
        assertFalse(result.getItemList().isEmpty());
        assertEquals(CarPaymentTypeEnum.CASH.getCode(), result.getPaymentType()); // 使用现金支付
        
        // 测试完成后恢复couponFlag值
        ReflectionTestUtils.setField(calculateService, "couponFlag", true);
    }

    @Test
    public void testCalculateAmount_DisabledCouponFlag_EmptyProductList() {
        // 设置couponFlag为false
        ReflectionTestUtils.setField(calculateService, "couponFlag", false);
        
        // 准备测试数据 - 空的商品列表测试边界情况
        CalculateAmountDTO dto = createCalculateAmountDTO(false);
        // 不使用优惠券，避免抛出"所选优惠券不满足使用条件或不存在"的异常
        dto.setCouponCode(null);
        dto.setCouponType(null);
        
        // 返回空的购物车列表，但不是null
        when(shopCalculateAmountService.checkCartItem(any(), eq(CONSUMER_CODE)))
                .thenReturn(new ArrayList<>());

        // Mock ProductSkuApi - 空列表但不是null
        CommonResult<List<ProductSkuRespVO>> skuResult = CommonResult.success(new ArrayList<>());
        when(productSkuApi.getSkuList(any())).thenReturn(skuResult);

        // Mock ProductCouponUsageRuleApi - 空列表但不是null
        CommonResult<List<ListCanUsedCouponModelRespDTO>> couponRelResult = CommonResult.success(new ArrayList<>());
        when(productCouponUsageRuleApi.listCanUsedCouponModel(any())).thenReturn(couponRelResult);

        // Mock PointApi - 正确的返回格式
        AvailablePointResp pointResp = new AvailablePointResp();
        pointResp.setPoint(0);
        CommonResult<AvailablePointResp> pointResult = CommonResult.success(pointResp);
        when(pointApi.obtainAvailablePoint(eq(CONSUMER_CODE))).thenReturn(pointResult);

        // Mock CouponApi - 确保返回数据不为null
        PageResult<AvailableCouponResp> couponPage = new PageResult<>();
        couponPage.setList(new ArrayList<>());
        couponPage.setTotal(0L);
        CommonResult<PageResult<AvailableCouponResp>> couponResult = CommonResult.success(couponPage);
        when(couponApi.obtainAvailableCouponList(any(), eq(CONSUMER_CODE))).thenReturn(couponResult);

        // Mock ShopCalculateAmountService计算金额 - 空列表返回0
        when(shopCalculateAmountService.calculateCartItemAmount(anyList(), eq(true)))
                .thenReturn(new BigDecimal("0"));

        // 执行测试
        try {
            CalculateAmountVO result = calculateService.calculateAmount(dto, CONSUMER_CODE);

            // 验证结果
            assertNotNull(result);
            assertEquals("0.00", result.getTotalAmount()); // 总金额0元
            assertEquals("0.00", result.getCostAmount()); // 没有优惠，实付0元
            assertEquals("0", result.getDiscountTotalAmount()); // 没有优惠金额
            assertFalse(result.isUseCouponCode()); // 不使用优惠券
            assertNull(result.getCostPoints()); // 不使用积分
            assertNotNull(result.getItemList());
            assertTrue(result.getItemList().isEmpty()); // 购物车项为空
            assertEquals(CarPaymentTypeEnum.CASH.getCode(), result.getPaymentType()); // 使用现金支付
        } catch (NullPointerException e) {
            fail("NullPointerException in getAvailableCouponList: " + e.getMessage() + 
                 " at line 496 in CalculateServiceImpl.java");
        } finally {
            // 测试完成后恢复设置
            ReflectionTestUtils.setField(calculateService, "couponFlag", true);
        }
    }

    @Test
    public void testCalculateAmount_FromPDPWithoutCartItems() {
        // 准备测试数据 - 模拟从PDP页面直接计算
        CalculateAmountDTO dto = new CalculateAmountDTO();
        dto.setIgnoreDiscount(false);
        dto.setFromShopCartPage(false);
        
        // 模拟产品项
        List<ProductSpuSkuDTO> productItems = new ArrayList<>();
        ProductSpuSkuDTO productItem = new ProductSpuSkuDTO();
        productItem.setProductSpuCode("product-code-1");
        productItem.setProductSkuCode("sku-code-1");
        productItem.setQuantity(1);
        productItems.add(productItem);
        
        ProductSpuSkuDTO productItem2 = new ProductSpuSkuDTO();
        productItem2.setProductSpuCode("product-code-2");
        productItem2.setProductSkuCode("sku-code-2");
        productItem2.setQuantity(2);
        productItems.add(productItem2);
        
        dto.setProductItems(productItems);

        // Mock ProductSkuApi - 确保返回的数据完整
        List<ProductSkuRespVO> skuList = createProductSkuRespList();
        // 确保modelCode字段不为null
        for (ProductSkuRespVO sku : skuList) {
            if (sku.getModelCode() == null) {
                sku.setModelCode("model-" + sku.getProductSkuCode());
            }
        }
        CommonResult<List<ProductSkuRespVO>> skuResult = CommonResult.success(skuList);
        when(productSkuApi.getSkuList(any())).thenReturn(skuResult);

        // Mock ProductCouponUsageRuleApi - 确保所有的优惠券关系都完整
        List<ListCanUsedCouponModelRespDTO> couponRelList = createCouponRelList();
        CommonResult<List<ListCanUsedCouponModelRespDTO>> couponRelResult = CommonResult.success(couponRelList);
        when(productCouponUsageRuleApi.listCanUsedCouponModel(any())).thenReturn(couponRelResult);

        // Mock PointApi
        AvailablePointResp pointResp = new AvailablePointResp();
        pointResp.setPoint(1000);
        CommonResult<AvailablePointResp> pointResult = CommonResult.success(pointResp);
        when(pointApi.obtainAvailablePoint(eq(CONSUMER_CODE))).thenReturn(pointResult);

        // Mock CouponApi
        PageResult<AvailableCouponResp> couponPage = createAvailableCouponPage();
        CommonResult<PageResult<AvailableCouponResp>> couponResult = CommonResult.success(couponPage);
        when(couponApi.obtainAvailableCouponList(any(), eq(CONSUMER_CODE))).thenReturn(couponResult);

        // Mock ShopCalculateAmountService计算金额
        when(shopCalculateAmountService.calculateCartItemAmount(anyList(), eq(false)))
                .thenReturn(new BigDecimal("10000")); // 100元转为分

        // Mock 优惠计算结果
        PromotionRespDto promotionRespDto = new PromotionRespDto();
        promotionRespDto.setCouponTypeEnum(CouponTypeEnum.VOUCHER);
        promotionRespDto.setDiscountTotalAmount("10.00"); // 10元优惠
        
        // 确保所有商品信息完整且无null值
        List<CartProductSkuInfo> discountedSkuList = createDiscountedCartProductSkuList();
        for (CartProductSkuInfo item : discountedSkuList) {
            // 确保所有关键字段都有值
            if (item.getProductSkuCode() == null) {
                item.setProductSkuCode("default-sku-code");
            }
            if (item.getProductCode() == null) {
                item.setProductCode("default-product-code");
            }
            if (item.getSalePrice() == null) {
                item.setSalePrice("0.00");
            }
        }
        
        promotionRespDto.setCartSkuProductList(discountedSkuList);
        promotionRespDto.setChooseCoupon(createPromotionDto("coupon-1", CouponTypeEnum.VOUCHER.getType()));
        
        when(shopCalculateAmountService.compareCouponAmount(anyList(), anyList(), any(), isNull()))
                .thenReturn(promotionRespDto);

        try {
            // 执行测试
            CalculateAmountVO result = calculateService.calculateAmount(dto, CONSUMER_CODE);

            // 验证结果
            assertNotNull(result);
            assertEquals("100.00", result.getTotalAmount()); // 总金额100元
            assertEquals("90.00", result.getCostAmount()); // 优惠后实付90元
            assertEquals("10.00", result.getDiscountTotalAmount()); // 优惠金额10元
            assertTrue(result.isUseCouponCode()); // 使用了优惠券
            assertNull(result.getCostPoints()); // 不使用积分
            assertNotNull(result.getItemList());
            assertFalse(result.getItemList().isEmpty());
            assertEquals(CarPaymentTypeEnum.COUPON.getCode(), result.getPaymentType()); // 使用优惠券支付
        } catch (NullPointerException e) {
            // 如果仍然出现NullPointerException，打印更多信息以辅助调试
            fail("NullPointerException occurred: " + e.getMessage() + 
                 ". Check CalculateServiceImpl.java line 124 for possible stream.collect(Collectors.toMap()) with null key or value");
        }
    }

    @Test
    public void testCalculateAmount_SkuMapGeneration() {
        // 准备测试数据 - 模拟从PDP页面直接计算
        CalculateAmountDTO dto = new CalculateAmountDTO();
        dto.setIgnoreDiscount(false);
        dto.setFromShopCartPage(false);
        
        // 创建产品sku列表，确保ModelCode不为null
        List<ProductSkuRespVO> skuList = new ArrayList<>();
        
        ProductSkuRespVO sku1 = new ProductSkuRespVO();
        sku1.setProductSkuCode("sku-code-1");
        sku1.setProductSpuCode("product-code-1");
        sku1.setSalePrice("50.00");
        sku1.setSalePoints(500);
        sku1.setSalePointsPrice("45.00");
        sku1.setMarketPrice("60.00");
        sku1.setModelCode("model-code-1"); // 显式设置modelCode，避免null
        
        ProductDetailsVO detailsVO1 = new ProductDetailsVO();
        detailsVO1.setProductName("测试商品1");
        detailsVO1.setSupportCashAndPoints(true);
        detailsVO1.setBusinessCode("BG");
        sku1.setProductDetailsVO(detailsVO1);
        sku1.setAttributeArr(new ArrayList<>()); // 确保不为null
        skuList.add(sku1);
        
        // 模拟产品项
        List<ProductSpuSkuDTO> productItems = new ArrayList<>();
        ProductSpuSkuDTO productItem = new ProductSpuSkuDTO();
        productItem.setProductSpuCode("product-code-1");
        productItem.setProductSkuCode("sku-code-1");
        productItem.setQuantity(1);
        productItems.add(productItem);
        
        dto.setProductItems(productItems);

        // Mock ProductSkuApi
        CommonResult<List<ProductSkuRespVO>> skuResult = CommonResult.success(skuList);
        when(productSkuApi.getSkuList(any())).thenReturn(skuResult);

        // Mock ProductCouponUsageRuleApi
        List<ListCanUsedCouponModelRespDTO> couponRelList = new ArrayList<>();
        CommonResult<List<ListCanUsedCouponModelRespDTO>> couponRelResult = CommonResult.success(couponRelList);
        when(productCouponUsageRuleApi.listCanUsedCouponModel(any())).thenReturn(couponRelResult);

        // Mock PointApi
        AvailablePointResp pointResp = new AvailablePointResp();
        pointResp.setPoint(0);
        CommonResult<AvailablePointResp> pointResult = CommonResult.success(pointResp);
        when(pointApi.obtainAvailablePoint(any())).thenReturn(pointResult);

        // Mock CouponApi
        PageResult<AvailableCouponResp> couponPage = new PageResult<>();
        couponPage.setList(new ArrayList<>());
        couponPage.setTotal(0L);
        CommonResult<PageResult<AvailableCouponResp>> couponResult = CommonResult.success(couponPage);
        when(couponApi.obtainAvailableCouponList(any(), any())).thenReturn(couponResult);

        // Mock ShopCalculateAmountService计算金额
        when(shopCalculateAmountService.calculateCartItemAmount(anyList(), anyBoolean()))
                .thenReturn(new BigDecimal("5000"));

        // 执行测试
        try {
            CalculateAmountVO result = calculateService.calculateAmount(dto, CONSUMER_CODE);
            assertNotNull(result);
        } catch (NullPointerException e) {
            fail("NullPointerException in skuToKingdeeSkuMap generation: " + e.getMessage());
        }
    }

    // Helper methods
    private CalculateAmountDTO createCalculateAmountDTO(boolean ignoreDiscount) {
        CalculateAmountDTO dto = new CalculateAmountDTO();
        dto.setCartItemCodeList(Arrays.asList("1", "2")); // 修正为String类型
        dto.setIgnoreDiscount(ignoreDiscount);
        dto.setFromShopCartPage(true);
        return dto;
    }
    
    private CalculateAmountDTO createCalculateAmountDTOWithCoupon(boolean ignoreDiscount, String couponCode, Integer couponType) {
        CalculateAmountDTO dto = createCalculateAmountDTO(ignoreDiscount);
        dto.setCouponCode(couponCode);
        dto.setCouponType(couponType);
        return dto;
    }

    private List<ShoppingCarItemDO> createShoppingCartItems() {
        List<ShoppingCarItemDO> items = new ArrayList<>();
        
        ShoppingCarItemDO item1 = new ShoppingCarItemDO();
        item1.setId(1L);
        item1.setConsumerCode(CONSUMER_CODE);
        item1.setProductCode("product-code-1");
        item1.setProductSkuCode("sku-code-1");
        item1.setQuantity(1);
        items.add(item1);

        ShoppingCarItemDO item2 = new ShoppingCarItemDO();
        item2.setId(2L);
        item2.setConsumerCode(CONSUMER_CODE);
        item2.setProductCode("product-code-2");
        item2.setProductSkuCode("sku-code-2");
        item2.setQuantity(2);
        items.add(item2);
        
        return items;
    }

    private List<ProductSkuRespVO> createProductSkuRespList() {
        List<ProductSkuRespVO> skuList = new ArrayList<>();
        
        ProductSkuRespVO sku1 = new ProductSkuRespVO();
        sku1.setProductSkuCode("sku-code-1");
        sku1.setProductSpuCode("product-code-1");
        sku1.setSalePrice("50.00"); // 50元
        sku1.setSalePoints(500);
        sku1.setSalePointsPrice("45.00"); // 45元
        sku1.setMarketPrice("60.00"); // 60元
        sku1.setModelCode("model-code-1"); // 设置modelCode字段
        
        ProductDetailsVO detailsVO1 = new ProductDetailsVO();
        detailsVO1.setProductName("测试商品1");
        detailsVO1.setSupportCashAndPoints(true);
        detailsVO1.setBusinessCode("BG");
        List<ProductAttributeRespVO> attrList1 = new ArrayList<>();
        ProductAttributeRespVO attr1 = new ProductAttributeRespVO();
        attr1.setAttributeValue("红色");
        attrList1.add(attr1);
        sku1.setAttributeArr(attrList1);
        sku1.setProductDetailsVO(detailsVO1);
        skuList.add(sku1);
        
        ProductSkuRespVO sku2 = new ProductSkuRespVO();
        sku2.setProductSkuCode("sku-code-2");
        sku2.setProductSpuCode("product-code-2");
        sku2.setSalePrice("25.00"); // 25元
        sku2.setSalePoints(250);
        sku2.setSalePointsPrice("22.50"); // 22.5元
        sku2.setMarketPrice("30.00"); // 30元
        sku2.setModelCode("model-code-2"); // 设置modelCode字段
        
        ProductDetailsVO detailsVO2 = new ProductDetailsVO();
        detailsVO2.setProductName("测试商品2");
        detailsVO2.setSupportCashAndPoints(true);
        detailsVO2.setBusinessCode("BG");
        List<ProductAttributeRespVO> attrList2 = new ArrayList<>();
        ProductAttributeRespVO attr2 = new ProductAttributeRespVO();
        attr2.setAttributeValue("蓝色");
        attrList2.add(attr2);
        sku2.setAttributeArr(attrList2);
        sku2.setProductDetailsVO(detailsVO2);
        skuList.add(sku2);
        
        return skuList;
    }

    private List<ListCanUsedCouponModelRespDTO> createCouponRelList() {
        List<ListCanUsedCouponModelRespDTO> couponRelList = new ArrayList<>();
        
        ListCanUsedCouponModelRespDTO couponRel1 = new ListCanUsedCouponModelRespDTO();
        couponRel1.setProductCode("product-code-1");
        
        List<ListCanUsedCouponModelRespDTO.CouponTemplateItemRespDTO> couponTemplateList1 = new ArrayList<>();
        ListCanUsedCouponModelRespDTO.CouponTemplateItemRespDTO couponTemplate1 = new ListCanUsedCouponModelRespDTO.CouponTemplateItemRespDTO();
        couponTemplate1.setCouponModelCode("coupon-model-1");
        couponTemplateList1.add(couponTemplate1);
        couponRel1.setCouponCodeList(couponTemplateList1);
        couponRelList.add(couponRel1);
        
        ListCanUsedCouponModelRespDTO couponRel2 = new ListCanUsedCouponModelRespDTO();
        couponRel2.setProductCode("product-code-2");
        
        List<ListCanUsedCouponModelRespDTO.CouponTemplateItemRespDTO> couponTemplateList2 = new ArrayList<>();
        ListCanUsedCouponModelRespDTO.CouponTemplateItemRespDTO couponTemplate2 = new ListCanUsedCouponModelRespDTO.CouponTemplateItemRespDTO();
        couponTemplate2.setCouponModelCode("coupon-model-2");
        couponTemplateList2.add(couponTemplate2);
        couponRel2.setCouponCodeList(couponTemplateList2);
        couponRelList.add(couponRel2);
        
        return couponRelList;
    }

    private PageResult<AvailableCouponResp> createAvailableCouponPage() {
        PageResult<AvailableCouponResp> pageResult = new PageResult<>();
        
        List<AvailableCouponResp> couponRespList = new ArrayList<>();
        AvailableCouponResp couponResp = new AvailableCouponResp();
        couponResp.setCouponCode("coupon-1");
        couponResp.setCouponModelCode("coupon-model-1");
        couponResp.setCouponModelName("测试优惠券");
        couponResp.setCouponModelClassify(2); // 现金券
        couponResp.setUseExplain("测试优惠券说明");
        couponResp.setMoney(new BigDecimal("10.00"));
        couponResp.setTriggerMoney(new BigDecimal("100.00"));
        couponResp.setCouponStatus(1); // 正常状态
        
        couponRespList.add(couponResp);
        pageResult.setList(couponRespList);
        pageResult.setTotal(1L);
        
        return pageResult;
    }
    
    private List<CartProductSkuInfo> createDiscountedCartProductSkuList() {
        List<CartProductSkuInfo> skuInfoList = new ArrayList<>();
        
        // 商品1
        CartProductSkuInfo skuInfo1 = new CartProductSkuInfo();
        skuInfo1.setCartItemId(1L);
        skuInfo1.setProductSkuCode("sku-code-1");
        skuInfo1.setProductCode("product-code-1");
        skuInfo1.setProductName("测试商品1");
        skuInfo1.setSalePrice("50.00");
        skuInfo1.setCouponPrice("45.00"); // 优惠后价格
        skuInfo1.setDiscountAmount("5.00"); // 优惠金额
        skuInfo1.setChooseCouponType(CouponTypeEnum.CASH_BACK.getType());
        skuInfo1.setChooseCouponCode("coupon-1");
        skuInfo1.setJoinCalculateFlag(true);
        skuInfo1.setFinalJoinCalculateFlag(true);
        skuInfoList.add(skuInfo1);
        
        // 商品2
        CartProductSkuInfo skuInfo2 = new CartProductSkuInfo();
        skuInfo2.setCartItemId(2L);
        skuInfo2.setProductSkuCode("sku-code-2");
        skuInfo2.setProductCode("product-code-2");
        skuInfo2.setProductName("测试商品2");
        skuInfo2.setSalePrice("25.00");
        skuInfo2.setCouponPrice("22.50"); // 优惠后价格
        skuInfo2.setDiscountAmount("2.50"); // 优惠金额
        skuInfo2.setChooseCouponType(CouponTypeEnum.CASH_BACK.getType());
        skuInfo2.setChooseCouponCode("coupon-1");
        skuInfo2.setJoinCalculateFlag(true);
        skuInfo2.setFinalJoinCalculateFlag(true);
        skuInfoList.add(skuInfo2);
        
        // 商品2的第二个SKU
        CartProductSkuInfo skuInfo3 = new CartProductSkuInfo();
        skuInfo3.setCartItemId(2L);
        skuInfo3.setProductSkuCode("sku-code-2");
        skuInfo3.setProductCode("product-code-2");
        skuInfo3.setProductName("测试商品2");
        skuInfo3.setSalePrice("25.00");
        skuInfo3.setCouponPrice("22.50"); // 优惠后价格
        skuInfo3.setDiscountAmount("2.50"); // 优惠金额
        skuInfo3.setChooseCouponType(CouponTypeEnum.CASH_BACK.getType());
        skuInfo3.setChooseCouponCode("coupon-1");
        skuInfo3.setJoinCalculateFlag(true);
        skuInfo3.setFinalJoinCalculateFlag(true);
        skuInfoList.add(skuInfo3);
        
        return skuInfoList;
    }
    
    private List<CartProductSkuInfo> createPointsDiscountedCartProductSkuList() {
        List<CartProductSkuInfo> skuInfoList = new ArrayList<>();
        
        // 商品1 - 使用积分
        CartProductSkuInfo skuInfo1 = new CartProductSkuInfo();
        skuInfo1.setCartItemId(1L);
        skuInfo1.setProductSkuCode("sku-code-1");
        skuInfo1.setProductCode("product-code-1");
        skuInfo1.setProductName("测试商品1");
        skuInfo1.setSalePrice("50.00");
        skuInfo1.setSalePoints(500);
        skuInfo1.setSalePointsPrice("45.00");
        skuInfo1.setDiscountAmount("20.00"); // 积分抵扣金额
        skuInfo1.setChooseCouponType(CouponTypeEnum.POINTS.getType());
        skuInfo1.setJoinCalculateFlag(true);
        skuInfo1.setFinalJoinCalculateFlag(true);
        skuInfo1.setChooseFlag(true); // 标记使用积分
        skuInfoList.add(skuInfo1);
        
        // 商品2 - 不使用积分
        CartProductSkuInfo skuInfo2 = new CartProductSkuInfo();
        skuInfo2.setCartItemId(2L);
        skuInfo2.setProductSkuCode("sku-code-2");
        skuInfo2.setProductCode("product-code-2");
        skuInfo2.setProductName("测试商品2");
        skuInfo2.setSalePrice("25.00");
        skuInfo2.setSalePoints(250);
        skuInfo2.setSalePointsPrice("22.50");
        skuInfo2.setJoinCalculateFlag(true);
        skuInfo2.setFinalJoinCalculateFlag(false); // 不参与积分计算
        skuInfoList.add(skuInfo2);
        
        // 商品2的第二个SKU - 不使用积分
        CartProductSkuInfo skuInfo3 = new CartProductSkuInfo();
        skuInfo3.setCartItemId(2L);
        skuInfo3.setProductSkuCode("sku-code-2");
        skuInfo3.setProductCode("product-code-2");
        skuInfo3.setProductName("测试商品2");
        skuInfo3.setSalePrice("25.00");
        skuInfo3.setSalePoints(250);
        skuInfo3.setSalePointsPrice("22.50");
        skuInfo3.setJoinCalculateFlag(true);
        skuInfo3.setFinalJoinCalculateFlag(false); // 不参与积分计算
        skuInfoList.add(skuInfo3);
        
        return skuInfoList;
    }
    
    private PromotionDto createPromotionDto(String couponCode, Integer couponType) {
        PromotionDto promotionDto = new PromotionDto();
        promotionDto.setCouponCode(couponCode);
        promotionDto.setCouponModelCode("coupon-model-1");
        promotionDto.setCouponModelName("测试优惠券");
        promotionDto.setCouponModelClassify(couponType);
        promotionDto.setMoney("10.00");
        promotionDto.setTriggerMoney("100.00");
        promotionDto.setValidStartTime(LocalDateTime.now());
        promotionDto.setValidEndTime(LocalDateTime.now().plusDays(1)); // 一天后过期
        return promotionDto;
    }
} 
package com.jlr.ecp.order.service.internal.price;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.dal.dataobject.cart.ShoppingCarItemDO;
import com.jlr.ecp.order.dal.mysql.cart.ShoppingCarItemMapper;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.service.internal.promotion.PromotionalFactory;
import com.jlr.ecp.order.service.internal.promotion.PromotionalStrategy;
import com.jlr.ecp.order.service.internal.promotion.dto.PromotionRespDto;
import com.jlr.ecp.order.util.money.MoneyUtil;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopCalculateAmountServiceTest {

    @Mock
    private ShoppingCarItemMapper carItemMapper;

    @InjectMocks
    private ShopCalculateAmountService shopCalculateAmountService;

    private MockedStatic<PromotionalFactory> promotionalFactoryMockedStatic;

    private AutoCloseable mockStaticCloseable;
    @Before
    public void setUp() {
        mockStaticCloseable = Mockito.mockStatic(PromotionalFactory.class);
    }

    @After
    public void tearDown() throws Exception {
        mockStaticCloseable.close();
    }

    @Test
    public void testCheckCartItem_Success() {
        // 准备测试数据
        List<String> cartItemCodeList = Arrays.asList("item1", "item2");
        String customerCode = "customer1";
        List<ShoppingCarItemDO> expectedItems = Arrays.asList(
            new ShoppingCarItemDO().setCartItemCode("item1"),
            new ShoppingCarItemDO().setCartItemCode("item2")
        );

        // 模拟行为
        when(carItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(expectedItems);

        // 执行测试
        List<ShoppingCarItemDO> result = shopCalculateAmountService.checkCartItem(cartItemCodeList, customerCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(carItemMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test(expected = RuntimeException.class)
    public void testCheckCartItem_EmptyList() {
        // 准备测试数据
        List<String> cartItemCodeList = new ArrayList<>();
        String customerCode = "customer1";

        // 执行测试
        shopCalculateAmountService.checkCartItem(cartItemCodeList, customerCode);
    }

    @Test(expected = RuntimeException.class)
    public void testCheckCartItem_CountMismatch() {
        // 准备测试数据
        List<String> cartItemCodeList = Arrays.asList("item1", "item2");
        String customerCode = "customer1";
        List<ShoppingCarItemDO> expectedItems = Arrays.asList(
            new ShoppingCarItemDO().setCartItemCode("item1")
        );

        // 模拟行为
        when(carItemMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(expectedItems);

        // 执行测试
        shopCalculateAmountService.checkCartItem(cartItemCodeList, customerCode);
    }

    @Test
    public void testCalculateCartItemAmount_Success() {
        // 准备测试数据
        List<CartProductSkuInfo> items = Arrays.asList(
            new CartProductSkuInfo().setSalePrice("1000").setJoinCalculateFlag(true),
            new CartProductSkuInfo().setSalePrice("2000").setJoinCalculateFlag(true)
        );

        // 执行测试
        BigDecimal result = shopCalculateAmountService.calculateCartItemAmount(items, true);

        // 验证结果
        assertEquals("3000.00", MoneyUtil.convertFromCents(result));
    }

    @Test
    public void testCalculateCartItemAmount_WithJoinCalculateFlag() {
        // 准备测试数据
        List<CartProductSkuInfo> items = Arrays.asList(
            new CartProductSkuInfo().setSalePrice("1000").setJoinCalculateFlag(true),
            new CartProductSkuInfo().setSalePrice("2000").setJoinCalculateFlag(false)
        );

        // 执行测试
        BigDecimal result = shopCalculateAmountService.calculateCartItemAmount(items, false);

        // 验证结果
        assertEquals("1000.00", MoneyUtil.convertFromCents(result));
    }

    @Test(expected = RuntimeException.class)
    public void testCalculateCartItemAmount_EmptyList() {
        // 准备测试数据
        List<CartProductSkuInfo> items = new ArrayList<>();

        // 执行测试
        shopCalculateAmountService.calculateCartItemAmount(items, true);
    }
    @Test
    public void testCompareCouponAmount_EmptyCouponList() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfoList = Arrays.asList(
                createSkuInfo("1000", true, "PROMO1")
        );
        List<PromotionDto> couponList = new ArrayList<>();
        Integer paymentType = 1;

        // 执行测试
        PromotionRespDto result = shopCalculateAmountService.compareCouponAmount(
                skuInfoList, couponList, null, paymentType
        );

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testCompareCouponAmount_WithUserChoosePromotion() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfoList = Arrays.asList(
            createSkuInfo("1000", true, "PROMO1")
        );
        PromotionDto promo1 = createPromotion("PROMO1", CouponTypeEnum.CASH_BACK);
        promo1.setCouponCode("test123");
        List<PromotionDto> couponList = Arrays.asList(
            promo1
        );
        PromotionDto userChoosePromotion = promo1;
        Integer paymentType = 1;

        // 模拟静态方法
        PromotionalStrategy strategy = mock(PromotionalStrategy.class);
        Mockito.when(PromotionalFactory.getPromotional(any(CouponTypeEnum.class)))
            .thenReturn(strategy);
        when(strategy.executePromotional(anyList(), any(PromotionDto.class)))
            .thenReturn(new PromotionRespDto().setDiscountTotalAmount("10").setCouponTypeEnum(CouponTypeEnum.CASH_BACK).setChooseCoupon(promo1).setCostAmount("90"));

        // 调用测试方法
        PromotionRespDto result = shopCalculateAmountService.compareCouponAmount(
            skuInfoList, couponList, userChoosePromotion, paymentType
        );

        // 验证结果
        assertNotNull(result);
        assertEquals("10", result.getDiscountTotalAmount());
        assertEquals("90", result.getCostAmount());
        assertEquals(CouponTypeEnum.CASH_BACK, result.getCouponTypeEnum());
        assertEquals("test123", result.getChooseCoupon().getCouponCode());
    }

    @Test
    public void testCompareCouponAmount_WithoutUserChoosePromotion() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfoList = Arrays.asList(
            createSkuInfo("1000", true, "PROMO1")
        );
        List<PromotionDto> couponList = Arrays.asList(
            createPromotion("PROMO1", CouponTypeEnum.CASH_BACK).setCouponModelClassify(1)
        );
        Integer paymentType = 1;

        // 模拟静态方法
        PromotionalStrategy strategy = mock(PromotionalStrategy.class);
        Mockito.when(PromotionalFactory.getPromotional(any(CouponTypeEnum.class)))
                .thenReturn(strategy);
        when(strategy.executePromotional(anyList(), any(PromotionDto.class)))
                .thenReturn(new PromotionRespDto().setDiscountTotalAmount("10").setCouponTypeEnum(CouponTypeEnum.CASH_BACK));


        // 执行测试
        PromotionRespDto result = shopCalculateAmountService.compareCouponAmount(
            skuInfoList, couponList, null, paymentType
        );

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testCompareCouponAmount_WithPointsPromotion() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfoList = Arrays.asList(
            createSkuInfo("1000", true, "PROMO1")
        );
        PromotionDto promo1 = createPromotion("PROMO1", CouponTypeEnum.POINTS);
        promo1.setCouponCode("points123");
        List<PromotionDto> couponList = Arrays.asList(
            promo1
        );
        Integer paymentType = 1;

        // 模拟静态方法
        PromotionalStrategy strategy = mock(PromotionalStrategy.class);
        Mockito.when(PromotionalFactory.getPromotional(any(CouponTypeEnum.class)))
            .thenReturn(strategy);
        when(strategy.executePromotional(anyList(), any(PromotionDto.class)))
            .thenReturn(new PromotionRespDto().setDiscountTotalAmount("10").setCouponTypeEnum(CouponTypeEnum.POINTS).setChooseCoupon(promo1).setCostAmount("90"));

        // 调用测试方法
        PromotionRespDto result = shopCalculateAmountService.compareCouponAmount(
            skuInfoList, couponList, null, paymentType
        );

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testCompareCouponAmount_WithPaymentTypeMismatch() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfoList = Arrays.asList(
            createSkuInfo("1000", true, "PROMO1")
        );
        PromotionDto promo1 = createPromotion("PROMO1", CouponTypeEnum.CASH_BACK);
        promo1.setCouponCode("cash123");
        List<PromotionDto> couponList = Arrays.asList(
            promo1
        );
        Integer paymentType = 2; // 不匹配的支付类型

        // 模拟静态方法
        PromotionalStrategy strategy = mock(PromotionalStrategy.class);
        Mockito.when(PromotionalFactory.getPromotional(any(CouponTypeEnum.class)))
            .thenReturn(strategy);
        when(strategy.executePromotional(anyList(), any(PromotionDto.class)))
            .thenReturn(new PromotionRespDto().setDiscountTotalAmount("10").setCouponTypeEnum(CouponTypeEnum.CASH_BACK).setChooseCoupon(promo1).setCostAmount("90"));

        // 调用测试方法
        PromotionRespDto result = shopCalculateAmountService.compareCouponAmount(
            skuInfoList, couponList, null, paymentType
        );

        // 验证结果
        assertNull(result);
    }



    private CartProductSkuInfo createSkuInfo(String salePrice, boolean joinCalculateFlag, String couponModelCode) {
        CartProductSkuInfo skuInfo = new CartProductSkuInfo();
        skuInfo.setSalePrice(salePrice);
        skuInfo.setJoinCalculateFlag(joinCalculateFlag);
        skuInfo.setCouponModuleCodeList(Arrays.asList(couponModelCode));
        return skuInfo;
    }

    private PromotionDto createPromotion(String couponModelCode, CouponTypeEnum couponType) {
        PromotionDto promotion = new PromotionDto();
        promotion.setCouponModelCode(couponModelCode);
        promotion.setCouponModelClassify(couponType.getType());
        return promotion;
    }
} 
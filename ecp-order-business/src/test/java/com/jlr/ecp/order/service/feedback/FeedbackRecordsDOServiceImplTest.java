package com.jlr.ecp.order.service.feedback;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.jlr.ecp.order.api.order.vo.feedback.Option;
import com.jlr.ecp.order.api.order.vo.feedback.OrderFeedbackDimension;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.controller.admin.dashboard.dto.FeedbackStatisticQueryDTO;
import com.jlr.ecp.order.controller.admin.dashboard.vo.LineChartData;
import com.jlr.ecp.order.controller.admin.dashboard.vo.TableRespVo;
import com.jlr.ecp.order.controller.app.feedback.dto.FeedbackSubmitDTO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackConfigDO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackDimensionsDO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackRecordsDO;
import com.jlr.ecp.order.dal.dataobject.feedback.FeedbackSnapshotDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackConfigDOMapper;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackRecordsDOMapper;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackSnapshotDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FeedbackRecordsDOServiceImplTest {

    @Mock
    private FeedbackConfigDOMapper mockFeedbackConfigDOMapper;
    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private OrderInfoDOMapper mockOrderInfoDOMapper;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private FeedbackSnapshotDOMapper mockFeedbackSnapshotDOMapper;

    @Mock
    private FeedbackRecordsDOMapper baseMapper;

    @InjectMocks
    private FeedbackRecordsDOServiceImpl feedbackRecordsDOServiceImplUnderTest;

    @Before
    public void setUp() throws Exception {
        feedbackRecordsDOServiceImplUnderTest.ecpIdUtil = mockEcpIdUtil;
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), FeedbackConfigDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), FeedbackSnapshotDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), FeedbackDimensionsDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), FeedbackRecordsDO.class);
    }

    @Test
    public void testDestroy() {
        // Setup
        // Run the test
        feedbackRecordsDOServiceImplUnderTest.destroy();

        // Verify the results
    }

    @Test
    public void testSubmitFeedback() {
        // Setup
        final FeedbackSubmitDTO dto = new FeedbackSubmitDTO();
        dto.setFeedbackCode("feedbackCode");
        dto.setOrderCode("orderCode");
        final OrderFeedbackDimension orderFeedbackDimension = new OrderFeedbackDimension();
        orderFeedbackDimension.setName("name");
        orderFeedbackDimension.setType(0);
        final Option option = new Option();
        orderFeedbackDimension.setOptionJson(List.of(option));
        orderFeedbackDimension.setResult(List.of("value"));
        dto.setDimensionsContent(List.of(orderFeedbackDimension));

        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setBusinessCode("businessCode");
        feedbackConfigDO.setEnableStatus(2);

        // Configure OrderInfoDOMapper.queryOrderDoByOrderCode(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("orderCode")
                .orderType(0)
                .build();

        // Configure OrderInfoDOMapper.queryVCSSubOrderInfoByParentCode(...).
        final List<OrderInfoDO> infoDOList = List.of(OrderInfoDO.builder()
                .orderCode("orderCode")
                .orderType(0)
                .build());


        // Run the test
        final Boolean result = feedbackRecordsDOServiceImplUnderTest.submitFeedback(dto);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testSubmitFeedback_FeedbackConfigDOMapperReturnsNull() {
        // Setup
        final FeedbackSubmitDTO dto = new FeedbackSubmitDTO();
        dto.setFeedbackCode("feedbackCode");
        dto.setOrderCode("orderCode");
        final OrderFeedbackDimension orderFeedbackDimension = new OrderFeedbackDimension();
        orderFeedbackDimension.setName("name");
        orderFeedbackDimension.setType(0);
        final Option option = new Option();
        orderFeedbackDimension.setOptionJson(List.of(option));
        orderFeedbackDimension.setResult(List.of("value"));
        dto.setDimensionsContent(List.of(orderFeedbackDimension));


        // Run the test
        final Boolean result = feedbackRecordsDOServiceImplUnderTest.submitFeedback(dto);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testSubmitFeedback_OrderInfoDOMapperQueryOrderDoByOrderCodeReturnsNull() {
        // Setup
        final FeedbackSubmitDTO dto = new FeedbackSubmitDTO();
        dto.setFeedbackCode("feedbackCode");
        dto.setOrderCode("orderCode");
        final OrderFeedbackDimension orderFeedbackDimension = new OrderFeedbackDimension();
        orderFeedbackDimension.setName("name");
        orderFeedbackDimension.setType(0);
        final Option option = new Option();
        orderFeedbackDimension.setOptionJson(List.of(option));
        orderFeedbackDimension.setResult(List.of("value"));
        dto.setDimensionsContent(List.of(orderFeedbackDimension));

        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setBusinessCode("businessCode");
        feedbackConfigDO.setEnableStatus(0);


        // Run the test
        final Boolean result = feedbackRecordsDOServiceImplUnderTest.submitFeedback(dto);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testSubmitFeedback_OrderInfoDOMapperQueryVCSSubOrderInfoByParentCodeReturnsNoItems() {
        // Setup
        final FeedbackSubmitDTO dto = new FeedbackSubmitDTO();
        dto.setFeedbackCode("feedbackCode");
        dto.setOrderCode("orderCode");
        final OrderFeedbackDimension orderFeedbackDimension = new OrderFeedbackDimension();
        orderFeedbackDimension.setName("name");
        orderFeedbackDimension.setType(0);
        final Option option = new Option();
        orderFeedbackDimension.setOptionJson(List.of(option));
        orderFeedbackDimension.setResult(List.of("value"));
        dto.setDimensionsContent(List.of(orderFeedbackDimension));

        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setBusinessCode("businessCode");
        feedbackConfigDO.setEnableStatus(0);

        // Configure OrderInfoDOMapper.queryOrderDoByOrderCode(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .orderCode("orderCode")
                .orderType(0)
                .build();


        // Run the test
        final Boolean result = feedbackRecordsDOServiceImplUnderTest.submitFeedback(dto);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testScoreCount() {
        // Setup
        final FeedbackStatisticQueryDTO dto = new FeedbackStatisticQueryDTO("feedbackDimensions", "feedbackCode",
                "snapshotCode", "orderChannel");

        // Configure FeedbackSnapshotDOMapper.selectOneBySnapshotCode(...).
        final FeedbackSnapshotDO feedbackSnapshotDO = new FeedbackSnapshotDO();
        feedbackSnapshotDO.setCreatedTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackSnapshotDO.setIsDeleted(false);
        feedbackSnapshotDO.setSnapshotJson("[{\"name\": \" 您通常使用哪些支付方式？\", \"sort\": 1, \"type\": 1, \"remark\": \"\", \"typeText\": \"单选题\", \"mustInput\": 0, \"optionList\": [{\"sort\": 1, \"option\": \"支付宝\"}, {\"sort\": 2, \"option\": \"微信支付\"}, {\"sort\": 3, \"option\": \"银行卡支付\"}, {\"sort\": 4, \"option\": \"货到付款\"}, {\"sort\": 5, \"option\": \"其他\"}], \"mustInputText\": \"否\", \"dimensionsCode\": \"1902707697814667264\"}, {\"name\": \"您希望商城增加哪些类型的商品？\", \"sort\": 2, \"type\": 2, \"remark\": \"\", \"typeText\": \"多选题\", \"mustInput\": 0, \"optionList\": [{\"sort\": 1, \"option\": \"服装鞋帽\"}, {\"sort\": 2, \"option\": \"家居用品\"}, {\"sort\": 3, \"option\": \"数码电子\"}, {\"sort\": 4, \"option\": \"美妆护肤\"}, {\"sort\": 5, \"option\": \"食品饮料\"}, {\"sort\": 6, \"option\": \"其他\"}], \"mustInputText\": \"否\", \"dimensionsCode\": \"1902707697814667265\"}, {\"name\": \"物流配送的速度\", \"sort\": 3, \"type\": 0, \"remark\": \"\", \"typeText\": \"星级（5分制）\", \"mustInput\": 1, \"mustInputText\": \"是\", \"dimensionsCode\": \"1902707697818861568\"}, {\"name\": \"配送员的服务态度\", \"sort\": 4, \"type\": 0, \"remark\": \"\", \"typeText\": \"星级（5分制）\", \"mustInput\": 1, \"mustInputText\": \"是\", \"dimensionsCode\": \"1902707697818861569\"}, {\"name\": \"支付页面的加载速度\", \"sort\": 5, \"type\": 0, \"remark\": \"\", \"typeText\": \"星级（5分制）\", \"mustInput\": 1, \"mustInputText\": \"是\", \"dimensionsCode\": \"1902707697818861570\"}, {\"name\": \"支付过程的安全性\", \"sort\": 6, \"type\": 0, \"remark\": \"\", \"typeText\": \"星级（5分制）\", \"mustInput\": 0, \"mustInputText\": \"否\", \"dimensionsCode\": \"1902707697818861571\"}, {\"name\": \"您通常通过哪些设备访问本商城？\", \"sort\": 7, \"type\": 1, \"remark\": \"\", \"typeText\": \"单选题\", \"mustInput\": 1, \"optionList\": [{\"sort\": 1, \"option\": \"手机\"}, {\"sort\": 2, \"option\": \"平板电脑\"}, {\"sort\": 3, \"option\": \"平板电脑\"}, {\"sort\": 4, \"option\": \"台式电脑\"}], \"mustInputText\": \"是\", \"dimensionsCode\": \"1902707697818861572\"}, {\"name\": \"您认为本商城的网站/APP 有哪些方面需\", \"sort\": 8, \"type\": 2, \"remark\": \"\", \"typeText\": \"多选题\", \"mustInput\": 1, \"optionList\": [{\"sort\": 1, \"option\": \"界面设计\"}, {\"sort\": 2, \"option\": \"操作流畅度\"}, {\"sort\": 3, \"option\": \"功能完善度\"}, {\"sort\": 4, \"option\": \"商品搜索功能\"}, {\"sort\": 5, \"option\": \"商品详情页信息\"}], \"mustInputText\": \"是\", \"dimensionsCode\": \"1902707697818861573\"}]");
        feedbackSnapshotDO.setEnableTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        feedbackSnapshotDO.setDowntime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        when(mockFeedbackSnapshotDOMapper.selectOneBySnapshotCode("snapshotCode")).thenReturn(feedbackSnapshotDO);

        // Run the test
        final Map<String, Object> result = feedbackRecordsDOServiceImplUnderTest.scoreCount(dto);

        // Verify the results
    }

    @Test
    public void testScoreCount_FeedbackSnapshotDOMapperReturnsNull() {
        // Setup
        final FeedbackStatisticQueryDTO dto = new FeedbackStatisticQueryDTO("feedbackDimensions", "feedbackCode",
                "snapshotCode", "orderChannel");
        when(mockFeedbackSnapshotDOMapper.selectOneBySnapshotCode("snapshotCode")).thenReturn(null);

        // Run the test
        final Map<String, Object> result = feedbackRecordsDOServiceImplUnderTest.scoreCount(dto);

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testConvertLineChartDataToTableRespVo() {
        // Setup
        final List<LineChartData> lineChartData = List.of(
                new LineChartData(List.of("value"), "title", List.of(new BigDecimal("0.00"))));
        final TableRespVo expectedResult = new TableRespVo(List.of(new TableRespVo.HeaderItem("name", "", List.of())),
                List.of(Map.ofEntries(Map.entry("value", "value"))));

        // Run the test
        final TableRespVo result = feedbackRecordsDOServiceImplUnderTest.convertLineChartDataToTableRespVo(
                lineChartData, List.of("value"));

        // Verify the results
        assertThat(result).isNotNull();
    }
}

package com.jlr.ecp.order.api.order;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.inventory.api.InventoryOrderApi;
import com.jlr.ecp.inventory.dto.qry.InventoryOrderUpdateDTO;
import com.jlr.ecp.order.api.order.dto.OrderLogisticStatusChangeReqDto;
import com.jlr.ecp.order.api.order.vo.BrandGoodsOrderInfoPageVO;
import com.jlr.ecp.order.api.order.vo.bak.OrderRespVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemLogisticsDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemLogisticsDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.logistic.DeliveryNotificationEnum;
import com.jlr.ecp.order.enums.order.OrderLogisticsStatusEnum;
import com.jlr.ecp.order.kafka.producer.BrandedGoodsNotificationProducer;
import com.jlr.ecp.order.service.logistic.OrderItemLogisticService;
import com.jlr.ecp.order.service.order.BrandGoodsOrderInfoDOService;
import com.jlr.ecp.order.service.order.handler.ShortLinkHandler;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrderApiImplTest {

    @Mock
    private OrderInfoDOMapper orderInfoDOMapper;

    @Mock
    private OrderItemDOMapper orderItemDOMapper;

    @Mock
    private OrderRefundItemDOMapper orderRefundItemDOMapper;

    @Mock
    private OrderItemLogisticsDOMapper orderItemLogisticsDOMapper;

    @Mock
    private OrderRefundDOMapper orderRefundDOMapper;

    @Mock
    private OrderItemLogisticService orderItemLogisticService;

    @Mock
    private BrandGoodsOrderInfoDOService brandGoodsOrderInfoDOService;

    @Mock
    private BrandedGoodsNotificationProducer notificationProducer;

    @Mock
    private ShortLinkHandler shortLinkHandler;

    @Mock
    private InventoryOrderApi inventoryOrderApi;

    @InjectMocks
    private OrderApiImpl orderApi;

    private static final String TEST_ORDER_CODE = "TEST123456";
    private static final String TEST_PARENT_ORDER_CODE = "PARENT123456";
    private static final String TEST_ORDER_ITEM_CODE = "ITEM123456";

    @Before
    public void setUp() {
        // 初始化 TableInfoHelper，解决 LambdaUpdateWrapper 问题
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderInfoDO.class);
    }

    @Test
    public void testViewOrderInfo_Success() {
        // 准备测试数据
        OrderInfoDO mockOrderInfo = new OrderInfoDO();
        mockOrderInfo.setOrderCode(TEST_ORDER_CODE);
        mockOrderInfo.setParentOrderCode(TEST_PARENT_ORDER_CODE);
        mockOrderInfo.setOrderType(1); // 使用Integer类型
        mockOrderInfo.setCostAmount(10000); // 使用Integer类型，表示100.00元
        mockOrderInfo.setExcludeTaxAmount(9000); // 使用Integer类型，表示90.00元
        mockOrderInfo.setCreatedTime(LocalDateTime.now());
        mockOrderInfo.setFreightAmount(1000); // 使用Integer类型，表示10.00元
        mockOrderInfo.setFreightTax(new BigDecimal("1.00")); // 使用BigDecimal类型
        mockOrderInfo.setBusinessCode(BusinessIdEnum.BRAND_GOODS.getCode());

        when(orderInfoDOMapper.selectOne(any())).thenReturn(mockOrderInfo);
        when(orderRefundDOMapper.selectList(any())).thenReturn(new ArrayList<>());

        // 执行测试
        CommonResult<OrderRespVO> result = orderApi.viewOrderInfo(TEST_ORDER_CODE);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(TEST_ORDER_CODE, result.getData().getOrderInfo().getOrderCode());
        assertEquals(TEST_PARENT_ORDER_CODE, result.getData().getOrderInfo().getParentOrderCode());
    }

    @Test
    public void testPageOrders_Success() {
        // 准备测试数据
        String orderNumber = "TEST123";
        List<Integer> logisticsOrderStatusList = List.of(1, 2);
        LocalDateTime startTime = LocalDateTime.now().minusDays(1);
        LocalDateTime endTime = LocalDateTime.now();
        String updateTimeSort = "DESC";
        boolean closedWithNoPay = false;
        Integer pageNo = 1;
        Integer pageSize = 10;

        // 创建测试数据
        BrandGoodsOrderInfoPageVO mockOrder = new BrandGoodsOrderInfoPageVO();
        mockOrder.setOrderCode(TEST_ORDER_CODE);
        List<BrandGoodsOrderInfoPageVO> mockOrderList = List.of(mockOrder);

        PageResult<BrandGoodsOrderInfoPageVO> mockPageResult = new PageResult<>();
        mockPageResult.setTotal(1L);
        mockPageResult.setList(mockOrderList);

        when(brandGoodsOrderInfoDOService.getPage(any())).thenReturn(mockPageResult);

        // 执行测试
        CommonResult<PageResult<BrandGoodsOrderInfoPageVO>> result = orderApi.pageOrders(
            orderNumber, logisticsOrderStatusList, startTime, endTime, 
            updateTimeSort, closedWithNoPay, pageNo, pageSize
        );

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(1L, result.getData().getTotal().longValue());
        assertNotNull(result.getData().getList());
        assertEquals(1, result.getData().getList().size());
        assertEquals(TEST_ORDER_CODE, result.getData().getList().get(0).getOrderCode());
    }

    @Test
    public void testUpdateOrderStatusOnLogisticChange_Success() {
        // 准备测试数据
        OrderLogisticStatusChangeReqDto reqDto = new OrderLogisticStatusChangeReqDto();
        reqDto.setOrderCode(TEST_ORDER_CODE);

        OrderInfoDO mockOrderInfo = new OrderInfoDO();
        mockOrderInfo.setOrderCode(TEST_ORDER_CODE);
        mockOrderInfo.setLogisticsStatus(OrderLogisticsStatusEnum.PENDING_SHIPMENT.getCode());

        // 创建订单项
        OrderItemDO mockOrderItem = new OrderItemDO();
        mockOrderItem.setOrderItemCode(TEST_ORDER_ITEM_CODE);
        mockOrderItem.setOrderCode(TEST_ORDER_CODE);
        List<OrderItemDO> mockOrderItems = List.of(mockOrderItem);

        // 创建物流信息
        OrderItemLogisticsDO mockLogistics = new OrderItemLogisticsDO();
        mockLogistics.setOrderCode(TEST_ORDER_CODE);
        mockLogistics.setOrderItemCode(TEST_ORDER_ITEM_CODE);
        List<OrderItemLogisticsDO> mockLogisticsList = List.of(mockLogistics);

        when(orderInfoDOMapper.selectOne(any())).thenReturn(mockOrderInfo);
        when(orderItemDOMapper.selectList(any())).thenReturn(mockOrderItems);
        when(orderItemLogisticsDOMapper.selectList(any())).thenReturn(mockLogisticsList);
        when(orderItemLogisticService.checkAndBuildInfoStatus(any(), any(), any(), any()))
            .thenReturn(DeliveryNotificationEnum.NONE);

        // 执行测试
        CommonResult<Boolean> result = orderApi.updateOrderStatusOnLogisticChange(reqDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
    }
    
    @Test
    public void testSyncGuanyiOrder_Success() {
        // 准备测试数据
        List<String> orderCodeList = List.of(TEST_ORDER_CODE);
        CommonResult<String> mockSyncResult = CommonResult.success("同步成功");

        // 使用 doReturn 而不是 when，避免严格匹配问题
        doReturn(mockSyncResult).when(inventoryOrderApi).orderSync(any(InventoryOrderUpdateDTO.class));
        
        // 使用 doReturn 设置 update 方法的返回值，匹配任何参数
        doReturn(1).when(orderInfoDOMapper).update(any(), any());
        
        // 执行测试
        CommonResult<String> result = orderApi.syncGuanyiOrder(orderCodeList);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("同步完成", result.getData());
        
        // 验证inventoryOrderApi.orderSync被调用
        verify(inventoryOrderApi, times(1)).orderSync(any(InventoryOrderUpdateDTO.class));
        
        // 验证orderInfoDOMapper.update被调用
        verify(orderInfoDOMapper, times(1)).update(any(), any());
    }
    
    @Test
    public void testSyncGuanyiOrder_EmptyList() {
        // 准备测试数据 - 空列表
        List<String> orderCodeList = new ArrayList<>();
        
        // 执行测试
        CommonResult<String> result = orderApi.syncGuanyiOrder(orderCodeList);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals("待同步订单列表为空", result.getData());
        
        // 验证inventoryOrderApi.orderSync未被调用
        verify(inventoryOrderApi, never()).orderSync(any());
    }
    
    @Test(expected = Exception.class)
    public void testSyncGuanyiOrder_SyncFailed() {
        // 准备测试数据
        List<String> orderCodeList = List.of(TEST_ORDER_CODE);
        CommonResult<String> mockFailResult = CommonResult.error(500, "同步失败");

        // 使用 doReturn 模拟失败的响应
        doReturn(mockFailResult).when(inventoryOrderApi).orderSync(any(InventoryOrderUpdateDTO.class));
        
        // 执行测试
        orderApi.syncGuanyiOrder(orderCodeList);
        
        // 验证inventoryOrderApi.orderSync被调用
        verify(inventoryOrderApi, times(1)).orderSync(any(InventoryOrderUpdateDTO.class));
        
        // 验证orderInfoDOMapper.update未被调用
        verify(orderInfoDOMapper, never()).update(any(), any());
    }
} 
package com.jlr.ecp.order.service.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.order.dto.ECouponOrderPageReqDTO;
import com.jlr.ecp.order.api.order.vo.ECouponOrderInfoPageVO;
import com.jlr.ecp.order.api.order.vo.ECouponOrderItemVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EcouponOrderInfoDOServiceImplTest {

    @Mock
    private OrderInfoDOMapper mockOrderInfoDOMapper;

    @Mock
    private OrderItemDOMapper mockOrderItemDOMapper;

    @InjectMocks
    private EcouponOrderInfoDOServiceImpl ecouponOrderInfoDOServiceImpl;

    @Before
    public void setUp() {
        // 移除通用Mock行为
    }

    @Test
    public void testGetPage_Success() {
        // 准备测试数据
        ECouponOrderPageReqDTO dto = new ECouponOrderPageReqDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setWxPhone("13800138000");
        dto.setContactPhone("13900139000");
        dto.setCouponStatus("1,2");
        dto.setAfterSalesStatus("0,1");

        // 配置OrderInfoDOMapper.getEcouponOrdersWithPaging(...)
        ECouponOrderInfoPageVO orderInfoPageVO = new ECouponOrderInfoPageVO();
        orderInfoPageVO.setOrderCode("orderCode1");
        
        Page<ECouponOrderInfoPageVO> page = new Page<>();
        page.setRecords(Collections.singletonList(orderInfoPageVO));
        page.setTotal(1L);
        
        when(mockOrderInfoDOMapper.getEcouponOrdersWithPaging(any(Page.class), any(ECouponOrderPageReqDTO.class)))
            .thenReturn(page);

        // 配置OrderItemDOMapper.selectList(...)
        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode("orderItemCode1");
        orderItemDO.setOrderCode("orderCode1");
        orderItemDO.setIsDeleted(false);
        
        List<OrderItemDO> orderItemDOList = new ArrayList<>();
        orderItemDOList.add(orderItemDO);
        
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDOList);

        // 配置OrderInfoDOMapper.getEcouponOrderDetailsByCodes(...)
        ECouponOrderItemVO orderItemVO = new ECouponOrderItemVO();
        orderItemVO.setOrderCode("orderCode1");
        orderItemVO.setOrderItemCode("orderItemCode1");
        
        List<ECouponOrderItemVO> orderItemVOList = new ArrayList<>();
        orderItemVOList.add(orderItemVO);
        
        when(mockOrderInfoDOMapper.getEcouponOrderDetailsByCodes(any())).thenReturn(orderItemVOList);

        // 执行测试
        PageResult<ECouponOrderInfoPageVO> result = ecouponOrderInfoDOServiceImpl.getPage(dto);

        // 验证结果
        assertThat(result.getTotal()).isEqualTo(1L);
        assertThat(result.getList()).hasSize(1);
        assertThat(result.getList().get(0).getOrderCode()).isEqualTo("orderCode1");
        assertThat(result.getList().get(0).getOrderItems()).hasSize(1);
    }

    @Test
    public void testGetPage_NoOrders() {
        // 准备测试数据
        ECouponOrderPageReqDTO dto = new ECouponOrderPageReqDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        
        // 配置OrderInfoDOMapper.getEcouponOrdersWithPaging(...) - 返回空结果
        Page<ECouponOrderInfoPageVO> page = new Page<>();
        page.setRecords(Collections.emptyList());
        page.setTotal(0L);
        
        when(mockOrderInfoDOMapper.getEcouponOrdersWithPaging(any(Page.class), any(ECouponOrderPageReqDTO.class)))
            .thenReturn(page);

        // 执行测试
        PageResult<ECouponOrderInfoPageVO> result = ecouponOrderInfoDOServiceImpl.getPage(dto);

        // 验证结果
        assertThat(result.getTotal()).isEqualTo(0L);
        assertThat(result.getList()).isEmpty();
    }

    @Test
    public void testGetPage_WithMultipleOrderItems() {
        // 准备测试数据
        ECouponOrderPageReqDTO dto = new ECouponOrderPageReqDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        
        // 配置OrderInfoDOMapper.getEcouponOrdersWithPaging(...)
        ECouponOrderInfoPageVO orderInfoPageVO = new ECouponOrderInfoPageVO();
        orderInfoPageVO.setOrderCode("orderCode1");
        
        Page<ECouponOrderInfoPageVO> page = new Page<>();
        page.setRecords(Collections.singletonList(orderInfoPageVO));
        page.setTotal(1L);
        
        when(mockOrderInfoDOMapper.getEcouponOrdersWithPaging(any(Page.class), any(ECouponOrderPageReqDTO.class)))
            .thenReturn(page);

        // 配置OrderItemDOMapper.selectList(...) - 多个订单项
        OrderItemDO orderItem1 = new OrderItemDO();
        orderItem1.setOrderItemCode("orderItemCode1");
        orderItem1.setOrderCode("orderCode1");
        orderItem1.setIsDeleted(false);
        
        OrderItemDO orderItem2 = new OrderItemDO();
        orderItem2.setOrderItemCode("orderItemCode2");
        orderItem2.setOrderCode("orderCode1");
        orderItem2.setIsDeleted(false);
        
        List<OrderItemDO> orderItemDOList = Arrays.asList(orderItem1, orderItem2);
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDOList);

        // 配置OrderInfoDOMapper.getEcouponOrderDetailsByCodes(...)
        ECouponOrderItemVO orderItemVO1 = new ECouponOrderItemVO();
        orderItemVO1.setOrderCode("orderCode1");
        orderItemVO1.setOrderItemCode("orderItemCode1");
        
        ECouponOrderItemVO orderItemVO2 = new ECouponOrderItemVO();
        orderItemVO2.setOrderCode("orderCode1");
        orderItemVO2.setOrderItemCode("orderItemCode2");
        
        List<ECouponOrderItemVO> orderItemVOList = Arrays.asList(orderItemVO1, orderItemVO2);
        when(mockOrderInfoDOMapper.getEcouponOrderDetailsByCodes(any())).thenReturn(orderItemVOList);

        // 执行测试
        PageResult<ECouponOrderInfoPageVO> result = ecouponOrderInfoDOServiceImpl.getPage(dto);

        // 验证结果
        assertThat(result.getTotal()).isEqualTo(1L);
        assertThat(result.getList()).hasSize(1);
        assertThat(result.getList().get(0).getOrderItems()).hasSize(2);
        assertThat(result.getList().get(0).getOrderItemCodes()).isEqualTo("orderItemCode1,orderItemCode2");
    }

    @Test
    public void testGetPage_WithFilterConditions() {
        // 准备测试数据 - 带过滤条件
        ECouponOrderPageReqDTO dto = new ECouponOrderPageReqDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setWxPhone("13800138000");
        dto.setContactPhone("13900139000");
        dto.setCouponStatus("1,2");
        dto.setAfterSalesStatus("0,1");
        
        // 配置OrderInfoDOMapper.getEcouponOrdersWithPaging(...)
        ECouponOrderInfoPageVO orderInfoPageVO = new ECouponOrderInfoPageVO();
        orderInfoPageVO.setOrderCode("orderCode1");
        
        Page<ECouponOrderInfoPageVO> page = new Page<>();
        page.setRecords(Collections.singletonList(orderInfoPageVO));
        page.setTotal(1L);
        
        when(mockOrderInfoDOMapper.getEcouponOrdersWithPaging(any(Page.class), any(ECouponOrderPageReqDTO.class)))
            .thenReturn(page);

        // 配置OrderItemDOMapper.selectList(...)
        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode("orderItemCode1");
        orderItemDO.setOrderCode("orderCode1");
        orderItemDO.setIsDeleted(false);
        
        List<OrderItemDO> orderItemDOList = Collections.singletonList(orderItemDO);
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDOList);

        // 配置OrderInfoDOMapper.getEcouponOrderDetailsByCodes(...)
        ECouponOrderItemVO orderItemVO = new ECouponOrderItemVO();
        orderItemVO.setOrderCode("orderCode1");
        orderItemVO.setOrderItemCode("orderItemCode1");
        
        List<ECouponOrderItemVO> orderItemVOList = Collections.singletonList(orderItemVO);
        when(mockOrderInfoDOMapper.getEcouponOrderDetailsByCodes(any())).thenReturn(orderItemVOList);

        // 执行测试
        PageResult<ECouponOrderInfoPageVO> result = ecouponOrderInfoDOServiceImpl.getPage(dto);

        // 验证结果
        assertThat(result.getTotal()).isEqualTo(1L);
        assertThat(result.getList()).hasSize(1);
        assertThat(result.getList().get(0).getOrderCode()).isEqualTo("orderCode1");
        assertThat(result.getList().get(0).getOrderItems()).hasSize(1);
    }

    @Test(expected = RuntimeException.class)
    public void testGetPage_WhenMapperThrowsException() {
        // 准备测试数据
        ECouponOrderPageReqDTO dto = new ECouponOrderPageReqDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        
        // 模拟Mapper抛出异常
        when(mockOrderInfoDOMapper.getEcouponOrdersWithPaging(any(Page.class), any(ECouponOrderPageReqDTO.class)))
            .thenThrow(new RuntimeException("数据库查询异常"));
        
        // 执行测试 - 预期抛出异常
        ecouponOrderInfoDOServiceImpl.getPage(dto);
    }
} 
//package com.jlr.ecp.order.service.coupon;
//
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.doNothing;
//import static org.mockito.Mockito.when;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.MockitoJUnitRunner;
//
//import com.jlr.ecp.order.dal.dataobject.order.OrderCouponDetailDO;
//import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
//import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
//import com.jlr.ecp.order.dal.mysql.order.OrderCouponDetailDOMapper;
//import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
//import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
//import com.jlr.ecp.order.enums.order.OrderStatusEnum;
//import com.jlr.ecp.order.service.coupon.status.CouponStatusUpdateService;
//import com.jlr.ecp.order.service.coupon.status.TransactionOperator;
//import com.jlr.ecp.order.service.coupon.status.dto.CouponStatusNotifyDto;
//
///**
// * @projectName: ecp-order-service
// * @package: com.jlr.ecp.order.service.coupon
// * @className: StatusUpdateTest
// * @author: gaoqig
// * @description: 卡券状态变更测试
// * @date: 2025/3/20 11:57
// * @version: 1.0
// */
//@RunWith(MockitoJUnitRunner.class)
//public class StatusUpdateTest {
//    @InjectMocks
//    private CouponStatusUpdateService couponStatusUpdateService;
//
//    @Mock
//    private OrderCouponDetailDOMapper orderCouponDetailDOMapper;
//
//    @Mock
//    private OrderItemDOMapper orderItemDOMapper;
//
//    @Mock
//    private OrderInfoDOMapper orderInfoDOMapper;
//
//    @Mock
//    private TransactionOperator transactionOperator;
//
//    private CouponStatusNotifyDto couponStatusNotifyDto;
//    private OrderCouponDetailDO orderCouponDetailDO;
//    private OrderCouponDetailDO orderCouponDetailDO2;
//    private OrderCouponDetailDO orderCouponDetailDO3;
//
//    private OrderItemDO orderItemDO;
//    private OrderItemDO orderItemDO2;
//    private OrderItemDO orderItemDO3;
//    private OrderInfoDO orderInfoDO;
//
//    @Test
//    public void testDealWithCouponStatus_Temp() {
//        couponStatusNotifyDto.setCouponCode("0910487883430507");
//        //boolean result = couponStatusUpdateService.dealWithCouponStatus(couponStatusNotifyDto);
//        //assertTrue(result);
//    }
//
//    @Before
//    public void setUp() {
//        couponStatusNotifyDto = new CouponStatusNotifyDto();
//        couponStatusNotifyDto.setCouponCode("7498423232419433");
////        WAREHOUSING(1, "入库"),
////        NOT_ACTIVE(2, "未生效"),
////        PENDING_USE(3, "待使用"),
////        INVALIDATED(4, "已作废"),
////        VERIFIED(5, "已核销"),
////        EXPIRED(6, "已过期");
//        //couponStatusNotifyDto.setCouponStatus(5); // EXPIRED
//
//        orderCouponDetailDO = new OrderCouponDetailDO();
//        orderCouponDetailDO.setOrderCode("LEM520250321171536001900608");
//        orderCouponDetailDO.setOrderItemCode("1903012605357809664");
//        orderCouponDetailDO.setUpdatedTime(LocalDateTime.now());
//        orderCouponDetailDO.setStatus(3);
//        orderCouponDetailDO.setCouponCode("7498423232419433");
//
//        orderCouponDetailDO2 = new OrderCouponDetailDO();
//        orderCouponDetailDO2.setOrderCode("LEM520250321171536001900608");
//        orderCouponDetailDO2.setOrderItemCode("1903012605357809665");
//        orderCouponDetailDO2.setUpdatedTime(LocalDateTime.now());
//        orderCouponDetailDO2.setStatus(3);
//        orderCouponDetailDO2.setCouponCode("94643b1404ff0134");
//
//        orderCouponDetailDO3 = new OrderCouponDetailDO();
//        orderCouponDetailDO3.setOrderCode("LEM520250321171536001900608");
//        orderCouponDetailDO3.setOrderItemCode("1903012605357809664");
//        orderCouponDetailDO3.setUpdatedTime(LocalDateTime.now());
//        orderCouponDetailDO3.setStatus(3);
//        orderCouponDetailDO3.setCouponCode("7498423232419433");
//
//
//        orderItemDO.setItemStatus(1);
//        orderItemDO.setOrderItemCode("1903012605357809664");
//        orderItemDO.setOrderCode("LEM520250321171536001900608");
//        orderItemDO.setProductQuantity(3);
//
//        orderItemDO2.setItemStatus(1);
//        orderItemDO2.setOrderItemCode("1903012605357809664");
//        orderItemDO2.setOrderCode("LEM520250321171536001900608");
//        orderItemDO2.setProductQuantity(2);
//
//        orderItemDO3.setItemStatus(3);
//        orderItemDO3.setOrderItemCode("1903012605357809664");
//        orderItemDO3.setOrderCode("LEM520250321171536001900608");
//        orderItemDO3.setProductQuantity(2);
//
//        orderInfoDO = new OrderInfoDO();
//        orderInfoDO.setOrderCode("LEM520250321103734001900608");
//        orderInfoDO.setOrderStatus(OrderStatusEnum.PAID.getCode());
//    }
//
//    @Test
//    public void testDealWithCouponStatus_UsedCoupon() {
//        couponStatusNotifyDto.setCouponStatus(5); // USED
//        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
//        when(orderItemDOMapper.selectList(any())).thenReturn(List.of(orderItemDO, orderItemDO2, orderItemDO3));
//        when(orderCouponDetailDOMapper.selectList(any())).thenReturn(List.of( orderCouponDetailDO2,orderCouponDetailDO3));
//        when(orderInfoDOMapper.selectOne(any())).thenReturn(orderInfoDO);
//        doNothing().when(transactionOperator).updateOrderCouponAndOrderInfo(any(), any(), any());
//        //boolean result = couponStatusUpdateService.dealWithCouponStatus(couponStatusNotifyDto);
//        //assertTrue(result);
//    }
//
//    @Test
//    public void testDealWithCouponStatus_UnsupportedStatus() {
//        couponStatusNotifyDto.setCouponStatus(999); // 不支持的状态
////        boolean result = couponStatusUpdateService.dealWithCouponStatus(couponStatusNotifyDto);
////        assertFalse(result);
//    }
//
//}

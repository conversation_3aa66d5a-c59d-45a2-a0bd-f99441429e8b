package com.jlr.ecp.order.service.cart;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.inventory.api.InventoryOrderApi;
import com.jlr.ecp.inventory.dto.SkuStockRespDTO;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.app.cart.dto.CartGroupedBylineReq;
import com.jlr.ecp.order.controller.app.cart.vo.ShopCartMultiTypeProdVo;
import com.jlr.ecp.order.controller.app.cart.vo.ShoppingCarItemVO;
import com.jlr.ecp.order.controller.app.order.vo.SeriesMappingVO;
import com.jlr.ecp.order.dal.dataobject.cart.ShoppingCarItemDO;
import com.jlr.ecp.order.dal.mysql.cart.ShoppingCarItemMapper;
import com.jlr.ecp.order.enums.cart.CartItemTypeEnum;
import com.jlr.ecp.order.enums.cart.ProductShelfStatusEnum;
import com.jlr.ecp.product.api.product.ProductApi;
import com.jlr.ecp.product.api.product.vo.*;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.*;

import static com.jlr.ecp.order.service.Constant.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ShoppingCarItemServiceImplTest {

    @Mock
    private ShoppingCarItemMapper mockCarItemMapper;
    @Mock
    private ProductApi mockProductApi;
    @Mock
    private RedisService mockRedisService; // 确保已声明 RedisService 的 Mock
    @Mock
    private InventoryOrderApi mockInventoryOrderApi;

    @InjectMocks
    private ShoppingCarItemServiceImpl shoppingCarItemServiceImplUnderTest;
    
    @Before
    public void setUp() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), ShoppingCarItemDO.class);
    }

    // 测试正常删除流程
    @Test
    public void testDeleteByConsumerCodeAndICR_SuccessfulDeletion() {
        // Arrange
        String consumerCode = "consumer123";
        String incontrolId = "icr456";
        String brandCode = "brand789";

        // 模拟mapper返回成功
        Mockito.when(mockCarItemMapper.update(Mockito.any(), Mockito.any())).thenReturn(1);

        // Act
        Boolean result = shoppingCarItemServiceImplUnderTest.deleteByConsumerCodeAndICR(
                consumerCode, incontrolId, brandCode);

        // Assert
        assertTrue(result);

        // 验证update方法被正确调用
        ArgumentCaptor<ShoppingCarItemDO> entityCaptor = ArgumentCaptor.forClass(ShoppingCarItemDO.class);
        ArgumentCaptor<LambdaUpdateWrapper<ShoppingCarItemDO>> wrapperCaptor = ArgumentCaptor.forClass(LambdaUpdateWrapper.class);

        Mockito.verify(mockCarItemMapper).update(entityCaptor.capture(), wrapperCaptor.capture());

        // 验证实体对象
        ShoppingCarItemDO updatedEntity = entityCaptor.getValue();
        assertTrue(updatedEntity.getIsDeleted());

        // 验证Wrapper条件
        LambdaUpdateWrapper<ShoppingCarItemDO> wrapper = wrapperCaptor.getValue();
        assertNotNull(wrapper);

        // 验证eq条件
        assertEquals(null, wrapper.getParamNameValuePairs().get("eq1"));
    }

    // 测试删除时发生异常
    @Test(expected = ServiceException.class)
    public void testDeleteByConsumerCodeAndICR_ThrowsException() {
        // Arrange
        String consumerCode = "consumer123";
        String incontrolId = "icr456";
        String brandCode = "brand789";

        // 模拟mapper抛出异常
        Mockito.doThrow(new RuntimeException("DB error"))
                .when(mockCarItemMapper)
                .update(Mockito.any(), Mockito.any());

        // Act
        try {
            shoppingCarItemServiceImplUnderTest.deleteByConsumerCodeAndICR(
                    consumerCode, incontrolId, brandCode);
        } finally {
            // 验证update确实被调用
            Mockito.verify(mockCarItemMapper).update(Mockito.any(), Mockito.any());
        }
    }

    @Test
    public void testProcessShoppingCarItemForCount_NullList_ReturnsZero() {
        // Arrange
        List<ShoppingCarItemVO> nullList = null;
        List<String> icrAccountList = Arrays.asList("account1");

        // Act
        Integer result = shoppingCarItemServiceImplUnderTest.processShoppingCarItemForCount(nullList);

        // Assert
        assertEquals(0, result.intValue());
    }

    @Test
    public void testProcessShoppingCarItemForCount_NoVCSItems_ReturnsTotalQuantity() {
        // Arrange
        ShoppingCarItemVO item1 = new ShoppingCarItemVO();
        item1.setBusinessCode("OTHER");
        item1.setQuantity(2);

        ShoppingCarItemVO item2 = new ShoppingCarItemVO();
        item2.setBusinessCode("ANOTHER");
        item2.setQuantity(3);

        List<ShoppingCarItemVO> itemList = Arrays.asList(item1, item2);
        List<String> icrAccountList = Arrays.asList("account1");

        // Act
        Integer result = shoppingCarItemServiceImplUnderTest.processShoppingCarItemForCount(itemList);

        // Assert
        assertEquals(5, result.intValue());
    }

    @Test
    public void testProcessShoppingCarItemForCount_MixedItemsWithVCSAndOtherBusinessCodes_CalculatesCorrectTotal() {
        // Arrange
        // VCS items
        ShoppingCarItemVO vcsItem1 = new ShoppingCarItemVO();
        vcsItem1.setBusinessCode(BusinessIdEnum.VCS.getCode());
        vcsItem1.setQuantity(2);
        vcsItem1.setIncontrolId("account1");

        ShoppingCarItemVO vcsItem2 = new ShoppingCarItemVO();
        vcsItem2.setBusinessCode(BusinessIdEnum.VCS.getCode());
        vcsItem2.setQuantity(3);
        vcsItem2.setIncontrolId("account2");

        // Other business item
        ShoppingCarItemVO otherItem = new ShoppingCarItemVO();
        otherItem.setBusinessCode("OTHER");
        otherItem.setQuantity(5);

        List<ShoppingCarItemVO> itemList = Arrays.asList(vcsItem1, vcsItem2, otherItem);
        List<String> icrAccountList = Arrays.asList("account1");

        // Act
        Integer result = shoppingCarItemServiceImplUnderTest.processShoppingCarItemForCount(itemList);

        // Assert
        assertEquals(10, result.intValue()); // VCS account1 (2) + other (5)
    }

    @Test
    public void testProcessShoppingCarItemForCount_VCSItemsWithMatchingIcrAccounts_IncludesFilteredVCSItems() {
        // Arrange
        ShoppingCarItemVO vcsItem1 = new ShoppingCarItemVO();
        vcsItem1.setBusinessCode(BusinessIdEnum.VCS.getCode());
        vcsItem1.setQuantity(3);
        vcsItem1.setIncontrolId("account1");

        ShoppingCarItemVO vcsItem2 = new ShoppingCarItemVO();
        vcsItem2.setBusinessCode(BusinessIdEnum.VCS.getCode());
        vcsItem2.setQuantity(2);
        vcsItem2.setIncontrolId("account2");

        List<ShoppingCarItemVO> itemList = Arrays.asList(vcsItem1, vcsItem2);
        List<String> icrAccountList = Arrays.asList("account1", "account3");

        // Act
        Integer result = shoppingCarItemServiceImplUnderTest.processShoppingCarItemForCount(itemList);

        // Assert
        assertEquals(5, result.intValue()); // Only account1 matches
    }

    @Test
    public void testFindCartList() {
        // Setup
        final ShoppingCarItemVO shoppingCarItemVO = getShoppingCarItemVO();
        final List<ShoppingCarItemVO> expectedResult = List.of(shoppingCarItemVO);

        // Configure ShoppingCarItemMapper.selectList(...).
        final List<ShoppingCarItemDO> shoppingCarItemDOS = List.of(ShoppingCarItemDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .cartItemCode(CART_ITEM_CODE)
                .cartItemType(0)
                .productCode(PRODUCT_CODE)
                .quantity(0)
                .brandCode(BRAND_CODE)
                .build());
        when(mockCarItemMapper.selectList(any())).thenReturn(shoppingCarItemDOS);

        // Configure ProductApi.getProductCartView(...).
        final ProductCartViewVO productCartViewVO = getProductCartViewVO();
        final CommonResult<List<ProductCartViewVO>> listCommonResult = CommonResult.success(List.of(productCartViewVO));
        final ProductCodeListVO productCodeListVO = new ProductCodeListVO();
        productCodeListVO.setProductCodeList(List.of(PRODUCT_CODE));
        when(mockProductApi.getProductCartView(productCodeListVO)).thenReturn(listCommonResult);

        // 关键修复：模拟 Redis 返回空 Map
        when(mockRedisService.getCacheMap(Constants.REDIS_KEY.SERIES_CACHE_KEY)).thenReturn(new HashMap<>());


        // Run the test
        final List<ShoppingCarItemVO> result = shoppingCarItemServiceImplUnderTest.findCartList(CONSUMER_CODE,
                BRAND_CODE, "");

        // Verify the results
        assertThat(result.get(0).getConsumerCode()).isEqualTo(expectedResult.get(0).getConsumerCode());
    }

    @NotNull
    private static ProductCartViewVO getProductCartViewVO() {
        final ProductCartViewVO productCartViewVO = new ProductCartViewVO();
        productCartViewVO.setProductCode(PRODUCT_CODE);
        productCartViewVO.setProductName(PRODUCT_NAME);
        productCartViewVO.setIsDeleted(false);
        productCartViewVO.setShelfStatus(0);
        final ProductDetailReqVO productDetailReqVO1 = new ProductDetailReqVO();
        productCartViewVO.setProductDetailReqVO(productDetailReqVO1);
        final ProductSkuRespVO productSkuRespVO1 = new ProductSkuRespVO();
        productCartViewVO.setProductAttr(List.of(productSkuRespVO1));
        final ProductRelationInfoReqVO productRelationInfoReqVO1 = new ProductRelationInfoReqVO();
        productCartViewVO.setPolicyInfo(List.of(productRelationInfoReqVO1));
        return productCartViewVO;
    }

    @NotNull
    private static ShoppingCarItemVO getShoppingCarItemVO() {
        final ShoppingCarItemVO shoppingCarItemVO = new ShoppingCarItemVO();
        shoppingCarItemVO.setId(0L);
        shoppingCarItemVO.setCartItemType(0);
        shoppingCarItemVO.setConsumerCode(CONSUMER_CODE);
        shoppingCarItemVO.setCartItemCode(CART_ITEM_CODE);
        shoppingCarItemVO.setProductCode(PRODUCT_CODE);
        shoppingCarItemVO.setProductName(PRODUCT_NAME);
        shoppingCarItemVO.setQuantity(0);
        final ProductSkuRespVO productSkuRespVO = new ProductSkuRespVO();
        shoppingCarItemVO.setSkuList(List.of(productSkuRespVO));
        shoppingCarItemVO.setIsDeleted(false);
        final ProductDetailReqVO productDetailReqVO = new ProductDetailReqVO();
        shoppingCarItemVO.setProductDetailReqVO(productDetailReqVO);
        shoppingCarItemVO.setShelfStatus(0);
        final ProductRelationInfoReqVO productRelationInfoReqVO = new ProductRelationInfoReqVO();
        shoppingCarItemVO.setPolicyInfo(List.of(productRelationInfoReqVO));
        return shoppingCarItemVO;
    }

    @Test
    public void testCountCartNum() {
        // Setup
        // Configure ShoppingCarItemMapper.selectList(...).
        List<ShoppingCarItemDO> shoppingCarItemDOS = List.of(ShoppingCarItemDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .cartItemCode(CART_ITEM_CODE)
                .cartItemType(0)
                .productCode(PRODUCT_CODE)
                .quantity(0)
                .brandCode(BRAND_CODE)
                .build());
        when(mockCarItemMapper.selectList(any())).thenReturn(shoppingCarItemDOS);

        // Run the test
        final Integer result = shoppingCarItemServiceImplUnderTest.countCartNum(CONSUMER_CODE, BRAND_CODE);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    public void testDelete() {
        // Setup
        // Run the test
        final Boolean result = shoppingCarItemServiceImplUnderTest.delete(List.of("value"));

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testDeleteByConsumerCode() {
        // Setup
        // Run the test
        final Boolean result = shoppingCarItemServiceImplUnderTest.deleteByConsumerCode(CONSUMER_CODE);

        // Verify the results
        assertThat(result).isTrue();
        ShoppingCarItemDO itemDO = new ShoppingCarItemDO();
        itemDO.setConsumerCode(CONSUMER_CODE);
        itemDO.setIsDeleted(true);
        verify(mockCarItemMapper).update(any(), any(LambdaUpdateWrapper.class));
    }

    @Test
    public void testProductByServiceLineList() {
        // 准备基础测试数据
        String jlrId = "test_jlr";
        CartGroupedBylineReq req = new CartGroupedBylineReq();
        req.setIncontrolNameList(Arrays.asList("icr1", "icr2"));

        // 模拟数据库返回购物车项
        ShoppingCarItemDO item1 = ShoppingCarItemDO.builder()
                .consumerCode(jlrId)
                .productCode("P001")
                .incontrolId("icr1")
                .seriesCode("SERIES_001")
                .cartItemType(CartItemTypeEnum.BRAND_GOODS.getCode())
                .build();
        item1.setCreatedTime(LocalDateTime.now());

        ShoppingCarItemDO item2 = ShoppingCarItemDO.builder()
                .consumerCode(jlrId)
                .productCode("P002")
                .build();
        item2.setCreatedTime(LocalDateTime.now());

        when(mockCarItemMapper.selectList(any())).thenReturn(Arrays.asList(item1, item2));

        // 确保商品数据包含 skuList
        ProductCartViewVO vcsProduct = new ProductCartViewVO();
        vcsProduct.setProductCode("P001");
        vcsProduct.setBusinessCode(BusinessIdEnum.VCS.getCode());
        vcsProduct.setShelfStatus(ProductShelfStatusEnum.UP.getCode());
        vcsProduct.setProductAttr(List.of(new ProductSkuRespVO()));

        ProductCartViewVO otherProduct = new ProductCartViewVO();
        otherProduct.setProductCode("P002");
        otherProduct.setBusinessCode(BusinessIdEnum.BRAND_GOODS.getCode()); // 明确设置业务代码
        otherProduct.setShelfStatus(ProductShelfStatusEnum.UP.getCode());
        otherProduct.setProductAttr(Collections.emptyList());

        when(mockProductApi.getProductCartView(any()))
                .thenReturn(CommonResult.success(Arrays.asList(vcsProduct, otherProduct)));

        // 模拟Redis系列数据
        SeriesMappingVO seriesMapping = new SeriesMappingVO();
        seriesMapping.setSeriesName("Test Series");
        when(mockRedisService.getCacheMap(Constants.REDIS_KEY.SERIES_CACHE_KEY))
                .thenReturn(Collections.singletonMap("SERIES_001", JSON.toJSONString(seriesMapping)));

        // 模拟库存服务返回
        lenient().when(mockInventoryOrderApi.querySkuStock(any()))
                .thenReturn(CommonResult.success(Collections.singletonList(
                        new SkuStockRespDTO("MODEL_001", 5)
                )));

        // 执行测试
        ShopCartMultiTypeProdVo result = shoppingCarItemServiceImplUnderTest.productByServiceLineList(jlrId, req);

        // 验证结果结构
        Map<String, List<ShoppingCarItemVO>> businessMap = result.getBusinessCodeCarItemMap();
        assertThat(businessMap).containsKeys(BusinessIdEnum.VCS.getCode(), Constants.MIXED);

        // 验证VCS业务线
        List<ShoppingCarItemVO> vcsItems = businessMap.get(BusinessIdEnum.VCS.getCode());
        assertThat(vcsItems)
                .hasSize(1)
                .allSatisfy(item -> {
                    assertThat(item.getBusinessCode()).isEqualTo(BusinessIdEnum.VCS.getCode());
                    assertThat(item.getSeriesName()).isEqualTo("Test Series");
                });

        // 验证MIXED业务线
        List<ShoppingCarItemVO> mixedItems = businessMap.get(Constants.MIXED);
        assertThat(mixedItems)
                .hasSize(1)
                .allSatisfy(item -> {
                    assertThat(item.getBusinessCode()).isEqualTo(BusinessIdEnum.BRAND_GOODS.getCode());
                });
    }

    @Test
    public void testProductByServiceLineList_WithoutICRAccounts() {
        // 准备VCS商品但无ICR账号
        ShoppingCarItemDO vcsItem = ShoppingCarItemDO.builder()
                .consumerCode("test_jlr")
                .productCode("P004")
                .incontrolId("icr4")
                .build();

        when(mockCarItemMapper.selectList(any())).thenReturn(Collections.singletonList(vcsItem));

        // 执行测试（不传ICR账号）
        ShopCartMultiTypeProdVo result = shoppingCarItemServiceImplUnderTest.productByServiceLineList("test_jlr", null);

        // 验证VCS商品被过滤
        assertThat(result.getBusinessCodeCarItemMap())
                .doesNotContainKey(BusinessIdEnum.VCS.getCode());
    }

    @Test
    public void testProductByServiceLineList_WithEmptyCart() {
        // 模拟空购物车
        when(mockCarItemMapper.selectList(any())).thenReturn(Collections.emptyList());

        // 执行测试
        ShopCartMultiTypeProdVo result = shoppingCarItemServiceImplUnderTest.productByServiceLineList("empty_user", new CartGroupedBylineReq());

        // 验证空结果
        assertThat(result.getBusinessCodeCarItemMap()).isEmpty();
    }

}

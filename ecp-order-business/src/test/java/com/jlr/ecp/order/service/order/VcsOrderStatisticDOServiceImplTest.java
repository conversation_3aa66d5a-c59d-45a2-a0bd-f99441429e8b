package com.jlr.ecp.order.service.order;

import com.alibaba.fastjson.JSON;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppTabsVO;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.controller.app.order.vo.SeriesMappingVO;
import com.jlr.ecp.order.dal.dataobject.order.VcsOrderStatisticDO;
import com.jlr.ecp.order.dal.mysql.order.VcsOrderStatisticDOMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static com.jlr.ecp.order.service.Constant.*;

@RunWith(MockitoJUnitRunner.class)
public class VcsOrderStatisticDOServiceImplTest {

    @Mock
    private VcsOrderStatisticDOMapper mockVcsOrderStatisticDOMapper;
    @Mock
    private RedisService mockRedisService;

    @InjectMocks
    private VcsOrderStatisticDOServiceImpl vcsOrderStatisticDOServiceImplUnderTest;

    @Test
    public void testGetTabs() {
        // Setup
        final OrderAppTabsVO orderAppTabsVO = new OrderAppTabsVO();
        orderAppTabsVO.setBrandNameView(BRAND_NAME_VIEW);
        orderAppTabsVO.setSeriesCodes(List.of(SERIES_CODE));
        final List<OrderAppTabsVO> expectedResult = List.of(orderAppTabsVO);

        // Configure VcsOrderStatisticDOMapper.selectList(...).
        final List<VcsOrderStatisticDO> vcsOrderStatisticDOS = List.of(VcsOrderStatisticDO.builder()
                .consumerCode(CONSUMER_CODE)
                .seriesName(BRAND_NAME_VIEW)
                .seriesCode(SERIES_CODE)
                .brandCode(BRAND_CODE)
                .build());
        when(mockVcsOrderStatisticDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderStatisticDOS);
        final SeriesMappingVO seriesMappingVO = SeriesMappingVO.builder()
                .seriesCode(SERIES_CODE)
                .seriesName(SERIES_NAME)
                .brandNameView(BRAND_NAME_VIEW)
                .build();
        when(mockRedisService.getMultiCacheMapValue("global:series:mapping", Set.of(SERIES_CODE)))
                .thenReturn(List.of(JSON.toJSONString(seriesMappingVO)));

        // Run the test
        final List<OrderAppTabsVO> result = vcsOrderStatisticDOServiceImplUnderTest.getTabs(CONSUMER_CODE, CLIENT_ID);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetTabsVcsOrderStatisticDOMapperReturnsNoItems() {
        // Setup
        when(mockVcsOrderStatisticDOMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<OrderAppTabsVO> result = vcsOrderStatisticDOServiceImplUnderTest.getTabs(CONSUMER_CODE, CLIENT_ID);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetTabsRedisServiceReturnsNoItems() {
        // Setup
        // Configure VcsOrderStatisticDOMapper.selectList(...).
        final List<VcsOrderStatisticDO> vcsOrderStatisticDOS = List.of(VcsOrderStatisticDO.builder()
                .consumerCode(CONSUMER_CODE)
                .seriesName(BRAND_NAME_VIEW)
                .seriesCode(SERIES_CODE)
                .brandCode(BRAND_CODE)
                .build());
        when(mockVcsOrderStatisticDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderStatisticDOS);

        // Run the test
        final List<OrderAppTabsVO> result = vcsOrderStatisticDOServiceImplUnderTest.getTabs(CONSUMER_CODE, CLIENT_ID);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}

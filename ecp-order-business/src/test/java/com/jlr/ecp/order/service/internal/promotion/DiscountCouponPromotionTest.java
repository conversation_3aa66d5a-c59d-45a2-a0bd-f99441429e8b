
package com.jlr.ecp.order.service.internal.promotion;


import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.service.internal.promotion.dto.PromotionRespDto;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.wildfly.common.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class DiscountCouponPromotionTest {

    private DiscountCouponPromotion discountCouponPromotionUnderTest;

    @Before
    public void setUp() {
        discountCouponPromotionUnderTest = new DiscountCouponPromotion();
    }

    @Test
    public void executePromotional_NoSkuCanUsePromotion_ReturnsNull() {
        // 设置
        final CartProductSkuInfo cartProductSkuInfo = new CartProductSkuInfo();
        cartProductSkuInfo.setProductCode("productCode");
        cartProductSkuInfo.setProductSkuCode("productSkuCode");
        cartProductSkuInfo.setSalePrice("100");
        cartProductSkuInfo.setCouponModuleCodeList(Arrays.asList("otherCode"));
        final List<CartProductSkuInfo> skuInfos = Arrays.asList(cartProductSkuInfo);
        final PromotionDto promotion = new PromotionDto();
        promotion.setCouponModelCode("couponModelCode");
        promotion.setDiscountPercent(10.0);

        // 运行测试
        final PromotionRespDto result = discountCouponPromotionUnderTest.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void executePromotional_SkuCanUsePromotion_ReturnsPromotionRespDto() {
        // 设置
        final CartProductSkuInfo cartProductSkuInfo = new CartProductSkuInfo();
        cartProductSkuInfo.setProductCode("productCode");
        cartProductSkuInfo.setProductSkuCode("productSkuCode");
        cartProductSkuInfo.setSalePrice("100");
        cartProductSkuInfo.setJoinCalculateFlag(true);
        cartProductSkuInfo.setCouponModuleCodeList(Arrays.asList("couponModelCode"));
        final List<CartProductSkuInfo> skuInfos = Arrays.asList(cartProductSkuInfo);
        final PromotionDto promotion = new PromotionDto();
        promotion.setCouponModelCode("couponModelCode");
        promotion.setDiscountPercent(10.0);

        // 运行测试
        final PromotionRespDto result = discountCouponPromotionUnderTest.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNotNull(result);
    }

}
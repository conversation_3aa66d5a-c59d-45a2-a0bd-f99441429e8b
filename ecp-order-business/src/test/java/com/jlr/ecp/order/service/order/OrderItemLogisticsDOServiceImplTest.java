package com.jlr.ecp.order.service.order;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.logistics.api.packageinfo.PackageInfoApi;
import com.jlr.ecp.logistics.api.packageinfo.dto.PackageInfoQueryDTO;
import com.jlr.ecp.logistics.api.packageinfo.dto.PackageInfoQueryRespDTO;
import com.jlr.ecp.logistics.api.packageinfo.dto.QueryTrackData;
import com.jlr.ecp.order.controller.app.order.dto.OrderLogisticsQryDTO;
import com.jlr.ecp.order.controller.app.order.vo.OrderLogisticsQryRespVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemLogisticsDO;
import com.jlr.ecp.order.dal.mysql.order.OrderItemLogisticsDOMapper;
import com.jlr.ecp.order.util.PIPLDataUtil;
import com.jlr.ecp.system.api.address.AddressConfigApi;
import com.jlr.ecp.system.api.address.dto.AddressNameDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.wildfly.common.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class OrderItemLogisticsDOServiceImplTest {

    @InjectMocks
    private OrderItemLogisticsDOServiceImpl orderItemLogisticsDOService;

    @Mock
    private OrderItemLogisticsDOMapper orderItemLogisticsDOMapper;

    @Mock
    private PackageInfoApi packageInfoApi;

    @Mock
    private AddressConfigApi addressConfigApi;

    @Mock
    private PIPLDataUtil piplDataUtil;

    private OrderLogisticsQryDTO orderLogisticsQryDTO;

    @Before
    public void setUp() {
        orderLogisticsQryDTO = new OrderLogisticsQryDTO();
        orderLogisticsQryDTO.setOrderCode("12345");
        orderLogisticsQryDTO.setNumber("67890");
        orderLogisticsQryDTO.setCompany("company");
    }

    @Test
    public void queryOrderLogisticsInfo_WithValidOrderCodeAndNumber_ReturnsOrderLogisticsQryRespVO() {
        OrderItemLogisticsDO orderItemLogisticsDO = new OrderItemLogisticsDO();
        orderItemLogisticsDO.setRecipient("encodedRecipient");
        orderItemLogisticsDO.setRecipientPhone("encodedPhone");
        orderItemLogisticsDO.setDetailAddress("encodedAddress");
        orderItemLogisticsDO.setLogisticsCompany("company");
        orderItemLogisticsDO.setProvinceCode("1");
        orderItemLogisticsDO.setCityCode("2");
        orderItemLogisticsDO.setAreaCode("3");

        List<OrderItemLogisticsDO> orderItemLogisticsDOList = new ArrayList<>();
        orderItemLogisticsDOList.add(orderItemLogisticsDO);

        when(orderItemLogisticsDOMapper.selectList(any(QueryWrapper.class))).thenReturn(orderItemLogisticsDOList);

        AddressNameDTO addressNameDTO = new AddressNameDTO();
        addressNameDTO.setProvince("province");
        addressNameDTO.setCity("city");
        addressNameDTO.setArea("area");

        when(addressConfigApi.getNameByCode("1", "2", "3")).thenReturn(CommonResult.success(addressNameDTO));

        PackageInfoQueryRespDTO packageInfoQueryRespDTO = new PackageInfoQueryRespDTO();
        List<QueryTrackData> queryTrackDataList = new ArrayList<>();
        QueryTrackData queryTrackData = new QueryTrackData();
        queryTrackData.setFtime("2023-01-01 12:00:00");
        queryTrackData.setContext("context");
        queryTrackData.setStatus("status");
        queryTrackDataList.add(queryTrackData);
        packageInfoQueryRespDTO.setData(queryTrackDataList);

        when(packageInfoApi.queryPackageInfo(any(PackageInfoQueryDTO.class))).thenReturn(CommonResult.success(packageInfoQueryRespDTO));

        when(piplDataUtil.getDecodeText("encodedRecipient")).thenReturn("decodedRecipient");
        when(piplDataUtil.getDecodeText("encodedPhone")).thenReturn("decodedPhone");
        when(piplDataUtil.getDecodeText("encodedAddress")).thenReturn("decodedAddress");

        OrderLogisticsQryRespVO result = orderItemLogisticsDOService.queryOrderLogisticsInfo(orderLogisticsQryDTO);

        assertNotNull(result);
    }

    @Test
    public void queryOrderLogisticsInfo_WithEmptyOrderCodeAndNumber_ReturnsEmptyOrderLogisticsQryRespVO() {
        orderLogisticsQryDTO.setOrderCode("");
        orderLogisticsQryDTO.setNumber("");

        when(orderItemLogisticsDOMapper.selectList(any(QueryWrapper.class))).thenReturn(new ArrayList<>());

        OrderLogisticsQryRespVO result = orderItemLogisticsDOService.queryOrderLogisticsInfo(orderLogisticsQryDTO);

        assertNotNull(result);
    }

    @Test
    public void queryOrderLogisticsInfo_WithInvalidDate_ReturnsOrderLogisticsQryRespVOWithNullDate() {
        OrderItemLogisticsDO orderItemLogisticsDO = new OrderItemLogisticsDO();
        orderItemLogisticsDO.setRecipient("encodedRecipient");
        orderItemLogisticsDO.setRecipientPhone("encodedPhone");
        orderItemLogisticsDO.setDetailAddress("encodedAddress");
        orderItemLogisticsDO.setLogisticsCompany("company");
        orderItemLogisticsDO.setProvinceCode("1");
        orderItemLogisticsDO.setCityCode("2");
        orderItemLogisticsDO.setAreaCode("3");

        List<OrderItemLogisticsDO> orderItemLogisticsDOList = new ArrayList<>();
        orderItemLogisticsDOList.add(orderItemLogisticsDO);

        when(orderItemLogisticsDOMapper.selectList(any(QueryWrapper.class))).thenReturn(orderItemLogisticsDOList);

        AddressNameDTO addressNameDTO = new AddressNameDTO();
        addressNameDTO.setProvince("province");
        addressNameDTO.setCity("city");
        addressNameDTO.setArea("area");

        when(addressConfigApi.getNameByCode("1", "2", "3")).thenReturn(CommonResult.success(addressNameDTO));

        PackageInfoQueryRespDTO packageInfoQueryRespDTO = new PackageInfoQueryRespDTO();
        List<QueryTrackData> queryTrackDataList = new ArrayList<>();
        QueryTrackData queryTrackData = new QueryTrackData();
        queryTrackData.setFtime("invalid-date");
        queryTrackData.setContext("context");
        queryTrackData.setStatus("status");
        queryTrackDataList.add(queryTrackData);
        packageInfoQueryRespDTO.setData(queryTrackDataList);

        when(packageInfoApi.queryPackageInfo(any(PackageInfoQueryDTO.class))).thenReturn(CommonResult.success(packageInfoQueryRespDTO));

        when(piplDataUtil.getDecodeText("encodedRecipient")).thenReturn("decodedRecipient");
        when(piplDataUtil.getDecodeText("encodedPhone")).thenReturn("decodedPhone");
        when(piplDataUtil.getDecodeText("encodedAddress")).thenReturn("decodedAddress");

        OrderLogisticsQryRespVO result = orderItemLogisticsDOService.queryOrderLogisticsInfo(orderLogisticsQryDTO);

        assertNotNull(result);
    }
}

package com.jlr.ecp.order.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.dal.dataobject.order.OrderDiscountDetailDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderDiscountDetailDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.enums.payment.DiscountTypeEnum;
import com.jlr.ecp.order.enums.payment.PayTypeEnum;
import com.jlr.ecp.payment.api.order.vo.SubmitPayOrderReq;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.wildfly.common.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class SubmitPayOrderComponentTest {

    @Mock
    private OrderDiscountDetailDOMapper mockOrderDiscountDetailDOMapper;
    @Mock
    private OrderItemDOMapper mockOrderItemDOMapper;

    @InjectMocks
    private SubmitPayOrderComponent submitPayOrderComponentUnderTest;

    @Before
    public void setUp() {
    }

    @Test
    public void testProcessToOrderItemList() {
        // Setup
        final OrderInfoDO orderInfo = OrderInfoDO.builder()
                .orderCode("orderCode")
                .freightAmount(0)
                .businessCode("businessCode")
                .build();
        final List<OrderInfoDO> childOrders = List.of(OrderInfoDO.builder()
                .orderCode("orderCode")
                .freightAmount(0)
                .businessCode("businessCode")
                .build());
        final SubmitPayOrderReq.OrderItem orderItem = new SubmitPayOrderReq.OrderItem();
        orderItem.setItemCode("couponCode");
        orderItem.setItemModelCode("couponModelCode");
        orderItem.setItemType("code");
        orderItem.setItemValue(0L);
        orderItem.setPayAmount(0L);
        orderItem.setProductName("productName");
        orderItem.setProductNo("productCode");
        orderItem.setBusinessLine("businessLineCode");
        final List<SubmitPayOrderReq.OrderItem> expectedResult = List.of(orderItem);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> orderItemDOS = List.of(OrderItemDO.builder()
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productCode("productCode")
                .productName("productName")
                .costAmount(0)
                .build());
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDOS);

        // Configure OrderDiscountDetailDOMapper.selectOne(...).
        final OrderDiscountDetailDO discountDetailDO = OrderDiscountDetailDO.builder()
                .orderItemCode("orderItemCode")
                .discountType(0)
                .couponCode("couponCode")
                .couponModelCode("couponModelCode")
                .costPoints(0)
                .discountAmount(0)
                .build();
        when(mockOrderDiscountDetailDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(discountDetailDO);

        // Run the test
        final List<SubmitPayOrderReq.OrderItem> result = submitPayOrderComponentUnderTest.processToOrderItemList(
                orderInfo, childOrders, Set.of("value"));

        // Verify the results
        assertNotNull(result);
    }

    @Test
    public void testProcessToOrderItemList_OrderItemDOMapperReturnsNoItems() {
        // Setup
        final OrderInfoDO orderInfo = OrderInfoDO.builder()
                .orderCode("orderCode")
                .freightAmount(0)
                .businessCode("businessCode")
                .build();
        final List<OrderInfoDO> childOrders = List.of(OrderInfoDO.builder()
                .orderCode("orderCode")
                .freightAmount(0)
                .businessCode("businessCode")
                .build());
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.emptyList());

        // Configure OrderDiscountDetailDOMapper.selectOne(...).
        final OrderDiscountDetailDO discountDetailDO = OrderDiscountDetailDO.builder()
                .orderItemCode("orderItemCode")
                .discountType(0)
                .couponCode("couponCode")
                .couponModelCode("couponModelCode")
                .costPoints(0)
                .discountAmount(0)
                .build();

        // Run the test
        final List<SubmitPayOrderReq.OrderItem> result = submitPayOrderComponentUnderTest.processToOrderItemList(
                orderInfo, childOrders, Set.of("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testProcessOrderItem() {
        // Setup
        final OrderInfoDO childOrderOrderInfoDo = OrderInfoDO.builder()
                .orderCode("orderCode")
                .freightAmount(0)
                .businessCode("businessCode")
                .build();
        final SubmitPayOrderReq.OrderItem orderItem = new SubmitPayOrderReq.OrderItem();
        orderItem.setItemCode("couponCode");
        orderItem.setItemModelCode("couponModelCode");
        orderItem.setItemType("code");
        orderItem.setItemValue(0L);
        orderItem.setPayAmount(0L);
        orderItem.setProductName("productName");
        orderItem.setProductNo("productCode");
        orderItem.setBusinessLine("businessLineCode");
        final List<SubmitPayOrderReq.OrderItem> expectedResult = List.of(orderItem);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> orderItemDOS = List.of(OrderItemDO.builder()
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productCode("productCode")
                .productName("productName")
                .costAmount(0)
                .build());
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDOS);

        // Configure OrderDiscountDetailDOMapper.selectOne(...).
        final OrderDiscountDetailDO discountDetailDO = OrderDiscountDetailDO.builder()
                .orderItemCode("orderItemCode")
                .discountType(0)
                .couponCode("couponCode")
                .couponModelCode("couponModelCode")
                .costPoints(0)
                .discountAmount(0)
                .build();
        when(mockOrderDiscountDetailDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(discountDetailDO);

        // Run the test
        final List<SubmitPayOrderReq.OrderItem> result = submitPayOrderComponentUnderTest.processOrderItem(
                childOrderOrderInfoDo, Set.of("value"));

        // Verify the results
        assertNotNull(result);
    }

    @Test
    public void testProcessOrderItem_OrderItemDOMapperReturnsNoItems() {
        // Setup
        final OrderInfoDO childOrderOrderInfoDo = OrderInfoDO.builder()
                .orderCode("orderCode")
                .freightAmount(0)
                .businessCode("businessCode")
                .build();
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(Collections.emptyList());

        // Configure OrderDiscountDetailDOMapper.selectOne(...).
        final OrderDiscountDetailDO discountDetailDO = OrderDiscountDetailDO.builder()
                .orderItemCode("orderItemCode")
                .discountType(0)
                .couponCode("couponCode")
                .couponModelCode("couponModelCode")
                .costPoints(0)
                .discountAmount(0)
                .build();

        // Run the test
        final List<SubmitPayOrderReq.OrderItem> result = submitPayOrderComponentUnderTest.processOrderItem(
                childOrderOrderInfoDo, Set.of("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testProcessOrderDiscountDetail() {
        // Setup
        final OrderDiscountDetailDO orderDiscountDetailDo = OrderDiscountDetailDO.builder()
                .orderItemCode("orderItemCode")
                .discountType(DiscountTypeEnum.COUPON_DISCOUNT.getType())
                .couponCode("couponCode")
                .couponModelCode("couponModelCode")
                .costPoints(0)
                .discountAmount(0)
                .build();
        final OrderItemDO orderItemDo = OrderItemDO.builder()
                .orderItemCode("orderItemCode")
                .orderCode("orderCode")
                .productCode("productCode")
                .productName("productName")
                .costAmount(0)
                .build();
        final OrderInfoDO childOrderOrderInfoDo = OrderInfoDO.builder()
                .orderCode("orderCode")
                .freightAmount(0)
                .businessCode("businessCode")
                .build();
        final SubmitPayOrderReq.OrderItem orderItem = new SubmitPayOrderReq.OrderItem();
        orderItem.setItemCode("couponCode");
        orderItem.setItemModelCode("couponModelCode");
        orderItem.setItemType("code");
        orderItem.setItemValue(0L);
        orderItem.setPayAmount(0L);
        orderItem.setProductName("productName");
        orderItem.setProductNo("productCode");
        orderItem.setBusinessLine("businessLineCode");
        final MultiValueMap<PayTypeEnum, SubmitPayOrderReq.OrderItem> orderItemsMap = new LinkedMultiValueMap<>(
                Map.ofEntries(Map.entry(PayTypeEnum.CASH, List.of(orderItem))));

        // Run the test
        submitPayOrderComponentUnderTest.processOrderDiscountDetail(orderDiscountDetailDo, new HashSet<>(), orderItemDo,
                childOrderOrderInfoDo, orderItemsMap);

        // Verify the results
    }

    @Test
    public void testProcessPayTypeItemsMap() {
        // Setup
        final SubmitPayOrderReq.OrderItem orderItem = new SubmitPayOrderReq.OrderItem();
        orderItem.setItemCode("couponCode");
        orderItem.setItemModelCode("couponModelCode");
        orderItem.setItemType("code");
        orderItem.setItemValue(0L);
        orderItem.setPayAmount(0L);
        orderItem.setProductName("productName");
        orderItem.setProductNo("productCode");
        orderItem.setBusinessLine("businessLineCode");
        final MultiValueMap<PayTypeEnum, SubmitPayOrderReq.OrderItem> orderItemsMap = new LinkedMultiValueMap<>(
                Map.ofEntries(Map.entry(PayTypeEnum.CASH, List.of(orderItem))));
        final SubmitPayOrderReq.OrderItem orderItem1 = new SubmitPayOrderReq.OrderItem();
        orderItem1.setItemCode("couponCode");
        orderItem1.setItemModelCode("couponModelCode");
        orderItem1.setItemType("code");
        orderItem1.setItemValue(0L);
        orderItem1.setPayAmount(0L);
        orderItem1.setProductName("productName");
        orderItem1.setProductNo("productCode");
        orderItem1.setBusinessLine("businessLineCode");
        final List<SubmitPayOrderReq.OrderItem> expectedResult = List.of(orderItem1);

        // Run the test
        final List<SubmitPayOrderReq.OrderItem> result = submitPayOrderComponentUnderTest.processPayTypeItemsMap(
                orderItemsMap);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testCombineMultiItems() {
        // Setup
        final SubmitPayOrderReq.OrderItem orderItem = new SubmitPayOrderReq.OrderItem();
        orderItem.setItemCode("couponCode");
        orderItem.setItemModelCode("couponModelCode");
        orderItem.setItemType("code");
        orderItem.setItemValue(0L);
        orderItem.setPayAmount(0L);
        orderItem.setProductName("productName");
        orderItem.setProductNo("productCode");
        orderItem.setBusinessLine("businessLineCode");
        final List<SubmitPayOrderReq.OrderItem> orderItems = List.of(orderItem, orderItem);
        final SubmitPayOrderReq.OrderItem expectedResult = new SubmitPayOrderReq.OrderItem();
        expectedResult.setItemCode("couponCode");
        expectedResult.setItemModelCode("couponModelCode");
        expectedResult.setItemType("code");
        expectedResult.setItemValue(0L);
        expectedResult.setPayAmount(0L);
        expectedResult.setProductName("productName");
        expectedResult.setProductNo("productCode");
        expectedResult.setBusinessLine("businessLineCode");

        // Run the test
        final SubmitPayOrderReq.OrderItem result = submitPayOrderComponentUnderTest.combineMultiItems(orderItems);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetPaymentCenterBusinessCode() {
        assertThat(submitPayOrderComponentUnderTest.getPaymentCenterBusinessCode("businessCode"))
                .isEqualTo(null);
    }
}

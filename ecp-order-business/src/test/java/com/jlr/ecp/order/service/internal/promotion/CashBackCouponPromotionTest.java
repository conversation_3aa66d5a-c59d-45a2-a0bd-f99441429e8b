package com.jlr.ecp.order.service.internal.promotion;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.util.Arrays;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.service.internal.promotion.dto.PromotionRespDto;

@RunWith(MockitoJUnitRunner.class)
public class CashBackCouponPromotionTest {

    @InjectMocks
    private CashBackCouponPromotion cashBackCouponPromotion;

    @Before
    public void setUp() {
        // 初始化测试数据
    }

    @Test
    public void testExecutePromotional_WithValidPromotion() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("1000", true, "PROMO1"),
            createSkuInfo("2000", true, "PROMO1")
        );
        PromotionDto promotion = createPromotion("PROMO1", "3000", "100");

        // 执行测试
        PromotionRespDto result = cashBackCouponPromotion.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNotNull(result);
        assertEquals("3000.00", result.getDiscountTotalAmount());
        assertEquals("0.00", result.getCostAmount());
        assertEquals(2, result.getCartSkuProductList().size());
        assertEquals(CouponTypeEnum.CASH_BACK, result.getCouponTypeEnum());
        
        // 验证商品优惠金额分配
        List<CartProductSkuInfo> resultSkuInfos = result.getCartSkuProductList();
        assertEquals("1000.00", resultSkuInfos.get(0).getDiscountAmount());
        assertEquals("2000.00", resultSkuInfos.get(1).getDiscountAmount());
    }

    @Test
    public void testExecutePromotional_WithNoEligibleProducts() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("1000", true, "PROMO2"),
            createSkuInfo("2000", true, "PROMO2")
        );
        PromotionDto promotion = createPromotion("PROMO1", "3000", "100");

        // 执行测试
        PromotionRespDto result = cashBackCouponPromotion.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testExecutePromotional_WithAmountBelowThreshold() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("1000", true, "PROMO1"),
            createSkuInfo("1000", true, "PROMO1")
        );
        PromotionDto promotion = createPromotion("PROMO1", "3000", "100");

        // 执行测试
        PromotionRespDto result = cashBackCouponPromotion.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testExecutePromotional_WithMixedProducts() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("1000", true, "PROMO1"),
            createSkuInfo("2000", true, "PROMO1"),
            createSkuInfo("3000", true, "PROMO2")
        );
        PromotionDto promotion = createPromotion("PROMO1", "3000", "100");

        // 执行测试
        PromotionRespDto result = cashBackCouponPromotion.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNotNull(result);
        assertEquals("3000.00", result.getDiscountTotalAmount());
        assertEquals("3000.00", result.getCostAmount());
        assertEquals(3, result.getCartSkuProductList().size());
        
        // 验证商品优惠金额分配
        List<CartProductSkuInfo> resultSkuInfos = result.getCartSkuProductList();
        assertEquals("1000.00", resultSkuInfos.get(0).getDiscountAmount());
        assertEquals("2000.00", resultSkuInfos.get(1).getDiscountAmount());
        assertNull(resultSkuInfos.get(2).getDiscountAmount());
    }

    @Test
    public void testExecutePromotional_WithSingleProduct() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("3000", true, "PROMO1")
        );
        PromotionDto promotion = createPromotion("PROMO1", "3000", "100");

        // 执行测试
        PromotionRespDto result = cashBackCouponPromotion.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNotNull(result);
        assertEquals("3000.00", result.getDiscountTotalAmount());
        assertEquals("0.00", result.getCostAmount());
        assertEquals(1, result.getCartSkuProductList().size());
        
        // 验证商品优惠金额分配
        List<CartProductSkuInfo> resultSkuInfos = result.getCartSkuProductList();
        assertEquals("3000.00", resultSkuInfos.get(0).getDiscountAmount());
    }

    @Test
    public void testCheckUserChoose_WithValidProducts() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("1000", true, "PROMO1"),
            createSkuInfo("2000", true, "PROMO1")
        );
        List<PromotionDto> promotions = Arrays.asList(
            createPromotion("PROMO1", "3000", "100").setCouponCode("test123")
        );
        PromotionDto userChoosePromotion = createPromotion("PROMO1", "3000", "100");
        userChoosePromotion.setCouponCode("test123");
        // 执行测试
        List<CartProductSkuInfo> result = cashBackCouponPromotion.checkUserChoose(skuInfos, promotions, userChoosePromotion);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    @Test(expected = RuntimeException.class)
    public void testCheckUserChoose_WithAmountBelowThreshold() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("1000", true, "PROMO1"),
            createSkuInfo("1000", true, "PROMO1")
        );
        List<PromotionDto> promotions = Arrays.asList(
            createPromotion("PROMO1", "3000", "100")
        );
        PromotionDto userChoosePromotion = createPromotion("PROMO1", "3000", "100");

        // 执行测试
        cashBackCouponPromotion.checkUserChoose(skuInfos, promotions, userChoosePromotion);
    }

    @Test
    public void testExecutePromotional_WithExactThreshold() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("1500", true, "PROMO1"),
            createSkuInfo("1500", true, "PROMO1")
        );
        PromotionDto promotion = createPromotion("PROMO1", "3000", "100");

        // 执行测试
        PromotionRespDto result = cashBackCouponPromotion.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNotNull(result);
        assertEquals("3000.00", result.getDiscountTotalAmount());
        assertEquals("0.00", result.getCostAmount());
        assertEquals(2, result.getCartSkuProductList().size());
        
        // 验证商品优惠金额分配
        List<CartProductSkuInfo> resultSkuInfos = result.getCartSkuProductList();
        assertEquals("1500.00", resultSkuInfos.get(0).getDiscountAmount());
        assertEquals("1500.00", resultSkuInfos.get(1).getDiscountAmount());
    }

    private CartProductSkuInfo createSkuInfo(String salePrice, boolean joinCalculateFlag, String couponModelCode) {
        CartProductSkuInfo skuInfo = new CartProductSkuInfo();
        skuInfo.setSalePrice(salePrice);
        skuInfo.setJoinCalculateFlag(joinCalculateFlag);
        skuInfo.setCouponModuleCodeList(Arrays.asList(couponModelCode));
        return skuInfo;
    }

    private PromotionDto createPromotion(String couponModelCode, String triggerAmount, String discountAmount) {
        PromotionDto promotion = new PromotionDto();
        promotion.setCouponModelCode(couponModelCode);
        promotion.setTriggerAmount(triggerAmount);
        promotion.setDiscountAmount(discountAmount);
        promotion.setCouponModelClassify(CouponTypeEnum.CASH_BACK.getType());
        return promotion;
    }
} 
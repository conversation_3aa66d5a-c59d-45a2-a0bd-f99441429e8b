package com.jlr.ecp.order.service.dashboard.impl;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.controller.admin.dashboard.dto.*;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ChartRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ProductSalesTrendRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.TableRespVo;
import com.jlr.ecp.order.controller.admin.dashboard.vo.ValetDashboardRespVO;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class valetDashboardServiceImplTest {

    @Mock
    private OrderInfoDOMapper mockOrderInfoDOMapper;
    @Mock
    private OrderRefundItemDOMapper mockOrderRefundItemDOMapper;

    @InjectMocks
    private valetDashboardServiceImpl valetDashboardServiceImplUnderTest;

    @Test
    public void testGetValetOrderStatistics() {
        // Setup
        final ValetDashboardReqDTO reqDTO = new ValetDashboardReqDTO();
        reqDTO.setStartTime("startTime");
        reqDTO.setEndTime("endTime");
        reqDTO.setOrderChannel("orderChannel");

        final ValetDashboardRespVO expectedResult = new ValetDashboardRespVO();
        expectedResult.setValetOrderCount(new BigDecimal("0.00"));
        expectedResult.setTotalOrderCount(new BigDecimal("0.00"));
        expectedResult.setValetOrderPercentage("0%");
        expectedResult.setValetPaidCount(new BigDecimal("0.00"));
        expectedResult.setTotalPaidCount(new BigDecimal("0.00"));
        expectedResult.setPaidPercentage("0%");
        expectedResult.setValetGmv(new BigDecimal("0.00"));
        expectedResult.setTotalGmv(new BigDecimal("0.00"));
        expectedResult.setGmvPercentage("0%");
        expectedResult.setValetIncome(new BigDecimal("0.00"));
        expectedResult.setTotalIncome(new BigDecimal("0.00"));
        expectedResult.setIncomePercentage("0%");
        expectedResult.setValetRefundAmount(new BigDecimal("0.00"));
        expectedResult.setTotalRefundAmount(new BigDecimal("0.00"));
        expectedResult.setRefundPercentage("0%");
        expectedResult.setValetCustomerPrice(new BigDecimal("0"));
        expectedResult.setTotalCustomerPrice(new BigDecimal("0"));
        expectedResult.setCustomerPriceDiff("0%");
        expectedResult.setValetProductCount(new BigDecimal("0"));
        expectedResult.setTotalProductCount(new BigDecimal("0"));

        // Configure OrderInfoDOMapper.countOrderByChannel(...).
        final ValetDashboardTempDTO valetDashboardTempDTO = new ValetDashboardTempDTO();
        valetDashboardTempDTO.setValetOrderCount(new BigDecimal("0.00"));
        valetDashboardTempDTO.setTotalOrderCount(new BigDecimal("0.00"));
        valetDashboardTempDTO.setValetPaidCount(new BigDecimal("0.00"));
        valetDashboardTempDTO.setTotalPaidCount(new BigDecimal("0.00"));
        valetDashboardTempDTO.setValetGmv(new BigDecimal("0.00"));
        valetDashboardTempDTO.setTotalGmv(new BigDecimal("0.00"));
        valetDashboardTempDTO.setValetRefundAmount(new BigDecimal("0.00"));
        valetDashboardTempDTO.setTotalRefundAmount(new BigDecimal("0.00"));
        valetDashboardTempDTO.setValetProductCount(new BigDecimal("0"));
        valetDashboardTempDTO.setTotalProductCount(new BigDecimal("0"));
        when(mockOrderInfoDOMapper.countOrderByChannel("orderChannel", "startTime", "endTime"))
                .thenReturn(valetDashboardTempDTO);

        // Configure OrderInfoDOMapper.countOrderByPaymentStatus(...).
        final ValetDashboardTempDTO valetDashboardTempDTO1 = new ValetDashboardTempDTO();
        valetDashboardTempDTO1.setValetOrderCount(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setTotalOrderCount(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setValetPaidCount(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setTotalPaidCount(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setValetGmv(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setTotalGmv(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setValetRefundAmount(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setTotalRefundAmount(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setValetProductCount(new BigDecimal("0.00"));
        valetDashboardTempDTO1.setTotalProductCount(new BigDecimal("0.00"));
        when(mockOrderInfoDOMapper.countOrderByPaymentStatus("orderChannel", "startTime", "endTime"))
                .thenReturn(valetDashboardTempDTO1);

        // Configure OrderRefundItemDOMapper.getRefundAmounts(...).
        final ValetDashboardTempDTO valetDashboardTempDTO2 = new ValetDashboardTempDTO();
        valetDashboardTempDTO2.setValetOrderCount(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setTotalOrderCount(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setValetPaidCount(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setTotalPaidCount(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setValetGmv(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setTotalGmv(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setValetRefundAmount(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setTotalRefundAmount(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setValetProductCount(new BigDecimal("0.00"));
        valetDashboardTempDTO2.setTotalProductCount(new BigDecimal("0.00"));
        when(mockOrderRefundItemDOMapper.getRefundAmounts("orderChannel", "startTime", "endTime"))
                .thenReturn(valetDashboardTempDTO2);

        // Run the test
        final ValetDashboardRespVO result = valetDashboardServiceImplUnderTest.getValetOrderStatistics(reqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSalesTrend_TOTAL_ORDER_NUMBER() {
        // Setup
        final ValetSalesTrendReqDTO reqDTO = new ValetSalesTrendReqDTO();
        reqDTO.setStartTime("2020-01-01");
        reqDTO.setEndTime("2021-01-01");
        reqDTO.setOrderChannel("orderChannel");
        reqDTO.setKpi(1);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult("0", "0");

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = valetDashboardServiceImplUnderTest.getSalesTrend(reqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetSalesTrend_TOTAL_ORDER_AMOUNT() {
        // Setup
        final ValetSalesTrendReqDTO reqDTO = new ValetSalesTrendReqDTO();
        reqDTO.setStartTime("2020-01-01");
        reqDTO.setEndTime("2021-01-01");
        reqDTO.setOrderChannel("orderChannel");
        reqDTO.setKpi(1);

        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult("0", "0.00");

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = valetDashboardServiceImplUnderTest.getSalesTrend(reqDTO);

        // Verify the results
        assertEquals(expectedResult.getData().getChart(), result.getData().getChart());
    }

    @NotNull
    private CommonResult<ProductSalesTrendRespVo> getProductSalesTrendRespVoCommonResult(String number, String v) {
        // 构建期望结果
        final ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();

        // 构建 ChartRespVo
        final ChartRespVo chart = new ChartRespVo();
        chart.setXAxis(List.of("2020", "2021"));

        // 构建 ChartRespVo.ChartItemVo
        final ChartRespVo.ChartItemVo chartItemVo1 = new ChartRespVo.ChartItemVo();
        chartItemVo1.setName("InControl Remote Service");
        chartItemVo1.setData(List.of("0", "0"));

        final ChartRespVo.ChartItemVo chartItemVo2 = new ChartRespVo.ChartItemVo();
        chartItemVo2.setName("InControl Online Service");
        chartItemVo2.setData(List.of("0", "0"));

        final ChartRespVo.ChartItemVo chartItemVo3 = new ChartRespVo.ChartItemVo();
        chartItemVo3.setName("InControl Remote + Online Service");
        chartItemVo3.setData(List.of("0", "0"));

        chart.setDataList(List.of(chartItemVo1, chartItemVo2, chartItemVo3));
        productSalesTrendRespVo.setChart(chart);

        // 构建 TableRespVo
        final TableRespVo table = new TableRespVo();

        // 构建 TableRespVo.HeaderItem
        final TableRespVo.HeaderItem headerItem1 = new TableRespVo.HeaderItem();
        headerItem1.setProp("firstColumn");
        headerItem1.setLabel("");

        final TableRespVo.HeaderItem headerItem2 = new TableRespVo.HeaderItem();
        headerItem2.setProp("total");
        headerItem2.setLabel("");

        final TableRespVo.HeaderItem headerItem3 = new TableRespVo.HeaderItem();
        headerItem3.setProp("2020");
        headerItem3.setLabel("2020");

        final TableRespVo.HeaderItem headerItem4 = new TableRespVo.HeaderItem();
        headerItem4.setProp("2021");
        headerItem4.setLabel("2021");

        table.setHeaders(List.of(headerItem1, headerItem2, headerItem3, headerItem4));

        // 构建 TableRespVo.TableData
        final Map<String, String> tableData1 = Map.ofEntries(
                Map.entry("total", number),
                Map.entry("firstColumn", "InControl Remote Service"),
                Map.entry("2021", "0"),
                Map.entry("2020", "0")
        );

        final Map<String, String> tableData2 = Map.ofEntries(
                Map.entry("total", v),
                Map.entry("firstColumn", "InControl Online Service"),
                Map.entry("2021", "0"),
                Map.entry("2020", "0")
        );

        final Map<String, String> tableData3 = Map.ofEntries(
                Map.entry("total", v),
                Map.entry("firstColumn", "InControl Remote + Online Service"),
                Map.entry("2021", "0"),
                Map.entry("2020", "0")
        );

        table.setTableData(List.of(tableData1, tableData2, tableData3));
        productSalesTrendRespVo.setTable(table);

        final CommonResult<ProductSalesTrendRespVo> expectedResult = CommonResult.success(productSalesTrendRespVo);

        // Configure OrderInfoDOMapper.getSalesTrendByChannelByValet(...).
        final SqlResultDTO sqlResultDTO1 = new SqlResultDTO();
        sqlResultDTO1.setOrderType(0);
        sqlResultDTO1.setLabel("InControl Remote Service");
        sqlResultDTO1.setQuantity(0L);
        sqlResultDTO1.setAmount(0L);

        final SqlResultDTO sqlResultDTO2 = new SqlResultDTO();
        sqlResultDTO2.setOrderType(0);
        sqlResultDTO2.setLabel("InControl Online Service");
        sqlResultDTO2.setQuantity(0L);
        sqlResultDTO2.setAmount(0L);

        final SqlResultDTO sqlResultDTO3 = new SqlResultDTO();
        sqlResultDTO3.setOrderType(0);
        sqlResultDTO3.setLabel("InControl Remote + Online Service");
        sqlResultDTO3.setQuantity(0L);
        sqlResultDTO3.setAmount(0L);

        final List<SqlResultDTO> resultDTOS = List.of(sqlResultDTO1, sqlResultDTO2, sqlResultDTO3);
        when(mockOrderInfoDOMapper.getSalesTrendByChannelByValet(any(SqlQueryDTO.class))).thenReturn(resultDTOS);
        return expectedResult;
    }


    @Test
    public void testGetSalesTrend_UNIT_PRICE_PER_CUSTOMER() {
        // Setup
        final ValetSalesTrendReqDTO reqDTO = new ValetSalesTrendReqDTO();
        reqDTO.setStartTime("2020-01-01");
        reqDTO.setEndTime("2021-01-01");
        reqDTO.setOrderChannel("orderChannel");
        reqDTO.setKpi(1);

        // 构建期望结果
        final CommonResult<ProductSalesTrendRespVo> expectedResult = getProductSalesTrendRespVoCommonResult("0.00", "0.00");

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = valetDashboardServiceImplUnderTest.getSalesTrend(reqDTO);

        // Verify the results
        assertEquals(expectedResult.getData().getChart(), result.getData().getChart());
    }


    @Test
    public void testGetSalesTrend_INCOME() {
        // Setup
        final ValetSalesTrendReqDTO reqDTO = new ValetSalesTrendReqDTO();
        reqDTO.setStartTime("2020-01-01");
        reqDTO.setEndTime("2021-01-01");
        reqDTO.setOrderChannel("orderChannel");
        reqDTO.setKpi(5);

        // 构建期望结果
        final ProductSalesTrendRespVo productSalesTrendRespVo = new ProductSalesTrendRespVo();

        // 构建 ChartRespVo
        final ChartRespVo chart = new ChartRespVo();
        chart.setXAxis(List.of("2020", "2021"));

        // 构建 ChartRespVo.ChartItemVo
        final ChartRespVo.ChartItemVo chartItemVo1 = new ChartRespVo.ChartItemVo();
        chartItemVo1.setName("InControl Remote Service");
        chartItemVo1.setData(List.of("0.00", "0.00"));

        final ChartRespVo.ChartItemVo chartItemVo2 = new ChartRespVo.ChartItemVo();
        chartItemVo2.setName("InControl Online Service");
        chartItemVo2.setData(List.of("0.00", "0.00"));

        final ChartRespVo.ChartItemVo chartItemVo3 = new ChartRespVo.ChartItemVo();
        chartItemVo3.setName("InControl Remote + Online Service");
        chartItemVo3.setData(List.of("0.00", "0.00"));

        chart.setDataList(List.of(chartItemVo1, chartItemVo2, chartItemVo3));
        productSalesTrendRespVo.setChart(chart);

        // 构建 TableRespVo
        final TableRespVo table = new TableRespVo();

        // 构建 TableRespVo.HeaderItem
        final TableRespVo.HeaderItem headerItem1 = new TableRespVo.HeaderItem();
        headerItem1.setProp("firstColumn");
        headerItem1.setLabel("");

        final TableRespVo.HeaderItem headerItem2 = new TableRespVo.HeaderItem();
        headerItem2.setProp("total");
        headerItem2.setLabel("");

        final TableRespVo.HeaderItem headerItem3 = new TableRespVo.HeaderItem();
        headerItem3.setProp("2020");
        headerItem3.setLabel("2020");

        final TableRespVo.HeaderItem headerItem4 = new TableRespVo.HeaderItem();
        headerItem4.setProp("2021");
        headerItem4.setLabel("2021");

        table.setHeaders(List.of(headerItem1, headerItem2, headerItem3, headerItem4));

        // 构建 TableRespVo.TableData
        final Map<String, String> tableData1 = Map.ofEntries(
                Map.entry("total", "0.00"),
                Map.entry("firstColumn", "InControl Remote Service"),
                Map.entry("2021", "0.00"),
                Map.entry("2020", "0.00")
        );

        final Map<String, String> tableData2 = Map.ofEntries(
                Map.entry("total", "0.00"),
                Map.entry("firstColumn", "InControl Online Service"),
                Map.entry("2021", "0.00"),
                Map.entry("2020", "0.00")
        );

        final Map<String, String> tableData3 = Map.ofEntries(
                Map.entry("total", "0.00"),
                Map.entry("firstColumn", "InControl Remote + Online Service"),
                Map.entry("2021", "0.00"),
                Map.entry("2020", "0.00")
        );

        table.setTableData(List.of(tableData1, tableData2, tableData3));
        productSalesTrendRespVo.setTable(table);

        final CommonResult<ProductSalesTrendRespVo> expectedResult = CommonResult.success(productSalesTrendRespVo);

        // Configure OrderInfoDOMapper.getSalesTrendByChannelByValet(...).
        final SqlResultDTO sqlResultDTO1 = new SqlResultDTO();
        sqlResultDTO1.setOrderType(0);
        sqlResultDTO1.setLabel("InControl Remote Service");
        sqlResultDTO1.setQuantity(0L);
        sqlResultDTO1.setAmount(0L);

        final SqlResultDTO sqlResultDTO2 = new SqlResultDTO();
        sqlResultDTO2.setOrderType(0);
        sqlResultDTO2.setLabel("InControl Online Service");
        sqlResultDTO2.setQuantity(0L);
        sqlResultDTO2.setAmount(0L);

        final SqlResultDTO sqlResultDTO3 = new SqlResultDTO();
        sqlResultDTO3.setOrderType(0);
        sqlResultDTO3.setLabel("InControl Remote + Online Service");
        sqlResultDTO3.setQuantity(0L);
        sqlResultDTO3.setAmount(0L);


        // Configure OrderInfoDOMapper.getSalesTrendRefundByChannelByValet(...).
        final SqlResultDTO sqlResultDTO4 = new SqlResultDTO();
        sqlResultDTO4.setOrderType(0);
        sqlResultDTO4.setLabel("InControl Remote Service");
        sqlResultDTO4.setQuantity(0L);
        sqlResultDTO4.setAmount(0L);

        final SqlResultDTO sqlResultDTO5 = new SqlResultDTO();
        sqlResultDTO5.setOrderType(0);
        sqlResultDTO5.setLabel("InControl Online Service");
        sqlResultDTO5.setQuantity(0L);
        sqlResultDTO5.setAmount(0L);

        final SqlResultDTO sqlResultDTO6 = new SqlResultDTO();
        sqlResultDTO6.setOrderType(0);
        sqlResultDTO6.setLabel("InControl Remote + Online Service");
        sqlResultDTO6.setQuantity(0L);
        sqlResultDTO6.setAmount(0L);

        final List<SqlResultDTO> resultDTOS1 = List.of(sqlResultDTO4, sqlResultDTO5, sqlResultDTO6);
        when(mockOrderInfoDOMapper.getSalesTrendRefundByChannelByValet(any(SqlQueryDTO.class))).thenReturn(resultDTOS1);

        // Run the test
        final CommonResult<ProductSalesTrendRespVo> result = valetDashboardServiceImplUnderTest.getSalesTrend(reqDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }


    @Test
    public void testGetValetKpiList() {
        // Setup
        final CommonResult<List<KpiQueryDTO>> expectedResult = CommonResult.success(
                List.of(
                        new KpiQueryDTO(1, "总订单数", "Total On-behalf of Order Counts", "代客下单总订单数", "pcs"),
                        new KpiQueryDTO(2, "成交订单数", "Turnover On-behalf of Order Counts", "代客下单成交订单数", "pcs"),
                        new KpiQueryDTO(3, "总订单交易额", "Turnover On-behalf of Order Transactional Volume", "代客下单成交订单总交易额", "RMB"),
                        new KpiQueryDTO(4, "总收入", "Total On-behalf of Income", "代客下单总收入", "RMB"),
                        new KpiQueryDTO(5, "总退款", "Total On-behalf of Refund Volume", "代客下单总退款额", "RMB"),
                        new KpiQueryDTO(6, "成交客单价", "ATV for On-behalf of", "代客下单成交客单价", "RMB")
                )
        );

        // Run the test
        final CommonResult<List<KpiQueryDTO>> result = valetDashboardServiceImplUnderTest.getValetKpiList();

        // Verify the results
        assertEquals(expectedResult, result);
    }

}

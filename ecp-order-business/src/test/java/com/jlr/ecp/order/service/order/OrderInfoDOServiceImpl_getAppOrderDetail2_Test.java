package com.jlr.ecp.order.service.order;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppDetailPage;
import com.jlr.ecp.order.api.order.vo.brandcategory.OrderBrandOrderInfoVO;
import com.jlr.ecp.order.dal.dataobject.order.*;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.mysql.order.*;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.handle.RefundHandler;
import com.jlr.ecp.payment.api.invoice.InvoiceApiV2;
import com.jlr.ecp.payment.api.invoice.vo.InvoiceStatusVO;
import com.jlr.ecp.product.api.sku.dto.ProductSnapshotDTO;
import com.jlr.ecp.product.api.snapshot.ProductSnapshotApi;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrderInfoDOServiceImpl_getAppOrderDetail2_Test {

    @Mock
    private OrderInfoDOMapper orderInfoDOMapper;

    @Mock
    private OrderItemDOMapper orderItemDOMapper;

    @Mock
    private OrderDiscountDetailDOMapper orderDiscountDetailDOMapper;

    @Mock
    private OrderItemLogisticsDOMapper orderItemLogisticsDOMapper;

    @Mock
    private OrderRefundItemDOMapper orderRefundItemDOMapper;
    
    @Mock
    private OrderRefundDOMapper orderRefundDOMapper;
    
    @Mock
    private OrderCouponDetailDOMapper orderCouponDetailDOMapper;

    @Mock
    private ProductSnapshotApi productSnapshotApi;

    @Mock
    private RefundHandler refundHandler;

    @Mock
    private InvoiceApiV2 invoiceApi;
    
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    
    @Mock
    private ValueOperations<String, Object> valueOperations;

    @InjectMocks
    private OrderInfoDOServiceImpl orderInfoDOService;

    private String testOrderCode;
    private String testJlrId;

    @Before
    public void setUp() {
        testOrderCode = "testOrderCode";
        testJlrId = "testJlrId";
        
        // 配置 Redis mock
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get(any())).thenReturn(null);
    }

    private OrderInfoDO createMockOrderInfo() {
        OrderInfoDO mockOrderInfo = new OrderInfoDO();
        
        // 设置订单基本信息
        mockOrderInfo.setId(1L);
        mockOrderInfo.setOrderCode(testOrderCode);
        mockOrderInfo.setOrderType(1);
        mockOrderInfo.setOrderStatus(2);
        mockOrderInfo.setOrderChannel(1);
        mockOrderInfo.setConsumerCode(testJlrId);
        mockOrderInfo.setWxNickName("测试用户");
        mockOrderInfo.setWxPhone("***********");
        mockOrderInfo.setBusinessCode("BG");
        mockOrderInfo.setPaymentStatus(1);
        
        // 设置物流状态（针对BG商品）
        mockOrderInfo.setLogisticsStatus(90201); // 设置为待发货状态

        // 设置订单金额信息（单位：分）
        mockOrderInfo.setOriginalFeeTotalAmount(100000);
        mockOrderInfo.setFeeTotalAmount(100000);
        mockOrderInfo.setCostAmount(100000);
        mockOrderInfo.setDiscountTotalAmount(0);
        mockOrderInfo.setFreightAmount(0);

        // 设置订单时间信息
        LocalDateTime now = LocalDateTime.now();
        mockOrderInfo.setOrderTime(now);
        mockOrderInfo.setPaymentTime(now);
        mockOrderInfo.setCompletedTime(now);

        // 设置订单其他信息
        mockOrderInfo.setCustomerRemark("测试订单");
        mockOrderInfo.setIsDeleted(false);
        mockOrderInfo.setCreatedBy("system");
        mockOrderInfo.setUpdatedBy("system");
        
        return mockOrderInfo;
    }

    private OrderItemDO createMockOrderItem() {
        OrderItemDO mockOrderItem = new OrderItemDO();
        mockOrderItem.setId(1L);
        mockOrderItem.setOrderCode(testOrderCode);
        mockOrderItem.setOrderItemCode("testOrderItemCode");
        mockOrderItem.setProductVersionCode("testProductVersionCode");
        mockOrderItem.setProductSalePrice(100000);
        mockOrderItem.setCostAmount(100000);
        mockOrderItem.setProductQuantity(1);
        mockOrderItem.setPointAmount(100);
        mockOrderItem.setProductImageUrl("http://test.com/image.jpg");
        mockOrderItem.setItemFulfillmentType(1);
        mockOrderItem.setIsDeleted(false);;
        return mockOrderItem;
    }

    private ProductSnapshotDTO createMockProductSnapshot() {
        ProductSnapshotDTO mockSnapshot = new ProductSnapshotDTO();
        mockSnapshot.setProductVersionCode("testProductVersionCode");
        mockSnapshot.setProductCode("testProductCode");
        mockSnapshot.setProductSkuCode("testProductSkuCode");
        mockSnapshot.setProductName("测试商品");
        mockSnapshot.setProductImageUrl("http://test.com/image.jpg");
        mockSnapshot.setProductAttribute("service_year:一年,car_system:PIVI");
        mockSnapshot.setProductMarketPrice(100000L);
        mockSnapshot.setProductSalePrice(100000L);
        return mockSnapshot;
    }
    
    private OrderRefundItemDO createMockOrderRefundItem() {
        OrderRefundItemDO mockRefundItem = new OrderRefundItemDO();
        mockRefundItem.setId(1L);
        mockRefundItem.setOrderItemCode("testOrderItemCode");
        mockRefundItem.setRefundOrderCode("testRefundOrderCode");
        mockRefundItem.setRefundMoney(100000);
        mockRefundItem.setRefundQuantity(1);
        mockRefundItem.setRefundFreight(0);
        mockRefundItem.setIsDeleted(false);
        return mockRefundItem;
    }
    
    private OrderRefundDO createMockOrderRefundDO() {
        OrderRefundDO mockRefundDO = new OrderRefundDO();
        mockRefundDO.setId(1L);
        mockRefundDO.setRefundOrderCode("testRefundOrderCode");
        mockRefundDO.setOriginOrderCode(testOrderCode);
        mockRefundDO.setRefundOrderStatus(10); // 假设10是某个退款状态
        mockRefundDO.setRefundMoney(100000);
        mockRefundDO.setRefundMoneyAmount(100000);
        mockRefundDO.setRefundRemark("测试退款");
        mockRefundDO.setIsDeleted(false);
        return mockRefundDO;
    }
    
    private OrderCouponDetailDO createMockOrderCouponDetail() {
        OrderCouponDetailDO mockCouponDetail = new OrderCouponDetailDO();
        mockCouponDetail.setId(1L);
        mockCouponDetail.setOrderCode(testOrderCode);
        mockCouponDetail.setOrderItemCode("testOrderItemCode");
        mockCouponDetail.setCouponCode("testCouponCode");
        mockCouponDetail.setStatus(1); // 假设1是某个卡券状态
        mockCouponDetail.setIsDeleted(false);
        return mockCouponDetail;
    }
    
    private InvoiceStatusVO createMockInvoiceStatus() {
        InvoiceStatusVO invoiceStatus = new InvoiceStatusVO();
        invoiceStatus.setOrderCode(testOrderCode);
        invoiceStatus.setInvoiceStatus(1); // 假设1是某个发票状态
        return invoiceStatus;
    }

    @Test
    public void getAppOrderDetail2_shouldReturnOrderDetail_whenOrderExists() {
        // 准备
        OrderInfoDO mockOrderInfo = createMockOrderInfo();
        OrderItemDO mockOrderItem = createMockOrderItem();
        List<OrderItemDO> mockOrderItems = Collections.singletonList(mockOrderItem);
        List<OrderDiscountDetailDO> mockDiscountDetails = new ArrayList<>();
        List<OrderItemLogisticsDO> mockLogistics = new ArrayList<>();
        ProductSnapshotDTO mockSnapshot = createMockProductSnapshot();
        List<ProductSnapshotDTO> mockSnapshotList = Collections.singletonList(mockSnapshot);
        OrderRefundItemDO mockRefundItem = createMockOrderRefundItem();
        List<OrderRefundItemDO> mockRefundItems = Collections.singletonList(mockRefundItem);
        OrderRefundDO mockRefundDO = createMockOrderRefundDO();
        List<OrderRefundDO> mockRefundDOs = Collections.singletonList(mockRefundDO);
        OrderCouponDetailDO mockCouponDetail = createMockOrderCouponDetail();
        List<OrderCouponDetailDO> mockCouponDetails = Collections.singletonList(mockCouponDetail);
        InvoiceStatusVO mockInvoiceStatus = createMockInvoiceStatus();
        List<InvoiceStatusVO> mockInvoiceStatuses = Collections.singletonList(mockInvoiceStatus);

        // 设置 mock 行为
        when(orderInfoDOMapper.selectOne(any())).thenReturn(mockOrderInfo);
        when(orderItemDOMapper.selectList(any())).thenReturn(mockOrderItems);
        when(orderDiscountDetailDOMapper.selectList(any())).thenReturn(mockDiscountDetails);
        when(orderItemLogisticsDOMapper.selectList(any())).thenReturn(mockLogistics);
        when(orderRefundItemDOMapper.selectList(any())).thenReturn(mockRefundItems);
        when(orderRefundDOMapper.selectList(any())).thenReturn(mockRefundDOs);
        when(productSnapshotApi.getProductSnapshotList(any())).thenReturn(CommonResult.success(mockSnapshotList));
        when(invoiceApi.getInvokeStatusListOnGeneral(any())).thenReturn(CommonResult.success(mockInvoiceStatuses));

        // 执行
        OrderAppDetailPage result = orderInfoDOService.getAppOrderDetail2(testOrderCode, testJlrId);

        // 验证
        assertNotNull(result);
        assertNotNull(result.getOrderInfo());
        OrderBrandOrderInfoVO orderInfo = result.getOrderInfo();
        assertEquals(testOrderCode, orderInfo.getOrderCode());
        assertEquals("1000.00", orderInfo.getCostAmount());
        assertEquals("0.00", orderInfo.getShipping());
        assertEquals("1000.00", orderInfo.getOriginalAmount());

        // 验证 mock 调用
        verify(orderInfoDOMapper, times(1)).selectOne(any());
        verify(orderItemDOMapper, times(1)).selectList(any());
        verify(orderDiscountDetailDOMapper, times(1)).selectList(any());
        verify(orderItemLogisticsDOMapper, times(1)).selectList(any());
        verify(orderRefundItemDOMapper, times(1)).selectList(any());
        verify(orderRefundDOMapper, times(1)).selectList(any());
        verify(productSnapshotApi, times(1)).getProductSnapshotList(any());
        verify(invoiceApi, times(1)).getInvokeStatusListOnGeneral(any());
    }

    @Test
    public void getAppOrderDetail2_shouldReturnEmptyDetail_whenOrderNotExists() {
        // 准备
        when(orderInfoDOMapper.selectOne(any())).thenReturn(null);

        // 执行
        OrderAppDetailPage result = orderInfoDOService.getAppOrderDetail2(testOrderCode, testJlrId);

        // 验证
        assertNotNull(result);
        assertNotNull(result.getOrderInfo());
        OrderBrandOrderInfoVO orderInfo = result.getOrderInfo();
        assertNull(orderInfo.getOrderCode());
        assertNull(orderInfo.getCostAmount());
        assertNull(orderInfo.getOriginalAmount());

        // 验证 mock 调用
        verify(orderInfoDOMapper, times(1)).selectOne(any());
    }

    @Test(expected = RuntimeException.class)
    public void getAppOrderDetail2_shouldThrowException_whenDatabaseError() {
        // 准备
        when(orderInfoDOMapper.selectOne(any())).thenThrow(new RuntimeException("数据库异常"));

        // 执行
        orderInfoDOService.getAppOrderDetail2(testOrderCode, testJlrId);

        // 验证
        verify(orderInfoDOMapper, times(1)).selectOne(any());
    }
}
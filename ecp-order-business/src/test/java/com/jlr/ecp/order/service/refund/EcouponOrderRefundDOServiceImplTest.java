package com.jlr.ecp.order.service.refund;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.api.refund.dto.BaseOrderRefundApplyDTO;
import com.jlr.ecp.order.constant.Constants;
import com.jlr.ecp.order.controller.app.refund.vo.EcouponRefundItemDetailVO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.handle.RefundHandler;
import com.jlr.ecp.order.service.refund.ecoupon.EcouponOrderRefundDOServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class EcouponOrderRefundDOServiceImplTest {

    private static final String ORDER_CODE = "OR2025040100001";
    private static final String REFUND_ORDER_CODE = "RF2025040100001";
    private static final String ORDER_ITEM_CODE = "OI2025040100001";

    @Mock
    private RefundHandler mockRefundHandler;
    
    @Mock
    private OrderRefundDOMapper mockOrderRefundDOMapper;
    
    @Mock
    private OrderItemDOMapper mockOrderItemDOMapper;
    
    @Mock
    private OrderRefundItemDOMapper mockOrderRefundItemDOMapper;

    @InjectMocks
    private EcouponOrderRefundDOServiceImpl serviceUnderTest;

    @Test
    public void testEcouponOrderRefundApply() {
        // Setup
        List<BaseOrderRefundApplyDTO> refundApplyDTOs = new ArrayList<>();
        BaseOrderRefundApplyDTO dto = new BaseOrderRefundApplyDTO();
        refundApplyDTOs.add(dto);
        Integer operationType = 1;
        
        when(mockRefundHandler.refundProcess(any(), anyInt())).thenReturn(REFUND_ORDER_CODE);

        // Execute
        String result = serviceUnderTest.ecouponOrderRefundApply(refundApplyDTOs, operationType);

        // Verify
        assertThat(result).isEqualTo(REFUND_ORDER_CODE);
        verify(mockRefundHandler).refundProcess(eq(refundApplyDTOs), eq(operationType));
    }

    @Test
    public void testGetEcouponRefundItemDetail() {
        // Setup
        OrderRefundDO orderRefundDO = new OrderRefundDO();
        orderRefundDO.setRefundOrderCode(REFUND_ORDER_CODE);
        orderRefundDO.setOriginOrderCode(ORDER_CODE);

        OrderItemDO orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode(ORDER_ITEM_CODE);
        orderItemDO.setProductName("测试电子券");
        orderItemDO.setProductCode("PCODE123");
        orderItemDO.setProductMarketPrice(10000);
        orderItemDO.setProductSalePrice(8000);
        orderItemDO.setProductImageUrl("http://example.com/image.jpg");
        orderItemDO.setProductQuantity(1);

        OrderRefundItemDO orderRefundItemDO = new OrderRefundItemDO();
        orderRefundItemDO.setRefundOrderCode(REFUND_ORDER_CODE);
        orderRefundItemDO.setOrderItemCode(ORDER_ITEM_CODE);
        orderRefundItemDO.setRefundMoney(8000);
        orderRefundItemDO.setRefundQuantity(1);

        LambdaQueryWrapperX<OrderRefundDO> refundWrapper = new LambdaQueryWrapperX<OrderRefundDO>()
                .eq(OrderRefundDO::getRefundOrderCode, REFUND_ORDER_CODE)
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(OrderRefundDO::getId)
                .last(Constants.LIMIT_ONE);
                
        LambdaQueryWrapperX<OrderItemDO> itemWrapper = new LambdaQueryWrapperX<OrderItemDO>()
                .eq(OrderItemDO::getOrderItemCode, ORDER_ITEM_CODE)
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(OrderItemDO::getId)
                .last(Constants.LIMIT_ONE);
                
        LambdaQueryWrapperX<OrderRefundItemDO> refundItemWrapper = new LambdaQueryWrapperX<OrderRefundItemDO>()
                .eq(OrderRefundItemDO::getOrderItemCode, ORDER_ITEM_CODE)
                .eq(OrderRefundItemDO::getRefundOrderCode, REFUND_ORDER_CODE)
                .eq(BaseDO::getIsDeleted, false)
                .orderByDesc(OrderRefundItemDO::getId)
                .last(Constants.LIMIT_ONE);

        // Configure mocks
        when(mockOrderRefundDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundDO);
        when(mockOrderItemDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDO);
        when(mockOrderRefundItemDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderRefundItemDO);
        doAnswer(invocation -> {
            EcouponRefundItemDetailVO detailVO = (EcouponRefundItemDetailVO) invocation.getArgument(0);
            detailVO.setRefundMoneyAmount(BigDecimal.valueOf(orderRefundItemDO.getRefundMoney() / 100.0).toString());
            return null;
        }).when(mockRefundHandler).buildRefundItemDetailVO(any(EcouponRefundItemDetailVO.class), any(OrderRefundDO.class), any(OrderItemDO.class), any(OrderRefundItemDO.class));

        // Execute
        EcouponRefundItemDetailVO result = serviceUnderTest.getEcouponRefundItemDetail(ORDER_ITEM_CODE, REFUND_ORDER_CODE);

        // Verify
        assertThat(result).isNotNull();
        verify(mockOrderRefundDOMapper).selectOne(any(LambdaQueryWrapperX.class));
        verify(mockOrderItemDOMapper).selectOne(any(LambdaQueryWrapperX.class));
        verify(mockOrderRefundItemDOMapper).selectOne(any(LambdaQueryWrapperX.class));
        verify(mockRefundHandler).buildRefundItemDetailVO(any(EcouponRefundItemDetailVO.class), eq(orderRefundDO), eq(orderItemDO), eq(orderRefundItemDO));
    }
    
    @Test
    public void testGetEcouponRefundItemDetail_WhenAnyNull_ReturnsEmptyDetail() {
        // Setup
        when(mockOrderRefundDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(null);
        
        // Execute
        EcouponRefundItemDetailVO result = serviceUnderTest.getEcouponRefundItemDetail(ORDER_ITEM_CODE, REFUND_ORDER_CODE);
        
        // Verify
        assertThat(result).isNotNull();
        verify(mockRefundHandler, never()).buildRefundItemDetailVO(any(), any(), any(), any());
    }
} 
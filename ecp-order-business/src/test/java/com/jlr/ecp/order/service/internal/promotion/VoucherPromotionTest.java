package com.jlr.ecp.order.service.internal.promotion;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.util.Arrays;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import com.jlr.ecp.order.api.cart.dto.CartProductSkuInfo;
import com.jlr.ecp.order.api.cart.dto.PromotionDto;
import com.jlr.ecp.order.enums.cart.CouponTypeEnum;
import com.jlr.ecp.order.service.internal.promotion.dto.PromotionRespDto;

@RunWith(MockitoJUnitRunner.class)
public class VoucherPromotionTest {

    @InjectMocks
    private VoucherPromotion voucherPromotion;

    @Before
    public void setUp() {
        // 初始化测试数据
    }

    @Test
    public void testExecutePromotional_WithValidPromotion() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("1000", true, "PROMO1"),
            createSkuInfo("2000", true, "PROMO1")
        );
        PromotionDto promotion = createPromotion("PROMO1", "100");

        // 执行测试
        PromotionRespDto result = voucherPromotion.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNotNull(result);
        assertEquals("100.00", result.getDiscountTotalAmount());
        assertEquals("2900.00", result.getCostAmount());
        assertEquals(2, result.getCartSkuProductList().size());
        assertEquals(CouponTypeEnum.VOUCHER, result.getCouponTypeEnum());
        
        // 验证商品优惠金额分配
        List<CartProductSkuInfo> resultSkuInfos = result.getCartSkuProductList();
        assertEquals("33.33", resultSkuInfos.get(0).getDiscountAmount());
        assertEquals("66.67", resultSkuInfos.get(1).getDiscountAmount());
    }

    @Test
    public void testExecutePromotional_WithNoEligibleProducts() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("1000", true, "PROMO2"),
            createSkuInfo("2000", true, "PROMO2")
        );
        PromotionDto promotion = createPromotion("PROMO1", "100");

        // 执行测试
        PromotionRespDto result = voucherPromotion.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNull(result);
    }

    @Test
    public void testExecutePromotional_WithMixedProducts() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("1000", true, "PROMO1"),
            createSkuInfo("2000", true, "PROMO1"),
            createSkuInfo("3000", true, "PROMO2")
        );
        PromotionDto promotion = createPromotion("PROMO1", "100");

        // 执行测试
        PromotionRespDto result = voucherPromotion.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNotNull(result);
        assertEquals("100.00", result.getDiscountTotalAmount());
        assertEquals("5900.00", result.getCostAmount());
        assertEquals(3, result.getCartSkuProductList().size());
        
        // 验证商品优惠金额分配
        List<CartProductSkuInfo> resultSkuInfos = result.getCartSkuProductList();
        assertEquals("33.33", resultSkuInfos.get(0).getDiscountAmount());
        assertEquals("66.67", resultSkuInfos.get(1).getDiscountAmount());
        assertNull(null, resultSkuInfos.get(2).getDiscountAmount());
    }

    @Test
    public void testExecutePromotional_WithSingleProduct() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("3000", true, "PROMO1")
        );
        PromotionDto promotion = createPromotion("PROMO1", "100");

        // 执行测试
        PromotionRespDto result = voucherPromotion.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNotNull(result);
        assertEquals("100.00", result.getDiscountTotalAmount());
        assertEquals("2900.00", result.getCostAmount());
        assertEquals(1, result.getCartSkuProductList().size());
        
        // 验证商品优惠金额分配
        List<CartProductSkuInfo> resultSkuInfos = result.getCartSkuProductList();
        assertEquals("100.00", resultSkuInfos.get(0).getDiscountAmount());
    }

    @Test
    public void testExecutePromotional_WithVoucherAmountGreaterThanTotal() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("1000", true, "PROMO1"),
            createSkuInfo("2000", true, "PROMO1")
        );
        PromotionDto promotion = createPromotion("PROMO1", "4000");

        // 执行测试
        PromotionRespDto result = voucherPromotion.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNotNull(result);
        assertEquals("3000.00", result.getDiscountTotalAmount());
        assertEquals("0.00", result.getCostAmount());
        assertEquals(2, result.getCartSkuProductList().size());
        
        // 验证商品优惠金额分配
        List<CartProductSkuInfo> resultSkuInfos = result.getCartSkuProductList();
        assertEquals("1000.00", resultSkuInfos.get(0).getDiscountAmount());
        assertEquals("2000.00", resultSkuInfos.get(1).getDiscountAmount());
    }

    @Test
    public void testExecutePromotional_WithEqualAmount() {
        // 准备测试数据
        List<CartProductSkuInfo> skuInfos = Arrays.asList(
            createSkuInfo("1500", true, "PROMO1"),
            createSkuInfo("1500", true, "PROMO1")
        );
        PromotionDto promotion = createPromotion("PROMO1", "3000");

        // 执行测试
        PromotionRespDto result = voucherPromotion.executePromotional(skuInfos, promotion);

        // 验证结果
        assertNotNull(result);
        assertEquals("3000.00", result.getDiscountTotalAmount());
        assertEquals("0.00", result.getCostAmount());
        assertEquals(2, result.getCartSkuProductList().size());
        
        // 验证商品优惠金额分配
        List<CartProductSkuInfo> resultSkuInfos = result.getCartSkuProductList();
        assertEquals("1500.00", resultSkuInfos.get(0).getDiscountAmount());
        assertEquals("1500.00", resultSkuInfos.get(1).getDiscountAmount());
    }

    private CartProductSkuInfo createSkuInfo(String salePrice, boolean joinCalculateFlag, String couponModelCode) {
        CartProductSkuInfo skuInfo = new CartProductSkuInfo();
        skuInfo.setSalePrice(salePrice);
        skuInfo.setJoinCalculateFlag(joinCalculateFlag);
        skuInfo.setCouponModuleCodeList(Arrays.asList(couponModelCode));
        return skuInfo;
    }

    private PromotionDto createPromotion(String couponModelCode, String money) {
        PromotionDto promotion = new PromotionDto();
        promotion.setCouponModelCode(couponModelCode);
        promotion.setMoney(money);
        promotion.setCouponModelClassify(CouponTypeEnum.VOUCHER.getType());
        return promotion;
    }
} 
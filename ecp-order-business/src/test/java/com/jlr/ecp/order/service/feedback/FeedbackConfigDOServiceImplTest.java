package com.jlr.ecp.order.service.feedback;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.PageParam;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.order.controller.admin.feedback.dto.*;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedBackConfigVO;
import com.jlr.ecp.order.controller.admin.feedback.vo.FeedbackDimensionsVO;
import com.jlr.ecp.order.controller.app.feedback.dto.FeedbackQueryDTO;
import com.jlr.ecp.order.controller.app.feedback.vo.FeedbackDimensionsAppVO;
import com.jlr.ecp.order.controller.app.feedback.vo.FeedbackSettingVO;
import com.jlr.ecp.order.dal.dataobject.feedback.*;
import com.jlr.ecp.order.dal.mysql.feedback.*;
import com.jlr.ecp.order.util.redis.RedisDelayQueueUtil;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class FeedbackConfigDOServiceImplTest {

    @Mock
    private FeedbackConfigDOMapper baseMapper;
    @Mock
    private FeedbackDimensionsDOMapper mockFeedbackDimensionsDOMapper;
    @Mock
    private FeedbackModifyLogDOMapper mockModifyLogDOMapper;
    @Mock
    private FeedbackSnapshotDOMapper mockFeedbackSnapshotDOMapper;
    @Mock
    private FeedbackRecordsDOMapper mockFeedbackRecordsDOMapper;
    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private RedisDelayQueueUtil mockRedisDelayQueueUtil;


    @InjectMocks
    private FeedbackConfigDOServiceImpl feedbackConfigDOServiceImplUnderTest;

    @BeforeEach
    public void setUp() {

        feedbackConfigDOServiceImplUnderTest.ecpIdUtil = mockEcpIdUtil;
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), FeedbackConfigDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), FeedbackSnapshotDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), FeedbackDimensionsDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(),""), FeedbackRecordsDO.class);

        // 确保mock对象的行为
        when(baseMapper.updateById(any())).thenReturn(1);
        when(mockFeedbackSnapshotDOMapper.updateById(any())).thenReturn(1);
        when(baseMapper.update(any(), any())).thenReturn(1);
        when(baseMapper.insert(any())).thenReturn(1);

    }

    @Test
    public void testGetOneByFeedBackCode() {
        // Setup
        final FeedBackConfigVO expectedResult = new FeedBackConfigVO();
        expectedResult.setFeedbackDimensionsText("feedbackDimensionsText");
        final FeedbackDimensionsVO feedbackDimensionsVO = new FeedbackDimensionsVO();
        final FeedbackOptionsDTO feedbackOptionsDTO = new FeedbackOptionsDTO();
        feedbackDimensionsVO.setOptionList(List.of(feedbackOptionsDTO));
        feedbackDimensionsVO.setTypeText("typeText");
        feedbackDimensionsVO.setMustInputText("mustInputText");
        expectedResult.setDimensionsList(List.of(feedbackDimensionsVO));
        expectedResult.setEnableStatusText("enableStatusText");

        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        // Configure FeedbackDimensionsDOMapper.selectListByFeedBackCode(...).
        final FeedbackDimensionsDO feedbackDimensionsDO = new FeedbackDimensionsDO();
        feedbackDimensionsDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackDimensionsDO.setIsDeleted(false);
        feedbackDimensionsDO.setRevision(0);
        feedbackDimensionsDO.setDimensionsCode("dimensionsCode");
        feedbackDimensionsDO.setFeedbackCode("feedbackCode");
        feedbackDimensionsDO.setType(0);
        feedbackDimensionsDO.setOptionJson("[{\"sort\": 1, \"option\": \"支付宝\"}, {\"sort\": 2, \"option\": \"微信支付\"}, {\"sort\": 3, \"option\": \"银行卡支付\"}, {\"sort\": 4, \"option\": \"货到付款\"}, {\"sort\": 5, \"option\": \"其他\"}]");
        feedbackDimensionsDO.setMustInput(0);
        final List<FeedbackDimensionsDO> feedbackDimensionsDOS = List.of(feedbackDimensionsDO);
        when(mockFeedbackDimensionsDOMapper.selectListByFeedBackCode("feedbackCode")).thenReturn(feedbackDimensionsDOS);

        // Run the test
        final FeedBackConfigVO result = feedbackConfigDOServiceImplUnderTest.getOneByFeedBackCode("feedbackCode");

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetOneByFeedBackCode_FeedbackConfigDOMapperReturnsNull() {
        // Setup
        final FeedBackConfigVO expectedResult = new FeedBackConfigVO();
        expectedResult.setFeedbackDimensionsText("feedbackDimensionsText");
        final FeedbackDimensionsVO feedbackDimensionsVO = new FeedbackDimensionsVO();
        final FeedbackOptionsDTO feedbackOptionsDTO = new FeedbackOptionsDTO();
        feedbackDimensionsVO.setOptionList(List.of(feedbackOptionsDTO));
        feedbackDimensionsVO.setTypeText("typeText");
        feedbackDimensionsVO.setMustInputText("mustInputText");
        expectedResult.setDimensionsList(List.of(feedbackDimensionsVO));
        expectedResult.setEnableStatusText("enableStatusText");

        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(null);

        // Run the test
        final FeedBackConfigVO result = feedbackConfigDOServiceImplUnderTest.getOneByFeedBackCode("feedbackCode");

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetOneByFeedBackCode_FeedbackDimensionsDOMapperReturnsNoItems() {
        // Setup
        final FeedBackConfigVO expectedResult = new FeedBackConfigVO();
        expectedResult.setFeedbackDimensionsText("feedbackDimensionsText");
        final FeedbackDimensionsVO feedbackDimensionsVO = new FeedbackDimensionsVO();
        final FeedbackOptionsDTO feedbackOptionsDTO = new FeedbackOptionsDTO();
        feedbackDimensionsVO.setOptionList(List.of(feedbackOptionsDTO));
        feedbackDimensionsVO.setTypeText("typeText");
        feedbackDimensionsVO.setMustInputText("mustInputText");
        expectedResult.setDimensionsList(List.of(feedbackDimensionsVO));
        expectedResult.setEnableStatusText("enableStatusText");

        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        when(mockFeedbackDimensionsDOMapper.selectListByFeedBackCode("feedbackCode"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final FeedBackConfigVO result = feedbackConfigDOServiceImplUnderTest.getOneByFeedBackCode("feedbackCode");

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testEdit() {
        // Setup
        final FeedBackUpdateDTO updateDTO = new FeedBackUpdateDTO();
        final FeedbackDimensionsDTO feedbackDimensionsDTO = new FeedbackDimensionsDTO();
        feedbackDimensionsDTO.setType(0);
        final FeedbackOptionsDTO feedbackOptionsDTO = new FeedbackOptionsDTO();
        feedbackDimensionsDTO.setOptionList(List.of(feedbackOptionsDTO));
        updateDTO.setDimensionsList(List.of(feedbackDimensionsDTO));
        updateDTO.setFeedbackCode("feedbackCode");
        updateDTO.setRevision(0);

        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        // Configure FeedbackDimensionsDOMapper.selectListByFeedBackCode(...).
        final FeedbackDimensionsDO feedbackDimensionsDO = new FeedbackDimensionsDO();
        feedbackDimensionsDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackDimensionsDO.setIsDeleted(false);
        feedbackDimensionsDO.setRevision(0);
        feedbackDimensionsDO.setDimensionsCode("dimensionsCode");
        feedbackDimensionsDO.setFeedbackCode("feedbackCode");
        feedbackDimensionsDO.setType(0);
        feedbackDimensionsDO.setOptionJson("[{\"sort\": 1, \"option\": \"支付宝\"}, {\"sort\": 2, \"option\": \"微信支付\"}, {\"sort\": 3, \"option\": \"银行卡支付\"}, {\"sort\": 4, \"option\": \"货到付款\"}, {\"sort\": 5, \"option\": \"其他\"}]");
        feedbackDimensionsDO.setMustInput(0);
        final List<FeedbackDimensionsDO> feedbackDimensionsDOS = List.of(feedbackDimensionsDO);
        when(mockFeedbackDimensionsDOMapper.selectListByFeedBackCode("feedbackCode")).thenReturn(feedbackDimensionsDOS);

        when(mockEcpIdUtil.nextIdStr()).thenReturn("dimensionsCode");

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.edit(updateDTO);

        // Verify the results
        assertThat(result).isFalse();

    }

    @Test
    public void testEdit_FeedbackConfigDOMapperReturnsNull() {
        // Setup
        final FeedBackUpdateDTO updateDTO = new FeedBackUpdateDTO();
        final FeedbackDimensionsDTO feedbackDimensionsDTO = new FeedbackDimensionsDTO();
        feedbackDimensionsDTO.setType(0);
        final FeedbackOptionsDTO feedbackOptionsDTO = new FeedbackOptionsDTO();
        feedbackDimensionsDTO.setOptionList(List.of(feedbackOptionsDTO));
        updateDTO.setDimensionsList(List.of(feedbackDimensionsDTO));
        updateDTO.setFeedbackCode("feedbackCode");
        updateDTO.setRevision(0);

        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(null);

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.edit(updateDTO);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testEdit_FeedbackDimensionsDOMapperSelectListByFeedBackCodeReturnsNoItems() {
        // Setup
        final FeedBackUpdateDTO updateDTO = new FeedBackUpdateDTO();
        final FeedbackDimensionsDTO feedbackDimensionsDTO = new FeedbackDimensionsDTO();
        feedbackDimensionsDTO.setType(0);
        final FeedbackOptionsDTO feedbackOptionsDTO = new FeedbackOptionsDTO();
        feedbackDimensionsDTO.setOptionList(List.of(feedbackOptionsDTO));
        updateDTO.setDimensionsList(List.of(feedbackDimensionsDTO));
        updateDTO.setFeedbackCode("feedbackCode");
        updateDTO.setRevision(0);

        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        when(mockFeedbackDimensionsDOMapper.selectListByFeedBackCode("feedbackCode"))
                .thenReturn(Collections.emptyList());
        when(mockEcpIdUtil.nextIdStr()).thenReturn("dimensionsCode");

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.edit(updateDTO);

        // Verify the results
        assertThat(result).isFalse();

    }

    @Test
    public void testDelete() {
        // Setup
        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        when(mockFeedbackSnapshotDOMapper.selectCount(any(LambdaQueryWrapperX.class))).thenReturn(0L);

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.delete("feedbackCode");

        // Verify the results
        assertThat(result).isFalse();

    }

    @Test
    public void testDelete_FeedbackConfigDOMapperReturnsNull() {
        // Setup
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(null);

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.delete("feedbackCode");

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testSelectFeedBackPage() {
        // Setup
        final PageParam dto = new PageParam();
        dto.setPageNo(1);
        dto.setPageSize(10);

        // Mock FeedbackConfigDO
        FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setId(1L);
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setEnableStatus(1);  // 修改这里，确保状态是待启用
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.now().minusMinutes(1)); // 设置一个过去的时间
        when(baseMapper.selectPage(eq(dto), any(LambdaQueryWrapperX.class))).thenReturn(new PageResult<>(List.of(feedbackConfigDO), 1L));

        final FeedBackConfigVO feedBackConfigVO = new FeedBackConfigVO();
        feedBackConfigVO.setFeedbackDimensionsText("feedbackDimensionsText");
        final FeedbackDimensionsVO feedbackDimensionsVO = new FeedbackDimensionsVO();
        final FeedbackOptionsDTO feedbackOptionsDTO = new FeedbackOptionsDTO();
        feedbackDimensionsVO.setOptionList(List.of(feedbackOptionsDTO));
        feedbackDimensionsVO.setTypeText("typeText");
        feedbackDimensionsVO.setMustInputText("mustInputText");
        feedBackConfigVO.setDimensionsList(List.of(feedbackDimensionsVO));
        feedBackConfigVO.setEnableStatusText("enableStatusText");
        final PageResult<FeedBackConfigVO> expectedResult = new PageResult<>(List.of(feedBackConfigVO), 1L);


        // Run the test
        final PageResult<FeedBackConfigVO> result = feedbackConfigDOServiceImplUnderTest.selectFeedBackPage(dto);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testUpdateEnableStatus_ThrowsServiceException() {
        // Setup
        final FeedBackEnableStatusDTO dto = new FeedBackEnableStatusDTO();
        dto.setFeedbackCode("feedbackCode");
        dto.setEnableStatus(0);
        dto.setEnableTime("2024-04-02 02:21:11");
        dto.setEnableAuto(false);

        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        // Configure FeedbackDimensionsDOMapper.selectListByFeedBackCode(...).
        final FeedbackDimensionsDO feedbackDimensionsDO = new FeedbackDimensionsDO();
        feedbackDimensionsDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackDimensionsDO.setIsDeleted(false);
        feedbackDimensionsDO.setRevision(0);
        feedbackDimensionsDO.setDimensionsCode("dimensionsCode");
        feedbackDimensionsDO.setFeedbackCode("feedbackCode");
        feedbackDimensionsDO.setType(0);
        feedbackDimensionsDO.setOptionJson("[{\"sort\": 1, \"option\": \"支付宝\"}, {\"sort\": 2, \"option\": \"微信支付\"}, {\"sort\": 3, \"option\": \"银行卡支付\"}, {\"sort\": 4, \"option\": \"货到付款\"}, {\"sort\": 5, \"option\": \"其他\"}]");
        feedbackDimensionsDO.setMustInput(0);
        final List<FeedbackDimensionsDO> feedbackDimensionsDOS = List.of(feedbackDimensionsDO);

        // Configure FeedbackConfigDOMapper.selectOneByDimensionsAndStatus(...).
        final FeedbackConfigDO feedbackConfigDO1 = new FeedbackConfigDO();
        feedbackConfigDO1.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setIsDeleted(false);
        feedbackConfigDO1.setRevision(0);
        feedbackConfigDO1.setId(0L);
        feedbackConfigDO1.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO1.setFeedbackCode("feedbackCode");
        feedbackConfigDO1.setEnableStatus(0);
        feedbackConfigDO1.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByDimensionsAndStatus("feedbackDimensions", 1,
                LocalDateTime.of(2024, 4, 2, 2, 21, 11))).thenReturn(feedbackConfigDO1);

        // Run the test
        assertThatThrownBy(() -> feedbackConfigDOServiceImplUnderTest.updateEnableStatus(dto))
                .isInstanceOf(ServiceException.class);

    }

    @Test
    public void testUpdateEnableStatus_FeedbackConfigDOMapperSelectOneByFeedBackCodeReturnsNull() {
        // Setup
        final FeedBackEnableStatusDTO dto = new FeedBackEnableStatusDTO();
        dto.setFeedbackCode("feedbackCode");
        dto.setEnableStatus(0);
        dto.setEnableTime("2024-04-02 02:21:11");
        dto.setEnableAuto(false);

        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(null);

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.updateEnableStatus(dto);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUpdateEnableStatus_FeedbackDimensionsDOMapperReturnsNoItems() {
        // Setup
        final FeedBackEnableStatusDTO dto = new FeedBackEnableStatusDTO();
        dto.setFeedbackCode("feedbackCode");
        dto.setEnableStatus(0);
        dto.setEnableTime("2029-04-02 02:21:11");
        dto.setEnableAuto(false);

        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2029, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);


        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.updateEnableStatus(dto);

        // Verify the results
        assertThat(result).isFalse();

    }

    @Test
    public void testUpdateEnableStatus_FeedbackConfigDOMapperSelectOneByDimensionsAndStatusReturnsNull() {
        // Setup
        final FeedBackEnableStatusDTO dto = new FeedBackEnableStatusDTO();
        dto.setFeedbackCode("feedbackCode");
        dto.setEnableStatus(1);
        dto.setEnableTime("2029-04-02 02:21:11");
        dto.setEnableAuto(false);

        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.now());
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2029, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        when(baseMapper.selectOneByDimensionsAndStatus("feedbackDimensions", 1,
                LocalDateTime.of(2029, 4, 2, 2, 21, 11))).thenReturn(null);

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.updateEnableStatus(dto);

        // Verify the results
        assertThat(result).isFalse();

    }

    @Test
    public void testUpdateDisableStatus() {
        // Setup
        final FeedBackEnableStatusDTO dto = new FeedBackEnableStatusDTO();
        dto.setFeedbackCode("feedbackCode");
        dto.setEnableStatus(0);
        dto.setEnableTime("2024-04-02 02:21:11");
        dto.setEnableAuto(false);

        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        // Configure FeedbackSnapshotDOMapper.selectOne(...).
        final FeedbackSnapshotDO feedbackSnapshotDO = new FeedbackSnapshotDO();
        feedbackSnapshotDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setIsDeleted(false);
        feedbackSnapshotDO.setRevision(0);
        feedbackSnapshotDO.setId(0L);
        feedbackSnapshotDO.setFeedbackCode("feedbackCode");
        feedbackSnapshotDO.setSnapshotCode("snapshotCode");
        feedbackSnapshotDO.setSnapshotJson("snapshotJson");
        feedbackSnapshotDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setSubmitNum(0);
        when(mockFeedbackSnapshotDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(feedbackSnapshotDO);

        when(mockFeedbackRecordsDOMapper.selectCount(any(LambdaQueryWrapperX.class))).thenReturn(0L);

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.updateDisableStatus(dto);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUpdateDisableStatus_FeedbackConfigDOMapperReturnsNull() {
        // Setup
        final FeedBackEnableStatusDTO dto = new FeedBackEnableStatusDTO();
        dto.setFeedbackCode("feedbackCode");
        dto.setEnableStatus(0);
        dto.setEnableTime("2024-04-02 02:21:11");
        dto.setEnableAuto(false);

        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(null);

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.updateDisableStatus(dto);

        // Verify the results
        assertThat(result).isFalse();
    }

    @Test
    public void testUpdateDisableStatus_FeedbackSnapshotDOMapperSelectOneReturnsNull() {
        // Setup
        final FeedBackEnableStatusDTO dto = new FeedBackEnableStatusDTO();
        dto.setFeedbackCode("feedbackCode");
        dto.setEnableStatus(0);
        dto.setEnableTime("2024-04-02 02:21:11");
        dto.setEnableAuto(false);

        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        when(mockFeedbackSnapshotDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(null);

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.updateDisableStatus(dto);

        // Verify the results
        assertThat(result).isFalse();

    }

    @Test
    public void testGetFeedBackByDimensions() {
        // Setup
        final FeedbackQueryDTO dto = new FeedbackQueryDTO();
        dto.setFeedbackDimensions("feedbackDimensions");

        final FeedbackSettingVO expectedResult = new FeedbackSettingVO();
        expectedResult.setSnapshotCode("snapshotCode");
        expectedResult.setFeedbackDimensionsText("feedbackDimensionsText");
        final FeedbackDimensionsAppVO feedbackDimensionsAppVO = new FeedbackDimensionsAppVO();
        final FeedbackOptionsDTO feedbackOptionsDTO = new FeedbackOptionsDTO();
        feedbackDimensionsAppVO.setOptionJson(List.of(feedbackOptionsDTO));
        feedbackDimensionsAppVO.setTypeText("typeText");
        feedbackDimensionsAppVO.setMustInputText("mustInputText");
        expectedResult.setDimensionsContent(List.of(feedbackDimensionsAppVO));

        // Configure FeedbackConfigDOMapper.selectOneByDimensions(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByDimensions("feedbackDimensions")).thenReturn(feedbackConfigDO);

        // Configure FeedbackDimensionsDOMapper.selectListByFeedBackCode(...).
        final FeedbackDimensionsDO feedbackDimensionsDO = new FeedbackDimensionsDO();
        feedbackDimensionsDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackDimensionsDO.setIsDeleted(false);
        feedbackDimensionsDO.setRevision(0);
        feedbackDimensionsDO.setDimensionsCode("dimensionsCode");
        feedbackDimensionsDO.setFeedbackCode("feedbackCode");
        feedbackDimensionsDO.setType(0);
        feedbackDimensionsDO.setOptionJson("[{\"sort\": 1, \"option\": \"支付宝\"}, {\"sort\": 2, \"option\": \"微信支付\"}, {\"sort\": 3, \"option\": \"银行卡支付\"}, {\"sort\": 4, \"option\": \"货到付款\"}, {\"sort\": 5, \"option\": \"其他\"}]");
        feedbackDimensionsDO.setMustInput(0);
        final List<FeedbackDimensionsDO> feedbackDimensionsDOS = List.of(feedbackDimensionsDO);
        when(mockFeedbackDimensionsDOMapper.selectListByFeedBackCode("feedbackCode")).thenReturn(feedbackDimensionsDOS);

        // Configure FeedbackSnapshotDOMapper.selectOne(...).
        final FeedbackSnapshotDO feedbackSnapshotDO = new FeedbackSnapshotDO();
        feedbackSnapshotDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setIsDeleted(false);
        feedbackSnapshotDO.setRevision(0);
        feedbackSnapshotDO.setId(0L);
        feedbackSnapshotDO.setFeedbackCode("feedbackCode");
        feedbackSnapshotDO.setSnapshotCode("snapshotCode");
        feedbackSnapshotDO.setSnapshotJson("snapshotJson");
        feedbackSnapshotDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setSubmitNum(0);
        when(mockFeedbackSnapshotDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(feedbackSnapshotDO);

        // Run the test
        final FeedbackSettingVO result = feedbackConfigDOServiceImplUnderTest.getFeedBackByDimensions(dto);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetFeedBackByDimensions_FeedbackConfigDOMapperReturnsNull() {
        // Setup
        final FeedbackQueryDTO dto = new FeedbackQueryDTO();
        dto.setFeedbackDimensions("feedbackDimensions");

        final FeedbackSettingVO expectedResult = new FeedbackSettingVO();
        expectedResult.setSnapshotCode("snapshotCode");
        expectedResult.setFeedbackDimensionsText("feedbackDimensionsText");
        final FeedbackDimensionsAppVO feedbackDimensionsAppVO = new FeedbackDimensionsAppVO();
        final FeedbackOptionsDTO feedbackOptionsDTO = new FeedbackOptionsDTO();
        feedbackDimensionsAppVO.setOptionJson(List.of(feedbackOptionsDTO));
        feedbackDimensionsAppVO.setTypeText("typeText");
        feedbackDimensionsAppVO.setMustInputText("mustInputText");
        expectedResult.setDimensionsContent(List.of(feedbackDimensionsAppVO));

        when(baseMapper.selectOneByDimensions("feedbackDimensions")).thenReturn(null);

        // Run the test
        final FeedbackSettingVO result = feedbackConfigDOServiceImplUnderTest.getFeedBackByDimensions(dto);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetFeedBackByDimensions_FeedbackDimensionsDOMapperReturnsNoItems() {
        // Setup
        final FeedbackQueryDTO dto = new FeedbackQueryDTO();
        dto.setFeedbackDimensions("feedbackDimensions");

        final FeedbackSettingVO expectedResult = new FeedbackSettingVO();
        expectedResult.setSnapshotCode("snapshotCode");
        expectedResult.setFeedbackDimensionsText("feedbackDimensionsText");
        final FeedbackDimensionsAppVO feedbackDimensionsAppVO = new FeedbackDimensionsAppVO();
        final FeedbackOptionsDTO feedbackOptionsDTO = new FeedbackOptionsDTO();
        feedbackDimensionsAppVO.setOptionJson(List.of(feedbackOptionsDTO));
        feedbackDimensionsAppVO.setTypeText("typeText");
        feedbackDimensionsAppVO.setMustInputText("mustInputText");
        expectedResult.setDimensionsContent(List.of(feedbackDimensionsAppVO));

        // Configure FeedbackConfigDOMapper.selectOneByDimensions(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByDimensions("feedbackDimensions")).thenReturn(feedbackConfigDO);

        when(mockFeedbackDimensionsDOMapper.selectListByFeedBackCode("feedbackCode"))
                .thenReturn(Collections.emptyList());

        // Configure FeedbackSnapshotDOMapper.selectOne(...).
        final FeedbackSnapshotDO feedbackSnapshotDO = new FeedbackSnapshotDO();
        feedbackSnapshotDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setIsDeleted(false);
        feedbackSnapshotDO.setRevision(0);
        feedbackSnapshotDO.setId(0L);
        feedbackSnapshotDO.setFeedbackCode("feedbackCode");
        feedbackSnapshotDO.setSnapshotCode("snapshotCode");
        feedbackSnapshotDO.setSnapshotJson("snapshotJson");
        feedbackSnapshotDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setSubmitNum(0);
        when(mockFeedbackSnapshotDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(feedbackSnapshotDO);

        // Run the test
        final FeedbackSettingVO result = feedbackConfigDOServiceImplUnderTest.getFeedBackByDimensions(dto);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testLaunchFeedback() {
        // Setup
        String feedbackCode = "feedbackCode";

        // Mock FeedbackConfigDO
        FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setId(1L);
        feedbackConfigDO.setFeedbackCode(feedbackCode);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setEnableStatus(1);  // 修改这里，确保状态是待启用
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.now().minusMinutes(1)); // 设置一个过去的时间



        // Mock FeedbackSnapshotDO
        FeedbackSnapshotDO snapshotDO = new FeedbackSnapshotDO();
        snapshotDO.setId(1L);
        snapshotDO.setFeedbackCode(feedbackCode);
        snapshotDO.setSnapshotCode("snapshotCode");
        snapshotDO.setSubmitNum(0);

        // Mock FeedbackDimensionsDO
        FeedbackDimensionsDO dimensionsDO = new FeedbackDimensionsDO();
        dimensionsDO.setId(1L);
        dimensionsDO.setDimensionsCode("dimensionsCode");
        dimensionsDO.setFeedbackCode(feedbackCode);
        dimensionsDO.setType(0);
        dimensionsDO.setOptionJson("[{\"option\":\"test\"}]");
        List<FeedbackDimensionsDO> dimensionsList = Collections.singletonList(dimensionsDO);


    }

    @Test
    public void testLaunchFeedback_FeedbackConfigDOMapperSelectOneByFeedBackCodeReturnsNull() {
        // Setup
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(null);

        // Run the test
        feedbackConfigDOServiceImplUnderTest.launchFeedback("feedbackCode");

        // Verify the results
    }

    @Test
    public void testLaunchFeedback_FeedbackConfigDOMapperSelectOneByDimensionsAndStatusReturnsNull() {
        // Setup
        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        when(baseMapper.selectOneByDimensionsAndStatus("feedbackDimensions", 2,
                null)).thenReturn(null);

        // Configure FeedbackDimensionsDOMapper.selectListByFeedBackCode(...).
        final FeedbackDimensionsDO feedbackDimensionsDO = new FeedbackDimensionsDO();
        feedbackDimensionsDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackDimensionsDO.setIsDeleted(false);
        feedbackDimensionsDO.setRevision(0);
        feedbackDimensionsDO.setDimensionsCode("dimensionsCode");
        feedbackDimensionsDO.setFeedbackCode("feedbackCode");
        feedbackDimensionsDO.setType(0);
        feedbackDimensionsDO.setOptionJson("[{\"sort\": 1, \"option\": \"支付宝\"}, {\"sort\": 2, \"option\": \"微信支付\"}, {\"sort\": 3, \"option\": \"银行卡支付\"}, {\"sort\": 4, \"option\": \"货到付款\"}, {\"sort\": 5, \"option\": \"其他\"}]");
        feedbackDimensionsDO.setMustInput(0);
        final List<FeedbackDimensionsDO> feedbackDimensionsDOS = List.of(feedbackDimensionsDO);
        when(mockFeedbackDimensionsDOMapper.selectListByFeedBackCode("feedbackCode")).thenReturn(feedbackDimensionsDOS);

        // Run the test
        feedbackConfigDOServiceImplUnderTest.launchFeedback("feedbackCode");

    }

    @Test
    public void testLaunchFeedback_FeedbackSnapshotDOMapperSelectOneReturnsNull() {
        // Setup
        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        // Configure FeedbackConfigDOMapper.selectOneByDimensionsAndStatus(...).
        final FeedbackConfigDO feedbackConfigDO1 = new FeedbackConfigDO();
        feedbackConfigDO1.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setIsDeleted(false);
        feedbackConfigDO1.setRevision(0);
        feedbackConfigDO1.setId(0L);
        feedbackConfigDO1.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO1.setFeedbackCode("feedbackCode");
        feedbackConfigDO1.setEnableStatus(0);
        feedbackConfigDO1.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setLastModifyUser("lastModifyUser");


        // Configure FeedbackDimensionsDOMapper.selectListByFeedBackCode(...).
        final FeedbackDimensionsDO feedbackDimensionsDO = new FeedbackDimensionsDO();
        feedbackDimensionsDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackDimensionsDO.setIsDeleted(false);
        feedbackDimensionsDO.setRevision(0);
        feedbackDimensionsDO.setDimensionsCode("dimensionsCode");
        feedbackDimensionsDO.setFeedbackCode("feedbackCode");
        feedbackDimensionsDO.setType(0);
        feedbackDimensionsDO.setOptionJson("[{\"sort\": 1, \"option\": \"支付宝\"}, {\"sort\": 2, \"option\": \"微信支付\"}, {\"sort\": 3, \"option\": \"银行卡支付\"}, {\"sort\": 4, \"option\": \"货到付款\"}, {\"sort\": 5, \"option\": \"其他\"}]");
        feedbackDimensionsDO.setMustInput(0);
        final List<FeedbackDimensionsDO> feedbackDimensionsDOS = List.of(feedbackDimensionsDO);
        when(mockFeedbackDimensionsDOMapper.selectListByFeedBackCode("feedbackCode")).thenReturn(feedbackDimensionsDOS);

        // Run the test
        feedbackConfigDOServiceImplUnderTest.launchFeedback("feedbackCode");

    }

    @Test
    public void testLaunchFeedback_FeedbackDimensionsDOMapperReturnsNoItems() {
        // Setup
        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        // Configure FeedbackConfigDOMapper.selectOneByDimensionsAndStatus(...).
        final FeedbackConfigDO feedbackConfigDO1 = new FeedbackConfigDO();
        feedbackConfigDO1.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setIsDeleted(false);
        feedbackConfigDO1.setRevision(0);
        feedbackConfigDO1.setId(0L);
        feedbackConfigDO1.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO1.setFeedbackCode("feedbackCode");
        feedbackConfigDO1.setEnableStatus(0);
        feedbackConfigDO1.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setLastModifyUser("lastModifyUser");

        // Configure FeedbackSnapshotDOMapper.selectOne(...).
        final FeedbackSnapshotDO feedbackSnapshotDO = new FeedbackSnapshotDO();
        feedbackSnapshotDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setIsDeleted(false);
        feedbackSnapshotDO.setRevision(0);
        feedbackSnapshotDO.setId(0L);
        feedbackSnapshotDO.setFeedbackCode("feedbackCode");
        feedbackSnapshotDO.setSnapshotCode("snapshotCode");
        feedbackSnapshotDO.setSnapshotJson("snapshotJson");
        feedbackSnapshotDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setSubmitNum(0);

        when(mockFeedbackDimensionsDOMapper.selectListByFeedBackCode("feedbackCode"))
                .thenReturn(Collections.emptyList());

        // Run the test
        feedbackConfigDOServiceImplUnderTest.launchFeedback("feedbackCode");

    }

    @Test
    public void testGetPassTimeUnEnableFeedback() {
        // Setup
        // Configure FeedbackConfigDOMapper.selectList(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        final List<FeedbackConfigDO> feedbackConfigDOS = List.of(feedbackConfigDO);
        when(baseMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(feedbackConfigDOS);

        // Run the test
        final List<String> result = feedbackConfigDOServiceImplUnderTest.getPassTimeUnEnableFeedback(0);

        // Verify the results
        assertThat(result).isEqualTo(List.of("feedbackCode"));
    }

    @Test
    public void testGetPassTimeUnEnableFeedback_FeedbackConfigDOMapperReturnsNoItems() {
        // Setup
        when(baseMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final List<String> result = feedbackConfigDOServiceImplUnderTest.getPassTimeUnEnableFeedback(0);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testHandPassUnEnableFeedback() {
        // Setup
        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        // Configure FeedbackConfigDOMapper.selectOneByDimensionsAndStatus(...).
        final FeedbackConfigDO feedbackConfigDO1 = new FeedbackConfigDO();
        feedbackConfigDO1.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setIsDeleted(false);
        feedbackConfigDO1.setRevision(0);
        feedbackConfigDO1.setId(0L);
        feedbackConfigDO1.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO1.setFeedbackCode("feedbackCode");
        feedbackConfigDO1.setEnableStatus(0);
        feedbackConfigDO1.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setLastModifyUser("lastModifyUser");

        // Configure FeedbackSnapshotDOMapper.selectOne(...).
        final FeedbackSnapshotDO feedbackSnapshotDO = new FeedbackSnapshotDO();
        feedbackSnapshotDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setIsDeleted(false);
        feedbackSnapshotDO.setRevision(0);
        feedbackSnapshotDO.setId(0L);
        feedbackSnapshotDO.setFeedbackCode("feedbackCode");
        feedbackSnapshotDO.setSnapshotCode("snapshotCode");
        feedbackSnapshotDO.setSnapshotJson("snapshotJson");
        feedbackSnapshotDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setSubmitNum(0);


        // Configure FeedbackDimensionsDOMapper.selectListByFeedBackCode(...).
        final FeedbackDimensionsDO feedbackDimensionsDO = new FeedbackDimensionsDO();
        feedbackDimensionsDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackDimensionsDO.setIsDeleted(false);
        feedbackDimensionsDO.setRevision(0);
        feedbackDimensionsDO.setDimensionsCode("dimensionsCode");
        feedbackDimensionsDO.setFeedbackCode("feedbackCode");
        feedbackDimensionsDO.setType(0);
        feedbackDimensionsDO.setOptionJson("[{\"sort\": 1, \"option\": \"支付宝\"}, {\"sort\": 2, \"option\": \"微信支付\"}, {\"sort\": 3, \"option\": \"银行卡支付\"}, {\"sort\": 4, \"option\": \"货到付款\"}, {\"sort\": 5, \"option\": \"其他\"}]");
        feedbackDimensionsDO.setMustInput(0);
        final List<FeedbackDimensionsDO> feedbackDimensionsDOS = List.of(feedbackDimensionsDO);
        when(mockFeedbackDimensionsDOMapper.selectListByFeedBackCode("feedbackCode")).thenReturn(feedbackDimensionsDOS);

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.handPassUnEnableFeedback("feedbackCode");

        // Verify the results
        assertThat(result).isTrue();

    }

    @Test
    public void testHandPassUnEnableFeedback_FeedbackConfigDOMapperSelectOneByFeedBackCodeReturnsNull() {
        // Setup
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(null);

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.handPassUnEnableFeedback("feedbackCode");

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testHandPassUnEnableFeedback_FeedbackConfigDOMapperSelectOneByDimensionsAndStatusReturnsNull() {
        // Setup
        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);


        // Configure FeedbackDimensionsDOMapper.selectListByFeedBackCode(...).
        final FeedbackDimensionsDO feedbackDimensionsDO = new FeedbackDimensionsDO();
        feedbackDimensionsDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackDimensionsDO.setIsDeleted(false);
        feedbackDimensionsDO.setRevision(0);
        feedbackDimensionsDO.setDimensionsCode("dimensionsCode");
        feedbackDimensionsDO.setFeedbackCode("feedbackCode");
        feedbackDimensionsDO.setType(0);
        feedbackDimensionsDO.setOptionJson("[{\"sort\": 1, \"option\": \"支付宝\"}, {\"sort\": 2, \"option\": \"微信支付\"}, {\"sort\": 3, \"option\": \"银行卡支付\"}, {\"sort\": 4, \"option\": \"货到付款\"}, {\"sort\": 5, \"option\": \"其他\"}]");
        feedbackDimensionsDO.setMustInput(0);
        final List<FeedbackDimensionsDO> feedbackDimensionsDOS = List.of(feedbackDimensionsDO);
        when(mockFeedbackDimensionsDOMapper.selectListByFeedBackCode("feedbackCode")).thenReturn(feedbackDimensionsDOS);

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.handPassUnEnableFeedback("feedbackCode");

        // Verify the results
        assertThat(result).isTrue();

    }

    @Test
    public void testHandPassUnEnableFeedback_FeedbackSnapshotDOMapperSelectOneReturnsNull() {
        // Setup
        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        // Configure FeedbackConfigDOMapper.selectOneByDimensionsAndStatus(...).
        final FeedbackConfigDO feedbackConfigDO1 = new FeedbackConfigDO();
        feedbackConfigDO1.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setIsDeleted(false);
        feedbackConfigDO1.setRevision(0);
        feedbackConfigDO1.setId(0L);
        feedbackConfigDO1.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO1.setFeedbackCode("feedbackCode");
        feedbackConfigDO1.setEnableStatus(0);
        feedbackConfigDO1.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setLastModifyUser("lastModifyUser");


        // Configure FeedbackDimensionsDOMapper.selectListByFeedBackCode(...).
        final FeedbackDimensionsDO feedbackDimensionsDO = new FeedbackDimensionsDO();
        feedbackDimensionsDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackDimensionsDO.setIsDeleted(false);
        feedbackDimensionsDO.setRevision(0);
        feedbackDimensionsDO.setDimensionsCode("dimensionsCode");
        feedbackDimensionsDO.setFeedbackCode("feedbackCode");
        feedbackDimensionsDO.setType(0);
        feedbackDimensionsDO.setOptionJson("[{\"sort\": 1, \"option\": \"支付宝\"}, {\"sort\": 2, \"option\": \"微信支付\"}, {\"sort\": 3, \"option\": \"银行卡支付\"}, {\"sort\": 4, \"option\": \"货到付款\"}, {\"sort\": 5, \"option\": \"其他\"}]");
        feedbackDimensionsDO.setMustInput(0);
        final List<FeedbackDimensionsDO> feedbackDimensionsDOS = List.of(feedbackDimensionsDO);
        when(mockFeedbackDimensionsDOMapper.selectListByFeedBackCode("feedbackCode")).thenReturn(feedbackDimensionsDOS);

        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.handPassUnEnableFeedback("feedbackCode");

        // Verify the results
        assertThat(result).isTrue();


    }

    @Test
    public void testHandPassUnEnableFeedback_FeedbackDimensionsDOMapperReturnsNoItems() {
        // Setup
        // Configure FeedbackConfigDOMapper.selectOneByFeedBackCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        when(baseMapper.selectOneByFeedBackCode("feedbackCode")).thenReturn(feedbackConfigDO);

        // Configure FeedbackConfigDOMapper.selectOneByDimensionsAndStatus(...).
        final FeedbackConfigDO feedbackConfigDO1 = new FeedbackConfigDO();
        feedbackConfigDO1.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setIsDeleted(false);
        feedbackConfigDO1.setRevision(0);
        feedbackConfigDO1.setId(0L);
        feedbackConfigDO1.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO1.setFeedbackCode("feedbackCode");
        feedbackConfigDO1.setEnableStatus(0);
        feedbackConfigDO1.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO1.setLastModifyUser("lastModifyUser");

        // Configure FeedbackSnapshotDOMapper.selectOne(...).
        final FeedbackSnapshotDO feedbackSnapshotDO = new FeedbackSnapshotDO();
        feedbackSnapshotDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setIsDeleted(false);
        feedbackSnapshotDO.setRevision(0);
        feedbackSnapshotDO.setId(0L);
        feedbackSnapshotDO.setFeedbackCode("feedbackCode");
        feedbackSnapshotDO.setSnapshotCode("snapshotCode");
        feedbackSnapshotDO.setSnapshotJson("snapshotJson");
        feedbackSnapshotDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setSubmitNum(0);


        // Run the test
        final Boolean result = feedbackConfigDOServiceImplUnderTest.handPassUnEnableFeedback("feedbackCode");

        // Verify the results
        assertThat(result).isTrue();

    }

    @Test
    public void testGetFeedBackCodeListByDimensionsCode() {
        // Setup
        // Configure FeedbackConfigDOMapper.selectFeedBackCodeListByDimensionsCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        final List<FeedbackConfigDO> feedbackConfigDOS = List.of(feedbackConfigDO);
        when(baseMapper.selectFeedBackCodeListByDimensionsCode("dimensionsCode"))
                .thenReturn(feedbackConfigDOS);

        // Configure FeedbackSnapshotDOMapper.getSnapshotCodeByFeedbackCode(...).
        final FeedbackSnapshotDO feedbackSnapshotDO = new FeedbackSnapshotDO();
        feedbackSnapshotDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setIsDeleted(false);
        feedbackSnapshotDO.setRevision(0);
        feedbackSnapshotDO.setId(0L);
        feedbackSnapshotDO.setFeedbackCode("feedbackCode");
        feedbackSnapshotDO.setSnapshotCode("snapshotCode");
        feedbackSnapshotDO.setSnapshotJson("snapshotJson");
        feedbackSnapshotDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setSubmitNum(0);
        final List<FeedbackSnapshotDO> feedbackSnapshotDOS = List.of(feedbackSnapshotDO);


        // Run the test
        final List<String> result = feedbackConfigDOServiceImplUnderTest.getFeedBackCodeListByDimensionsCode(
                "dimensionsCode");

        // Verify the results
        assertThat(result).isEqualTo(List.of());
    }

    @Test
    public void testGetFeedBackCodeListByDimensionsCode_FeedbackConfigDOMapperReturnsNoItems() {
        // Setup
        when(baseMapper.selectFeedBackCodeListByDimensionsCode("dimensionsCode"))
                .thenReturn(Collections.emptyList());

        // Configure FeedbackSnapshotDOMapper.getSnapshotCodeByFeedbackCode(...).
        final FeedbackSnapshotDO feedbackSnapshotDO = new FeedbackSnapshotDO();
        feedbackSnapshotDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setIsDeleted(false);
        feedbackSnapshotDO.setRevision(0);
        feedbackSnapshotDO.setId(0L);
        feedbackSnapshotDO.setFeedbackCode("feedbackCode");
        feedbackSnapshotDO.setSnapshotCode("snapshotCode");
        feedbackSnapshotDO.setSnapshotJson("snapshotJson");
        feedbackSnapshotDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackSnapshotDO.setSubmitNum(0);
        final List<FeedbackSnapshotDO> feedbackSnapshotDOS = List.of(feedbackSnapshotDO);


        // Run the test
        final List<String> result = feedbackConfigDOServiceImplUnderTest.getFeedBackCodeListByDimensionsCode(
                "dimensionsCode");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testGetFeedBackCodeListByDimensionsCode_FeedbackSnapshotDOMapperReturnsNoItems() {
        // Setup
        // Configure FeedbackConfigDOMapper.selectFeedBackCodeListByDimensionsCode(...).
        final FeedbackConfigDO feedbackConfigDO = new FeedbackConfigDO();
        feedbackConfigDO.setUpdatedTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setIsDeleted(false);
        feedbackConfigDO.setRevision(0);
        feedbackConfigDO.setId(0L);
        feedbackConfigDO.setFeedbackDimensions("feedbackDimensions");
        feedbackConfigDO.setFeedbackCode("feedbackCode");
        feedbackConfigDO.setEnableStatus(0);
        feedbackConfigDO.setEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setDowntime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setScheduleEnableTime(LocalDateTime.of(2024, 4, 2, 2, 21, 11));
        feedbackConfigDO.setLastModifyUser("lastModifyUser");
        final List<FeedbackConfigDO> feedbackConfigDOS = List.of(feedbackConfigDO);
        when(baseMapper.selectFeedBackCodeListByDimensionsCode("dimensionsCode"))
                .thenReturn(feedbackConfigDOS);



        // Run the test
        final List<String> result = feedbackConfigDOServiceImplUnderTest.getFeedBackCodeListByDimensionsCode(
                "dimensionsCode");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }
}

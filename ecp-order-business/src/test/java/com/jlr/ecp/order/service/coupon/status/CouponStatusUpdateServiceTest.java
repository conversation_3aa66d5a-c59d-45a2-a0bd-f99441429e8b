package com.jlr.ecp.order.service.coupon.status;

import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.dal.dataobject.order.OrderCouponDetailDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderInfoDO;
import com.jlr.ecp.order.dal.dataobject.order.OrderItemDO;
import com.jlr.ecp.order.dal.mysql.order.OrderCouponDetailDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderInfoDOMapper;
import com.jlr.ecp.order.dal.mysql.order.OrderItemDOMapper;
import com.jlr.ecp.order.enums.order.EcouponStatusEnum;
import com.jlr.ecp.order.enums.order.OrderItemCouponStatusEnum;
import com.jlr.ecp.order.enums.order.OrderStatusEnum;
import com.jlr.ecp.order.service.coupon.status.Enum.CouponStatusFromSqsEnum;
import com.jlr.ecp.order.service.coupon.status.dto.CouponStatusNotifyDto;
import com.jlr.ecp.order.util.RedisReentrantLockUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.transaction.support.TransactionCallback;
import software.amazon.awssdk.services.sqs.model.Message;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class CouponStatusUpdateServiceTest {

    @InjectMocks
    private CouponStatusUpdateService couponStatusUpdateService;

    @Mock
    private OrderCouponDetailDOMapper orderCouponDetailDOMapper;

    @Mock
    private OrderInfoDOMapper orderInfoDOMapper;

    @Mock
    private OrderItemDOMapper orderItemDOMapper;

    @Mock
    private TransactionOperator transactionOperator;

    @Mock
    private ProducerTool producerTool;

    @Mock
    private RedisReentrantLockUtil redisReentrantLockUtil;

    @Mock
    private Redisson redisson;

    @Mock
    private RedisService redisService;

    @Mock
    private RLock rLock;

    private CouponStatusNotifyDto couponStatusNotifyDto;
    private OrderCouponDetailDO orderCouponDetailDO;
    private OrderCouponDetailDO orderCouponDetailDO2;
    private OrderCouponDetailDO orderCouponDetailDO3;

    private OrderItemDO orderItemDO;
    private OrderItemDO orderItemDO2;
    private OrderItemDO orderItemDO3;
    private OrderInfoDO orderInfoDO;
    private Message message;

    @Before
    public void setUp() {
        couponStatusNotifyDto = new CouponStatusNotifyDto();
        couponStatusNotifyDto.setCouponCode("testCouponCode");
        couponStatusNotifyDto.setCouponStatus(CouponStatusFromSqsEnum.USED.getCouponStatus());
        couponStatusNotifyDto.setUpdateTime(LocalDateTime.now());

        orderCouponDetailDO = new OrderCouponDetailDO();
        orderCouponDetailDO.setCouponCode("testCouponCode");
        orderCouponDetailDO.setOrderCode("testOrderCode");
        orderCouponDetailDO.setOrderItemCode("testOrderItemCode");
        orderCouponDetailDO.setStatus(EcouponStatusEnum.PENDING_USE.getCode());

        orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode("testOrderItemCode");
        orderItemDO.setItemStatus(OrderItemCouponStatusEnum.PENDING_VERIFICATION.getCode());

        orderInfoDO = new OrderInfoDO();
        orderInfoDO.setOrderCode("testOrderCode");
        orderInfoDO.setOrderStatus(OrderStatusEnum.ORDERED.getCode());

        message = Message.builder().build();
    }


    @Test
    public void testDealWithMessage_CouponNotFound() {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(null);

        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertTrue(result);
        verify(orderCouponDetailDOMapper, never()).updateById(any(OrderCouponDetailDO.class));
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testDealWithMessage_OrderStatusMismatch() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
//        when(orderItemDOMapper.getByOrderItemCode(anyString())).thenReturn(orderItemDO);
//        when(orderInfoDOMapper.getParentCode(anyString())).thenReturn(orderInfoDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);
        
        // 设置订单状态为已支付，这样会导致状态不匹配
        orderInfoDO.setOrderStatus(OrderStatusEnum.PAID.getCode());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
        verify(transactionOperator, never()).updateOrderCouponAndOrderInfo(any(), any(), any());
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testDealWithMessage_LockAcquisitionFailed() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(false);

        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
        verify(transactionOperator, never()).updateOrderCouponAndOrderInfo(any(), any(), any());
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testDealWithMessage_OrderItemNotFound() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
//        when(orderItemDOMapper.getByOrderItemCode(anyString())).thenReturn(null);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);

        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
        verify(orderCouponDetailDOMapper, never()).updateById(any(OrderCouponDetailDO.class));
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testDealWithMessage_OrderInfoNotFound() {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
        when(orderInfoDOMapper.selectOne(any())).thenReturn(orderInfoDO);
//        when(orderItemDOMapper.getByOrderItemCode(anyString())).thenReturn(orderItemDO);
//        when(orderInfoDOMapper.getParentCode(anyString())).thenReturn(null);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);

        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
        verify(orderCouponDetailDOMapper, never()).updateById(any(OrderCouponDetailDO.class));
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }




    @Test
    public void testDealWithMessage_LockException() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
//        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenThrow(new InterruptedException("Test exception"));

        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
        verify(orderCouponDetailDOMapper, never()).updateById(any(OrderCouponDetailDO.class));
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }






    @Test
    public void testDealWithMessage_ExpiredStatus() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
//        when(orderItemDOMapper.getByOrderItemCode(anyString())).thenReturn(orderItemDO);
//        when(orderInfoDOMapper.getParentCode(anyString())).thenReturn(orderInfoDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);
        
        // 设置优惠券状态为已过期
        couponStatusNotifyDto.setCouponStatus(CouponStatusFromSqsEnum.EXPIRED.getCouponStatus());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertTrue(result);
        verify(transactionOperator).updateOrderCouponDetail(any(), any(), anyInt());
        verify(producerTool).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testDealWithMessage_CancelledStatus() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
//        when(orderItemDOMapper.getByOrderItemCode(anyString())).thenReturn(orderItemDO);
//        when(orderInfoDOMapper.getParentCode(anyString())).thenReturn(orderInfoDO);
//        when(redisson.getLock(anyString())).thenReturn(rLock);
//        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
//        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);
        
        // 设置优惠券状态为已作废
        couponStatusNotifyDto.setCouponStatus(CouponStatusFromSqsEnum.CANCELLED.getCouponStatus());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertTrue(result);
        verify(orderCouponDetailDOMapper).updateById(any(OrderCouponDetailDO.class));
        verify(producerTool).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testDealWithMessage_OrderItemStatusMismatch() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
//        when(orderItemDOMapper.getByOrderItemCode(anyString())).thenReturn(orderItemDO);
//        when(orderInfoDOMapper.getParentCode(anyString())).thenReturn(orderInfoDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);
        
        // 设置订单项状态为已核销，这样会导致状态不匹配
        orderItemDO.setItemStatus(OrderItemCouponStatusEnum.VERIFIED.getCode());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
        verify(transactionOperator, never()).updateOrderCouponAndOrderInfo(any(), any(), any());
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }



    @Test
    public void testDealWithMessage_OrderItemReclaimedStatus() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
//        when(orderItemDOMapper.getByOrderItemCode(anyString())).thenReturn(orderItemDO);
//        when(orderInfoDOMapper.getParentCode(anyString())).thenReturn(orderInfoDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);
        
        // 设置订单项状态为已回收
        orderItemDO.setItemStatus(OrderItemCouponStatusEnum.RECLAIMED.getCode());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
        verify(transactionOperator, never()).updateOrderCouponAndOrderInfo(any(), any(), any());
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testDealWithMessage_OrderItemPendingIssuanceStatus() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
//        when(orderItemDOMapper.getByOrderItemCode(anyString())).thenReturn(orderItemDO);
//        when(orderInfoDOMapper.getParentCode(anyString())).thenReturn(orderInfoDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);
        
        // 设置订单项状态为待发放
        orderItemDO.setItemStatus(OrderItemCouponStatusEnum.PENDING_ISSUANCE.getCode());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
        verify(transactionOperator, never()).updateOrderCouponAndOrderInfo(any(), any(), any());
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testDealWithMessage_OrderCompletedStatus() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
//        when(orderItemDOMapper.getByOrderItemCode(anyString())).thenReturn(orderItemDO);
//        when(orderInfoDOMapper.getParentCode(anyString())).thenReturn(orderInfoDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);
        
        // 设置订单状态为已完成
        orderInfoDO.setOrderStatus(OrderStatusEnum.COMPLETED.getCode());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
        verify(transactionOperator, never()).updateOrderCouponAndOrderInfo(any(), any(), any());
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testDealWithMessage_OrderClosedStatus() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
//        when(orderItemDOMapper.getByOrderItemCode(anyString())).thenReturn(orderItemDO);
//        when(orderInfoDOMapper.getParentCode(anyString())).thenReturn(orderInfoDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);
        
        // 设置订单状态为已关闭
        orderInfoDO.setOrderStatus(OrderStatusEnum.CLOSED.getCode());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
        verify(transactionOperator, never()).updateOrderCouponAndOrderInfo(any(), any(), any());
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testUpdateThreeOrderInfo_Success() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);

        // 设置订单状态为已下单，这样可以通过状态检查
        orderInfoDO.setOrderStatus(OrderStatusEnum.ORDERED.getCode());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
//        assertTrue(result);
//        verify(transactionOperator).updateOrderCouponAndOrderInfo(any(), any(), any());
//        verify(producerTool).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testUpdateThreeOrderInfo_KafkaSendFailed() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);

        // 设置订单状态为已下单，这样可以通过状态检查
        orderInfoDO.setOrderStatus(OrderStatusEnum.ORDERED.getCode());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
//        assertTrue(result);
//        verify(transactionOperator).updateOrderCouponAndOrderInfo(any(), any(), any());
//        verify(producerTool).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testUpdateThreeOrderInfo_OrderNotFound() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);

        // 设置订单状态为已下单，这样可以通过状态检查
        orderInfoDO.setOrderStatus(OrderStatusEnum.ORDERED.getCode());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
        verify(transactionOperator, never()).updateOrderCouponAndOrderInfo(any(), any(), any());
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testUpdateThreeOrderInfo_OrderItemNotFound() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);

        // 设置订单状态为已下单，这样可以通过状态检查
        orderInfoDO.setOrderStatus(OrderStatusEnum.ORDERED.getCode());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
        verify(transactionOperator, never()).updateOrderCouponAndOrderInfo(any(), any(), any());
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testUpdateThreeOrderInfo_TransactionFailed() throws InterruptedException {
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);

        // 设置订单状态为已下单，这样可以通过状态检查
        orderInfoDO.setOrderStatus(OrderStatusEnum.ORDERED.getCode());
        // 设置订单行状态为待核销，这样可以通过状态检查
        orderItemDO.setItemStatus(OrderItemCouponStatusEnum.PENDING_VERIFICATION.getCode());
        // 设置优惠券状态为待使用，这样可以通过状态检查
        orderCouponDetailDO.setStatus(EcouponStatusEnum.PENDING_USE.getCode());
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertFalse(result);
//        verify(transactionOperator).updateOrderCouponAndOrderInfo(any(), any(), any());
        verify(producerTool, never()).sendMsg(anyString(), anyString(), any());
    }

    @Test
    public void testDealWithMessage_UpdateTwoTables(){
        // 设置mock行为
        when(orderCouponDetailDOMapper.selectOne(any())).thenReturn(orderCouponDetailDO);
        when(redisson.getLock(anyString())).thenReturn(rLock);
        when(redisReentrantLockUtil.tryLock(any(), anyInt(), anyInt(), any())).thenReturn(true);
        when(redisReentrantLockUtil.unlock(any(), anyInt())).thenReturn(true);
        
        // 设置订单状态为已下单，这样可以通过状态检查
        orderInfoDO.setOrderStatus(OrderStatusEnum.ORDERED.getCode());
        
        // 初始化并设置orderItemDO的所有必要字段
        orderItemDO = new OrderItemDO();
        orderItemDO.setOrderItemCode("testOrderItemCode");
        orderItemDO.setOrderCode("testOrderCode");
        orderItemDO.setItemStatus(OrderItemCouponStatusEnum.PENDING_VERIFICATION.getCode());
        orderItemDO.setIsDeleted(false);
        orderItemDO.setId(1L);
        
        // 设置优惠券状态为待使用，这样可以通过状态检查
        orderCouponDetailDO.setStatus(EcouponStatusEnum.PENDING_USE.getCode());
        orderCouponDetailDO.setOrderCode("testOrderCode");
        orderCouponDetailDO.setOrderItemCode("testOrderItemCode");
        
        // 模拟查询其他订单券明细，返回空列表，表示所有券都是终态
        when(orderCouponDetailDOMapper.selectList(any())).thenReturn(new ArrayList<>());
        when(orderItemDOMapper.selectOne(any())).thenReturn(orderItemDO);
        
        // 创建多个订单行数据
        OrderItemDO orderItemDO1 = new OrderItemDO();
        orderItemDO1.setOrderItemCode("testOrderItemCode1");
        orderItemDO1.setOrderCode("testOrderCode");
        orderItemDO1.setItemStatus(OrderItemCouponStatusEnum.PENDING_VERIFICATION.getCode()); // 非终态
        orderItemDO1.setIsDeleted(false);
        orderItemDO1.setId(2L);
        
        OrderItemDO orderItemDO2 = new OrderItemDO();
        orderItemDO2.setOrderItemCode("testOrderItemCode2");
        orderItemDO2.setOrderCode("testOrderCode");
        orderItemDO2.setItemStatus(OrderItemCouponStatusEnum.VERIFIED.getCode()); // 终态
        orderItemDO2.setIsDeleted(false);
        orderItemDO2.setId(3L);
        
        // 模拟查询订单行列表，返回多条数据
        List<OrderItemDO> orderItemList = Arrays.asList(orderItemDO1, orderItemDO2);
        when(orderItemDOMapper.selectList(any())).thenReturn(orderItemList);
        
        // 执行测试
        Boolean result = couponStatusUpdateService.dealWithMessage(message, couponStatusNotifyDto);

        // 验证结果
        assertTrue(result);
        verify(transactionOperator).updateOrderCouponAndOrderItemInfo(any(), any());
        verify(producerTool).sendMsg(anyString(), anyString(), any());
    }
} 
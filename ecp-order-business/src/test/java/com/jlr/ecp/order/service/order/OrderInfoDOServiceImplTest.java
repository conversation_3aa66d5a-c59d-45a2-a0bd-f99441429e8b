package com.jlr.ecp.order.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jlr.ecp.consumer.api.consumer.ConsumerApi;
import com.jlr.ecp.consumer.api.consumer.dto.ConsumerInfoDTO;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.security.core.service.SecurityFrameworkService;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.notification.api.dto.ShortLinkReqDto;
import com.jlr.ecp.notification.api.jaguarlandover.ShorLinkAPI;
import com.jlr.ecp.order.api.order.dto.*;
import com.jlr.ecp.order.api.order.dto.address.OrderGiftAddressDTO;
import com.jlr.ecp.order.api.order.dto.customer.GuestOrderCreateCustomerServiceDTO;
import com.jlr.ecp.order.api.order.dto.customer.ResendMessageDTO;
import com.jlr.ecp.order.api.order.vo.*;
import com.jlr.ecp.order.api.order.vo.address.AppOrderGiftAddressDetailVO;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppDetailPage;
import com.jlr.ecp.order.api.order.vo.apporderdetail.OrderAppDetailPolicyRespVO;
import com.jlr.ecp.order.api.order.vo.base.OrderItemBaseVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.OrderBrandOrderInfoVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.OrderBrandVehicleInfoVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.OrderNewBrandRespVO;
import com.jlr.ecp.order.api.order.vo.brandcategory.ProductBrandCategoriedItemInfoVO;
import com.jlr.ecp.order.api.order.vo.detail.*;
import com.jlr.ecp.order.api.payment.dto.PayRequestDTO;
import com.jlr.ecp.order.component.SubmitPayOrderComponent;
import com.jlr.ecp.order.config.RedisService;
import com.jlr.ecp.order.controller.app.order.dto.OrderLatestListReqDTO;
import com.jlr.ecp.order.controller.app.order.vo.Integration.OrderIntegrationLatestOrderRespVO;
import com.jlr.ecp.order.controller.app.order.vo.Integration.OrderIntegrationPageRespVO;
import com.jlr.ecp.order.controller.app.order.vo.Integration.crcQueryOrderInfo.OrderIntegrationRespVO;
import com.jlr.ecp.order.controller.app.order.vo.PayOrderRespVO;
import com.jlr.ecp.order.dal.dataobject.customer.order.CustomerServiceOrderDO;
import com.jlr.ecp.order.dal.dataobject.order.*;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundDO;
import com.jlr.ecp.order.dal.dataobject.refund.OrderRefundItemDO;
import com.jlr.ecp.order.dal.mysql.customer.order.CustomerServiceOrderDOMapper;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackConfigDOMapper;
import com.jlr.ecp.order.dal.mysql.feedback.FeedbackRecordsDOMapper;
import com.jlr.ecp.order.dal.mysql.order.*;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundDOMapper;
import com.jlr.ecp.order.dal.mysql.refund.OrderRefundItemDOMapper;
import com.jlr.ecp.order.enums.order.EcouponStatusEnum;
import com.jlr.ecp.order.enums.order.OrderTypeEnum;
import com.jlr.ecp.order.enums.order.RefundCouponStatusEnum;
import com.jlr.ecp.order.enums.order.RefundOrderOperationTypeEnum;
import com.jlr.ecp.order.enums.payment.DiscountTypeEnum;
import com.jlr.ecp.order.enums.phone.PhoneNumberDecodeUtil;
import com.jlr.ecp.order.handle.RefundHandler;
import com.jlr.ecp.order.service.cart.ShoppingCarItemService;
import com.jlr.ecp.order.service.order.address.OrderGiftAddressService;
import com.jlr.ecp.order.util.PIPLDataUtil;
import com.jlr.ecp.order.util.machine.OrderRefundStatusMachine;
import com.jlr.ecp.payment.api.invoice.InvoiceApiV2;
import com.jlr.ecp.payment.api.invoice.dto.PaperInvoiceModifyRespDTO;
import com.jlr.ecp.payment.api.invoice.vo.*;
import com.jlr.ecp.payment.api.order.PayOrderApi;
import com.jlr.ecp.payment.api.order.vo.PayOrderSubmitRespVO;
import com.jlr.ecp.payment.api.order.vo.PrePayOrderSubmitReqVO;
import com.jlr.ecp.product.api.policy.PolicyApi;
import com.jlr.ecp.product.api.policy.vo.OrderDetailPolicyRespVO;
import com.jlr.ecp.product.api.sku.ProductSkuApi;
import com.jlr.ecp.product.api.sku.dto.ProductSnapshotDTO;
import com.jlr.ecp.subscription.api.consumer.ConsumerServiceApi;
import com.jlr.ecp.subscription.api.consumer.dto.ConsumerIncontrolDTO;
import com.jlr.ecp.subscription.api.icrvehicle.IcrVehicleApi;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleListRespVO;
import com.jlr.ecp.subscription.api.incontrol.IncontrolVehicleAPI;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.api.manual.ManualRenewServiceApi;
import com.jlr.ecp.subscription.api.subscripiton.SubscriptionServiceApi;
import com.jlr.ecp.subscription.api.subscripiton.dto.SubscriptionServiceQueryDTO;
import com.jlr.ecp.subscription.api.subscripiton.vo.SubscriptionServiceQueryVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.VcsOrderFulfilmentApi;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.VcsOrderFulfilmentRespVO;
import com.jlr.ecp.system.api.permission.PermissionApi;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.jayway.jsonpath.internal.path.PathCompiler.fail;
import static com.jlr.ecp.order.constant.Constants.CUSTOMER_SERVICE_ORDER;
import static com.jlr.ecp.order.enums.ErrorCodeConstants.*;
import static com.jlr.ecp.order.service.Constant.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OrderInfoDOServiceImplTest {

    @Mock
    private ProductSkuApi mockProductSkuApi;
    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private PIPLDataUtil mockPiplDataUtil;
    @Mock
    private OrderInfoDOMapper mockOrderInfoDOMapper;
    @Mock
    private OrderItemDOService mockOrderItemDOService;
    @Mock
    private OrderStatusLogDOMapper mockOrderStatusLogDOMapper;
    @Mock
    private OrderTermsDOService mockOrderTermsDOService;
    @Mock
    private IncontrolVehicleAPI mockIncontrolVehicleAPI;
    @Mock
    private VcsOrderInfoDOService mockVcsOrderInfoDOService;
    @Mock
    private VcsOrderInfoDOMapper mockVcsOrderInfoDOMapper;
    @Mock
    private ShoppingCarItemService mockShoppingCarItemService;

    @Mock
    private SecurityFrameworkService mockSecurityFrameworkService;
    @Mock
    private OrderItemDOMapper mockOrderItemDOMapper;
    @Mock
    private OrderModifyDetailLogDOMapper mockModifyDetailLogMapper;
    @Mock
    private VcsOrderFulfilmentApi mockVcsOrderFulfilmentApi;

    @Mock
    private OrderRefundItemDOMapper mockOrderRefundItemDOMapper;
    @Mock
    private OrderStatusMappingDOMapper mockOrderStatusMappingDOMapper;
    @Mock
    private OrderTermsDOMapper mockOrderTermsDOMapper;
    @Mock
    private OrderRefundDOMapper mockOrderRefundDOMapper;
    @Mock
    private PolicyApi mockPolicyApi;
    @Mock
    private ProducerTool mockProducerTool;
    @Mock
    private VcsOrderStatisticDOMapper mockVcsOrderStatisticDOMapper;
    @Mock
    private OrderStatisticDOMapper mockOrderStatisticDOMapper;
    @Mock
    private ConsumerApi mockConsumerApi;
    @Mock
    private PermissionApi mockPermissionApi;
    @Mock
    private OrderRefundStatusMachine mockOrderRefundStatusMachine;
    @Mock
    private SubscriptionServiceApi mockSubscriptionServiceApi;
    @Mock
    private ShorLinkAPI mockShorLinkAPI;
    @Mock
    private InvoiceApiV2 mockInvoiceApi;
    @Mock
    private PayOrderApi mockPayOrderApi;
    @Mock
    private PhoneNumberDecodeUtil mockPhoneNumberDecodeUtil;
    @Mock
    private RedisTemplate<String, Object> mockRedisTemplate;
    @Mock
    private DataSourceTransactionManager mockTransactionManager;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private ConsumerServiceApi mockConsumerServiceApi;
    @Mock
    private FeedbackRecordsDOMapper mockFeedbackRecordsDOMapper;
    @Mock
    private ManualRenewServiceApi mockManualRenewServiceApi;
    @Mock
    private FeedbackConfigDOMapper mockFeedbackConfigDOMapper;

    @Mock
    private OrderGiftAddressService mockOrderGiftAddressService;

    @Mock
    private OrderDiscountDetailDOMapper mockOrderDiscountDetailDOMapper;

    @Mock
    private OrderPaymentRecordsMapper mockOrderPaymentRecordsMapper;

    @Mock
    private OrderItemLogisticsDOMapper mockOrderItemLogisticsDOMapper;

    @Mock
    private TransactionTemplate mockTransactionTemplate;

    @Mock
    private SubmitPayOrderComponent submitPayOrderComponent;


    @Mock
    private OrderCouponDetailDOMapper mockOrderCouponDetailDOMapper;

    @Mock
    private RefundHandler mockRefundHandler;

    @Mock
    private CustomerServiceOrderDOMapper mockCustomerServiceOrderDOMapper;

    @Mock
    private IcrVehicleApi mockIcrVehicleApi;

    @InjectMocks
    private OrderInfoDOServiceImpl orderInfoDOServiceImplUnderTest;





    @Before
    public void setUp() {
        // Reset all mocks
        reset(mockProductSkuApi, mockEcpIdUtil, mockPiplDataUtil, mockOrderInfoDOMapper,
                mockOrderItemDOService, mockOrderStatusLogDOMapper, mockOrderTermsDOService,
                mockIncontrolVehicleAPI, mockVcsOrderInfoDOService, mockVcsOrderInfoDOMapper,
                mockShoppingCarItemService, mockOrderItemDOMapper, mockModifyDetailLogMapper,
                mockVcsOrderFulfilmentApi, mockOrderRefundItemDOMapper, mockOrderStatusMappingDOMapper,
                mockOrderTermsDOMapper, mockOrderRefundDOMapper, mockPolicyApi, mockProducerTool,
                mockVcsOrderStatisticDOMapper, mockOrderStatisticDOMapper, mockConsumerApi,
                mockPermissionApi, mockOrderRefundStatusMachine, mockSubscriptionServiceApi,
                mockShorLinkAPI, mockInvoiceApi, mockPayOrderApi, mockPhoneNumberDecodeUtil,
                mockRedisTemplate, mockTransactionManager, mockRedisService, mockConsumerServiceApi,
                mockOrderDiscountDetailDOMapper, mockOrderPaymentRecordsMapper, mockOrderItemLogisticsDOMapper,
                mockOrderCouponDetailDOMapper, mockRefundHandler, mockFeedbackRecordsDOMapper);

        ReflectionTestUtils.setField(orderInfoDOServiceImplUnderTest, "objectMapper", new ObjectMapper());
        ReflectionTestUtils.setField(orderInfoDOServiceImplUnderTest, "payment", "payment");
        ReflectionTestUtils.setField(orderInfoDOServiceImplUnderTest, "cancel", "cancel");
        ReflectionTestUtils.setField(orderInfoDOServiceImplUnderTest, "baseMapper", mockOrderInfoDOMapper);
        orderInfoDOServiceImplUnderTest.ecpIdUtil = mockEcpIdUtil;

        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderRefundItemDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderRefundDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), OrderModifyDetailLogDO.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), VCSOrderInfoDO.class);
    }

    @Test
    public void testCreateOrderInfo() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        final OrderShopCarItemDTO itemDTO = getCarItemDTO();
        itemDTO.setCartItemCode("3");
        itemDTO.setCartItemType(3);
        orderCreateDTO.setShopCarItemList(List.of(itemDTO));
        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);
        final OrderGiftAddressDTO orderGiftAddressDTO = getOrderGiftAddressDTO();
        orderCreateDTO.setGiftInfoDTO(orderGiftAddressDTO);

        // Configure ConsumerServiceApi.getConsumerByCodeOrICR(...).
        final ConsumerIncontrolDTO consumerIncontrolDTO = new ConsumerIncontrolDTO();
        consumerIncontrolDTO.setId(0L);
        consumerIncontrolDTO.setConsumerCode(CONSUMER_CODE);
        consumerIncontrolDTO.setIncontrolId(IN_CONTROL_ID);
        consumerIncontrolDTO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDTO.setUnbindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<ConsumerIncontrolDTO> consumerIncontrolDTOCommonResult = CommonResult.success(
                consumerIncontrolDTO);
//        when(mockConsumerServiceApi.getConsumerByCodeOrICR(CONSUMER_CODE, null, CLIENT_ID))
//                .thenReturn(consumerIncontrolDTOCommonResult);

        // Configure OrderInfoDOMapper.selectUnpaidOrdersForVCS(...).
//        when(mockOrderInfoDOMapper.selectUnpaidOrAfterSalesOrdersForVCS(anyList())).thenReturn(Collections.emptyList());

//        when(mockVcsOrderFulfilmentApi.checkCanBuyService(anyList())).thenReturn(CommonResult.success(true));

        // Configure ProductSkuApi.verifyOrderProducts(...).
        ProductSnapshotDTO snapshotDTO = new ProductSnapshotDTO();
        BeanUtil.copyProperties(itemDTO, snapshotDTO);
        snapshotDTO.setProductSalePrice(100L);
        Map<String, ProductSnapshotDTO> map = new HashMap<>();

        map.put(snapshotDTO.getProductSkuCode(), snapshotDTO);
        when(mockProductSkuApi.verifyOrderProducts(any())).thenReturn(CommonResult.success(map));

        when(mockPermissionApi.getEncryptText(WX_PHONE)).thenReturn(CommonResult.success(VALUE));
        when(mockEcpIdUtil.nextId()).thenReturn(0L);

        ValueOperations<String, Object> valueOpsMock = mock(ValueOperations.class);
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOpsMock);
        when(valueOpsMock.get(anyString())).thenReturn(null);
//        when(mockPhoneNumberDecodeUtil.getEncryptText(IN_CONTROL_ID)).thenReturn(IN_CONTROL_ID);
//        when(mockPhoneNumberDecodeUtil.getIncontrolIdMix(IN_CONTROL_ID)).thenReturn(IN_CONTROL_ID_MIX);
//        when(mockPhoneNumberDecodeUtil.getVinMix(CAR_VIN)).thenReturn(CAR_VIN_MIX);

        // Configure OrderStatisticDOMapper.selectOne(...).
        final OrderStatisticDO orderStatisticDO = OrderStatisticDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderTotalCount(0)
                .vcsOrderTotalCount(0)
                .brandGoodsTotalCount(0)
                .build();
        when(mockOrderStatisticDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderStatisticDO);

        when(mockOrderInfoDOMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0L);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build());
//        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderInfoDOS);

//        when(mockVcsOrderInfoDOMapper.selectCount(any(LambdaQueryWrapperX.class))).thenReturn(0L);

        // Configure VcsOrderStatisticDOMapper.selectOne(...).
        final VcsOrderStatisticDO vcsOrderStatisticDO = VcsOrderStatisticDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .orderCount(0)
                .brandCode(CLIENT_ID)
                .build();
//        when(mockVcsOrderStatisticDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderStatisticDO);

        // Run the test
        //assertThatThrownBy(()->orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID)) .isInstanceOf(Exception.class);
        orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID);
        // Verify the results
//        verify(mockOrderInfoDOMapper).insert(any());
//        verify(mockOrderStatusLogDOMapper).insert(any());
//        verify(mockOrderItemDOService).saveBatch(anyList());
//        verify(mockOrderTermsDOService).saveBatch(anyList());
//        verify(mockVcsOrderInfoDOService).saveBatch(anyList());
    }

    @Test
    public void testCreateOrderInfoForValet() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        globalInfoDTO.setConsumerCode(CUSTOMER_SERVICE_ORDER);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        final OrderShopCarItemDTO itemDTO = getCarItemDTO();
        itemDTO.setCartItemCode("3");
        itemDTO.setCartItemType(3);
        orderCreateDTO.setShopCarItemList(List.of(itemDTO));
        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);
        final OrderGiftAddressDTO orderGiftAddressDTO = getOrderGiftAddressDTO();
        orderCreateDTO.setGiftInfoDTO(orderGiftAddressDTO);

        GuestOrderCreateCustomerServiceDTO customerServiceOrderDTO = new GuestOrderCreateCustomerServiceDTO();
        customerServiceOrderDTO.setReceivePhone("12345678901");
        customerServiceOrderDTO.setMessageTemplateCode("TEMPLATE_CODE_001");
        customerServiceOrderDTO.setCustomerServiceRemark("这是一个备注示例");
        orderCreateDTO.setCustomerServiceOrderDTO(customerServiceOrderDTO);

        // Configure ConsumerServiceApi.getConsumerByCodeOrICR(...).
        final ConsumerIncontrolDTO consumerIncontrolDTO = new ConsumerIncontrolDTO();
        consumerIncontrolDTO.setId(0L);
        consumerIncontrolDTO.setConsumerCode(CUSTOMER_SERVICE_ORDER);
        consumerIncontrolDTO.setIncontrolId(IN_CONTROL_ID);
        consumerIncontrolDTO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDTO.setUnbindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<ConsumerIncontrolDTO> consumerIncontrolDTOCommonResult = CommonResult.success(
                consumerIncontrolDTO);
//        when(mockConsumerServiceApi.getConsumerByCodeOrICR(CUSTOMER_SERVICE_ORDER, null, CLIENT_ID))
//                .thenReturn(consumerIncontrolDTOCommonResult);

        // Configure OrderInfoDOMapper.selectUnpaidOrdersForVCS(...).
//        when(mockOrderInfoDOMapper.selectUnpaidOrAfterSalesOrdersForVCS(anyList())).thenReturn(Collections.emptyList());

//        when(mockVcsOrderFulfilmentApi.checkCanBuyService(anyList())).thenReturn(CommonResult.success(true));

        // Configure ProductSkuApi.verifyOrderProducts(...).
        ProductSnapshotDTO snapshotDTO = new ProductSnapshotDTO();
        BeanUtil.copyProperties(itemDTO, snapshotDTO);
        snapshotDTO.setProductSalePrice(100L);
        Map<String, ProductSnapshotDTO> map = new HashMap<>();

        map.put(snapshotDTO.getProductSkuCode(), snapshotDTO);
        when(mockProductSkuApi.verifyOrderProducts(any())).thenReturn(CommonResult.success(map));

//        when(mockPermissionApi.getEncryptText(WX_PHONE)).thenReturn(CommonResult.success(VALUE));
        when(mockEcpIdUtil.nextId()).thenReturn(0L);
//        when(mockPhoneNumberDecodeUtil.getEncryptText(IN_CONTROL_ID)).thenReturn(IN_CONTROL_ID);
//        when(mockPhoneNumberDecodeUtil.getIncontrolIdMix(IN_CONTROL_ID)).thenReturn(IN_CONTROL_ID_MIX);
//        when(mockPhoneNumberDecodeUtil.getVinMix(CAR_VIN)).thenReturn(CAR_VIN_MIX);

        // Configure OrderStatisticDOMapper.selectOne(...).
        final OrderStatisticDO orderStatisticDO = OrderStatisticDO.builder()
                .id(0L)
                .consumerCode(CUSTOMER_SERVICE_ORDER)
                .orderTotalCount(0)
                .vcsOrderTotalCount(0)
                .brandGoodsTotalCount(0)
                .build();
//        when(mockOrderStatisticDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderStatisticDO);

//        when(mockOrderInfoDOMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0L);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CUSTOMER_SERVICE_ORDER)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build());
//        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderInfoDOS);

//        when(mockVcsOrderInfoDOMapper.selectCount(any(LambdaQueryWrapperX.class))).thenReturn(0L);

        // Configure VcsOrderStatisticDOMapper.selectOne(...).
        final VcsOrderStatisticDO vcsOrderStatisticDO = VcsOrderStatisticDO.builder()
                .id(0L)
                .consumerCode(CUSTOMER_SERVICE_ORDER)
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .orderCount(0)
                .brandCode(CLIENT_ID)
                .build();
//        when(mockVcsOrderStatisticDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderStatisticDO);

        //Mock booleanCommonResult true
        //            CommonResult<Boolean> booleanCommonResult = vcsOrderFulfilmentApi.checkCanBuyServiceForPC(dtoList);
        when(mockVcsOrderFulfilmentApi.checkCanBuyServiceForPC(anyList())).thenReturn(CommonResult.success(true));

        // Run the test
        //assertThatThrownBy(()->orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID)) .isInstanceOf(Exception.class);
        orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID);
        // Verify the results
//        verify(mockOrderInfoDOMapper).insert(any());
//        verify(mockOrderStatusLogDOMapper).insert(any());
//        verify(mockOrderItemDOService).saveBatch(anyList());
//        verify(mockOrderTermsDOService).saveBatch(anyList());
//        verify(mockVcsOrderInfoDOService).saveBatch(anyList());
    }

    @Test
    public void testCreateOrderInfo_Not_BRAND_GOODS() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        final OrderShopCarItemDTO itemDTO = getCarItemDTO();
        itemDTO.setCartItemCode("3");
        itemDTO.setCartItemType(0);
        final OrderShopCarItemDTO childItemDTO = getCarItemDTO();
        childItemDTO.setCartItemCode("1");
        childItemDTO.setCartItemType(1);
        itemDTO.setChildList(List.of(childItemDTO));
        orderCreateDTO.setShopCarItemList(List.of(itemDTO));
        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);
        final OrderGiftAddressDTO orderGiftAddressDTO = getOrderGiftAddressDTO();
        orderCreateDTO.setGiftInfoDTO(orderGiftAddressDTO);
        orderCreateDTO.setDelKey(true);

        // Configure ConsumerServiceApi.getConsumerByCodeOrICR(...).
        final ConsumerIncontrolDTO consumerIncontrolDTO = new ConsumerIncontrolDTO();
        consumerIncontrolDTO.setId(0L);
        consumerIncontrolDTO.setConsumerCode(CONSUMER_CODE);
        consumerIncontrolDTO.setIncontrolId(IN_CONTROL_ID);
        consumerIncontrolDTO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDTO.setUnbindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<ConsumerIncontrolDTO> consumerIncontrolDTOCommonResult = CommonResult.success(
                consumerIncontrolDTO);
//        when(mockConsumerServiceApi.getConsumerByCodeOrICR(CONSUMER_CODE, null, CLIENT_ID))
//                .thenReturn(consumerIncontrolDTOCommonResult);

        mockRedisTemplate.delete(anyString());

        // Configure OrderInfoDOMapper.selectUnpaidOrdersForVCS(...).
        when(mockOrderInfoDOMapper.selectUnpaidOrAfterSalesOrdersForVCS(anyList())).thenReturn(Collections.emptyList());

//        when(mockVcsOrderFulfilmentApi.checkCanBuyService(anyList())).thenReturn(CommonResult.success(true));

        // Configure ProductSkuApi.verifyOrderProducts(...).
        ProductSnapshotDTO snapshotDTO = new ProductSnapshotDTO();
        BeanUtil.copyProperties(itemDTO, snapshotDTO);
        snapshotDTO.setProductSalePrice(100L);
        ProductSnapshotDTO childSnapshotDTO = new ProductSnapshotDTO();
        BeanUtil.copyProperties(childItemDTO, childSnapshotDTO);
        childSnapshotDTO.setProductQuantity(1);
        childSnapshotDTO.setProductSalePrice(1L);
        childSnapshotDTO.setFulfilmentType(1);
        snapshotDTO.setChildProductSnapshotList(List.of(childSnapshotDTO));
        Map<String, ProductSnapshotDTO> map = new HashMap<>();

        map.put(snapshotDTO.getProductSkuCode(), snapshotDTO);
        when(mockProductSkuApi.verifyOrderProducts(any())).thenReturn(CommonResult.success(map));

        when(mockPermissionApi.getEncryptText(WX_PHONE)).thenReturn(CommonResult.success(VALUE));
        when(mockEcpIdUtil.nextId()).thenReturn(0L);
        when(mockPhoneNumberDecodeUtil.getEncryptText(IN_CONTROL_ID)).thenReturn(IN_CONTROL_ID);
        when(mockPhoneNumberDecodeUtil.getIncontrolIdMix(IN_CONTROL_ID)).thenReturn(IN_CONTROL_ID_MIX);
        when(mockPhoneNumberDecodeUtil.getVinMix(CAR_VIN)).thenReturn(CAR_VIN_MIX);

        // Configure OrderStatisticDOMapper.selectOne(...).
        final OrderStatisticDO orderStatisticDO = OrderStatisticDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderTotalCount(0)
                .vcsOrderTotalCount(0)
                .brandGoodsTotalCount(0)
                .build();
        when(mockOrderStatisticDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderStatisticDO);

        when(mockOrderInfoDOMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0L);

        when(mockManualRenewServiceApi.checkRecordsInTransit(anyList())).thenReturn(CommonResult.success(false));

        doNothing().when(mockOrderGiftAddressService).saveOrderGiftAddress(any(OrderGiftAddressDTO.class), anyList());

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build());
        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderInfoDOS);

//        when(mockVcsOrderInfoDOMapper.selectCount(any(LambdaQueryWrapperX.class))).thenReturn(0L);

        // Configure VcsOrderStatisticDOMapper.selectOne(...).
        final VcsOrderStatisticDO vcsOrderStatisticDO = VcsOrderStatisticDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .orderCount(0)
                .brandCode(CLIENT_ID)
                .build();
        when(mockVcsOrderStatisticDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderStatisticDO);

        // Run the test
        OrderCreateRespVO result = orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID);
        assertNotNull(result.getOrderCodeList());
    }

    @NotNull
    private static OrderInfoDTO getOrderInfoDTO() {
        final OrderInfoDTO orderInfoDTO = new OrderInfoDTO();
        orderInfoDTO.setIncontrolId(IN_CONTROL_ID);
        final ContactInfoDTO contactInfoDTO = new ContactInfoDTO();
        contactInfoDTO.setContactPhone(CONTACT_PHONE);
        contactInfoDTO.setOperatorRemark(CUSTOMER_REMARK);
        contactInfoDTO.setWxNickName(WX_NICK_NAME);
        contactInfoDTO.setWxPhone(WX_PHONE);
        orderInfoDTO.setContactInfoDTO(contactInfoDTO);
        final PaymentInfoDTO paymentInfoDTO = new PaymentInfoDTO();
        paymentInfoDTO.setOriginalFeeTotalAmount("100");
        paymentInfoDTO.setFeeTotalAmount("100");
        paymentInfoDTO.setCostAmount("100");
        paymentInfoDTO.setDiscountTotalAmount("0");
        paymentInfoDTO.setFreightAmount("0");
        orderInfoDTO.setPaymentInfoDTO(paymentInfoDTO);
        return orderInfoDTO;
    }

    @NotNull
    private static OrderShopCarItemDTO getCarItemDTO() {
        final OrderShopCarItemDTO itemDTO = new OrderShopCarItemDTO();
//        itemDTO.setConsumerCode(CONSUMER_CODE);
        itemDTO.setCartItemCode(CART_ITEM_CODE);
        itemDTO.setCartItemType(1);
        itemDTO.setProductCode(PRODUCT_CODE);
        itemDTO.setPolicyCodeList(List.of(VALUE));
        itemDTO.setProductSkuCode(PRODUCT_SKU_CODE);
        itemDTO.setProductVersionCode(PRODUCT_VERSION_CODE);
        itemDTO.setProductName(PRODUCT_NAME);
        itemDTO.setProductImageUrl(PRODUCT_IMAGE_URL);
        itemDTO.setProductAttribute(PRODUCT_ATTRIBUTE);
        itemDTO.setSalePrice("100");
        itemDTO.setPayPrice("100");
        itemDTO.setBusinessName("VCS");
        itemDTO.setMarketPrice("100");
        itemDTO.setTaxRate(new BigDecimal("0.00"));
        itemDTO.setQuantity(1);
        itemDTO.setSeriesCode(SERIES_CODE);
        itemDTO.setSeriesName(SERIES_NAME);
        itemDTO.setCarVin(CAR_VIN);
        itemDTO.setChildList(new ArrayList<>());
        return itemDTO;
    }

    @Test
    public void testCreateOrderInfoUnpaid() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        final OrderShopCarItemDTO itemDTO = getCarItemDTO();
        orderCreateDTO.setShopCarItemList(List.of(itemDTO));

        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);
        final OrderGiftAddressDTO orderGiftAddressDTO = getOrderGiftAddressDTO();
        orderCreateDTO.setGiftInfoDTO(orderGiftAddressDTO);

        // Configure ConsumerServiceApi.getConsumerByCodeOrICR(...).
        final ConsumerIncontrolDTO consumerIncontrolDTO = new ConsumerIncontrolDTO();
        consumerIncontrolDTO.setId(0L);
        consumerIncontrolDTO.setConsumerCode(CONSUMER_CODE);
        consumerIncontrolDTO.setIncontrolId(IN_CONTROL_ID);
        consumerIncontrolDTO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDTO.setUnbindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<ConsumerIncontrolDTO> consumerIncontrolDTOCommonResult = CommonResult.success(
                consumerIncontrolDTO);
//        when(mockConsumerServiceApi.getConsumerByCodeOrICR(CONSUMER_CODE, null, CLIENT_ID))
//                .thenReturn(consumerIncontrolDTOCommonResult);

        // Configure OrderInfoDOMapper.selectUnpaidOrdersForVCS(...).
        final OrderUnpaidRelaDTO orderUnpaidRelaDTO = new OrderUnpaidRelaDTO();
        orderUnpaidRelaDTO.setConsumerCode(CONSUMER_CODE);
        orderUnpaidRelaDTO.setOrderCode(ORDER_CODE);
        final List<OrderUnpaidRelaDTO> orderUnpaidRelaDTOS = List.of(orderUnpaidRelaDTO);
//        when(mockOrderInfoDOMapper.selectUnpaidOrAfterSalesOrdersForVCS(anyList())).thenReturn(orderUnpaidRelaDTOS);

        // Configure ProductSkuApi.verifyOrderProducts(...).
        ProductSnapshotDTO snapshotDTO = new ProductSnapshotDTO();
        BeanUtil.copyProperties(itemDTO, snapshotDTO);
        snapshotDTO.setProductSalePrice(100L);
        Map<String, ProductSnapshotDTO> map = new HashMap<>();
        map.put(snapshotDTO.getProductSkuCode(), snapshotDTO);
//        when(mockProductSkuApi.verifyOrderProducts(any())).thenReturn(CommonResult.success(map));

//        assertThatThrownBy(()->orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID)) .isInstanceOf(Exception.class);
  
    }

    private OrderGiftAddressDTO getOrderGiftAddressDTO() {
        OrderGiftAddressDTO orderGiftAddressDTO = new OrderGiftAddressDTO();
        orderGiftAddressDTO.setNeedGift(0);
        orderGiftAddressDTO.setDetailAddress("address");
        orderGiftAddressDTO.setRecipientPhone("1");
        orderGiftAddressDTO.setRecipient("0");
        orderGiftAddressDTO.setCityCode("1");
        orderGiftAddressDTO.setAreaCode("2");
        orderGiftAddressDTO.setProvinceCode("3");
        return orderGiftAddressDTO;
    }

    @Test
    public void testCreateOrderInfoParent() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        final OrderShopCarItemDTO itemDTO = getCarItemDTO();
        final OrderShopCarItemDTO itemDTO2 = getCarItemDTO();
        itemDTO2.setCarVin("carVin2");
        itemDTO.setCartItemType(3);
        itemDTO2.setCartItemType(3);
        orderCreateDTO.setShopCarItemList(List.of(itemDTO, itemDTO2));
        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);
        final OrderGiftAddressDTO orderGiftAddressDTO = getOrderGiftAddressDTO();
        orderCreateDTO.setGiftInfoDTO(orderGiftAddressDTO);

        ValueOperations<String, Object> valueOpsMock = mock(ValueOperations.class);
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOpsMock);
        when(valueOpsMock.get(anyString())).thenReturn(null);

//        when(mockConsumerServiceApi.getConsumerByCodeOrICR(CONSUMER_CODE, null, CLIENT_ID))
//                .thenReturn(null);

        // Configure OrderInfoDOMapper.selectUnpaidOrdersForVCS(...).
//        when(mockOrderInfoDOMapper.selectUnpaidOrAfterSalesOrdersForVCS(anyList())).thenReturn(Collections.emptyList());

        // Configure ProductSkuApi.verifyOrderProducts(...).
        ProductSnapshotDTO snapshotDTO = new ProductSnapshotDTO();
        BeanUtil.copyProperties(itemDTO, snapshotDTO);
        snapshotDTO.setProductSalePrice(100L);
        Map<String, ProductSnapshotDTO> map = new HashMap<>();

        map.put(snapshotDTO.getProductSkuCode(), snapshotDTO);
        when(mockProductSkuApi.verifyOrderProducts(any())).thenReturn(CommonResult.success(map));

        when(mockPermissionApi.getEncryptText(WX_PHONE)).thenReturn(CommonResult.success(VALUE));
        when(mockEcpIdUtil.nextId()).thenReturn(0L);
//        when(mockPhoneNumberDecodeUtil.getEncryptText(IN_CONTROL_ID)).thenReturn(IN_CONTROL_ID);
//        when(mockPhoneNumberDecodeUtil.getIncontrolIdMix(IN_CONTROL_ID)).thenReturn(IN_CONTROL_ID_MIX);
//        when(mockPhoneNumberDecodeUtil.getVinMix(CAR_VIN)).thenReturn(CAR_VIN_MIX);

        // Configure OrderStatisticDOMapper.selectOne(...).
        final OrderStatisticDO orderStatisticDO = OrderStatisticDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderTotalCount(0)
                .vcsOrderTotalCount(0)
                .brandGoodsTotalCount(0)
                .build();
        when(mockOrderStatisticDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderStatisticDO);

        when(mockOrderInfoDOMapper.selectCount(any(LambdaQueryWrapper.class))).thenReturn(0L);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build());
//        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderInfoDOS);

//        when(mockVcsOrderInfoDOMapper.selectCount(any(LambdaQueryWrapperX.class))).thenReturn(0L);
//
//        // Configure VcsOrderStatisticDOMapper.selectOne(...).
        final VcsOrderStatisticDO vcsOrderStatisticDO = VcsOrderStatisticDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .orderCount(0)
                .brandCode(CLIENT_ID)
                .build();
//        when(mockVcsOrderStatisticDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderStatisticDO);

        //assertThatThrownBy(()->orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID)) .isInstanceOf(Exception.class);
        orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID);
        // Verify the results
//        verify(mockOrderInfoDOMapper).insertBatch(anyList());
//        verify(mockOrderStatusLogDOMapper).insertBatch(anyList());
//        verify(mockOrderItemDOService).saveBatch(anyList());
//        verify(mockOrderTermsDOService).saveBatch(anyList());
//        verify(mockVcsOrderInfoDOService).saveBatch(anyList());


    }

    @Test
    public void testCreateOrderInfoChannelError() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode("xx");
        globalInfoDTO.setChannelCode("xx");
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        final OrderShopCarItemDTO itemDTO = getCarItemDTO();
        orderCreateDTO.setShopCarItemList(List.of(itemDTO));
        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);

        // Run the test
        try {
            orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID);
        } catch (ServiceException e) {
            assertEquals(CHANNEL_CODE_ERROR.getCode(), e.getCode());
        }
    }

    @Test
    public void testCreateOrderInfoItemEmpty() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        orderCreateDTO.setShopCarItemList(Collections.emptyList());
        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);

        // Run the test
        try {
            orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID);
        } catch (ServiceException e) {
            assertEquals(SHOP_CAR_ITEM_EMPTY.getCode(), e.getCode());
        }
    }

    @Test
    public void testCreateOrderInfoHasUnpaidOrderInOtherWechat() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        final OrderShopCarItemDTO itemDTO = getCarItemDTO();
        orderCreateDTO.setShopCarItemList(List.of(itemDTO));
        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);
        final OrderGiftAddressDTO orderGiftAddressDTO = getOrderGiftAddressDTO();
        orderCreateDTO.setGiftInfoDTO(orderGiftAddressDTO);

        // Run the test
        assertThatThrownBy(()->orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID)) .isInstanceOf(Exception.class);
        // Verify the results
        final OrderCreateRespVO expected = new OrderCreateRespVO();
        expected.setHasUnpaidOrderInOtherWechat(true);
    }

    @Test
    public void testCreateOrderInfoCheckICRError2() {
        // 1. 创建 OrderCreateDTO 并设置必要参数
        OrderCreateDTO orderCreateDTO = new OrderCreateDTO();

        // 设置 GlobalInfoDTO（品牌、渠道等）
        GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);

        // 设置购物车商品项
        OrderShopCarItemDTO itemDTO = getCarItemDTO();
        orderCreateDTO.setShopCarItemList(List.of(itemDTO));

        // 设置订单信息（关键：设置 ConsumerCode 不等于 CUSTOMER_SERVICE_ORDER）
        OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderInfoDTO.setIncontrolId("EXPECTED_INCONTROL_ID"); // 期望的 incontrolId
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);

        // 设置赠品信息
        OrderGiftAddressDTO orderGiftAddressDTO = getOrderGiftAddressDTO();
        orderCreateDTO.setGiftInfoDTO(orderGiftAddressDTO);

        ValueOperations<String, Object> valueOpsMock = mock(ValueOperations.class);
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOpsMock);
        when(valueOpsMock.get(anyString())).thenReturn(null);

        // 2. Mock 依赖服务返回（模拟消费者信息不匹配）
        // 场景1：API 返回 data 为 null
//        when(mockConsumerServiceApi.getConsumerByCodeOrICR(
//                eq("NORMAL_CONSUMER_CODE"),
//                eq(null),
//                eq(CLIENT_ID)
//        )).thenReturn(CommonResult.success(null)); // data 为 null

        // 场景2（可选）：data.getIncontrolId() 不匹配
        ConsumerIncontrolDTO mismatchData = new ConsumerIncontrolDTO();
        mismatchData.setIncontrolId("WRONG_INCONTROL_ID"); // 故意设置不匹配的值
        when(mockConsumerServiceApi.getConsumerByCodeOrICR(
                eq(null),
                eq(null),
                eq(CLIENT_ID)
        )).thenReturn(CommonResult.success(mismatchData));

        // 3. 执行测试并验证异常
        try {
            orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID);
            fail("应抛出 ServiceException");
        } catch (ServiceException e) {
            assertEquals(CHECK_ICR_CONSUMER_ERROR.getCode(), e.getCode());
        }
    }

    @Test
    public void testCreateOrderInfoCanBuyServiceFalse() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        final OrderShopCarItemDTO itemDTO = getCarItemDTO();
        orderCreateDTO.setShopCarItemList(List.of(itemDTO));
        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);
        final OrderGiftAddressDTO orderGiftAddressDTO = getOrderGiftAddressDTO();
        orderCreateDTO.setGiftInfoDTO(orderGiftAddressDTO);
        ConsumerIncontrolDTO incontrolDTO = new ConsumerIncontrolDTO();
        incontrolDTO.setConsumerCode(CONSUMER_CODE);
        incontrolDTO.setIncontrolId(IN_CONTROL_ID);
        // Mock dependencies
//        when(mockConsumerServiceApi.getConsumerByCodeOrICR(CONSUMER_CODE, null, CLIENT_ID))
//                .thenReturn(CommonResult.success(incontrolDTO));

//        when(mockOrderInfoDOMapper.selectUnpaidOrAfterSalesOrdersForVCS(anyList())).thenReturn(Collections.emptyList());

//        when(mockVcsOrderFulfilmentApi.checkCanBuyService(anyList())).thenReturn(CommonResult.success(false));
//        when(mockProductSkuApi.verifyOrderProducts(any())).thenReturn(CommonResult.success(new HashMap<>()));
        // Mock TenantContextHolder
        try (MockedStatic<TenantContextHolder> mockedStatic = mockStatic(TenantContextHolder.class)) {
            mockedStatic.when(() -> TenantContextHolder.getTenantId()).thenReturn(1L);
            mockedStatic.when(() -> TenantContextHolder.setTenantId(any())).then(invocation -> null);

            // Run the test
//            assertThatThrownBy(()->orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID)) .isInstanceOf(Exception.class);
         
        }
    }
    @Test
    public void testCreateOrderInfoAfterSalesOrdersNull() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        final OrderShopCarItemDTO itemDTO = getCarItemDTO();
        orderCreateDTO.setShopCarItemList(List.of(itemDTO));
        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);
        final OrderGiftAddressDTO orderGiftAddressDTO = getOrderGiftAddressDTO();
        orderCreateDTO.setGiftInfoDTO(orderGiftAddressDTO);

        // Configure ConsumerServiceApi.getConsumerByCodeOrICR(...).
        final ConsumerIncontrolDTO consumerIncontrolDTO = new ConsumerIncontrolDTO();
        consumerIncontrolDTO.setId(0L);
        consumerIncontrolDTO.setConsumerCode(CONSUMER_CODE);
        consumerIncontrolDTO.setIncontrolId(IN_CONTROL_ID);
        consumerIncontrolDTO.setBindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        consumerIncontrolDTO.setUnbindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<ConsumerIncontrolDTO> consumerIncontrolDTOCommonResult = CommonResult.success(
                consumerIncontrolDTO);
//        when(mockConsumerServiceApi.getConsumerByCodeOrICR(CONSUMER_CODE, null, CLIENT_ID))
//                .thenReturn(consumerIncontrolDTOCommonResult);

        // Configure OrderInfoDOMapper.selectUnpaidOrdersForVCS(...).
//        when(mockOrderInfoDOMapper.selectUnpaidOrAfterSalesOrdersForVCS(anyList())).thenReturn(Collections.emptyList());

//        when(mockVcsOrderFulfilmentApi.checkCanBuyService(anyList())).thenReturn(CommonResult.success(true));
        // Run the test
        assertThatThrownBy(()->orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID)) .isInstanceOf(Exception.class);
        // Verify the results
        final OrderCreateRespVO expected = new OrderCreateRespVO();
        expected.setHasLongExpireOrActiveOrder(true);
    }

    @Test
    public void testCreateOrderInfoProductError() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        final OrderShopCarItemDTO itemDTO = getCarItemDTO();
        orderCreateDTO.setShopCarItemList(List.of(itemDTO));
        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);
        final OrderGiftAddressDTO orderGiftAddressDTO = getOrderGiftAddressDTO();
        orderCreateDTO.setGiftInfoDTO(orderGiftAddressDTO);

        ValueOperations<String, Object> valueOpsMock = mock(ValueOperations.class);
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOpsMock);
        when(valueOpsMock.get(anyString())).thenReturn(null);

        // Configure ProductSkuApi.verifyOrderProducts(...).
        ProductSnapshotDTO snapshotDTO = new ProductSnapshotDTO();
        BeanUtil.copyProperties(itemDTO, snapshotDTO);
        snapshotDTO.setProductSalePrice(100L);
        Map<String, ProductSnapshotDTO> map = new HashMap<>();

        map.put(snapshotDTO.getProductSkuCode(), snapshotDTO);
        when(mockProductSkuApi.verifyOrderProducts(any())).thenReturn(CommonResult.error(500, "error"));
        // Verify the results
        try {
            orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID);
        } catch (ServiceException e) {
            assertEquals(500, (long)e.getCode());
        }
    }

    @Test
    public void testCreateOrderInfoParentVcsItemsNull() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        OrderShopCarItemDTO itemDTO = getCarItemDTO();
        itemDTO.setCartItemType(2);
        OrderShopCarItemDTO itemDTO2 = getCarItemDTO();
        itemDTO2.setCartItemType(3);
        itemDTO2.setCarVin("carVin2");
        orderCreateDTO.setShopCarItemList(List.of(itemDTO, itemDTO2));
        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);

//        when(mockConsumerServiceApi.getConsumerByCodeOrICR(CONSUMER_CODE, null, CLIENT_ID))
//                .thenReturn(null);

        // Configure ProductSkuApi.verifyOrderProducts(...).
        ProductSnapshotDTO snapshotDTO = new ProductSnapshotDTO();
        BeanUtil.copyProperties(itemDTO, snapshotDTO);
        snapshotDTO.setProductSalePrice(100L);
        Map<String, ProductSnapshotDTO> map = new HashMap<>();

        map.put(snapshotDTO.getProductSkuCode(), snapshotDTO);
//        when(mockProductSkuApi.verifyOrderProducts(any())).thenReturn(CommonResult.success(map));

//        when(mockPermissionApi.getEncryptText(CONTACT_PHONE)).thenReturn(CommonResult.success(VALUE));

        // Configure OrderStatisticDOMapper.selectOne(...).
        final OrderStatisticDO orderStatisticDO = OrderStatisticDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderTotalCount(0)
                .vcsOrderTotalCount(0)
                .brandGoodsTotalCount(0)
                .build();

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build());

        // Configure VcsOrderStatisticDOMapper.selectOne(...).
        final VcsOrderStatisticDO vcsOrderStatisticDO = VcsOrderStatisticDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .orderCount(0)
                .brandCode(CLIENT_ID)
                .build();

        // Run the test
        assertThatThrownBy(()->orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID)) .isInstanceOf(Exception.class);
       
    }

    @Test
    public void testGetPage() {
        // Setup
        final OrderPageReqDTO dto = new OrderPageReqDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setOrderCode(PARENT_ORDER_CODE);
        dto.setInControlId(IN_CONTROL_ID);
        dto.setVin("vin");
        dto.setMobile("mobile");
        dto.setOrderStatus("1,2");
        dto.setStatusList(List.of(1,2));

        final OrderInfoPageVO orderInfoPageVO = new OrderInfoPageVO();
        orderInfoPageVO.setOrderCode(ORDER_CODE);
        orderInfoPageVO.setInControlId(IN_CONTROL_ID);
        orderInfoPageVO.setCarVin(CAR_VIN);
        orderInfoPageVO.setAfterStatus(1);
        orderInfoPageVO.setStatusText("已下单");
        final PageResult<OrderInfoPageVO> expectedResult = new PageResult<>(List.of(orderInfoPageVO), 0L);

        // Configure OrderInfoDOMapper.getParentCode(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .build();
        when(mockOrderInfoDOMapper.getParentCode(PARENT_ORDER_CODE)).thenReturn(orderInfoDO);

        // Configure OrderInfoDOMapper.getPage(...).
        OrderInfoPageVO pageVO = new OrderInfoPageVO();
        BeanUtil.copyProperties(orderInfoPageVO, pageVO);
        Page<OrderInfoPageVO> page = new Page<>();
        page.setRecords(List.of(pageVO));
        when(mockOrderInfoDOMapper.getPage(any(Page.class), eq(dto))).thenReturn(page);

        // Run the test
        final PageResult<OrderInfoPageVO> result = orderInfoDOServiceImplUnderTest.getPage(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOrderDetail() {
        // Reset mock to ensure clean state
        reset(mockInvoiceApi);
        
        // Setup
        final OrderDetailRespVO expectedResult = new OrderDetailRespVO();
        expectedResult.setOrderCode(CODE);
        final OrderDetailOrderStatusVO orderStatusInfo = new OrderDetailOrderStatusVO();
        final OrderDetailOrderStatusVO.OrderStatusProgress orderStatusProgress = new OrderDetailOrderStatusVO.OrderStatusProgress();
        orderStatusProgress.setAfterStatus(2);
        orderStatusProgress.setAfterStatusDesc("已支付");
        orderStatusProgress.setChangeTime("2020/01/01 00:00");
        orderStatusInfo.setOrderStatusProgressList(List.of(orderStatusProgress));
        expectedResult.setOrderStatusInfo(orderStatusInfo);
        final OrderDetailOrderInfoVO orderInfo = getOrderDetailOrderInfoVO();
        expectedResult.setOrderInfo(orderInfo);
        final OrderDetailCustomerInfoVO customerInfo = new OrderDetailCustomerInfoVO();
        customerInfo.setWxNickName("-");
        customerInfo.setWxPhone(CONTACT_PHONE);
        customerInfo.setWxPhoneMix(WX_PHONE_MIX);
        customerInfo.setContactPhone(CONTACT_PHONE);
        customerInfo.setContactPhoneMix(CONTACT_PHONE_MIX);
        customerInfo.setIncontrolId(IN_CONTROL_ID);
        customerInfo.setIncontrolIdMix(IN_CONTROL_ID_MIX);
        customerInfo.setConsumerCode(CONSUMER_CODE);
        expectedResult.setCustomerInfo(customerInfo);
        final OrderDetailVehicleInfoVO carInfo = new OrderDetailVehicleInfoVO();
        carInfo.setCarVin(CAR_VIN);
        carInfo.setCarVinMix(CAR_VIN_MIX);
        carInfo.setBrandCode(BRAND_CODE);
        carInfo.setBrandName(BRAND_NAME);
        carInfo.setSeriesCode(SERIES_CODE);
        carInfo.setSeriesName(SERIES_NAME);
        carInfo.setModelYear(MODEL_YEAR);
        carInfo.setConfigCode(CONFIG_CODE);
        carInfo.setConfigName(CONFIG_NAME);
        expectedResult.setCarInfo(carInfo);
        final OrderDetailEInvoiceInfoVO eInvoiceInfo = new OrderDetailEInvoiceInfoVO();
        expectedResult.setEInvoiceInfo(eInvoiceInfo);
        final OrderDetailPaperInvoiceInfoVO paperInvoiceInfo = new OrderDetailPaperInvoiceInfoVO();
        expectedResult.setPaperInvoiceInfo(paperInvoiceInfo);
        final OrderDetailProductInfoVO productInfo = new OrderDetailProductInfoVO();
        final ProductBrandCategoriedItemInfoVO productItemInfoVO = getProductItemInfoVO();
        productInfo.setProductItemInfoList(List.of(productItemInfoVO));
        expectedResult.setProductInfo(productInfo);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(LRE_ORDER_CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(2)
                .paymentStatus(1)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(1)
                .orderChannel(1)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .businessCode(BusinessIdEnum.LRE.getCode())  // 添加businessCode字段
                .build();
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderInfoDO);

        // Configure OrderStatusLogDOMapper.selectList(...).
        final List<OrderStatusLogDO> orderStatusLogDOS = List.of(OrderStatusLogDO.builder()
                .tenantId(0)
                .orderCode(CODE)
                .beforeStatus(1)
                .afterStatus(2)
                .changeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
//        when(mockOrderStatusLogDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderStatusLogDOS);

        // Configure VcsOrderInfoDOService.getOne(...).
        final VCSOrderInfoDO vcsOrderInfoDO = VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build();
//        when(mockVcsOrderInfoDOService.getOne(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDO);

//        when(mockPhoneNumberDecodeUtil.getDecodePhone(CAR_VIN)).thenReturn(CAR_VIN);

        // Configure IncontrolVehicleAPI.getIncontrolVehicleByCarVin(...).
        final IncontrolVehicleByCarDTO incontrolVehicleByCarDTO = new IncontrolVehicleByCarDTO();
        incontrolVehicleByCarDTO.setBrandCode(BRAND_CODE);
        incontrolVehicleByCarDTO.setBrandName(BRAND_NAME);
        incontrolVehicleByCarDTO.setConfigCode(CONFIG_CODE);
        incontrolVehicleByCarDTO.setConfigName(CONFIG_NAME);
        incontrolVehicleByCarDTO.setModelYear(MODEL_YEAR);
        final CommonResult<List<IncontrolVehicleByCarDTO>> listCommonResult = CommonResult.success(
                List.of(incontrolVehicleByCarDTO));
//        when(mockIncontrolVehicleAPI.getIncontrolVehicleByCarVin(List.of(CAR_VIN))).thenReturn(listCommonResult);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build());
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(list);

        // Configure VcsOrderInfoDOMapper.selectOne(...).
        final VCSOrderInfoDO vcsOrderInfoDO1 = VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build();
//        when(mockVcsOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDO1);

        // Configure VcsOrderFulfilmentApi.view(...).
        final VcsOrderFulfilmentRespVO vcsOrderFulfilmentRespVO = new VcsOrderFulfilmentRespVO();
        vcsOrderFulfilmentRespVO.setFulfilmentId(FULFILMENT_ID);
        vcsOrderFulfilmentRespVO.setOrderCode(CODE);
        vcsOrderFulfilmentRespVO.setOrderItemCode(ORDER_ITEM_CODE);
        vcsOrderFulfilmentRespVO.setServiceStatus(0);
        vcsOrderFulfilmentRespVO.setServiceStatusDesc(" - ");
        final CommonResult<VcsOrderFulfilmentRespVO> vcsOrderFulfilmentRespVOCommonResult = CommonResult.success(
                vcsOrderFulfilmentRespVO);
//        when(mockVcsOrderFulfilmentApi.view(ORDER_ITEM_CODE)).thenReturn(vcsOrderFulfilmentRespVOCommonResult);

//        when(mockOrderInfoDOMapper.getOrderStatusByOrderCode(CODE)).thenReturn(0);
//        when(mockOrderRefundItemDOMapper.getRefundOrderStatusByOrderItemCode(ORDER_ITEM_CODE)).thenReturn(0);

        // Configure OrderStatusMappingDOMapper.getStatusMapping(...).
        final OrderStatusMappingDO orderStatusMappingDO = OrderStatusMappingDO.builder()
                .orderStatus(1)
                .refundOrderStatus(0)
                .customerOrderStatusView(CUSTOMER_ORDER_STATUS_VIEW)
                .customerAfterSalesOrderStatusView(CUSTOMER_AFTER_SALES_ORDER_STATUS_VIEW)
                .operationOriginOrderCancelStatusView(OPERATION_ORIGIN_ORDER_CANCEL_STATUS_VIEW)
                .build();
//        when(mockOrderStatusMappingDOMapper.getStatusMapping(0, 0)).thenReturn(orderStatusMappingDO);

        // Configure OrderRefundItemDOMapper.selectOne(...).
        final OrderRefundItemDO orderRefundItemDO = OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode(REFUND_ORDER_CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .build();
//        when(mockOrderRefundItemDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDO);

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode(REFUND_ORDER_CODE)
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .rejectReason(REJECT_REASON)
                .build();
//        when(mockOrderRefundDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundDO);

        // Configure InvoiceApi.getInvoiceDetail(...).
        final InvoiceDetailVO invoiceDetailVO = new InvoiceDetailVO();
        final EInvoiceDetailVO eInvoiceVO = new EInvoiceDetailVO();
        invoiceDetailVO.setEInvoiceVO(eInvoiceVO);
        final PaperInvoiceDetailVO paperInvoiceVO = new PaperInvoiceDetailVO();
        invoiceDetailVO.setPaperInvoiceVO(paperInvoiceVO);
        final ESpecialInvoiceDetailVO eSpecialInvoiceVO = new ESpecialInvoiceDetailVO();
        invoiceDetailVO.setESpecialInvoiceVO(eSpecialInvoiceVO);
        final CommonResult<InvoiceDetailVO> validInvoiceDetailVOResult = CommonResult.success(invoiceDetailVO);
        
        // 使用anyString()匹配任何参数，确保无论调用什么参数都返回有效结果
        when(mockInvoiceApi.getInvoiceDetail(anyString())).thenReturn(validInvoiceDetailVOResult);
        
        // 配置折扣信息
        when(mockOrderDiscountDetailDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(new ArrayList<>());
        
        // 配置支付记录信息
        when(mockOrderPaymentRecordsMapper.queryLastByOrderCode(anyString())).thenReturn(null);


        // Run the test
        final OrderDetailRespVO result = orderInfoDOServiceImplUnderTest.getOrderDetail(LRE_ORDER_CODE);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetOrderDetailOrderNull() {
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        final OrderDetailRespVO result = orderInfoDOServiceImplUnderTest.getOrderDetail(CODE);
        OrderDetailRespVO orderDetailRespVO = new OrderDetailRespVO();
        orderDetailRespVO.setOrderCode(CODE);
        assertThat(result).isEqualTo(orderDetailRespVO);
    }

    @Test
    public void testGetOrderDetailVcsOrderNull() {
        // Setup
        final OrderDetailRespVO expectedResult = new OrderDetailRespVO();
        expectedResult.setOrderCode(CODE);
        final OrderDetailOrderStatusVO orderStatusInfo = new OrderDetailOrderStatusVO();
        final OrderDetailOrderStatusVO.OrderStatusProgress orderStatusProgress = new OrderDetailOrderStatusVO.OrderStatusProgress();
        orderStatusProgress.setAfterStatus(3);
        orderStatusProgress.setAfterStatusDesc("订单完成");
        orderStatusProgress.setChangeTime("2020/01/01 00:00 (拒绝退款)");
        orderStatusInfo.setOrderStatusProgressList(List.of(orderStatusProgress));
        expectedResult.setOrderStatusInfo(orderStatusInfo);
        final OrderDetailOrderInfoVO orderInfo = getOrderDetailOrderInfoVO();
        expectedResult.setOrderInfo(orderInfo);
        final OrderDetailCustomerInfoVO customerInfo = new OrderDetailCustomerInfoVO();
        customerInfo.setWxNickName("-");
        customerInfo.setWxPhone(CONTACT_PHONE);
        customerInfo.setWxPhoneMix(WX_PHONE_MIX);
        customerInfo.setContactPhone(CONTACT_PHONE);
        customerInfo.setContactPhoneMix(CONTACT_PHONE_MIX);
        expectedResult.setCustomerInfo(customerInfo);
        final OrderDetailProductInfoVO productInfo = new OrderDetailProductInfoVO();
        expectedResult.setProductInfo(productInfo);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(VCS_ORDER_CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(2)
                .paymentStatus(1)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(1)
                .orderChannel(1)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .businessCode(BusinessIdEnum.VCS.getCode())  // 添加businessCode字段
                .build();
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderInfoDO);

        // Configure OrderStatusLogDOMapper.selectList(...).
        final List<OrderStatusLogDO> orderStatusLogDOS = List.of(OrderStatusLogDO.builder()
                .tenantId(0)
                .orderCode(CODE)
                .beforeStatus(5)
                .afterStatus(3)
                .changeTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockOrderStatusLogDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderStatusLogDOS);

        when(mockVcsOrderInfoDOService.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 添加对feedbackRecordsDOMapper的模拟
        when(mockFeedbackRecordsDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Configure IncontrolVehicleAPI.getIncontrolVehicleByCarVin(...).
        final IncontrolVehicleByCarDTO incontrolVehicleByCarDTO = new IncontrolVehicleByCarDTO();
        incontrolVehicleByCarDTO.setBrandCode(BRAND_CODE);
        incontrolVehicleByCarDTO.setBrandName(BRAND_NAME);
        incontrolVehicleByCarDTO.setConfigCode(CONFIG_CODE);
        incontrolVehicleByCarDTO.setConfigName(CONFIG_NAME);
        incontrolVehicleByCarDTO.setModelYear(MODEL_YEAR);
        final CommonResult<List<IncontrolVehicleByCarDTO>> listCommonResult = CommonResult.success(
                List.of(incontrolVehicleByCarDTO));

        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(new ArrayList<>());

        // Configure VcsOrderInfoDOMapper.selectOne(...).
        final VCSOrderInfoDO vcsOrderInfoDO1 = VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build();

        // Configure VcsOrderFulfilmentApi.view(...).
        final VcsOrderFulfilmentRespVO vcsOrderFulfilmentRespVO = new VcsOrderFulfilmentRespVO();
        vcsOrderFulfilmentRespVO.setFulfilmentId(FULFILMENT_ID);
        vcsOrderFulfilmentRespVO.setOrderCode(CODE);
        vcsOrderFulfilmentRespVO.setOrderItemCode(ORDER_ITEM_CODE);
        vcsOrderFulfilmentRespVO.setServiceStatus(0);
        vcsOrderFulfilmentRespVO.setServiceStatusDesc(" - ");
        final CommonResult<VcsOrderFulfilmentRespVO> vcsOrderFulfilmentRespVOCommonResult = CommonResult.success(
                vcsOrderFulfilmentRespVO);


        // Configure OrderStatusMappingDOMapper.getStatusMapping(...).
        final OrderStatusMappingDO orderStatusMappingDO = OrderStatusMappingDO.builder()
                .orderStatus(1)
                .refundOrderStatus(1)
                .customerOrderStatusView(CUSTOMER_ORDER_STATUS_VIEW)
                .customerAfterSalesOrderStatusView(CUSTOMER_AFTER_SALES_ORDER_STATUS_VIEW)
                .operationOriginOrderCancelStatusView(OPERATION_ORIGIN_ORDER_CANCEL_STATUS_VIEW)
                .build();

        // Configure OrderRefundItemDOMapper.selectOne(...).
        final OrderRefundItemDO orderRefundItemDO = OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode(REFUND_ORDER_CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .build();

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode(REFUND_ORDER_CODE)
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .rejectReason(REJECT_REASON)
                .build();

        // Configure InvoiceApi.getInvoiceDetail(...).
        final CommonResult<InvoiceDetailVO> invoiceDetailVOCommonResult = CommonResult.success(null);
        when(mockInvoiceApi.getInvoiceDetail(VCS_ORDER_CODE)).thenReturn(invoiceDetailVOCommonResult);
        
        // 修复空指针异常问题，需要正确模拟InvoiceDetailVO对象
        final InvoiceDetailVO invoiceDetailVO = new InvoiceDetailVO();
        final EInvoiceDetailVO eInvoiceVO = new EInvoiceDetailVO();
        invoiceDetailVO.setEInvoiceVO(eInvoiceVO);
        final PaperInvoiceDetailVO paperInvoiceVO = new PaperInvoiceDetailVO();
        invoiceDetailVO.setPaperInvoiceVO(paperInvoiceVO);
        final ESpecialInvoiceDetailVO eSpecialInvoiceVO = new ESpecialInvoiceDetailVO();
        invoiceDetailVO.setESpecialInvoiceVO(eSpecialInvoiceVO);
        final CommonResult<InvoiceDetailVO> validInvoiceDetailVOResult = CommonResult.success(invoiceDetailVO);
        when(mockInvoiceApi.getInvoiceDetail(VCS_ORDER_CODE)).thenReturn(validInvoiceDetailVOResult);

         // Run the test
         final OrderDetailRespVO result = orderInfoDOServiceImplUnderTest.getOrderDetail(VCS_ORDER_CODE);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetOrderDetail_with_gift() {
        // Setup
        final OrderAppDetailPage expectedResult = new OrderAppDetailPage();
        final ProductBrandCategoriedItemInfoVO productItemInfo = getProductBrandCategoriedItemInfoVO();
        expectedResult.setProductItemInfo(productItemInfo);
        final OrderBrandVehicleInfoVO vehicleInfo = new OrderBrandVehicleInfoVO();
        vehicleInfo.setSeriesCode(SERIES_CODE);
        vehicleInfo.setSeriesName(SERIES_NAME);
        vehicleInfo.setCarVin(CAR_VIN);
        expectedResult.setVehicleInfo(vehicleInfo);
        final OrderBrandOrderInfoVO orderInfo = new OrderBrandOrderInfoVO();
        orderInfo.setOrderCode("LRcode");
        orderInfo.setOrderStatus(1);
        orderInfo.setInvoiceStatus(1);
        orderInfo.setCustomerOrderStatusView(CUSTOMER_ORDER_STATUS_VIEW);
        orderInfo.setCustomerAfterSalesOrderStatusView(CUSTOMER_AFTER_SALES_ORDER_STATUS_VIEW);
        orderInfo.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderInfo.setPaymentStatus(0);
        orderInfo.setPaymentStatusDesc("待支付");
        orderInfo.setPaymentTimeout(0L);
        orderInfo.setIsPaying(true);
        orderInfo.setPaymentMethod("微信支付");
        orderInfo.setCostAmount("0.00");
        expectedResult.setOrderInfo(orderInfo);

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode("LRcode")
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .build();
//        when(mockOrderItemDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderItemDO);

        // Configure VcsOrderInfoDOMapper.selectOne(...).
        final VCSOrderInfoDO vcsOrderInfoDO = VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode("LRcode")
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build();
//        when(mockVcsOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDO);

        // Configure VcsOrderFulfilmentApi.view(...).
        final VcsOrderFulfilmentRespVO vcsOrderFulfilmentRespVO = new VcsOrderFulfilmentRespVO();
        vcsOrderFulfilmentRespVO.setFulfilmentId(FULFILMENT_ID);
        vcsOrderFulfilmentRespVO.setOrderCode("LRcode");
        vcsOrderFulfilmentRespVO.setOrderItemCode(ORDER_ITEM_CODE);
        vcsOrderFulfilmentRespVO.setServiceStatus(0);
        vcsOrderFulfilmentRespVO.setServiceStatusDesc(" - ");
        final CommonResult<VcsOrderFulfilmentRespVO> vcsOrderFulfilmentRespVOCommonResult = CommonResult.success(
                vcsOrderFulfilmentRespVO);
//        when(mockVcsOrderFulfilmentApi.view(ORDER_ITEM_CODE)).thenReturn(vcsOrderFulfilmentRespVOCommonResult);

//        when(mockPhoneNumberDecodeUtil.getDecodePhone(CAR_VIN)).thenReturn(CAR_VIN);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode("LRcode")
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(1)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .giftAddress(1)
                .businessCode(BusinessIdEnum.VCS.getCode())
                .build();
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderInfoDO);

//        when(mockOrderInfoDOMapper.getOrderStatusByOrderCode("LRcode")).thenReturn(0);
//        when(mockOrderRefundItemDOMapper.getRefundOrderStatusByOrderItemCode(ORDER_ITEM_CODE)).thenReturn(0);

        // Configure OrderStatusMappingDOMapper.getStatusMapping(...).
        final OrderStatusMappingDO orderStatusMappingDO = OrderStatusMappingDO.builder()
                .orderStatus(1)
                .refundOrderStatus(1)
                .customerOrderStatusView(CUSTOMER_ORDER_STATUS_VIEW)
                .customerAfterSalesOrderStatusView(CUSTOMER_AFTER_SALES_ORDER_STATUS_VIEW)
                .operationOriginOrderCancelStatusView(OPERATION_ORIGIN_ORDER_CANCEL_STATUS_VIEW)
                .build();
//        when(mockOrderStatusMappingDOMapper.getStatusMapping(0, 0)).thenReturn(orderStatusMappingDO);

        AppOrderGiftAddressDetailVO addressDetailVO = new AppOrderGiftAddressDetailVO();
        addressDetailVO.setRecipient("recipient");
        addressDetailVO.setRecipientPhone("13101234567");
        addressDetailVO.setProvince("province");
        addressDetailVO.setCity("city");
        addressDetailVO.setArea("area");
        addressDetailVO.setDetailAddress("address");
        when(mockOrderGiftAddressService.getOrderGiftAddressByOrderCode(anyString())).thenReturn(addressDetailVO);


        // Configure OrderRefundItemDOMapper.selectOne(...).
        final OrderRefundItemDO orderRefundItemDO = OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode(REFUND_ORDER_CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .build();
//        when(mockOrderRefundItemDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDO);

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode(REFUND_ORDER_CODE)
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .rejectReason(REJECT_REASON)
                .build();
//        when(mockOrderRefundDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundDO);

        // Configure InvoiceApi.getInvoiceDetail(...).
        final InvoiceDetailVO invoiceDetailVO = new InvoiceDetailVO();
        final EInvoiceDetailVO eInvoiceVO = new EInvoiceDetailVO();
        invoiceDetailVO.setEInvoiceVO(eInvoiceVO);
        final PaperInvoiceDetailVO paperInvoiceVO = new PaperInvoiceDetailVO();
        invoiceDetailVO.setPaperInvoiceVO(paperInvoiceVO);
        final CommonResult<InvoiceDetailVO> invoiceDetailVOCommonResult = CommonResult.success(invoiceDetailVO);
        when(mockInvoiceApi.getInvoiceDetail("LRcode")).thenReturn(invoiceDetailVOCommonResult);

        // 配置feedbackRecordsDOMapper.selectList
        when(mockFeedbackRecordsDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final OrderDetailRespVO result = orderInfoDOServiceImplUnderTest.getOrderDetail("LRcode");

        // Verify the results
        assertThat(result).isNotNull();
    }

    /**
     * 测试获取Branded Goods类型订单的详情
     */
    @Test
    public void testGetOrderDetailForBrandGoods() {
        // Reset mock to ensure clean state
        reset(mockInvoiceApi, mockRefundHandler, mockOrderRefundItemDOMapper, mockOrderRefundDOMapper);
        
        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(1L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(BG_CODE)
                .originalFeeTotalAmount(10000)
                .feeTotalAmount(9000)
                .costAmount(9000)
                .discountTotalAmount(1000)
                .freightAmount(500)
                .excludeTaxAmount(8000)
                .taxAmount(1000)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(2)
                .paymentStatus(1)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(1)
                .orderChannel(1)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .businessCode(BusinessIdEnum.BRAND_GOODS.getCode())  // 使用BRAND_GOODS业务线代码
                .logisticsStatus(2) // 添加物流状态
                .build();
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderInfoDO);

        // 配置发票信息
        final InvoiceDetailVO invoiceDetailVO = new InvoiceDetailVO();
        final EInvoiceDetailVO eInvoiceVO = new EInvoiceDetailVO();
        invoiceDetailVO.setEInvoiceVO(eInvoiceVO);
        final PaperInvoiceDetailVO paperInvoiceVO = new PaperInvoiceDetailVO();
        invoiceDetailVO.setPaperInvoiceVO(paperInvoiceVO);
        final CommonResult<InvoiceDetailVO> invoiceDetailVOCommonResult = CommonResult.success(invoiceDetailVO);
        when(mockInvoiceApi.getInvoiceDetail(BG_CODE)).thenReturn(invoiceDetailVOCommonResult);

        // 配置订单项信息
        final List<OrderItemDO> orderItems = List.of(OrderItemDO.builder()
                .id(1L)
                .orderItemCode("BG_ITEM001")
                .orderCode(BG_CODE)
                .productVersionCode("BG_VERSION001")
                .productCode("BG_PROD001")
                .productSkuCode("BG_SKU001")
                .productName("品牌商品测试")
                .productImageUrl("http://test.com/bg_image.jpg")
                .productAttribute("尺寸:L,颜色:黑色")
                .productMarketPrice(10000)
                .productSalePrice(9000)
                .productQuantity(1)
                .totalAmount(9000)
                .costAmount(9000)
                .discountFeeAmount(1000)
                .excludeTaxTotalAmount(8000)
                .taxAmount(1000)
                .remark("品牌商品测试备注")
                .build());
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderItems);
        
        // 配置折扣信息
        when(mockOrderDiscountDetailDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(new ArrayList<>());
        
        // 配置支付记录信息
        when(mockOrderPaymentRecordsMapper.queryLastByOrderCode(anyString())).thenReturn(null);
        
        // 配置物流信息
        when(mockOrderItemLogisticsDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);
//        when(mockOrderItemLogisticsDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(new ArrayList<>());

        // 配置refundHandler
        when(mockRefundHandler.getLogisticsMaxRefundMoney(any(), any())).thenReturn(8000);
        
        // 配置退款信息
        final OrderRefundItemDO orderRefundItemDO = new OrderRefundItemDO();
        orderRefundItemDO.setRefundOrderCode("REFUND_001");
        when(mockOrderRefundItemDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDO);
        
        final OrderRefundDO orderRefundDO = new OrderRefundDO();
        orderRefundDO.setRefundOrderCode("REFUND_001");
        when(mockOrderRefundDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundDO);

        // Run the test
        final OrderDetailRespVO result = orderInfoDOServiceImplUnderTest.getOrderDetail(BG_CODE);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.getOrderCode()).isEqualTo(BG_CODE);
        // 验证返回结果中的OrderInfo不为空
        assertThat(result.getOrderInfo()).isNotNull();
        // 验证这个分支下使用的是物流状态
        assertThat(result.getOrderInfo().getOrderStatus()).isEqualTo(orderInfoDO.getLogisticsStatus());
    }
    @NotNull
    private static OrderDetailOrderInfoVO getOrderDetailOrderInfoVO() {
        final OrderDetailOrderInfoVO orderInfo = new OrderDetailOrderInfoVO();
        orderInfo.setOrderStatus(2);
        orderInfo.setOrderStatusDesc("已支付");
        orderInfo.setPaymentStatus(1);
        orderInfo.setPaymentStatusDesc("已支付");
        orderInfo.setRefundStatus(0);
        orderInfo.setRefundStatusDesc("未发生退款");
        orderInfo.setParentOrderCode(PARENT_ORDER_CODE);
        orderInfo.setOrderCode(CODE);
        orderInfo.setCostAmount("0.00");
        orderInfo.setOrderChannel(1);
        orderInfo.setOrderChannelDesc("路虎小程序");
        orderInfo.setCustomerRemark(CUSTOMER_REMARK);
        orderInfo.setOperatorRemark(OPERATOR_REMARK);
        orderInfo.setOrderCloseReason(DESC);
        return orderInfo;
    }

    @NotNull
    private static ProductBrandCategoriedItemInfoVO getProductItemInfoVO() {
        final ProductBrandCategoriedItemInfoVO productItemInfoVO = new ProductBrandCategoriedItemInfoVO();
        productItemInfoVO.setOrderItemCode(ORDER_ITEM_CODE);
        productItemInfoVO.setProductVersionCode(PRODUCT_VERSION_CODE);
        productItemInfoVO.setProductCode(PRODUCT_CODE);
        productItemInfoVO.setProductSkuCode(PRODUCT_SKU_CODE);
        productItemInfoVO.setProductName(PRODUCT_NAME);
        productItemInfoVO.setProductQuantity(0);
        productItemInfoVO.setProductAttribute("");
        productItemInfoVO.setProductMarketPrice("0.00");
        productItemInfoVO.setProductSalePrice("0.00");
        productItemInfoVO.setServiceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfoVO.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfoVO.setActualEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfoVO.setServiceStatus(0);
        productItemInfoVO.setServiceStatusDesc(" - ");
        productItemInfoVO.setOperationOriginOrderCancelStatusView(OPERATION_ORIGIN_ORDER_CANCEL_STATUS_VIEW);
        productItemInfoVO.setRemark(REMARK);
        productItemInfoVO.setRefundOrderCode(REFUND_ORDER_CODE);
        productItemInfoVO.setRejectReason(REJECT_REASON);
        return productItemInfoVO;
    }

    @Test
    public void testEditOrderDetail() {
        // Setup
        final OrderEditDTO orderEditDTO = getOrderEditDTO();

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(ORDER_CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark("operatorRemark1")
                .refundStatus(0)
                .orderCloseReason(DESC)
                .build();
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderInfoDO);

        when(mockPiplDataUtil.getDecodeText(RECIPIENT_NAME)).thenReturn(RECIPIENT_NAME);

        // Configure InvoiceApi.modifyPaperInvoice(...).
        final PaperInvoiceModifyRespDTO respDTO = new PaperInvoiceModifyRespDTO();
        respDTO.setSuccess(true);
        respDTO.setOldVal("{\"开票状态\":\"-\",\"发票抬头\":\"\",\"税号\":\"\"}");
        respDTO.setNewVal("{\"开票状态\":\"开票中\",\"发票抬头\":\"invoiceTitleName\",\"税号\":\"titleTaxNo\"}");
        final CommonResult<PaperInvoiceModifyRespDTO> result1 = CommonResult.success(respDTO);
        when(mockInvoiceApi.modifyPaperInvoice(any())).thenReturn(result1);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(ORDER_CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute(PRODUCT_ATTRIBUTE)
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build());
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(list);

        // Run the test
        final Boolean result = orderInfoDOServiceImplUnderTest.editOrderDetail(orderEditDTO);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockOrderInfoDOMapper).updateById(any());
        verify(mockOrderItemDOMapper).updateById(any());
        verify(mockModifyDetailLogMapper).createModifyLog(eq(ORDER_CODE), eq("编辑订单备注"), any(), any());
    }

    @Test
    public void testEditOrderDetailOrderNull() {
        // Setup
        final OrderEditDTO orderEditDTO = getOrderEditDTO();

        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // Verify the results
        try {
            orderInfoDOServiceImplUnderTest.editOrderDetail(orderEditDTO);
        } catch (ServiceException e) {
            assertEquals(ORDER_NOT_EXISTS.getCode(), e.getCode());
        }
    }

    @NotNull
    private static OrderEditDTO getOrderEditDTO() {
        final OrderEditDTO orderEditDTO = new OrderEditDTO();
        orderEditDTO.setOrderCode(ORDER_CODE);
        orderEditDTO.setOperatorRemark(OPERATOR_REMARK);
        final OrderEditItemDTO itemDTO = new OrderEditItemDTO();
        itemDTO.setProductSkuCode(PRODUCT_SKU_CODE);
        itemDTO.setRemark("remark1");
        orderEditDTO.setProductItemInfoList(List.of(itemDTO));
        final PaperInvoiceDTO paperInvoiceInfo = getPaperInvoiceDTO();
        orderEditDTO.setPaperInvoiceInfo(paperInvoiceInfo);
        orderEditDTO.setViewInvoice(false);
        return orderEditDTO;
    }

    @NotNull
    private static PaperInvoiceDTO getPaperInvoiceDTO() {
        final PaperInvoiceDTO paperInvoiceInfo = new PaperInvoiceDTO();
        paperInvoiceInfo.setInvoiceStatus(1);
        paperInvoiceInfo.setInvoiceTitleName(INVOICE_TITLE_NAME);
        paperInvoiceInfo.setTitleTaxNo("****************");
        paperInvoiceInfo.setCompanyMobile("companyMobile");
        paperInvoiceInfo.setCompanyAddress("companyAddress");
        paperInvoiceInfo.setCompanyBankName("companyBankName");
        paperInvoiceInfo.setCompanyBankAccount("companyBankAccount");
        paperInvoiceInfo.setRecipientName(RECIPIENT_NAME);
        paperInvoiceInfo.setRecipientPhone("***********");
        paperInvoiceInfo.setRecipientAddress("recipientAddress");
        return paperInvoiceInfo;
    }

    @Test
    public void testSelectModifyPage() {
        // Setup
        final OrderModifyPageReqDTO dto = new OrderModifyPageReqDTO();
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setOrderCode(ORDER_CODE);

        final OrderModifyPageVO orderModifyPageVO = new OrderModifyPageVO();
        orderModifyPageVO.setId(0L);
        orderModifyPageVO.setModifyField("开票状态、发票抬头、税号");
        orderModifyPageVO.setModifyFieldCount(3);
        final PageResult<OrderModifyPageVO> expectedResult = new PageResult<>(List.of(orderModifyPageVO), 0L);

        // Configure OrderModifyDetailLogDOMapper.selectPage(...).
        final PageResult<OrderModifyDetailLogDO> orderModifyDetailLogDOPageResult = new PageResult<>(
                List.of(OrderModifyDetailLogDO.builder()
                        .id(0L)
                        .orderCode(ORDER_CODE)
                        .modifyFieldCount(3)
                        .modifyFieldOldValue("{\"开票状态\":\"-\",\"发票抬头\":\"\",\"税号\":\"\"}")
                        .build()), 0L);
        when(mockModifyDetailLogMapper.selectPage(eq(dto), any(LambdaQueryWrapperX.class)))
                .thenReturn(orderModifyDetailLogDOPageResult);

        // Run the test
        final PageResult<OrderModifyPageVO> result = orderInfoDOServiceImplUnderTest.selectModifyPage(dto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetLogDetail() {
        // Setup
        final OrderModifyDetailVO expectedResult = new OrderModifyDetailVO();
        expectedResult.setModifyFieldOldValue(MODIFY_FIELD_OLD_VALUE);

        // Configure OrderModifyDetailLogDOMapper.selectOne(...).
        final OrderModifyDetailLogDO modifyDetailLogDO = OrderModifyDetailLogDO.builder()
                .id(0L)
                .orderCode(ORDER_CODE)
                .modifyFieldCount(0)
                .modifyFieldOldValue(MODIFY_FIELD_OLD_VALUE)
                .build();
        when(mockModifyDetailLogMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(modifyDetailLogDO);

        // Run the test
        final OrderModifyDetailVO result = orderInfoDOServiceImplUnderTest.getLogDetail(0L);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetAppOrderDetail() {
        // Setup
        final OrderAppDetailPage expectedResult = new OrderAppDetailPage();
        final ProductBrandCategoriedItemInfoVO productItemInfo = getProductBrandCategoriedItemInfoVO();
        expectedResult.setProductItemInfo(productItemInfo);
        final OrderBrandVehicleInfoVO vehicleInfo = new OrderBrandVehicleInfoVO();
        vehicleInfo.setSeriesCode(SERIES_CODE);
        vehicleInfo.setSeriesName(SERIES_NAME);
        vehicleInfo.setCarVin(CAR_VIN);
        expectedResult.setVehicleInfo(vehicleInfo);
        final OrderBrandOrderInfoVO orderInfo = new OrderBrandOrderInfoVO();
        orderInfo.setOrderCode("LRcode");
        orderInfo.setOrderStatus(1);
        orderInfo.setInvoiceStatus(1);
        orderInfo.setCustomerOrderStatusView(CUSTOMER_ORDER_STATUS_VIEW);
        orderInfo.setCustomerAfterSalesOrderStatusView(CUSTOMER_AFTER_SALES_ORDER_STATUS_VIEW);
        orderInfo.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderInfo.setPaymentStatus(0);
        orderInfo.setPaymentStatusDesc("待支付");
        orderInfo.setPaymentTimeout(0L);
        orderInfo.setIsPaying(true);
        orderInfo.setPaymentMethod("微信支付");
        orderInfo.setCostAmount("0.00");
        expectedResult.setOrderInfo(orderInfo);

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode("LRcode")
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .build();
//        when(mockOrderItemDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderItemDO);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build());
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(list);


        // Configure VcsOrderInfoDOMapper.selectOne(...).
        final VCSOrderInfoDO vcsOrderInfoDO = VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode("LRcode")
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build();
//        when(mockVcsOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDO);

        // Configure VcsOrderFulfilmentApi.view(...).
        final VcsOrderFulfilmentRespVO vcsOrderFulfilmentRespVO = new VcsOrderFulfilmentRespVO();
        vcsOrderFulfilmentRespVO.setFulfilmentId(FULFILMENT_ID);
        vcsOrderFulfilmentRespVO.setOrderCode("LRcode");
        vcsOrderFulfilmentRespVO.setOrderItemCode(ORDER_ITEM_CODE);
        vcsOrderFulfilmentRespVO.setServiceStatus(0);
        vcsOrderFulfilmentRespVO.setServiceStatusDesc(" - ");
        final CommonResult<VcsOrderFulfilmentRespVO> vcsOrderFulfilmentRespVOCommonResult = CommonResult.success(
                vcsOrderFulfilmentRespVO);
//        when(mockVcsOrderFulfilmentApi.view(ORDER_ITEM_CODE)).thenReturn(vcsOrderFulfilmentRespVOCommonResult);

//        when(mockPhoneNumberDecodeUtil.getDecodePhone(CAR_VIN)).thenReturn(CAR_VIN);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode("LRcode")
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(1)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .businessCode(BusinessIdEnum.VCS.getCode())
                .build();
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderInfoDO);

//        when(mockOrderInfoDOMapper.getOrderStatusByOrderCode("LRcode")).thenReturn(0);
        when(mockOrderRefundItemDOMapper.getRefundOrderStatusByOrderItemCode(ORDER_ITEM_CODE)).thenReturn(0);

        // Configure OrderStatusMappingDOMapper.getStatusMapping(...).
        final OrderStatusMappingDO orderStatusMappingDO = OrderStatusMappingDO.builder()
                .orderStatus(1)
                .refundOrderStatus(1)
                .customerOrderStatusView(CUSTOMER_ORDER_STATUS_VIEW)
                .customerAfterSalesOrderStatusView(CUSTOMER_AFTER_SALES_ORDER_STATUS_VIEW)
                .operationOriginOrderCancelStatusView(OPERATION_ORIGIN_ORDER_CANCEL_STATUS_VIEW)
                .build();
//        when(mockOrderStatusMappingDOMapper.getStatusMapping(0, 0)).thenReturn(orderStatusMappingDO);

        // Configure OrderRefundItemDOMapper.selectOne(...).
        final OrderRefundItemDO orderRefundItemDO = OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode(REFUND_ORDER_CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .build();
//        when(mockOrderRefundItemDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDO);

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode(REFUND_ORDER_CODE)
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .rejectReason(REJECT_REASON)
                .build();
        when(mockOrderRefundDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundDO);

        // Configure InvoiceApi.getInvoiceDetail(...).
        final InvoiceDetailVO invoiceDetailVO = new InvoiceDetailVO();
        final EInvoiceDetailVO eInvoiceVO = new EInvoiceDetailVO();
        invoiceDetailVO.setEInvoiceVO(eInvoiceVO);
        final PaperInvoiceDetailVO paperInvoiceVO = new PaperInvoiceDetailVO();
        invoiceDetailVO.setPaperInvoiceVO(paperInvoiceVO);
        final CommonResult<InvoiceDetailVO> invoiceDetailVOCommonResult = CommonResult.success(invoiceDetailVO);
        when(mockInvoiceApi.getInvoiceDetail("LRcode")).thenReturn(invoiceDetailVOCommonResult);

        // 配置feedbackRecordsDOMapper.selectList
        when(mockFeedbackRecordsDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final OrderDetailRespVO result = orderInfoDOServiceImplUnderTest.getOrderDetail("LRcode");

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetOrderDetail_LRE() {
        // Setup
        final OrderAppDetailPage expectedResult = new OrderAppDetailPage();
        final ProductBrandCategoriedItemInfoVO productItemInfo = getProductBrandCategoriedItemInfoVO();
        expectedResult.setProductItemInfo(productItemInfo);
        final OrderBrandVehicleInfoVO vehicleInfo = new OrderBrandVehicleInfoVO();
        vehicleInfo.setSeriesCode(SERIES_CODE);
        vehicleInfo.setSeriesName(SERIES_NAME);
        vehicleInfo.setCarVin(CAR_VIN);
        expectedResult.setVehicleInfo(vehicleInfo);
        final OrderBrandOrderInfoVO orderInfo = new OrderBrandOrderInfoVO();
        orderInfo.setOrderCode("LRcode");
        orderInfo.setOrderStatus(1);
        orderInfo.setInvoiceStatus(1);
        orderInfo.setCustomerOrderStatusView(CUSTOMER_ORDER_STATUS_VIEW);
        orderInfo.setCustomerAfterSalesOrderStatusView(CUSTOMER_AFTER_SALES_ORDER_STATUS_VIEW);
        orderInfo.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderInfo.setPaymentStatus(0);
        orderInfo.setPaymentStatusDesc("待支付");
        orderInfo.setPaymentTimeout(0L);
        orderInfo.setIsPaying(true);
        orderInfo.setPaymentMethod("微信支付");
        orderInfo.setCostAmount("0.00");
        expectedResult.setOrderInfo(orderInfo);

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode("LRcode")
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .build();
//        when(mockOrderItemDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderItemDO);

        // Configure VcsOrderInfoDOMapper.selectOne(...).
        final VCSOrderInfoDO vcsOrderInfoDO = VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode("LRcode")
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build();
//        when(mockVcsOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDO);

        // Configure VcsOrderFulfilmentApi.view(...).
        final VcsOrderFulfilmentRespVO vcsOrderFulfilmentRespVO = new VcsOrderFulfilmentRespVO();
        vcsOrderFulfilmentRespVO.setFulfilmentId(FULFILMENT_ID);
        vcsOrderFulfilmentRespVO.setOrderCode("LRcode");
        vcsOrderFulfilmentRespVO.setOrderItemCode(ORDER_ITEM_CODE);
        vcsOrderFulfilmentRespVO.setServiceStatus(0);
        vcsOrderFulfilmentRespVO.setServiceStatusDesc(" - ");
        final CommonResult<VcsOrderFulfilmentRespVO> vcsOrderFulfilmentRespVOCommonResult = CommonResult.success(
                vcsOrderFulfilmentRespVO);
//        when(mockVcsOrderFulfilmentApi.view(ORDER_ITEM_CODE)).thenReturn(vcsOrderFulfilmentRespVOCommonResult);

//        when(mockPhoneNumberDecodeUtil.getDecodePhone(CAR_VIN)).thenReturn(CAR_VIN);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode("LRcode")
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(1)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .businessCode(BusinessIdEnum.LRE.getCode())
                .build();
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderInfoDO);

//        when(mockOrderInfoDOMapper.getOrderStatusByOrderCode("LRcode")).thenReturn(0);
//        when(mockOrderRefundItemDOMapper.getRefundOrderStatusByOrderItemCode(ORDER_ITEM_CODE)).thenReturn(0);
        when(mockOrderPaymentRecordsMapper.queryLastByOrderCode(anyString())).thenReturn(null);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build());
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(list);

        // Configure OrderStatusMappingDOMapper.getStatusMapping(...).
        final OrderStatusMappingDO orderStatusMappingDO = OrderStatusMappingDO.builder()
                .orderStatus(1)
                .refundOrderStatus(1)
                .customerOrderStatusView(CUSTOMER_ORDER_STATUS_VIEW)
                .customerAfterSalesOrderStatusView(CUSTOMER_AFTER_SALES_ORDER_STATUS_VIEW)
                .operationOriginOrderCancelStatusView(OPERATION_ORIGIN_ORDER_CANCEL_STATUS_VIEW)
                .build();
//        when(mockOrderStatusMappingDOMapper.getStatusMapping(0, 0)).thenReturn(orderStatusMappingDO);

        // Configure OrderRefundItemDOMapper.selectOne(...).
        final OrderRefundItemDO orderRefundItemDO = OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode(REFUND_ORDER_CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .refundMoney(1)
                .build();
        when(mockOrderRefundItemDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDO);

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode(REFUND_ORDER_CODE)
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .rejectReason(REJECT_REASON)
                .couponRefundStatus(RefundCouponStatusEnum.REFUND_COMPLETED.getCode())
                .build();
        when(mockOrderRefundDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundDO);
        when(mockOrderDiscountDetailDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(List.of(OrderDiscountDetailDO.builder()
                        .discountType(DiscountTypeEnum.COUPON_DISCOUNT.getType())
                        .couponCode("couponCode")
                        .couponModelClassify(1)
                        .couponModelName("couponModelName")
                        .build()));

        // Configure InvoiceApi.getInvoiceDetail(...).
        final InvoiceDetailVO invoiceDetailVO = new InvoiceDetailVO();
        final EInvoiceDetailVO eInvoiceVO = new EInvoiceDetailVO();
        invoiceDetailVO.setEInvoiceVO(eInvoiceVO);
        final PaperInvoiceDetailVO paperInvoiceVO = new PaperInvoiceDetailVO();
        invoiceDetailVO.setPaperInvoiceVO(paperInvoiceVO);
        final CommonResult<InvoiceDetailVO> invoiceDetailVOCommonResult = CommonResult.success(invoiceDetailVO);
        when(mockInvoiceApi.getInvoiceDetail("LRcode")).thenReturn(invoiceDetailVOCommonResult);

        // 配置feedbackRecordsDOMapper.selectList
//        when(mockFeedbackRecordsDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(mockOrderCouponDetailDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(List.of(OrderCouponDetailDO.builder()
                .couponCode("couponCode")
                        .status(EcouponStatusEnum.VERIFIED.getCode())
                .build()));
        when(mockRefundHandler.getMaxRefundMoney(any(), eq(RefundOrderOperationTypeEnum.OPERATION_INITIATED.getCode()))).thenReturn(1);

        // Run the test
        final OrderDetailRespVO result = orderInfoDOServiceImplUnderTest.getOrderDetail("LRcode");

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetOrderDetail_BG() {
        // Setup
        final OrderAppDetailPage expectedResult = new OrderAppDetailPage();
        final ProductBrandCategoriedItemInfoVO productItemInfo = getProductBrandCategoriedItemInfoVO();
        expectedResult.setProductItemInfo(productItemInfo);
        final OrderBrandVehicleInfoVO vehicleInfo = new OrderBrandVehicleInfoVO();
        vehicleInfo.setSeriesCode(SERIES_CODE);
        vehicleInfo.setSeriesName(SERIES_NAME);
        vehicleInfo.setCarVin(CAR_VIN);
        expectedResult.setVehicleInfo(vehicleInfo);
        final OrderBrandOrderInfoVO orderInfo = new OrderBrandOrderInfoVO();
        orderInfo.setOrderCode("LRcode");
        orderInfo.setOrderStatus(1);
        orderInfo.setInvoiceStatus(1);
        orderInfo.setCustomerOrderStatusView(CUSTOMER_ORDER_STATUS_VIEW);
        orderInfo.setCustomerAfterSalesOrderStatusView(CUSTOMER_AFTER_SALES_ORDER_STATUS_VIEW);
        orderInfo.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderInfo.setPaymentStatus(0);
        orderInfo.setPaymentStatusDesc("待支付");
        orderInfo.setPaymentTimeout(0L);
        orderInfo.setIsPaying(true);
        orderInfo.setPaymentMethod("微信支付");
        orderInfo.setCostAmount("0.00");
        expectedResult.setOrderInfo(orderInfo);

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode("LRcode")
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .build();
//        when(mockOrderItemDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderItemDO);

        // Configure VcsOrderInfoDOMapper.selectOne(...).
        final VCSOrderInfoDO vcsOrderInfoDO = VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode("LRcode")
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build();
//        when(mockVcsOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDO);

        // Configure VcsOrderFulfilmentApi.view(...).
        final VcsOrderFulfilmentRespVO vcsOrderFulfilmentRespVO = new VcsOrderFulfilmentRespVO();
        vcsOrderFulfilmentRespVO.setFulfilmentId(FULFILMENT_ID);
        vcsOrderFulfilmentRespVO.setOrderCode("LRcode");
        vcsOrderFulfilmentRespVO.setOrderItemCode(ORDER_ITEM_CODE);
        vcsOrderFulfilmentRespVO.setServiceStatus(0);
        vcsOrderFulfilmentRespVO.setServiceStatusDesc(" - ");
        final CommonResult<VcsOrderFulfilmentRespVO> vcsOrderFulfilmentRespVOCommonResult = CommonResult.success(
                vcsOrderFulfilmentRespVO);
//        when(mockVcsOrderFulfilmentApi.view(ORDER_ITEM_CODE)).thenReturn(vcsOrderFulfilmentRespVOCommonResult);

//        when(mockPhoneNumberDecodeUtil.getDecodePhone(CAR_VIN)).thenReturn(CAR_VIN);

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode("LRcode")
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(1)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .businessCode(BusinessIdEnum.BRAND_GOODS.getCode())
                .build();
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderInfoDO);

//        when(mockOrderInfoDOMapper.getOrderStatusByOrderCode("LRcode")).thenReturn(0);
//        when(mockOrderRefundItemDOMapper.getRefundOrderStatusByOrderItemCode(ORDER_ITEM_CODE)).thenReturn(0);
        when(mockOrderPaymentRecordsMapper.queryLastByOrderCode(anyString())).thenReturn(null);


        // Configure OrderStatusMappingDOMapper.getStatusMapping(...).
        final OrderStatusMappingDO orderStatusMappingDO = OrderStatusMappingDO.builder()
                .orderStatus(1)
                .refundOrderStatus(1)
                .customerOrderStatusView(CUSTOMER_ORDER_STATUS_VIEW)
                .customerAfterSalesOrderStatusView(CUSTOMER_AFTER_SALES_ORDER_STATUS_VIEW)
                .operationOriginOrderCancelStatusView(OPERATION_ORIGIN_ORDER_CANCEL_STATUS_VIEW)
                .build();
//        when(mockOrderStatusMappingDOMapper.getStatusMapping(0, 0)).thenReturn(orderStatusMappingDO);

        // Configure OrderRefundItemDOMapper.selectOne(...).
        final OrderRefundItemDO orderRefundItemDO = OrderRefundItemDO.builder()
                .id(0L)
                .refundOrderCode(REFUND_ORDER_CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .build();
//        when(mockOrderRefundItemDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundItemDO);
        OrderItemLogisticsDO orderItemLogisticsDO = new OrderItemLogisticsDO();
        orderItemLogisticsDO.setRecipient("");
        when(mockOrderItemLogisticsDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderItemLogisticsDO);

        // Configure OrderRefundDOMapper.selectOne(...).
        final OrderRefundDO orderRefundDO = OrderRefundDO.builder()
                .id(0L)
                .refundOrderCode(REFUND_ORDER_CODE)
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .rejectReason(REJECT_REASON)
                .build();
//        when(mockOrderRefundDOMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(orderRefundDO);
        when(mockOrderDiscountDetailDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(List.of(OrderDiscountDetailDO.builder()
                        .discountType(DiscountTypeEnum.COUPON_DISCOUNT.getType())
                        .couponCode("couponCode")
                        .couponModelClassify(1)
                        .couponModelName("couponModelName")
                        .build()));

        // Configure InvoiceApi.getInvoiceDetail(...).
        final InvoiceDetailVO invoiceDetailVO = new InvoiceDetailVO();
        final EInvoiceDetailVO eInvoiceVO = new EInvoiceDetailVO();
        invoiceDetailVO.setEInvoiceVO(eInvoiceVO);
        final PaperInvoiceDetailVO paperInvoiceVO = new PaperInvoiceDetailVO();
        invoiceDetailVO.setPaperInvoiceVO(paperInvoiceVO);
        final CommonResult<InvoiceDetailVO> invoiceDetailVOCommonResult = CommonResult.success(invoiceDetailVO);
        when(mockInvoiceApi.getInvoiceDetail("LRcode")).thenReturn(invoiceDetailVOCommonResult);

        // 配置feedbackRecordsDOMapper.selectList
//        when(mockFeedbackRecordsDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final OrderDetailRespVO result = orderInfoDOServiceImplUnderTest.getOrderDetail("LRcode");

        // Verify the results
        assertThat(result).isNotNull();
    }

    @NotNull
    private static ProductBrandCategoriedItemInfoVO getProductBrandCategoriedItemInfoVO() {
        final ProductBrandCategoriedItemInfoVO productItemInfo = new ProductBrandCategoriedItemInfoVO();
        productItemInfo.setOrderItemCode(ORDER_ITEM_CODE);
        productItemInfo.setProductVersionCode(PRODUCT_VERSION_CODE);
        productItemInfo.setProductCode(PRODUCT_CODE);
        productItemInfo.setProductSkuCode(PRODUCT_SKU_CODE);
        productItemInfo.setProductName(PRODUCT_NAME);
        productItemInfo.setProductQuantity(0);
        productItemInfo.setProductAttribute("");
        productItemInfo.setProductMarketPrice("0.00");
        productItemInfo.setProductSalePrice("0.00");
        productItemInfo.setServiceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfo.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfo.setActualEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        productItemInfo.setServiceStatus(0);
        productItemInfo.setServiceStatusDesc(" - ");
        productItemInfo.setProductImageUrl(PRODUCT_IMAGE_URL);
        return productItemInfo;
    }

//    @Test
//    public void testOrderCancel() {
//        // Setup
//        final OrderCancelDTO orderCancelDTO = new OrderCancelDTO();
//        orderCancelDTO.setOrderCode(CODE);
//
//        final OrderCancelVO cancelVO = new OrderCancelVO();
//        cancelVO.setCustomerRemark(CUSTOMER_REMARK);
//        final CommonResult<OrderCancelVO> expectedResult = CommonResult.success(cancelVO);
//
//        // Configure OrderInfoDOMapper.queryOrderDoByOrderCode(...).
//        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
//                .id(0L)
//                .consumerCode(CONSUMER_CODE)
//                .orderCode(CODE)
//                .originalFeeTotalAmount(0)
//                .feeTotalAmount(0)
//                .costAmount(0)
//                .discountTotalAmount(0)
//                .freightAmount(0)
//                .excludeTaxAmount(0)
//                .taxAmount(0)
//                .parentOrderCode(PARENT_ORDER_CODE)
//                .orderStatus(1)
//                .paymentStatus(0)
//                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .orderType(0)
//                .orderChannel(1)
//                .wxNickName(WX_NICK_NAME)
//                .wxPhone(CONTACT_PHONE)
//                .wxPhoneMix(WX_PHONE_MIX)
//                .wxPhoneMd5(WX_PHONE_MD5)
//                .customerRemark(CUSTOMER_REMARK)
//                .contactPhone(CONTACT_PHONE)
//                .contactPhoneMix(CONTACT_PHONE_MIX)
//                .contactPhoneMd5(CONTACT_PHONE_MD5)
//                .operatorRemark(OPERATOR_REMARK)
//                .refundStatus(0)
//                .orderCloseReason(DESC)
//                .build();
//        when(mockOrderInfoDOMapper.queryOrderDoByOrderCode(CODE)).thenReturn(orderInfoDO);
//        TenantContextHolder.setTenantId(0L);
//        // Run the test
//        final CommonResult<OrderCancelVO> result = orderInfoDOServiceImplUnderTest.orderCancel(orderCancelDTO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(expectedResult);
//        verify(mockOrderStatusLogDOMapper).insert(any());
//        verify(mockOrderInfoDOMapper).updateById(any());
//    }
//
//    @Test
//    public void testOrderCancelDtoNull() {
//        // Run the test
//        final CommonResult<OrderCancelVO> result = orderInfoDOServiceImplUnderTest.orderCancel(null);
//
//        // Verify the results
//        assertThat(result).isEqualTo(CommonResult.error(ErrorCodeConstants.ORDER_CANCEL_REQ_NULL));
//    }
//
//    @Test
//    public void testOrderCancelOrderInfoDoNull() {
//        // Setup
//        final OrderCancelDTO orderCancelDTO = new OrderCancelDTO();
//        orderCancelDTO.setOrderCode(CODE);
//        // Run the test
//        final CommonResult<OrderCancelVO> result = orderInfoDOServiceImplUnderTest.orderCancel(orderCancelDTO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(CommonResult.error(ErrorCodeConstants.CANCEL_ORDER_INFO_NULL));
//    }

//    @Test
//    public void testOrderCancelNotOrdered() {
//        // Setup
//        final OrderCancelDTO orderCancelDTO = new OrderCancelDTO();
//        orderCancelDTO.setOrderCode(CODE);
//        // Configure OrderInfoDOMapper.queryOrderDoByOrderCode(...).
//        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
//                .id(0L)
//                .consumerCode(CONSUMER_CODE)
//                .orderCode(CODE)
//                .originalFeeTotalAmount(0)
//                .feeTotalAmount(0)
//                .costAmount(0)
//                .discountTotalAmount(0)
//                .freightAmount(0)
//                .excludeTaxAmount(0)
//                .taxAmount(0)
//                .parentOrderCode(PARENT_ORDER_CODE)
//                .orderStatus(2)
//                .paymentStatus(0)
//                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
//                .orderType(0)
//                .orderChannel(0)
//                .wxNickName(WX_NICK_NAME)
//                .wxPhone(CONTACT_PHONE)
//                .wxPhoneMix(WX_PHONE_MIX)
//                .wxPhoneMd5(WX_PHONE_MD5)
//                .customerRemark(CUSTOMER_REMARK)
//                .contactPhone(CONTACT_PHONE)
//                .contactPhoneMix(CONTACT_PHONE_MIX)
//                .contactPhoneMd5(CONTACT_PHONE_MD5)
//                .operatorRemark(OPERATOR_REMARK)
//                .refundStatus(0)
//                .orderCloseReason(DESC)
//                .build();
//        when(mockOrderInfoDOMapper.queryOrderDoByOrderCode(CODE)).thenReturn(orderInfoDO);
//        // Run the test
//        final CommonResult<OrderCancelVO> result = orderInfoDOServiceImplUnderTest.orderCancel(orderCancelDTO);
//
//        // Verify the results
//        assertThat(result).isEqualTo(CommonResult.error(ErrorCodeConstants.CANCEL_ORDER_STATUE_ERROR));
//    }

    @Test
    public void testGetNewOrderList() {
        // Setup
        final OrderBrandListNewDTO dto = new OrderBrandListNewDTO();
        dto.setSeriesCodeList(List.of(VALUE));
        dto.setConsumerCode(CONSUMER_CODE);

        final OrderNewBrandRespVO orderNewBrandRespVO = new OrderNewBrandRespVO();
        final OrderItemBaseVO productInfo = getOrderItemBaseVO();
        orderNewBrandRespVO.setProductInfo(productInfo);
        final OrderBrandVehicleInfoVO vehicleInfo = new OrderBrandVehicleInfoVO();
        vehicleInfo.setSeriesCode(SERIES_CODE);
        vehicleInfo.setSeriesName(SERIES_NAME);
        vehicleInfo.setCarVin(CAR_VIN);
        orderNewBrandRespVO.setVehicleInfo(vehicleInfo);
        final OrderBrandOrderInfoVO orderInfo = new OrderBrandOrderInfoVO();
        orderInfo.setOrderCode(CODE);
        orderInfo.setOrderStatus(1);
        orderInfo.setInvoiceStatus(0);
        orderInfo.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderInfo.setCostAmount("0.00");
        orderInfo.setCustomerOrderStatusView(CUSTOMER_ORDER_STATUS_VIEW);
        orderInfo.setServiceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderInfo.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        orderNewBrandRespVO.setOrderInfo(orderInfo);
        orderNewBrandRespVO.setOrderCode(CODE);
        orderNewBrandRespVO.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final List<OrderNewBrandRespVO> expectedResult = List.of(orderNewBrandRespVO);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build());
        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDOS);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build());
        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(list);

        // Configure OrderInfoDOMapper.selectList(...).
        final List<OrderInfoDO> orderInfoDOS = List.of(OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .build());
        when(mockOrderInfoDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDOS);

        // Configure OrderStatusMappingDOMapper.selectList(...).
        final List<OrderStatusMappingDO> orderStatusMappingDOS = List.of(OrderStatusMappingDO.builder()
                .orderStatus(1)
                .refundOrderStatus(-1)
                .customerOrderStatusView(CUSTOMER_ORDER_STATUS_VIEW)
                .customerAfterSalesOrderStatusView(CUSTOMER_AFTER_SALES_ORDER_STATUS_VIEW)
                .operationOriginOrderCancelStatusView(OPERATION_ORIGIN_ORDER_CANCEL_STATUS_VIEW)
                .build());
        when(mockOrderStatusMappingDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(orderStatusMappingDOS);

        // Configure InvoiceApi.getInvokeStatusList(...).
        final InvoiceStatusVO invoiceStatusVO = new InvoiceStatusVO();
        invoiceStatusVO.setOrderCode(CODE);
        invoiceStatusVO.setInvoiceStatus(0);
        final CommonResult<List<InvoiceStatusVO>> result1 = CommonResult.success(List.of(invoiceStatusVO));
        when(mockInvoiceApi.getInvokeStatusList(List.of(CODE))).thenReturn(result1);

//        when(mockPhoneNumberDecodeUtil.getDecodePhone(CAR_VIN)).thenReturn(CAR_VIN);

        // Configure feedbackRecordsDOMapper.selectList(...).
        //feedbackConfigDOMapper.selectList(new LambdaQueryWrapper<FeedbackConfigDO>()
        //                .eq(FeedbackConfigDO::getIsDeleted, 0)
        //                .eq(FeedbackConfigDO::getEnableStatus, FeedBackEnableStatusEnum.ENABLE.getCode()));
        when(mockFeedbackRecordsDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());


        // Run the test
        final List<OrderNewBrandRespVO> result = orderInfoDOServiceImplUnderTest.getNewOrderList(dto, 0);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @NotNull
    private static OrderItemBaseVO getOrderItemBaseVO() {
        final OrderItemBaseVO productInfo = new OrderItemBaseVO();
        productInfo.setOrderItemCode(ORDER_ITEM_CODE);
        productInfo.setProductVersionCode(PRODUCT_VERSION_CODE);
        productInfo.setProductCode(PRODUCT_CODE);
        productInfo.setProductSkuCode(PRODUCT_SKU_CODE);
        productInfo.setProductName(PRODUCT_NAME);
        productInfo.setProductImageUrl(PRODUCT_IMAGE_URL);
        productInfo.setProductAttribute("");
        productInfo.setProductMarketPrice("0.00");
        productInfo.setProductSalePrice("0.00");
        productInfo.setProductQuantity(0);
        return productInfo;
    }

    @Test
    public void testGetOrder() {
        // Setup
        final OrderIntegrationRespVO expectedResult = new OrderIntegrationRespVO();
          expectedResult.setPhoneNumber(PHONE_NUMBER);
          expectedResult.setOrderCode(ORDER_CODE);
          expectedResult.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure ConsumerApi.getConsumerByCode(...).
        final ConsumerInfoDTO consumerInfoDTO = new ConsumerInfoDTO();
        consumerInfoDTO.setConsumerCode(CONSUMER_CODE);
        consumerInfoDTO.setPhoneEncrypt(PHONE_ENCRYPT);
        consumerInfoDTO.setPhoneMd5(PHONE_MD5);
        consumerInfoDTO.setNickName("nickName");
        consumerInfoDTO.setCity("city");
        final CommonResult<ConsumerInfoDTO> consumerInfoDTOCommonResult = CommonResult.success(consumerInfoDTO);
        when(mockConsumerApi.getConsumerByCode(JLR_ID)).thenReturn(consumerInfoDTOCommonResult);

        when(mockPhoneNumberDecodeUtil.getDecodePhone(PHONE_ENCRYPT)).thenReturn(PHONE_NUMBER);

        // Configure OrderInfoDOMapper.queryOrderDoByOrderCode(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(ORDER_CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .build();
        when(mockOrderInfoDOMapper.queryOrderDoByOrderCode(ORDER_CODE)).thenReturn(orderInfoDO);

        // Run the test
        final OrderIntegrationRespVO result = orderInfoDOServiceImplUnderTest.getOrder(ORDER_CODE, JLR_ID);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOrderConsumerApiReturnsError() {
        // Setup
        final OrderIntegrationRespVO expectedResult = new OrderIntegrationRespVO();
        expectedResult.setOrderCode(CODE);
        expectedResult.setOrderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        // Configure ConsumerApi.getConsumerByCode(...).
        final CommonResult<ConsumerInfoDTO> consumerInfoDTOCommonResult = CommonResult.error(
                new ServiceException(500, "message"));
        when(mockConsumerApi.getConsumerByCode(JLR_ID)).thenReturn(consumerInfoDTOCommonResult);

        // Configure OrderInfoDOMapper.queryOrderDoByOrderCode(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .build();
        when(mockOrderInfoDOMapper.queryOrderDoByOrderCode(CODE)).thenReturn(orderInfoDO);

        // Run the test
        final OrderIntegrationRespVO result = orderInfoDOServiceImplUnderTest.getOrder(CODE, JLR_ID);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetLatestOrders() {
        // Setup
        final OrderLatestListReqDTO dto = new OrderLatestListReqDTO();
        dto.setPageNo(0);
        dto.setPageSize(0);
        dto.setJlrId(JLR_ID);
        dto.setCreatedTimeSort("createdTimeSort");

        final OrderIntegrationLatestOrderRespVO expectedResult = new OrderIntegrationLatestOrderRespVO();
        final OrderIntegrationPageRespVO orderIntegrationPageRespVO = getOrderIntegrationPageRespVO();
        expectedResult.setPageResult(new PageResult<>(List.of(orderIntegrationPageRespVO), 0L));
        expectedResult.setPhoneNumber(PHONE_NUMBER);

        // Configure ConsumerApi.getConsumerByCode(...).
        final ConsumerInfoDTO consumerInfoDTO = new ConsumerInfoDTO();
        consumerInfoDTO.setConsumerCode(CONSUMER_CODE);
        consumerInfoDTO.setPhoneEncrypt(PHONE_ENCRYPT);
        consumerInfoDTO.setPhoneMd5(PHONE_MD5);
        consumerInfoDTO.setNickName("nickName");
        consumerInfoDTO.setCity("city");
        final CommonResult<ConsumerInfoDTO> consumerInfoDTOCommonResult = CommonResult.success(consumerInfoDTO);
        when(mockConsumerApi.getConsumerByCode(JLR_ID)).thenReturn(consumerInfoDTOCommonResult);

        when(mockPhoneNumberDecodeUtil.getDecodePhone(PHONE_ENCRYPT)).thenReturn(PHONE_NUMBER);
        final List<OrderInfoDO> orderInfoDOS = List.of(OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .businessCode("VCS")  // 添加businessCode字段
                .build());
        final PageResult<OrderInfoDO> orderInfoDOPageResult = new PageResult<>(
                orderInfoDOS, 1L);
//        when(mockOrderInfoDOMapper
//                .selectPage(eq(dto),any(LambdaQueryWrapper.class))).thenReturn(orderInfoDOPageResult);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(ORDER_CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build());
//        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(list);

        // Run the test
       assertThatThrownBy(()->orderInfoDOServiceImplUnderTest.getLatestOrders(dto)).isInstanceOf(Exception.class);

        // Verify the results
    }

    @NotNull
    private static OrderIntegrationPageRespVO getOrderIntegrationPageRespVO() {
        final OrderIntegrationPageRespVO orderIntegrationPageRespVO = new OrderIntegrationPageRespVO();
        orderIntegrationPageRespVO.setOrderCode(ORDER_CODE);
        orderIntegrationPageRespVO.setCarVin(CAR_VIN);
        final OrderIntegrationPageRespVO.ProductItemInfoData productItemInfoData = new OrderIntegrationPageRespVO.ProductItemInfoData();
        productItemInfoData.setProductMarketPrice("0.00");
        productItemInfoData.setProductSalePrice("0.00");
        productItemInfoData.setProductQuantity(0);
        productItemInfoData.setProductAttribute("");
        orderIntegrationPageRespVO.setProductItemInfo(List.of(productItemInfoData));
        return orderIntegrationPageRespVO;
    }

    @Test
    public void testUpdateOrderStatus() {
        // Setup
        // Configure OrderInfoDOMapper.selectList(...).
        final List<OrderInfoDO> orderInfoDOS = List.of(OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .businessCode("VCS")  // 添加businessCode字段
                .build());
        when(mockOrderInfoDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDOS);

        // Run the test
        final Integer result = orderInfoDOServiceImplUnderTest.updateOrderStatus(ORDER_CODE);

        // Verify the results
        assertThat(result).isEqualTo(0);
        verify(mockOrderInfoDOMapper).updateBatch(anyList());
        verify(mockOrderStatusLogDOMapper).insertBatch(anyList());
    }

    @Test
    public void testUpdateOrderStatusOnSuccess() {
        // Setup
        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(LR_CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .businessCode("VCS")  // 添加businessCode字段
                .build();
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Configure OrderInfoDOMapper.selectList(...).
        final List<OrderInfoDO> orderInfoDOS = List.of(OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(LR_CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .build());
        when(mockOrderInfoDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDOS);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(LR_CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute(PRODUCT_ATTRIBUTE)
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build());
//        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(list);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(LR_CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build());
//        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDOS);

        // Configure SubscriptionServiceApi.findServiceExpiryDate(...).
        final SubscriptionServiceQueryVO subscriptionServiceQueryVO = new SubscriptionServiceQueryVO();
        subscriptionServiceQueryVO.setIncontrolId(IN_CONTROL_ID);
        subscriptionServiceQueryVO.setCarVin(CAR_VIN);
        subscriptionServiceQueryVO.setExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final SubscriptionServiceQueryDTO subscriptionServiceQueryDTO = new SubscriptionServiceQueryDTO();
        subscriptionServiceQueryDTO.setIncontrolId(IN_CONTROL_ID);
        subscriptionServiceQueryDTO.setCarVin(CAR_VIN);
        ValueOperations<String, Object> valueOpsMock = mock(ValueOperations.class);
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOpsMock);
        // 配置 setIfAbsent 的返回值
        when(valueOpsMock.setIfAbsent(anyString(), eq("1"), eq(1L), eq(TimeUnit.MINUTES)))
                .thenReturn(true);
        // 配置 mockTransactionTemplate.executeWithoutResult 的行为
//        doAnswer(invocation -> {
//            // 获取传入的 TransactionCallbackWithoutResult 对象
//            TransactionCallbackWithoutResult callback = invocation.getArgument(0);
//            // 执行回调逻辑
//            callback.doInTransaction(null);
//            return null;
//        }).when(mockTransactionTemplate).executeWithoutResult(null);


        // Run the test
        final Integer result = orderInfoDOServiceImplUnderTest.updateOrderStatusOnSuccess(LR_CODE, null);

        // Verify the results
        assertThat(result).isEqualTo(0);

    }

    @Test
    public void testUpdateOrderStatusOnSuccessOrderType1() {
        // Setup
        ValueOperations<String, Object> valueOpsMock = mock(ValueOperations.class);
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOpsMock);
        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(JA_CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(1)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .build();
//        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(JA_CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build());
//        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(list);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(JA_CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build());
//        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDOS);

//        when(mockEcpIdUtil.nextIdStr()).thenReturn("messageId");
//        when(mockPhoneNumberDecodeUtil.getDecodePhone(CONTACT_PHONE)).thenReturn(PHONE_NUMBER);
        ShortLinkReqDto shortLinkReqDto = new ShortLinkReqDto();
        shortLinkReqDto.setPath("ecp/pages/mine/order/detail/index");
        shortLinkReqDto.setQuery("orderCode=JAcode");
//        when(mockShorLinkAPI.genJaguarLink(shortLinkReqDto)).thenReturn(CommonResult.success(VALUE));

        // Configure SubscriptionServiceApi.findServiceExpiryDate(...).
        final SubscriptionServiceQueryVO subscriptionServiceQueryVO = new SubscriptionServiceQueryVO();
        subscriptionServiceQueryVO.setIncontrolId(IN_CONTROL_ID);
        subscriptionServiceQueryVO.setCarVin(CAR_VIN);
        subscriptionServiceQueryVO.setExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<SubscriptionServiceQueryVO> subscriptionServiceQueryVOCommonResult = CommonResult.success(
                subscriptionServiceQueryVO);
        final SubscriptionServiceQueryDTO subscriptionServiceQueryDTO = new SubscriptionServiceQueryDTO();
        subscriptionServiceQueryDTO.setIncontrolId(IN_CONTROL_ID);
        subscriptionServiceQueryDTO.setCarVin(CAR_VIN);
//        when(mockSubscriptionServiceApi.findServiceExpiryDate(subscriptionServiceQueryDTO))
//                .thenReturn(subscriptionServiceQueryVOCommonResult);

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(JA_CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build();
//        when(mockOrderItemDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDO);

//        when(mockPiplDataUtil.getDecodeText(CAR_VIN)).thenReturn("vin");

        // Run the test
        final Integer result = orderInfoDOServiceImplUnderTest.updateOrderStatusOnSuccess(JA_CODE, null);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testUpdateOrderStatusOnSuccessLr() {
        // Setup
        ValueOperations<String, Object> valueOpsMock = mock(ValueOperations.class);
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOpsMock);
        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(LR_CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(1)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .build();
//        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Configure OrderItemDOMapper.selectList(...).
        final List<OrderItemDO> list = List.of(OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(LR_CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build());
//        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(list);

        // Configure VcsOrderInfoDOMapper.selectList(...).
        final List<VCSOrderInfoDO> vcsOrderInfoDOS = List.of(VCSOrderInfoDO.builder()
                .vcsOrderCode(VCS_ORDER_CODE)
                .orderCode(LR_CODE)
                .orderItemCode(ORDER_ITEM_CODE)
                .consumerCode(CONSUMER_CODE)
                .incontrolId(IN_CONTROL_ID)
                .incontrolIdMd5(IN_CONTROL_ID_MD5)
                .incontrolIdMix(IN_CONTROL_ID_MIX)
                .carVin(CAR_VIN)
                .carVinMd5(CAR_VIN_MD5)
                .carVinMix(CAR_VIN_MIX)
                .serviceBeginDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .seriesName(SERIES_NAME)
                .seriesCode(SERIES_CODE)
                .build());
//        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(vcsOrderInfoDOS);

//        when(mockEcpIdUtil.nextIdStr()).thenReturn("messageId");
//        when(mockPhoneNumberDecodeUtil.getDecodePhone(CONTACT_PHONE)).thenReturn(PHONE_NUMBER);
        ShortLinkReqDto shortLinkReqDto = new ShortLinkReqDto();
        shortLinkReqDto.setPath("ecp/pages/mine/order/detail/index?orderCode=LRcode");
//        when(mockShorLinkAPI.genShortLink(shortLinkReqDto)).thenReturn(CommonResult.success(VALUE));

        // Configure SubscriptionServiceApi.findServiceExpiryDate(...).
        final SubscriptionServiceQueryVO subscriptionServiceQueryVO = new SubscriptionServiceQueryVO();
        subscriptionServiceQueryVO.setIncontrolId(IN_CONTROL_ID);
        subscriptionServiceQueryVO.setCarVin(CAR_VIN);
        subscriptionServiceQueryVO.setExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        final CommonResult<SubscriptionServiceQueryVO> subscriptionServiceQueryVOCommonResult = CommonResult.success(
                subscriptionServiceQueryVO);
        final SubscriptionServiceQueryDTO subscriptionServiceQueryDTO = new SubscriptionServiceQueryDTO();
        subscriptionServiceQueryDTO.setIncontrolId(IN_CONTROL_ID);
        subscriptionServiceQueryDTO.setCarVin(CAR_VIN);
//        when(mockSubscriptionServiceApi.findServiceExpiryDate(subscriptionServiceQueryDTO))
//                .thenReturn(subscriptionServiceQueryVOCommonResult);

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(LR_CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute("")
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build();
//        when(mockOrderItemDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDO);

//        when(mockPiplDataUtil.getDecodeText(CAR_VIN)).thenReturn("vin");

        // Run the test
        final Integer result = orderInfoDOServiceImplUnderTest.updateOrderStatusOnSuccess(LR_CODE, null);

        // Verify the results
        assertThat(result).isEqualTo(0);
    }

    @Test
    public void testBuildPaymentRequest() {
        // Setup
        final PayRequestDTO inputDto = new PayRequestDTO();
        inputDto.setAppNo(APP_NO);
        inputDto.setOrderCode(ORDER_CODE);
        inputDto.setOrderAmount(0);
        inputDto.setOpenid("openid");
        inputDto.setChannelCode(CHANNEL_CODE);
        OrderGiftAddressDTO orderGiftAddressDTO = new OrderGiftAddressDTO();
        orderGiftAddressDTO.setNeedGift(0);
        inputDto.setGiftInfoDTO(orderGiftAddressDTO);

        final PayOrderRespVO expectedResult = new PayOrderRespVO();

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .businessCode(BusinessIdEnum.BRAND_GOODS.getCode())
                .build();
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(ORDER_CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute(PRODUCT_ATTRIBUTE)
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build();
//        when(mockOrderItemDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDO);

        // Configure PayOrderApi.submitPayOrder(...).
        final PayOrderSubmitRespVO payOrderSubmitRespVO = new PayOrderSubmitRespVO();
        final CommonResult<PayOrderSubmitRespVO> payOrderSubmitRespVOCommonResult = CommonResult.success(
                payOrderSubmitRespVO);
//        when(mockPayOrderApi.submitPayOrder(any())).thenReturn(payOrderSubmitRespVOCommonResult);

        when(submitPayOrderComponent.processToOrderItemList(any(),any(),any())).thenReturn(List.of());

        // Run the test
        final PayOrderRespVO result = orderInfoDOServiceImplUnderTest.buildPaymentRequest(inputDto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testBuildPaymentRequestPayOrderApiReturnsError() {
        // Setup
        final PayRequestDTO inputDto = new PayRequestDTO();
        inputDto.setAppNo(APP_NO);
        inputDto.setOrderCode(CODE);
        inputDto.setOrderAmount(0);
        inputDto.setOpenid("openid");
        inputDto.setChannelCode(CHANNEL_CODE);
        OrderGiftAddressDTO orderGiftAddressDTO = new OrderGiftAddressDTO();
        orderGiftAddressDTO.setNeedGift(0);
        inputDto.setGiftInfoDTO(orderGiftAddressDTO);

        final PayOrderRespVO expectedResult = new PayOrderRespVO();

        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(1)
                .orderChannel(2)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .businessCode(BusinessIdEnum.BRAND_GOODS.getCode())
                .build();
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);

        // Configure OrderItemDOMapper.selectOne(...).
        final OrderItemDO orderItemDO = OrderItemDO.builder()
                .id(0L)
                .orderItemCode(ORDER_ITEM_CODE)
                .orderCode(ORDER_CODE)
                .productVersionCode(PRODUCT_VERSION_CODE)
                .productCode(PRODUCT_CODE)
                .productSkuCode(PRODUCT_SKU_CODE)
                .productName(PRODUCT_NAME)
                .productImageUrl(PRODUCT_IMAGE_URL)
                .productAttribute(PRODUCT_ATTRIBUTE)
                .productMarketPrice(0)
                .productSalePrice(0)
                .productQuantity(0)
                .totalAmount(0)
                .costAmount(0)
                .discountFeeAmount(0)
                .excludeTaxTotalAmount(0)
                .taxAmount(0)
                .remark(REMARK)
                .build();
//        when(mockOrderItemDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderItemDO);

        // Configure PayOrderApi.submitPayOrder(...).
        final PrePayOrderSubmitReqVO prePayOrderSubmitReqVO = new PrePayOrderSubmitReqVO();
        prePayOrderSubmitReqVO.setAppNo(APP_NO);
        prePayOrderSubmitReqVO.setOrderAmount(0);
        prePayOrderSubmitReqVO.setOrderCode(ORDER_CODE);
        prePayOrderSubmitReqVO.setChannelCode(CHANNEL_CODE);
        prePayOrderSubmitReqVO.setChannelExtras(Map.ofEntries(Map.entry(VALUE, VALUE)));

        when(submitPayOrderComponent.processToOrderItemList(any(),any(),any())).thenReturn(List.of());

        // Run the test
        final PayOrderRespVO result = orderInfoDOServiceImplUnderTest.buildPaymentRequest(inputDto);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testOrderTimeoutCancel() {
        // Setup
        // Configure OrderInfoDOMapper.selectList(...).
        final List<OrderInfoDO> orderInfoDOS = List.of(OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .build());
        when(mockOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderInfoDOS);
        TenantContextHolder.setTenantId(0L);
        // Run the test
        final Integer result = orderInfoDOServiceImplUnderTest.orderTimeoutCancel(0);

        // Verify the results
        assertThat(result).isZero();
    }

    @Test
    public void testCallPayment() {
        // Setup
        // Configure OrderInfoDOMapper.selectOne(...).
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .build();
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);
        ValueOperations<String, Object> valueOpsMock = mock(ValueOperations.class);
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOpsMock);
//        doNothing().when(valueOpsMock).set(anyString(), eq(true), anyLong(), any(TimeUnit.class));

        // Run the test
        final Boolean result = orderInfoDOServiceImplUnderTest.callPayment(CODE,"SUCCESS");

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testPaymentFail() {
        final OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .build();
        // Setup
        when(mockOrderInfoDOMapper.queryOrderDoByOrderCode(CODE)).thenReturn(orderInfoDO);

        // Run the test
        final Boolean result = orderInfoDOServiceImplUnderTest.paymentFail(CODE);

        // Verify the results
        assertThat(result).isTrue();
    }

    @Test
    public void testQueryOrderInfoByOrderCode() {
        // Setup
        final OrderInvoiceDTO orderInvoiceDTO = getOrderInvoiceDTO();
        final CommonResult<OrderInvoiceDTO> expectedResult = CommonResult.success(orderInvoiceDTO);

        // Configure OrderInfoDOMapper.selectList(...).
        final List<OrderInfoDO> orderInfoDOS = List.of(OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(ORDER_CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .orderStatus(1)
                .paymentStatus(0)
                .orderType(0)
                .build());
        when(mockOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderInfoDOS);

        // Run the test
        final CommonResult<OrderInvoiceDTO> result = orderInfoDOServiceImplUnderTest.queryOrderInfoByOrderCode(
                ORDER_CODE);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @NotNull
    private static OrderInvoiceDTO getOrderInvoiceDTO() {
        final OrderInvoiceDTO orderInvoiceDTO = new OrderInvoiceDTO();
        orderInvoiceDTO.setConsumerCode(CONSUMER_CODE);
        orderInvoiceDTO.setOrderCode(ORDER_CODE);
        orderInvoiceDTO.setOriginalFeeTotalAmount(0);
        orderInvoiceDTO.setFeeTotalAmount(0);
        orderInvoiceDTO.setCostAmount(0);
        orderInvoiceDTO.setDiscountTotalAmount(0);
        orderInvoiceDTO.setFreightAmount(0);
        orderInvoiceDTO.setOrderStatus(1);
        orderInvoiceDTO.setPaymentStatus(0);
        orderInvoiceDTO.setOrderType(0);
        return orderInvoiceDTO;
    }

    @Test
    public void testQueryOrderInfoByOrderCodeOrderInfoDOMapperReturnsNoItems() {
        // Setup
        final CommonResult<OrderInvoiceDTO> expectedResult = CommonResult.error(INVALID_ORDER);
        when(mockOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final CommonResult<OrderInvoiceDTO> result = orderInfoDOServiceImplUnderTest.queryOrderInfoByOrderCode(
                ORDER_CODE);

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testQueryOrderCodeSet() {
        // Setup
        // Configure OrderInfoDOMapper.selectList(...).
        final List<OrderInfoDO> orderInfoDOS = List.of(OrderInfoDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(CODE)
                .originalFeeTotalAmount(0)
                .feeTotalAmount(0)
                .costAmount(0)
                .discountTotalAmount(0)
                .freightAmount(0)
                .excludeTaxAmount(0)
                .taxAmount(0)
                .parentOrderCode(PARENT_ORDER_CODE)
                .orderStatus(1)
                .paymentStatus(0)
                .orderTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .orderType(0)
                .orderChannel(0)
                .wxNickName(WX_NICK_NAME)
                .wxPhone(CONTACT_PHONE)
                .wxPhoneMix(WX_PHONE_MIX)
                .wxPhoneMd5(WX_PHONE_MD5)
                .customerRemark(CUSTOMER_REMARK)
                .contactPhone(CONTACT_PHONE)
                .contactPhoneMix(CONTACT_PHONE_MIX)
                .contactPhoneMd5(CONTACT_PHONE_MD5)
                .operatorRemark(OPERATOR_REMARK)
                .refundStatus(0)
                .orderCloseReason(DESC)
                .build());
        when(mockOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(orderInfoDOS);

        // Run the test
        final Set<String> result = orderInfoDOServiceImplUnderTest.getAfterSalesOrderCode(List.of(VALUE));

        // Verify the results
        assertThat(result).isEqualTo(Set.of(CODE));
    }

    @Test
    public void testQueryOrderCodeSetOrderInfoDOMapperReturnsNoItems() {
        // Setup
        when(mockOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // Run the test
        final Set<String> result = orderInfoDOServiceImplUnderTest.getAfterSalesOrderCode(List.of(VALUE));

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testGetAppOrderDetailPolicyList_NoTermsFound() {
        // 1. Mock OrderInfoDO（非父订单）
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(new OrderInfoDO().setOrderCode(ORDER_CODE).setOrderType(1));

        // 2. Mock 无 OrderTermsDO 数据
        when(mockOrderTermsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(Collections.emptyList());

        // 3. 执行测试
        List<OrderAppDetailPolicyRespVO> result = orderInfoDOServiceImplUnderTest.getAppOrderDetailPolicyList(ORDER_CODE);

        // 4. 验证返回 null
        assertThat(result).isNull();
    }

    @Test
    public void testGetAppOrderDetailPolicyList_ParentOrder() {
        // 1. Mock 父订单和子订单
        OrderInfoDO parentOrder = new OrderInfoDO();
        parentOrder.setOrderCode("PARENT_ORDER");
        parentOrder.setOrderType(OrderTypeEnum.PARENT.getCode());

        OrderInfoDO childOrder1 = new OrderInfoDO();
        childOrder1.setOrderCode("CHILD1");
        OrderInfoDO childOrder2 = new OrderInfoDO();
        childOrder2.setOrderCode("CHILD2");

        // 模拟父订单查询返回子订单列表
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(parentOrder);
        when(mockOrderInfoDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(List.of(childOrder1, childOrder2));

        // 2. Mock OrderTermsDO 数据（多个子订单）
        OrderTermsDO terms1 = new OrderTermsDO();
        terms1.setOrderCode("CHILD1");
        terms1.setTermsCode("terms1");

        OrderTermsDO terms2 = new OrderTermsDO();
        terms2.setOrderCode("CHILD2");
        terms2.setTermsCode("terms2");

        when(mockOrderTermsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(List.of(terms1, terms2));

        // 3. Mock PolicyApi 响应
        OrderDetailPolicyRespVO policy1 = new OrderDetailPolicyRespVO();
        policy1.setPolicyCode("terms1");
        OrderDetailPolicyRespVO policy2 = new OrderDetailPolicyRespVO();
        policy2.setPolicyCode("terms2");
        when(mockPolicyApi.getPolicyList(eq(List.of("terms1", "terms2"))))
                .thenReturn(CommonResult.success(List.of(policy1, policy2)));

        // 4. 执行测试
        List<OrderAppDetailPolicyRespVO> result = orderInfoDOServiceImplUnderTest.getAppOrderDetailPolicyList("PARENT_ORDER");

        // 5. 验证结果
        assertThat(result).isNotNull();
        assertThat(result.size()).isEqualTo(2);
        assertThat(result.get(0).getPolicyCode()).isEqualTo("terms1");
        assertThat(result.get(1).getPolicyCode()).isEqualTo("terms2");
    }

    @Test
    public void testGetAppOrderDetailPolicyList_NonParentOrder() {
        // 1. Mock OrderInfoDO（非父订单）
        OrderInfoDO mockOrderInfo = new OrderInfoDO();
        mockOrderInfo.setOrderCode(ORDER_CODE);
        mockOrderInfo.setOrderType(1); // 设置为非父订单类型
        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapper.class)))
                .thenReturn(mockOrderInfo);

        // 2. Mock OrderTermsDO 数据
        OrderTermsDO orderTermsDO = OrderTermsDO.builder()
                .id(0L)
                .consumerCode(CONSUMER_CODE)
                .orderCode(ORDER_CODE)
                .termsCode("termsCode")
                .build();
        when(mockOrderTermsDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(List.of(orderTermsDO));

        // 3. Mock PolicyApi 响应
        OrderDetailPolicyRespVO policyResp = new OrderDetailPolicyRespVO();
        policyResp.setPolicyCode("policyCode");
        policyResp.setPolicyName("policyName");
        policyResp.setPolicyContent("policyContent");
        when(mockPolicyApi.getPolicyList(eq(List.of("termsCode"))))
                .thenReturn(CommonResult.success(List.of(policyResp)));

        // 4. 执行测试
        List<OrderAppDetailPolicyRespVO> result = orderInfoDOServiceImplUnderTest.getAppOrderDetailPolicyList(ORDER_CODE);

        // 5. 验证结果
        assertThat(result).isNotNull();
        assertThat(result.size()).isEqualTo(1);
        OrderAppDetailPolicyRespVO actual = result.get(0);
        assertThat(actual.getPolicyCode()).isEqualTo("policyCode");
        assertThat(actual.getPolicyName()).isEqualTo("policyName");
        assertThat(actual.getPolicyContent()).isEqualTo("policyContent");
    }
    @Test
    public void testGetLatestOrders_EmptyRecords() {
        // Setup
        OrderLatestListReqDTO dto = new OrderLatestListReqDTO();
        dto.setJlrId("testJlrId");
        dto.setPageNo(1);
        dto.setPageSize(10);

        Page<OrderInfoDO> page = new Page<>(1, 10);
        page.setRecords(new ArrayList<>());
        page.setTotal(0L);

        when(mockOrderInfoDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(page);

        // Run the test
        OrderIntegrationLatestOrderRespVO result = orderInfoDOServiceImplUnderTest.getLatestOrders(dto);

        // Verify the results
        assertThat(result).isNotNull();
    }

    @Test
    public void testGetLatestOrders_VCSBusinessWithBundledGoods() {
        // Setup
        OrderLatestListReqDTO dto = new OrderLatestListReqDTO();
        dto.setJlrId("testJlrId");
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setBusinessName("VCS");

        // Create test order
        OrderInfoDO orderInfo = new OrderInfoDO();
        orderInfo.setOrderCode("ORDER001");
        orderInfo.setOrderType(OrderTypeEnum.BUNDLED_GOODS.getCode());
        orderInfo.setCreatedTime(LocalDateTime.now());
        orderInfo.setOrderTime(LocalDateTime.now());
        orderInfo.setCostAmount(10000);

        Page<OrderInfoDO> page = new Page<>(1, 10);
        page.setRecords(List.of(orderInfo));
        page.setTotal(1L);

        when(mockOrderInfoDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(page);

        // Create test order item
        OrderItemDO orderItem = new OrderItemDO();
        orderItem.setOrderCode("ORDER001");
        orderItem.setOrderItemSpuType(2); // BUNDLE_GOOD
        orderItem.setProductQuantity(1);
        orderItem.setProductSalePrice(10000);
        orderItem.setTotalAmount(10000);
        orderItem.setIsDeleted(false);

        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(List.of(orderItem));

        VCSOrderInfoDO vcsOrderInfo = new VCSOrderInfoDO();
        vcsOrderInfo.setOrderCode("ORDER001");
        vcsOrderInfo.setCarVin("VIN123");

        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(List.of(vcsOrderInfo));

        when(mockPiplDataUtil.getDecodeText("VIN123")).thenReturn("VIN123");

        CommonResult<List<IcrVehicleListRespVO>> icrResult = CommonResult.success(new ArrayList<>());
        when(mockIcrVehicleApi.vinInfo(anyList())).thenReturn(icrResult);

        when(mockOrderPaymentRecordsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(new ArrayList<>());

        // Run the test
        OrderIntegrationLatestOrderRespVO result = orderInfoDOServiceImplUnderTest.getLatestOrders(dto);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.getPageResult()).isNotNull();
        assertThat(result.getPageResult().getList()).isNotEmpty();
        assertThat(result.getPageResult().getTotal()).isEqualTo(1L);
    }

    @Test
    public void testGetLatestOrders_BrandGoodsBusiness() {
        // Setup
        OrderLatestListReqDTO dto = new OrderLatestListReqDTO();
        dto.setJlrId("testJlrId");
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setBusinessName("Brand Goods");

        // Create test order
        OrderInfoDO orderInfo = new OrderInfoDO();
        orderInfo.setOrderCode("ORDER002");
        orderInfo.setOrderType(OrderTypeEnum.BRAND_GOOD.getCode());
        orderInfo.setCreatedTime(LocalDateTime.now());
        orderInfo.setOrderTime(LocalDateTime.now());
        orderInfo.setCostAmount(20000);

        Page<OrderInfoDO> page = new Page<>(1, 10);
        page.setRecords(List.of(orderInfo));
        page.setTotal(1L);

        when(mockOrderInfoDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(page);

        // Create test order item
        OrderItemDO orderItem = new OrderItemDO();
        orderItem.setOrderCode("ORDER002");
        orderItem.setProductQuantity(2);
        orderItem.setProductSalePrice(10000);
        orderItem.setTotalAmount(20000);
        orderItem.setIsDeleted(false);



        when(mockOrderPaymentRecordsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(new ArrayList<>());

        // Run the test
        OrderIntegrationLatestOrderRespVO result = orderInfoDOServiceImplUnderTest.getLatestOrders(dto);

        // Verify the results
        assertThat(result).isNotNull();

    }

    @Test
    public void testGetLatestOrders_VCSBusinessWithNormalGoods() {
        // Setup
        OrderLatestListReqDTO dto = new OrderLatestListReqDTO();
        dto.setJlrId("testJlrId");
        dto.setPageNo(1);
        dto.setPageSize(10);
        dto.setBusinessName("VCS");

        // Create test order
        OrderInfoDO orderInfo = new OrderInfoDO();
        orderInfo.setOrderCode("ORDER003");
        orderInfo.setOrderType(OrderTypeEnum.VCS.getCode());
        orderInfo.setCreatedTime(LocalDateTime.now());
        orderInfo.setOrderTime(LocalDateTime.now());
        orderInfo.setCostAmount(15000);

        Page<OrderInfoDO> page = new Page<>(1, 10);
        page.setRecords(List.of(orderInfo));
        page.setTotal(1L);

        when(mockOrderInfoDOMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class))).thenReturn(page);

        // Create test order item
        OrderItemDO orderItem = new OrderItemDO();
        orderItem.setOrderCode("ORDER003");
        orderItem.setOrderItemSpuType(1); // NORMAL_GOOD
        orderItem.setProductQuantity(1);
        orderItem.setProductSalePrice(15000);
        orderItem.setTotalAmount(15000);
        orderItem.setIsDeleted(false);

        when(mockOrderItemDOMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(List.of(orderItem));

        VCSOrderInfoDO vcsOrderInfo = new VCSOrderInfoDO();
        vcsOrderInfo.setOrderCode("ORDER003");
        vcsOrderInfo.setCarVin("VIN456");

        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(List.of(vcsOrderInfo));

        when(mockPiplDataUtil.getDecodeText("VIN456")).thenReturn("VIN456");

        CommonResult<List<IcrVehicleListRespVO>> icrResult = CommonResult.success(new ArrayList<>());
        when(mockIcrVehicleApi.vinInfo(anyList())).thenReturn(icrResult);

        when(mockOrderPaymentRecordsMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(new ArrayList<>());

        // Run the test
        OrderIntegrationLatestOrderRespVO result = orderInfoDOServiceImplUnderTest.getLatestOrders(dto);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.getPageResult()).isNotNull();
        assertThat(result.getPageResult().getList()).isNotEmpty();
        assertThat(result.getPageResult().getTotal()).isEqualTo(1L);
    }


    @Test
    public void testResendMessage_Success() {
        // Setup
        ResendMessageDTO resendMessageDTO = new ResendMessageDTO();
        resendMessageDTO.setOrderCode("LR123456789");
        resendMessageDTO.setReceivePhone("13800138000");
        resendMessageDTO.setServiceName("测试服务");
        resendMessageDTO.setCarVin("VIN1234567890");

        OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(1L)
                .orderCode("LR123456789")
                .orderStatus(1) // ORDERED
                .orderChannel(5) // CUSTOMER_SERVICE
                .orderType(1) // 非父订单
                .build();

        CustomerServiceOrderDO customerServiceOrderDO = CustomerServiceOrderDO.builder()
                .id(1L)
                .orderCode("LR123456789")
                .messageTemplateCode("TEMPLATE_001")
                .build();

        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);
        when(mockCustomerServiceOrderDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(customerServiceOrderDO);
        when(mockCustomerServiceOrderDOMapper.updateById(any(CustomerServiceOrderDO.class))).thenReturn(1);

        // Mock TenantContextHolder
        try (MockedStatic<TenantContextHolder> mockedTenantContextHolder = mockStatic(TenantContextHolder.class)) {
            mockedTenantContextHolder.when(TenantContextHolder::getTenantId).thenReturn(1L);

            // Run the test
            ResendMessageRespVO result = orderInfoDOServiceImplUnderTest.resendMessage(resendMessageDTO);

            // Verify the results
            assertThat(result).isNotNull();
            assertThat(result.getSuccess()).isTrue();

            // Verify interactions
            verify(mockOrderInfoDOMapper).selectOne(any(LambdaQueryWrapperX.class));
            verify(mockCustomerServiceOrderDOMapper).selectOne(any(LambdaQueryWrapperX.class));
            verify(mockCustomerServiceOrderDOMapper).updateById(any(CustomerServiceOrderDO.class));
            verify(mockProducerTool).sendMsg(anyString(), anyString(), anyString());
        }
    }
    @Test
    public void testBindOrder_Success() {
        // Setup
        String orderNumber = "LR123456789";
        String wxPhone = "13800138000";
        String jlrId = "JLR123456";
        String clientId = "CLIENT001";

        OrderInfoDO orderInfoDO = OrderInfoDO.builder()
                .id(1L)
                .orderCode(orderNumber)
                .orderStatus(1) // ORDERED
                .orderChannel(5) // CUSTOMER_SERVICE
                .orderType(1) // 非父订单
                .build();

        CustomerServiceOrderDO customerServiceOrderDO = CustomerServiceOrderDO.builder()
                .id(1L)
                .orderCode(orderNumber)
                .bindCustomer(0) // 未绑定
                .build();

        List<OrderTermsDO> orderTermsDOS = Arrays.asList(
                OrderTermsDO.builder().id(1L).orderCode(orderNumber).build(),
                OrderTermsDO.builder().id(2L).orderCode(orderNumber).build()
        );

        List<VCSOrderInfoDO> vcsOrderInfoDOS = Arrays.asList(
                VCSOrderInfoDO.builder().id(1L).orderCode(orderNumber).seriesCode("SERIES001").seriesName("Series Name 1").build(),
                VCSOrderInfoDO.builder().id(2L).orderCode(orderNumber).seriesCode("SERIES002").seriesName("Series Name 2").build()
        );

        when(mockOrderInfoDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(orderInfoDO);
        when(mockCustomerServiceOrderDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(customerServiceOrderDO);
        when(mockOrderTermsDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(orderTermsDOS);
        when(mockVcsOrderInfoDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(vcsOrderInfoDOS);
        when(mockOrderInfoDOMapper.updateById(any(OrderInfoDO.class))).thenReturn(1);

        when(mockCustomerServiceOrderDOMapper.updateById(any(CustomerServiceOrderDO.class))).thenReturn(1);
        when(mockPermissionApi.getEncryptText(anyString())).thenReturn(CommonResult.success("encryptedPhone"));

        // Mock TenantContextHolder
        try (MockedStatic<TenantContextHolder> mockedTenantContextHolder = mockStatic(TenantContextHolder.class)) {
            mockedTenantContextHolder.when(TenantContextHolder::getTenantId).thenReturn(1L);

            // Run the test
            OrderBindRespVO result = orderInfoDOServiceImplUnderTest.bindOrder(orderNumber, wxPhone, jlrId, clientId);

            // Verify the results
            assertThat(result).isNotNull();
            assertThat(result.isSuccess()).isTrue();

        }
    }
    @Test
    public void testPreValidateOrderInfo_CheckManualRenewInTransitReturnsResult() {
        // Setup
        OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        globalInfoDTO.setConsumerCode(CONSUMER_CODE);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        OrderShopCarItemDTO itemDTO = getCarItemDTO();
        itemDTO.setCarVin("VIN123");
        itemDTO.setCartItemType(1);
        orderCreateDTO.setShopCarItemList(List.of(itemDTO));
        OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);

        // Configure ProductSkuApi.verifyOrderProducts(...)
        ProductSnapshotDTO snapshotDTO = new ProductSnapshotDTO();
        BeanUtil.copyProperties(itemDTO, snapshotDTO);
        snapshotDTO.setProductSalePrice(100L);

        LinkedHashMap<String, Object> rawData = new LinkedHashMap<>();
        rawData.put("productSkuCode", snapshotDTO.getProductSkuCode());
        rawData.put("productSalePrice", snapshotDTO.getProductSalePrice());

        Map<String, LinkedHashMap> rawDataMap = new HashMap<>();
        rawDataMap.put(snapshotDTO.getProductSkuCode(), rawData);

        when(mockProductSkuApi.verifyOrderProducts(any())).thenReturn(CommonResult.success(rawDataMap));

        // Mock checkManualRenewInTransit to return a result by mocking manualRenewServiceApi
        when(mockOrderInfoDOMapper.selectUnpaidOrAfterSalesOrdersForVCS(anyList())).thenReturn(Collections.emptyList());
        when(mockVcsOrderFulfilmentApi.checkCanBuyServiceV2(anyList())).thenReturn(CommonResult.success(true));
        OrderCreateRespVO mockRespVO = new OrderCreateRespVO();
        mockRespVO.setHasManualRenewalOrder(true);
        when(mockManualRenewServiceApi.checkRecordsInTransit(anyList())).thenReturn(
                CommonResult.success(true)
        );

        // Run the test
        OrderCreateRespVO result = orderInfoDOServiceImplUnderTest.preValidateOrderInfo(orderCreateDTO);

        // Verify the results
        assertThat(result).isNotNull();
        assertThat(result.getHasManualRenewalOrder()).isTrue();

        // Verify interactions
        verify(mockProductSkuApi).verifyOrderProducts(any());
        verify(mockOrderInfoDOMapper).selectUnpaidOrAfterSalesOrdersForVCS(anyList());
        verify(mockVcsOrderFulfilmentApi).checkCanBuyServiceV2(anyList());
        verify(mockManualRenewServiceApi).checkRecordsInTransit(anyList());
    }

    @Test
    public void testCreateOrderInfoHasChangedICR() {
        // Setup
        final OrderCreateDTO orderCreateDTO = new OrderCreateDTO();
        final GlobalInfoDTO globalInfoDTO = new GlobalInfoDTO();
        globalInfoDTO.setGlobalBrandCode(LR);
        globalInfoDTO.setChannelCode(MLR);
        orderCreateDTO.setGlobalInfoDTO(globalInfoDTO);
        final OrderShopCarItemDTO itemDTO = getCarItemDTO();
        orderCreateDTO.setShopCarItemList(List.of(itemDTO));
        final OrderInfoDTO orderInfoDTO = getOrderInfoDTO();
        orderCreateDTO.setOrderInfoDTO(orderInfoDTO);
        final OrderGiftAddressDTO orderGiftAddressDTO = getOrderGiftAddressDTO();
        orderCreateDTO.setGiftInfoDTO(orderGiftAddressDTO);

        ValueOperations<String, Object> valueOpsMock = mock(ValueOperations.class);
        when(mockRedisTemplate.opsForValue()).thenReturn(valueOpsMock);
        when(valueOpsMock.get(eq("icr-check:null:carVin"))).thenReturn(null);
        when(valueOpsMock.get(eq("icr-valid:null:carVin"))).thenReturn("change");

        // Verify the results
        OrderCreateRespVO orderInfo = orderInfoDOServiceImplUnderTest.createOrderInfo(orderCreateDTO, CLIENT_ID);
        assertEquals(true, orderInfo.getHasChangedICR());
    }
}

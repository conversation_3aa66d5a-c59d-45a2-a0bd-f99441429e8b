apiVersion: apps/v1
kind: Deployment
metadata:
  name: ecp-order-service
spec:
  template:
    spec:
      containers:
        - name: ecp-order-service
          readinessProbe:
            httpGet:
              scheme: HTTP
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 18
          livenessProbe:
            httpGet:
              scheme: HTTP
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 10
          startupProbe:
            httpGet:
              scheme: HTTP
              path: /actuator/health
              port: 8080
            periodSeconds: 10
            failureThreshold: 30
            initialDelaySeconds: 10
            timeoutSeconds: 10